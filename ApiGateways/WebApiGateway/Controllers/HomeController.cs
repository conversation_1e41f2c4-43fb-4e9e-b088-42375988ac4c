using Microsoft.AspNetCore.Mvc;
using Core.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

namespace WebApiGateway.Controllers
{
    public class HomeController : AbpController
    {
        private readonly IConfiguration _configuration;

        public HomeController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IActionResult Index()
        {
            int.TryParse(_configuration["MicroserviceSwagger:IsEnable"], out var isEnableSwagger);
            return isEnableSwagger == 1 ? Redirect("/swagger") : Redirect("/");
        }
    }
}