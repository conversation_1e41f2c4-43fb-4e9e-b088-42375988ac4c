using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository.Merchant.FamilyMart;

namespace Merchant.FamilyMart.Invoice01.Application.Model.Request.RabbitMQ.SubscribeEventHandler
{
    public class FamilyMartInvoicePublishEventHandler : IDistributedEventHandler<FamilyMartInvoiceSendData>, ITransientDependency
    {
        private readonly IMongoFamilyMartInvoiceRepository _mongoFamilyMartInvoiceRepository;
        private ILogger<FamilyMartInvoicePublishEventHandler> _logger;
        public FamilyMartInvoicePublishEventHandler(IMongoFamilyMartInvoiceRepository mongoFamilyMartInvoiceRepository,
            ILogger<FamilyMartInvoicePublishEventHandler> logger)
        {
            _mongoFamilyMartInvoiceRepository = mongoFamilyMartInvoiceRepository;
            _logger = logger;
        }

        public async Task HandleEventAsync(FamilyMartInvoiceSendData eventData)
        {
            try
            {
                await _mongoFamilyMartInvoiceRepository.InsertManyAsync(eventData.Invoices);
            }
            catch (System.Exception e)
            {
                _logger.LogError("Rabbit thu" + eventData.dem);
                _logger.LogError("Rabbit co loi xay ra " + e.Message);
            }
        }
    }
}
