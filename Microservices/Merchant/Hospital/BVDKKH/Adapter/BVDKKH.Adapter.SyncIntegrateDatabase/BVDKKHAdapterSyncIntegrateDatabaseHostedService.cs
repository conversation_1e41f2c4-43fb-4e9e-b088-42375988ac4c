using Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Threading;
using System.Threading.Tasks;

namespace BVDKKH.Adapter.SyncIntegrateDatabase
{
    public class BVDKKHAdapterSyncIntegrateDatabaseHostedService : BackgroundService
    {
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using (var application = AbpApplicationFactory.Create<BVDKKHAdapterSyncIntegrateDatabaseApplicationModule>(options =>
            {
                options.UseAutofac();
                options.Services.AddLogging(c => c.AddSerilog());
            }))
            {
                application.Initialize();
            }

            return Task.CompletedTask;

        }
    }
}
