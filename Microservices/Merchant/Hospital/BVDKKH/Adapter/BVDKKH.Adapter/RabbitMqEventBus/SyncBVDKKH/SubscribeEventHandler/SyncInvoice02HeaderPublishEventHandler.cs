//using BVDKKH.Adapter.Factories.Services;
//using BVDKKH.Adapter.Repositories;
//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Localization.Resources.AbpLocalization;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using Core.Shared.MessageEventsData.SyncDbtg;
//using Dapper;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.Localization;
//using Microsoft.Extensions.Logging;
//using System;
//using System.Data.SqlClient;
//using System.Threading.Tasks;

//namespace BVDKKH.Adapter.RabbitMqEventBus.SyncBVDKKH.SubscribeEventHandler
//{
//    public class SyncInvoice02HeaderPublishEventHandler : IDistributedEventHandler<SyncInvoice02HeaderEventSendData>, ITransientDependency
//    {
//        private readonly IInvoice02HeaderShareDbRepository _invoice01HeaderShareDbRepository;
//        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
//        private readonly ILogger<SyncInvoice02HeaderPublishEventHandler> _logger;
//        private readonly ISyncInvoice02Service _syncInvoice01Service;
//        private readonly IConfiguration _configuration;
//        private readonly IAppFactory _appFactory;

//        public SyncInvoice02HeaderPublishEventHandler(IStringLocalizer<CoreLocalizationResource> localizer,
//            IInvoice02HeaderShareDbRepository invoice01HeaderShareDbRepository,
//            ILogger<SyncInvoice02HeaderPublishEventHandler> logger,
//            IConfiguration configuration,
//            ISyncInvoice02Service syncInvoice01Service,
//            IAppFactory appFactory)
//        {
//            _localizer = localizer;
//            _logger = logger;
//            _syncInvoice01Service = syncInvoice01Service;
//            _appFactory = appFactory;
//            _configuration = configuration;
//            _invoice01HeaderShareDbRepository = invoice01HeaderShareDbRepository;
//        }

//        public async Task HandleEventAsync(SyncInvoice02HeaderEventSendData eventData)
//        {
//            try
//            {
//                // update hd về dbtg core pdbvnis
//                await _invoice01HeaderShareDbRepository.SyncInvoiceNoAsync(eventData);

//                // update hd về dbtg của bệnh viện 
//                var connectionString = _configuration.GetSection("ConnectionStrings:Default").Value;
//                using (SqlConnection myConnection = new SqlConnection(connectionString))
//                {
//                    myConnection.Open();
//                    var queryUpdate = $@"UPDATE Invoices SET
//                                                ""InvoiceNo"" = '{eventData.InvoiceNo}',
//                                                ""SyncedByVNIs"" = 1,
//                                                ""LastSyncedByVnis"" = '{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff")}'
//                                             WHERE ""IdTransaction"" = '{eventData.TransactionId}' AND ""IdInvoiceTP"" = '{eventData.IdInvoiceTP}'";

//                    var invoices = await DapperSqlServerExtension.QueryWrapperAsync(connectionString, async (connection) =>
//                    {
//                        return await connection.QueryAsync(queryUpdate);
//                    });
//                }
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError($"HandleEvent Error: {ex.Message}");
//                _logger.LogError($"HandleEvent Error: {ex.InnerException}");
//            }
//        }
//    }
//}
