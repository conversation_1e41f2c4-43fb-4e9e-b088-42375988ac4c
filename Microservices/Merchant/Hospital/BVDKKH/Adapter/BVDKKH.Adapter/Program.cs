using Core.VaultSharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;

namespace BVDKKH.Adapter
{
    public class Program
    {
        public static int Main(string[] args)
        {
            //// ConfigureLogging.Configure();

            try
            {
                Log.Information("Starting BVDKKH.Adapter.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "BVDKKH.Adapter terminated unexpectedly!");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }

        }

        public static IHostBuilder CreateHostBuilder(string[] args)
        {
            try
            {
                return VaultConfigure.CreateHostBuilder(args)
                                    .ConfigureServices((hostContext, services) =>
                                    {
                                        ConfigureServices(services, hostContext.Configuration);
                                        services.AddHostedService<BVDKKHAdapterInvoiceHostedService>();
                                    })
                                    .UseAutofac()
                                    .UseSerilog();
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }
        }

        private static ServiceProvider ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            services.AddApplication<BVDKKHAdapterInvoiceApplicationModule>();

            return services.BuildServiceProvider();
        }

        #region
        //public static int Main(string[] args)
        //{
        //    // ConfigureLogging.Configure();

        //    try
        //    {
        //        Log.Debug("Starting BenhVienDaKhoaKhanhHoa.Adapter.");
        //        CreateHostBuilder(args).Build().Run();

        //        return 0;
        //    }
        //    catch (Exception ex)
        //    {
        //        Log.Fatal(ex, "BenhVienDaKhoaKhanhHoa.Adapter terminated unexpectedly!");
        //        return 1;
        //    }
        //    finally
        //    {
        //        Log.CloseAndFlush();
        //    }
        //}
        //public static IHostBuilder CreateHostBuilder(string[] args) =>
        //            Host.CreateDefaultBuilder(args)
        //                .ConfigureServices((_, services) =>
        //                {
        //                    ConfigureServices(services);
        //                    services.AddHostedService<BVDKKHAdapterInvoiceHostedService>();
        //                })
        //                .UseAutofac()
        //                .UseSerilog();

        //private static ServiceProvider ConfigureServices(IServiceCollection services)
        //{
        //    services.AddApplication<BVDKKHAdapterInvoiceApplicationModule>();

        //    return services.BuildServiceProvider();
        //}
        #endregion
    }
}