using Core.Shared.Validations;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VnisCore.Integrated.Dbtg.BVDKKH.Application
{
    public static class ServiceCollectionExtension
    {

        public static void AddConfigValidator<T>(this IServiceCollection services, List<Type> rules) where T : class
        {
            foreach (var rule in rules)
            {
                services.AddScoped(typeof(IValidationRule), rule);
            }

            services.AddScoped<IValidator, Validator<T>>((provider) =>
            {
                return new Validator<T>(provider, rules);
            });
        }

    }
}
