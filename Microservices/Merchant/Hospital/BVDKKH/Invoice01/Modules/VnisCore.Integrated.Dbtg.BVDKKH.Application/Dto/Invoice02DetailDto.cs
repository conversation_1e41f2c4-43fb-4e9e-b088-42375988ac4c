using System.Collections.Generic;

namespace VnisCore.Integrated.Dbtg.BVDKKH.Application.Dto
{
    public class Invoice02DetailDto
    {
        public long Id { get; set; }
        public int Index { get; set; }

        /// <summary>
        /// Chiết khấu
        /// </summary>
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        public decimal DiscountPercent { get; set; }

        /// <summary>
        /// Tổng tiền sau thuế
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tiền sau thuế phải lớn hơn hoặc bằng 0")]
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// Mã sản phẩm
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Tên đơn vị tính
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Đơn giá
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Tổng tiền hàng
        /// </summary>
        //[Range(0, double.MaxValue, ErrorMessage = "Tiền hàng phải lớn hơn hoặc bằng 0")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        public string TenantId { get; set; }

        public long InvoiceHeaderId { get; set; }

        public long ProductId { get; set; }
        public long UnitId { get; set; }
        public int RoundingUnit { get; set; }
        public bool HideQuantity { get; set; }
        public bool HideUnit { get; set; }
        public bool HideUnitPrice { get; set; }
        public long Partition { get; set; }
    }

}
