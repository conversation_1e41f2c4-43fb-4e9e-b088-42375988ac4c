//using Core.Domain.Repositories;
//using Core.Localization.Resources.AbpLocalization;
//using Core.Shared.Constants;
//using Core.Shared.Factory;
//using Core.Shared.Validations;
//using Core.Tvan.Constants;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Localization;
//using System;
//using System.Linq;
//using System.Threading.Tasks;
//using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
//using VnisCore.Integrated.Dbtg.BVDKKH.Application.Models.Requests.Commands;
//using VnisCore.Integrated.Dbtg.BVDKKH.Application.Repositories;

//namespace VnisCore.Integrated.Dbtg.BVDKKH.Application.ValidationRules.Delete
//{
//    public class DeleteInvoice02ApiCheckInfoErrorRule : IValidationRuleAsync<DeleteInvoice02ApiRequestModel, ValidationResult>
//    {
//        private readonly IValidationContext _validationContext;
//        private readonly IAppFactory _appFactory;
//        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

//        public DeleteInvoice02ApiCheckInfoErrorRule(
//            IValidationContext validationContext,
//            IAppFactory appFactory,
//            IStringLocalizer<CoreLocalizationResource> localizier)
//        {
//            _validationContext = validationContext;
//            _appFactory = appFactory;
//            _localizier = localizier;
//        }

//        public async Task<ValidationResult> HandleAsync(DeleteInvoice02ApiRequestModel input)
//        {
//            var tenantId = _validationContext.GetItem<Guid>("TenantId");
//            var invoiceReference = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

//            var repoRegistration = _appFactory.GetServiceDependency<INewRegistrationHeaderRepository>();
//            var registration = await repoRegistration.GetLastRegistration(tenantId);

//            if (registration.InvoiceHasCode || (!registration.InvoiceHasCode && registration.SendInvoiceMethod == (short)RegistrationSendInvoiceMethod.SendInvoice))
//            {
//                var invoice02ErrorRepo = _appFactory.Repository<Invoice02ErrorEntity, long>();
//                var infoErrors = await invoice02ErrorRepo.Where(x => x.TenantId == tenantId && x.InvoiceHeaderId == invoiceReference.Id).ToListAsync();

//                if (infoErrors.Count() == 0)
//                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.Delete.CannotDeleteHasNoErrorNotice"]);

//                var infoErrorAdj = infoErrors.OrderByDescending(x => x.CreationTime).FirstOrDefault(x => x.Action == (short)TThaiTBao.Huy);

//                if (infoErrorAdj == null)
//                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.Delete.CannotDeleteWrongNatureNotice"]);

//                if (infoErrorAdj.SignStatus != SignStatus.DaKy.GetHashCode())
//                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.Delete.CannotDeleteNotSignInvoiceError"]);

//                if (infoErrorAdj.TvanStatus == 0)
//                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.Delete.CannotDeleteHasNoAcceptForTCT"]);
//            }

//            return new ValidationResult(true);
//        }

//    }
//}


