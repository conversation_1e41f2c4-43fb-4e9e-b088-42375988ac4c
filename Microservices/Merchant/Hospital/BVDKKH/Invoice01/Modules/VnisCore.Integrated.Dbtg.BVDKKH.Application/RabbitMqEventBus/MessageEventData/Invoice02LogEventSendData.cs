using Core.EventBus;
using VnisCore.Integrated.Dbtg.BVDKKH.Application.Models;

namespace VnisCore.Integrated.Dbtg.BVDKKH.Application.RabbitMqEventBus.MessageEventData
{
    [EventName("invoice02.invoicelog02")]
    public class Invoice02LogEventSendData : Invoice02LogModel
    {
        public Invoice02LogEventSendData()
        {

        }

        public Invoice02LogEventSendData(Invoice02LogModel item)
        {
            TenantId = item.TenantId;
            UserId = item.UserId;
            Action = item.Action;
            ActionTime = item.ActionTime;
            InvoiceHeaderId = item.InvoiceHeaderId;
            Partition = item.Partition;
            InvoiceType = item.InvoiceType;
            UserName = item.UserName;
        }
    }
}
