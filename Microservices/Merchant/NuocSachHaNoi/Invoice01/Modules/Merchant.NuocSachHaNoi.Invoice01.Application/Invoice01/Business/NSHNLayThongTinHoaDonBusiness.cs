using Core;
using Core.Domain.Repositories;
using Core.Shared.Factory;

using Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Models.Requests.Queries;
using Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Models.Responses;

using Microsoft.EntityFrameworkCore;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Business
{
    public interface INSHNLayThongTinHoaDonBusiness
    {
        Task<List<NSHNInvoiceInfoResponseModel>> LayThongTinHoaDonAsync(NSHNInvoiceInfoRequestModel nSHNInvoiceInfoRequestModel);
    }

    public class NSHNLayThongTinHoaDonBusiness : INSHNLayThongTinHoaDonBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;

        public NSHNLayThongTinHoaDonBusiness(
            IAppFactory appFactory,
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header)
        {
            _appFactory = appFactory;
            _repoInvoice01Header = repoInvoice01Header;
        }

        public async Task<List<NSHNInvoiceInfoResponseModel>> LayThongTinHoaDonAsync(NSHNInvoiceInfoRequestModel nSHNInvoiceInfoRequestModel)
        {
            var result = new List<NSHNInvoiceInfoResponseModel>();
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            // x => x.ErpId.Substring(0, x.ErpId.Length - 5) => phải query như này vì lúc tạo hóa đơn mã đang lưu định dạng ErpId = $"{item.Ma_hoa_don}-{invoiceDate.Year}",
            var data = await _repoInvoice01Header.AsNoTracking()
                                         .Where(x => x.BuyerCode == nSHNInvoiceInfoRequestModel.MaKhachHang
                                                   && x.TenantId == tenantId)
                                         .WhereIf(!string.IsNullOrEmpty(nSHNInvoiceInfoRequestModel.MaHoaDon) && nSHNInvoiceInfoRequestModel.Nam.HasValue, x => x.ErpId == (nSHNInvoiceInfoRequestModel.MaHoaDon + "-" + nSHNInvoiceInfoRequestModel.Nam.ToString()))
                                         .WhereIf(!string.IsNullOrEmpty(nSHNInvoiceInfoRequestModel.MaHoaDon) && !nSHNInvoiceInfoRequestModel.Nam.HasValue, x => x.ErpId.Substring(0, x.ErpId.Length - 5) == nSHNInvoiceInfoRequestModel.MaHoaDon)
                                         .WhereIf(nSHNInvoiceInfoRequestModel.Thang.HasValue, x => x.InvoiceDateMonth == nSHNInvoiceInfoRequestModel.Thang)
                                         .WhereIf(nSHNInvoiceInfoRequestModel.Nam.HasValue, x => x.InvoiceDateYear == nSHNInvoiceInfoRequestModel.Nam)
                                         .ToListAsync();

            if (!data.Any())
            {
                throw new UserFriendlyException($@"Không tìm thấy thông tin với mã khách hàng {nSHNInvoiceInfoRequestModel.MaKhachHang}");
            }

            foreach (var item in data)
            {
                result.Add(new NSHNInvoiceInfoResponseModel
                {
                    TemplateNo = item.TemplateNo,
                    SerialNo = item.SerialNo,
                    InvoiceNo = item.InvoiceNo,
                    InvoiceDate = item.InvoiceDate,
                    InvoiceStatus = item.InvoiceStatus,
                    IsDeclared = item.IsDeclared,
                    SignStatus = item.SignStatus,
                    ErpId = string.IsNullOrEmpty(item.ErpId) ? null : item.ErpId.Substring(0, item.ErpId.Length - 5)
                });
            }

            return result;
        }
    }
}
