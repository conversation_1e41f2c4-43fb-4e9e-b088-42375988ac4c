using Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Models.Responses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Interfaces
{
    public interface INumberService<THeader> where THeader : BaseInvoiceHeader
    {
        /// <summary>
        /// Sinh số hóa đơn
        /// </summary>
        /// <param name="tenantCode">Tenant cần sinh số</param>
        /// <param name="templateNo">Mẫu só</param>
        /// <param name="serialNo"><PERSON><PERSON> hi<PERSON></param>
        /// <param name="date">Ngày</param>
        /// <returns></returns>
        Task<NumberResponseModel> GenerateNumberAsync(Guid tenantCode, short templateNo, string serialNo, DateTime date, int errorTimes = 0);

        /// <summary>
        /// <PERSON><PERSON><PERSON> số
        /// </summary>
        /// <param name="code"></param>
        /// <param name="number"></param>
        Task SaveNumberAsync(long id, NumberResponseModel number);
    }
}
