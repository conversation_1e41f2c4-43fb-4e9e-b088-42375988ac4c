using System.ComponentModel.DataAnnotations;

namespace Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Models.Requests.Queries
{
    public class NSHNTraCuuRequestModel
    {
        /// <summary>
        /// BuyerCode: M<PERSON> khách hàng
        /// </summary>
        [Required(ErrorMessage = "Mã khách hàng không được để trống")]
        [MaxLength(500, ErrorMessage = "Mã khách dài tối đa 500 ký tự")]
        public string <PERSON>KhachHang { get; set; }

        [Range(2020, 9999, ErrorMessage = "Năm phải nằm trong khoảng từ 2022 đến 9999")]
        public short? Nam { get; set; }

        [Range(1, 12, ErrorMessage = "Tháng phải nằm trong khoảng từ 1 đến 12")]
        public short? Thang { get; set; }
    }
}
