using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.Models;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Merchant.NuocSachHaNoi.Invoice01.Application.Invoice01.ValidationRules.Create
{
    public class CreateInvoiceCheckLicenseRule : IValidationRuleAsync<ValidateCreateInvoice01Model, ValidationResult>
    {
        private readonly ILicenseSharedService _licenseSharedService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;

        public CreateInvoiceCheckLicenseRule(ILicenseSharedService licenseSharedService,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory)
        {
            _licenseSharedService = licenseSharedService;
            _localizer = localizer;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(ValidateCreateInvoice01Model input)
        {
            var tenantCode = _appFactory.CurrentTenant.Id.Value;

            var cacheLicense = await _licenseSharedService.GetAsync(tenantCode);
            if (cacheLicense == null || cacheLicense.LicenseKey == null || !_licenseSharedService.CheckUseLicense(cacheLicense, 1))
                return new ValidationResult(false, "License hết hạn hoặc chưa đăng ký license");

            return new ValidationResult(true);
        }
    }
}
