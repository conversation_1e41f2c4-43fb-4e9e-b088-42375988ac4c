using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Dapper;

using Microsoft.Extensions.Localization;

using ReSendTaxReport01.Models;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Net;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using Core.Shared.Services;
using Serilog;
using Core.MultiTenancy;

namespace ReSendTaxReport01.Business
{
    public interface ISignService
    {
        Task<UsbTokenEntity> GetCertificateAsync(Guid idUser, Guid tenantId, long? idTemplate = null);

        Task<SignServerSettingModel> GetSettingsAsync(Guid tenantId);

        Task<string> GetTokenAsync(SignServerSettingModel setting, bool isSignHsm = false);

        Task<bool> IsSignHsmAsync(Guid tenantId);

        Task CreateBatch123Async(string host, string token, UsbTokenEntity certificate, bool isSignHsm = false);

        Task<string> Sign123Async(string host, string token, string requestUri, string xml, string tag);

        /// <summary>
        /// xóa batch ký
        /// </summary>
        /// <param name="host"></param>
        /// <param name="token"></param>
        /// <returns></returns>
        Task<bool> RemoveBatch123Async(string host, string token);
    }

    public class SignServer : ISignService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ISettingService _settingService;

        public SignServer(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ISettingService settingService)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _settingService = settingService;
        }

        public async Task CreateBatch123Async(string host, string token, UsbTokenEntity certificate, bool isSignHsm = false)
        {
            var cert = new object();

            //ký = hsm
            if (isSignHsm)
            {
                cert = new
                {
                    _CKA_LABEL = certificate.CkaLabel,
                    _CKA_ID = certificate.CkaId,
                    _SLOT_ID = certificate.TokenId,
                    _SubjectName = certificate.SubjectName,
                    _Issuer = certificate.Issuer,
                    _StartDate = certificate.StartDate.Date.ToString("yyyy-MM-dd"),
                    _EndDate = certificate.EndDate.Date.ToString("yyyy-MM-dd"),
                    _SerialNumber = certificate.SerialNumber
                };
            }
            else
            {
                cert = new
                {
                    _CKA_LABEL = certificate.CkaLabel,
                    _CKA_ID = certificate.CkaId,
                    _TOKEN_ID = certificate.TokenId,
                    _SubjectName = certificate.SubjectName,
                    _Issuer = certificate.Issuer,
                    _StartDate = certificate.StartDate.Date.ToString("yyyy-MM-dd"),
                    _EndDate = certificate.EndDate.Date.ToString("yyyy-MM-dd"),
                    _SerialNumber = certificate.SerialNumber
                };
            }

            var content = cert.JsonSerialize(false, false);
            var message = new HttpRequestMessage(HttpMethod.Post, "api/batch-signer/xmlnd123/create");
            message.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            message.Content = new StringContent($"certificate={content}", Encoding.UTF8, "application/x-www-form-urlencoded");

            var client = new HttpClient();
            client.BaseAddress = new Uri(host);
            var response = await client.SendAsync(message);
            var result = await response.Content.ReadAsStringAsync();

            if (response.StatusCode != HttpStatusCode.OK)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.CreateBatch.Error", new string[] { result }]);
        }

        public async Task<UsbTokenEntity> GetCertificateAsync(Guid idUser, Guid tenantId, long? idTemplate = null)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var idCertificates = new List<long>();
            if (idTemplate.HasValue)
            {
                idCertificates = await GetCertificateByUserAndTemplateAsync(idUser, idTemplate.Value);
            }
            else
            {
                idCertificates = await GetCertificateByUserAsync(idUser);
            }

            var query = $"Select * from \"UsbToken\" where \"Id\" in ({string.Join(",", idCertificates)}) " +
                        $"and \"IsOnServer\" = {1} " +
                        $"and \"TenantId\" = '{rawTenantId}' " +
                        $"and \"StartDate\" <= '{DateTime.Now.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'" +
                        $"and \"EndDate\" >= '{DateTime.Now.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'" +
                        $"and \"IsDeleted\" = {0} ";
            var certificate = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<UsbTokenEntity>(query);

            return certificate;
        }

        public async Task<SignServerSettingModel> GetSettingsAsync(Guid tenantId)
        {
            //var userName = (await _settingService.GetByCodeAsync(tenantId, SettingKey.UserNameSignServer.ToString()))?.Value;
            //var host = (await _settingService.GetByCodeAsync(tenantId, SettingKey.UrlSignServer.ToString()))?.Value;
            //var password = (await _settingService.GetByCodeAsync(tenantId, SettingKey.PasswordSignServer.ToString()))?.Value;

            var userName = (await GetSettingByCodeAsync(tenantId, SettingKey.UserNameSignServer.ToString()))?.Value;
            var host = (await GetSettingByCodeAsync(tenantId, SettingKey.UrlSignServer.ToString()))?.Value;
            var password = (await GetSettingByCodeAsync(tenantId, SettingKey.PasswordSignServer.ToString()))?.Value;

            return new SignServerSettingModel
            {
                Host = host,
                UserName = userName,
                Password = password
            };
        }

        public async Task<string> GetTokenAsync(SignServerSettingModel setting, bool isSignHsm = false)
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(setting.Host);

            var content = new StringContent($"username={setting.UserName}&password={setting.Password}", Encoding.UTF8, "application/x-www-form-urlencoded");

            var result = await client.PostAsync("api/auth/login", content);
            var token = await result.Content.ReadAsStringAsync();
            if (result.StatusCode != HttpStatusCode.OK)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.LoginSignServer.CannotLogin"]);

            token = token.Replace("\"", "");

            return token;
        }

        public async Task<bool> IsSignHsmAsync(Guid tenantId)
        {
            var isSignHsm = false;

            //var settingUsingHsm = await _settingService.GetByCodeAsync(tenantId, SettingKey.SignHsm.ToString());
            var settingUsingHsm = await GetSettingByCodeAsync(tenantId, SettingKey.SignHsm.ToString());
            if (settingUsingHsm != null && settingUsingHsm.Value == "1")
                isSignHsm = true;

            return isSignHsm;
        }

        public async Task<bool> RemoveBatch123Async(string host, string token)
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(host);
            var message = new HttpRequestMessage(HttpMethod.Get, $"api/batch-signer/xmlnd123/remove");
            message.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var result = await client.SendAsync(message);

            if (result.StatusCode != HttpStatusCode.OK)
            {
                var content = await result.Content.ReadAsStringAsync();
                Log.Error("Không thể hủy Batch ký: " + content);
                return false;
            }
            return true;
        }

        public async Task<string> Sign123Async(string host, string token, string requestUri, string xml, string tag)
        {
            var client = new HttpClient();
            client.BaseAddress = new Uri(host);

            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, requestUri);

            request.Content = new StringContent($"inputXml={WebUtility.UrlEncode(xml)}&tag={tag}", Encoding.UTF8, "application/x-www-form-urlencoded");
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

            var result = await client.SendAsync(request);
            var content = await result.Content.ReadAsStringAsync();
            if (result.StatusCode != HttpStatusCode.OK)
            {
                Log.Error($"Ký lỗi: {content}");
                return null;
            }

            // Kết quả trả về là string với nôi dung xml, tuy nhiên kiểu trả về lại là application/json
            // Nên cần convert về lại string
            return await result.Content.ReadAsStringAsync();
        }


        #region

        private async Task<TenantSetting> GetSettingByCodeAsync(Guid tenantId, string code)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var sql = $@"SELECT ""Value"" FROM ""VnisSettings"" WHERE ""TenantId"" = '{rawTenantId}' AND ""Code"" = '{code}' ";

            var setting = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<TenantSetting>(sql.ToString());

            return setting;
        }

        private async Task<List<long>> GetCertificateByUserAndTemplateAsync(Guid idUser, long idTemplate)
        {
            //Kiểm tra mẫu hóa đơn có được sử dụng cho tài khoản hiện tại không
            var query = $"Select * from \"AccountTokenTemplate\" where \"TemplateId\" = {idTemplate}";
            var certificatesByTemplate = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<AccountTokenTemplateEntity>(query)).ToList();

            if (!certificatesByTemplate.Any(x => x.UserId == idUser && !x.UsbTokenId.HasValue))
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.AccountTokenTemplate.NotFound"]);

            //Lấy các chứng thư số có thể ký mẫu hóa đơn hiện tại
            var certificateByTemplate = certificatesByTemplate.Where(x => x.UsbTokenId.HasValue && !x.UserId.HasValue)
                                                                      .ToList();

            if (certificateByTemplate == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.CertificateTemplate.NotFound"]);

            var codeCertificateByTemplates = certificateByTemplate.Select(x => x.UsbTokenId.Value);

            //Lấy các chứng thư số người dùng có thể sử dụng
            query = $"Select * from \"AccountTokenTemplate\" where \"UserId\" = '{OracleExtension.ConvertGuidToRaw(idUser)}' and \"TemplateId\" is null and \"UsbTokenId\" is not null";
            var certificateByUser = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<AccountTokenTemplateEntity>(query)).ToList();

            if (certificateByUser.Count == 0)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.CertificateByUser.NotFound"]);

            var idCertificateByUsers = certificateByUser.Select(x => x.UsbTokenId.Value);

            //Các mẫu hóa đơn người sử dụng dc ký và chứng thư số có thể ký
            var idCertificates = codeCertificateByTemplates
                .Where(x => idCertificateByUsers.Contains(x))
                .ToList();

            if (idCertificates.Count == 0)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Certificate.NotFound"]);

            return idCertificates;
        }

        private async Task<List<long>> GetCertificateByUserAsync(Guid idUser)
        {
            //Lấy các chứng thư số người dùng có thể sử dụng
            var query = $"Select * from \"AccountTokenTemplate\" where \"UserId\" = '{OracleExtension.ConvertGuidToRaw(idUser)}' and \"TemplateId\" is null and \"UsbTokenId\" is not null";

            var certificateByUser = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<AccountTokenTemplateEntity>(query)).ToList();
            if (certificateByUser.Count == 0)
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.CertificateByUser.NotFound"]);

            return certificateByUser.Select(x => x.UsbTokenId.Value).ToList();
        }

        #endregion
    }
}
