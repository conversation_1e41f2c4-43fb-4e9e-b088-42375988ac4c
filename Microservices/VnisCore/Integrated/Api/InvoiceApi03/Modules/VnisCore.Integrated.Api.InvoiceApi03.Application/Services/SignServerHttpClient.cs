using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Services
{
    public class SignServerHttpClient : ISignServerHttpClient
    {
        private readonly IAppFactory _appFactory;
        private readonly HttpClient _client;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public SignServerHttpClient(
            IAppFactory appFactory,
            IHttpClientFactory httpClientFactory,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _client = httpClientFactory.CreateClient("signserver");
        }

        public async Task<Invoice03ApiSignServerResponseModel> SignServer(Invoice03HeaderEntity invoice)
        {
            try
            {
                List<long> idInvoiceHeaders = new List<long>
                {
                    invoice.Id
                };

                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoiceHeaders), Encoding.UTF8, "application/json");
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, "api/sign/SignInvoice03")
                {
                    Content = httpContent
                };

                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var response = await _client.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();
                if (!response.IsSuccessStatusCode)
                {
                    Log.Error($"Quá trình ký hóa đơn xảy ra lỗi {content}");
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.Sign.SignFail"]);
                }

                var signInvoiceResponse = new Invoice03ApiSignServerResponseModel
                {
                    Id = invoice.Id,
                    ErpId = invoice.ErpId,
                    TransactionId = invoice.TransactionId,
                    TemplateNo = invoice.TemplateNo,
                    InvoiceNo = invoice.InvoiceNo,
                    SerialNo = invoice.SerialNo,
                    InvoiceStatus = invoice.InvoiceStatus,
                    SignStatus = SignStatus.DaKy
                };

                return signInvoiceResponse;
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.Api.Intergration.Sign.SignFail"]);
            }
        }
    }
}
