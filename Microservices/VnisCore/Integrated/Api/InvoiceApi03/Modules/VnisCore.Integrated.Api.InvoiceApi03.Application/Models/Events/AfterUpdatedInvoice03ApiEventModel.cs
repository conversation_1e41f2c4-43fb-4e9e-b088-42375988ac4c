using Core.Shared.Constants;
using MediatR;
using System;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Events
{
    public class AfterUpdatedInvoice03ApiEventModel : INotification
    {
        public bool Succeeded { get; set; }
        public Exception Exception { get; set; }
        public DataAfterUpdatedInvoice03ApiModel Data { get; set; }
    }

    public class DataAfterUpdatedInvoice03ApiModel
    {
        public DateTime InvoiceDate { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public long Id { get; set; }
        public Guid TenantId{ get; set; }
        public Guid UserId { get; set; }
        public VnisType Type { get; set; }
        public string UserFullName { get; set; }
        public string UserName { get; set; }
    }
}
