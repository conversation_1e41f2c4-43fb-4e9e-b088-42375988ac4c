//using System;
//using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;

//namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models
//{
//    public class Invoice03TaxBreakdownModel
//    {
//        public long Id { get; set; }

//        /// <summary>
//        /// Tên thuế
//        /// </summary>
//        public string Name { get; set; }

//        /// <summary>
//        /// % thuế
//        /// </summary>
//        public decimal VatPercent { get; set; }

//        /// <summary>
//        /// Tổng tiền thuế, lượng điều chỉnh nếu là hóa đơn tăng/giảm
//        /// </summary>
//        public decimal VatAmount { get; set; }

//        /// <summary>
//        /// tiền thuế hệ thống tính
//        /// </summary>
//        public decimal VatAmountBackUp { get; set; }

//        public string VatPercentDisplay { get; set; }
//        public Guid TenantId { get; set; }

//        //FK
//        public long InvoiceHeaderId { get; set; }

//        public static explicit operator Invoice03TaxBreakdownModel(Invoice03TaxBreakdownEntity entity)
//        {
//            if (entity == null)
//                return null;

//            return new Invoice03TaxBreakdownModel
//            {
//                Id = entity.Id,
//                InvoiceHeaderId = entity.InvoiceHeaderId,
//                Name = entity.Name,
//                VatAmountBackUp = entity.VatAmountBackUp,
//                VatAmount = entity.VatAmount,
//                VatPercent = entity.VatPercent,
//                VatPercentDisplay = entity.VatPercentDisplay,
//                TenantId = entity.TenantId
//            };
//        }
//    }

//}
