using Core.Shared.Attributes;
using MediatR;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands
{
    public class ApproveInvoice03ApiRequestModel : IRequest<ApproveInvoice03ApiResponseModel>
    {
        [JsonIgnore]
        public long Id { get; set; }
        public string ErpId { get; set; }
        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
      
        public short TemplateNo { get; set; }

        /// <summary>
        /// <PERSON><PERSON> hiệu hóa đơn
        /// </summary>
        public string SerialNo { get; set; }

        /// Số hóa đơn
        /// </summary>
        public string InvoiceNo { get; set; }
    }
}
