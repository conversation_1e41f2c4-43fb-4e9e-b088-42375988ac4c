using MediatR;

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands
{
    public class SignServerInvoice03ErrorRequestModel : IRequest<List<SignServerInvoiceErrorResponseModel>>
    {
        [Required(ErrorMessage = "ErpIds không được để trống")]
        public List<string> ErpIds { get; set; }
    }
}
