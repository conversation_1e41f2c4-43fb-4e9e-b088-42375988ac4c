using System;
using System.Collections.Generic;
using System.Linq;
using Core.Shared.Attributes;
using MediatR;
using System.ComponentModel.DataAnnotations;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands
{
    public class SendMailInvoice03ApiRequestModel : IRequest<SendMailInvoice03ApiResponseModel>
    {
        [Required(ErrorMessage = "Mẫu số không được để trống")]
        [TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Ký hiệu không được để trống")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        /// Số hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Số hóa đơn không được để trống")]
        [InvoiceNo(ErrorMessage = "Số hóa đơn không hợp lệ")]
        public string InvoiceNo { get; set; }

        /// <summary>
        ///  Mail
        /// </summary>
        [Required(ErrorMessage = "Email không được để trống")]
        public string Mail { get; set; }
    }
}
