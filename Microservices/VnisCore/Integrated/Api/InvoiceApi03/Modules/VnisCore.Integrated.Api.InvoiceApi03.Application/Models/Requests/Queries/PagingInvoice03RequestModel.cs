using Core.Application.Dtos;
using Core.Shared.Attributes;
using Core.Shared.Dto;
using Core.Shared.Models;
using MediatR;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Queries;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Queries
{
    public class PagingInvoice03RequestModel : PagedFullRequestDto, IRequest<PagedResultDto<PagingInvoice03ResponseModel>>
    {
        /// <summary>
        /// Id đánh dấu hóa đơn, ở đây mục đích search là 1 hồ sơ bệnh án
        /// </summary>
        [MaxLength(50, ErrorMessage = "Tối đa 50 ký tự")]
        public string TransactionId { get; set; }

        /// <summary>
        /// Id Erp
        /// </summary>
        [MaxLength(50, ErrorMessage = "Tối đa 50 ký tự")]
        public string ErpId { get; set; }

        /// <summary>
        /// người tạo Erp
        /// </summary>
        [MaxLength(500, ErrorMessage = "Tối đa 500 ký tự")]
        public string UserNameCreatorErp { get; set; }

        /// <summary>
        /// id mẫu hóa đơn
        /// </summary>
        public List<long> InvoiceTemplateIds { get; set; }

        /// <summary>
        /// số phiếu xuất
        /// </summary>
        [MaxLength(8, ErrorMessage = "Tối đa 8 ký tự")]
        public string InvoiceNo { get; set; }

        /// <summary>
        /// Trạng thái phiếu xuất kho
        /// </summary>
        //[EnumDataType(typeof(InvoiceStatus), ErrorMessage = "Trạng thái hóa đơn không đúng")]
        public List<int> InvoiceStatuses { get; set; }

        /// <summary>
        /// Trạng thái ký
        /// </summary>
        //[EnumDataType(typeof(SignStatus), ErrorMessage = "Trạng thái ký không đúng")]
        public List<int> SignStatuses { get; set; }

        /// <summary>
        /// Lập phiếu xuất kho từ ngày
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        [LessThanDate("CreateToDate", ErrorMessage = "Phải nhỏ hơn tạo đến ngày")]
        public DateTime? CreateFromDate { get; set; }

        /// <summary>
        /// Lập phiếu xuất kho đến ngày
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        [MoreThanDate("CreateFromDate", ErrorMessage = "Phải lớn hơn tạo từ ngày")]
        public DateTime? CreateToDate { get; set; }

        /// <summary>
        /// Phát hành hóa đơn từ ngày
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        [LessThanDate("IssueToDate", ErrorMessage = "Phải nhỏ hơn phát hành đến ngày")]
        public DateTime? IssueFromDate { get; set; }

        /// <summary>
        /// Phát hành hóa đơn đến ngày
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        [MoreThanDate("IssueFromDate", ErrorMessage = "Phải lớn hơn phát hành từ ngày")]
        public DateTime? IssueToDate { get; set; }


        /// <summary>
        /// Hủy hóa đơn từ ngày
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        [LessThanDate("CancelToDate", ErrorMessage = "Phải nhỏ hơn hủy đến ngày")]
        public DateTime? CancelFromDate { get; set; }

        /// <summary>
        /// Hủy hóa đơn đến ngày
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn ngày hiện tại")]
        [MoreThanDate("CancelFromDate", ErrorMessage = "Phải lớn hơn hủy từ ngày")]
        public DateTime? CancelToDate { get; set; }

        //[MaxLength(250, ErrorMessage = "Tối đa 250 ký tự")]
        //public string Embed { get; set; }

        /// <summary>
        /// Người tạo hóa đơn
        /// </summary>
        [MaxLength(250, ErrorMessage = "Tối đa 250 ký tự")]
        public string UserNameCreator { get; set; }

        /// <summary>
        /// Ngày phát hành hóa đơn
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Ngày phát hành phải nhỏ hơn ngày hiện tại")]
        public DateTime? IssuedTime { get; set; }

        ///// <summary>
        ///// tìm kiếm các hóa đơn có headerextra giá trị chứa trường này
        ///// </summary>
        //[MaxLength(2000, ErrorMessage = "HeaderExtra không nhập quá 2000 ký tự")]
        //public string HeaderExtra { get; set; }

        /// <summary>
        /// tìm kiếm theo danh sách các headerExtra theo từng trường extra cụ thể
        /// mỗi phần tử dạng A-B
        /// A là fieldname
        /// B là giá trị tìm kiếm theo extra A
        /// Nếu B = - => tìm B có value chứa -
        /// </summary>
        public List<string> HeaderExtras { get; set; }

        public bool IsNullInvoice { get; set; }

        public string Service { get; set; }

        public short? TemplateNo { get; set; }

        /// <summary>
        /// Trạng thái duyệt
        /// </summary>
        public List<int> ApproveStatuses { get; set; }

        /// <summary>
        /// Ngày in chuyển đổi hóa đơn
        /// </summary>
        [DataType(DataType.Date)]
        [LessThanCurrentDate(ErrorMessage = "Ngày chuyển đổi hóa đơn giấy phải nhỏ hơn ngày hiện tại")]
        public DateTime? PrintedTime { get; set; }

        /// <summary>
        /// Tên người in chuyển đổi
        /// </summary>
        public string UserNamePrinted { get; set; }

        public decimal? TotalPaymentAmount { get; set; }

        public List<InvoiceExtras> InvoiceHeaderExtras { get; set; }

        public class InvoiceExtras
        {
            public string FieldName { get; set; }
            public string FieldValue { get; set; }
        }
    }
}
