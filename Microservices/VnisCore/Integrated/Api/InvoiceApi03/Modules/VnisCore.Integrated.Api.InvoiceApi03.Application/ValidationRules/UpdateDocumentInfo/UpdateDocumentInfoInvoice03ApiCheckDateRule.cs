using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Repositories;


namespace VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.UpdateDocumentInfo
{
    class UpdateDocumentInfoInvoice03ApiCheckDateRule : IValidationRuleAsync<UpdateDocumentInfoInvoice03ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceReferenceRepository<Invoice03ReferenceEntity> _repoInvoiceReference;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateDocumentInfoInvoice03ApiCheckDateRule(IValidationContext validationContext,
             IInvoiceReferenceRepository<Invoice03ReferenceEntity> repoInvoiceReference,
             IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoInvoiceReference = repoInvoiceReference;
        }

        public async Task<ValidationResult> HandleAsync(UpdateDocumentInfoInvoice03ApiRequestModel input)
        {
            var invoice = _validationContext.GetItem<Invoice03HeaderEntity>("Invoice");

            if (invoice.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode())
            {
                if (input.DocumentDate < invoice.InvoiceDate || input.DocumentDate > DateTime.Now)
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.UpdateDocumentInfo.InvoiceDocumentDateRangeWithNow"]);

                return new ValidationResult(true);
            }

            var invoiceReference = await _repoInvoiceReference.GetByIdInvoiceHeaderAsync(invoice.Id);

            if (invoiceReference == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.UpdateDocumentInfo.InvoiceReferenceNotFound"]);

            if (invoiceReference.InvoiceHeaderId != invoiceReference.InvoiceReferenceId
                && (input.DocumentDate > invoice.InvoiceDate || input.DocumentDate < invoiceReference.InvoiceDateReference))
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.UpdateDocumentInfo.InvoiceDocumentDateRangeWithReference"]);

            return new ValidationResult(true);
        }
    }
}
