using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Approve
{
    public class ApproveInvoice03ApiCheckUserRule : IValidationRuleAsync<ApproveInvoice03ApiRequestModel, ValidationResult>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IValidationContext _validationContext;

        public ApproveInvoice03ApiCheckUserRule(IServiceProvider serviceProvider,
            IValidationContext validationContext)
        {
            _serviceProvider = serviceProvider;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(ApproveInvoice03ApiRequestModel input)
        {
            var userId = _validationContext.GetItem<Guid>("UserId");
            
            return new ValidationResult(true);
        }
    }
}
