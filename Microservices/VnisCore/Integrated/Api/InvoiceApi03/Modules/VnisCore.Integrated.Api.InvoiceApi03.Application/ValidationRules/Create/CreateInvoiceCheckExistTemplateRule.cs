using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using Core.Domain.Repositories;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Create
{
    public class CreateInvoiceCheckExistTemplateRule : IValidationRuleAsync<CreateInvoice03RequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IRepository<InvoiceTemplateEntity, long> _repoInvoiceTemplate;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateInvoiceCheckExistTemplateRule(
            IRepository<InvoiceTemplateEntity, long> repoInvoiceTemplate,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext)
        {
            _localizier = localizier;
            _repoInvoiceTemplate = repoInvoiceTemplate;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice03RequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var template = await _repoInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == input.TemplateNo && x.SerialNo == input.SerialNo);
            if (template == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.InvoiceTemplateNotFound"]);

            _validationContext.GetOrAddItem("Template", () =>
            {
                return template;
            });

            

            return new ValidationResult(true);
        }
    }
}
