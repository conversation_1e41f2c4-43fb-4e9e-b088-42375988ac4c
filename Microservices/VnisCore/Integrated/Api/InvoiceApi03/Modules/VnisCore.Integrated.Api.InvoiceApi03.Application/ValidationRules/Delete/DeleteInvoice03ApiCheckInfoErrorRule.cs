using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Core.Tvan.Constants;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.Delete
{
    public class DeleteInvoice03ApiCheckInfoErrorRule : IValidationRuleAsync<DeleteInvoice03ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public DeleteInvoice03ApiCheckInfoErrorRule(
            IValidationContext validationContext,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _validationContext = validationContext;
            _appFactory = appFactory;
            _localizier = localizier;
        }

        public async Task<ValidationResult> HandleAsync(DeleteInvoice03ApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var invoiceReference = _validationContext.GetItem<Invoice03HeaderEntity>("Invoice");

            var repoRegistration = _appFactory.GetServiceDependency<INewRegistrationHeaderRepository>();
            var registration = await repoRegistration.GetLastRegistration(tenantId);

            if (registration.InvoiceHasCode || (!registration.InvoiceHasCode && registration.SendInvoiceMethod == (short)RegistrationSendInvoiceMethod.SendInvoice))
            {
                var invoice03ErrorRepo = _appFactory.Repository<Invoice03ErrorEntity, long>();
                var infoErrors = await invoice03ErrorRepo.Where(x => x.TenantId == tenantId && x.InvoiceHeaderId == invoiceReference.Id).ToListAsync();

                if (infoErrors.Count() == 0)
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.Delete.CannotDeleteHasNoErrorNotice"]);

                //var infoErrorAdj = infoErrors.OrderByDescending(x => x.CreationTime).FirstOrDefault(x => x.Action == (short)TThaiTBao.Huy);
                var infoErrorAdj = infoErrors.OrderByDescending(x => x.CreationTime).FirstOrDefault();

                if (infoErrorAdj == null)
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.Delete.CannotDeleteWrongNatureNotice"]);

                if (infoErrorAdj.SignStatus != SignStatus.DaKy.GetHashCode())
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.Delete.CannotDeleteNotSignInvoiceError"]);

                if (infoErrorAdj.TvanStatus == 0)
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.Delete.CannotDeleteHasNoAcceptForTCT"]);

                //var tvanInfoInvoice03ErrorRepo = _appFactory.Repository<TvanInfoInvoice03ErrorEntity, long>();

                //var infoError = await tvanInfoInvoice03ErrorRepo.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.InvoiceHeaderId == invoiceReference.Id);

                //if (infoError == null)
                //    return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.Delete.CannotDeleteHasNoErrorNotice"]);

                //if (infoError.Action != (short)TThaiTBao.Huy)
                //    return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.Delete.CannotDeleteWrongNatureNotice"]);

                //if (infoError.Status != 1)
                //    return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.Delete.CannotDeleteHasNoAcceptForTCT"]);
            }

            return new ValidationResult(true);
        }

    }
}