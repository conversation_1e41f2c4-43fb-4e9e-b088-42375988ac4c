using Core.Shared.Factory;
using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.ValidationRules.CreateDocumentInfo
{
    /// <summary>
    /// check đã tồn tại biên bản hóa đơn hay chưa
    /// </summary>
    public class CreateDocumentInfoInvoice03ApiCheckExistRule : IValidationRuleAsync<CreateDocumentInfoInvoice03ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceDocumentRepository<Invoice03DocumentEntity> _repoInvoice03Document;
        private readonly IAppFactory _appFactory;

        public CreateDocumentInfoInvoice03ApiCheckExistRule(IValidationContext validationContext,
            IInvoiceDocumentRepository<Invoice03DocumentEntity> repoInvoice03Document,
            IAppFactory appFactory)
        {
            _validationContext = validationContext;
            _repoInvoice03Document = repoInvoice03Document;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateDocumentInfoInvoice03ApiRequestModel input)
        {
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var invoice = _validationContext.GetItem<Invoice03HeaderEntity>("Invoice");

            var invoiceDocument = await _validationContext.GetOrAddItemAsync("InvoiceDocument", async () =>
            {
                return await _repoInvoice03Document.GetByIdInvoiceHeaderAsync(tenantId, invoice.Id);
            });

            if (invoiceDocument != null)
                return new ValidationResult(false, "Biên bản hóa đơn đã tồn tại, hãy xóa biên bản cũ để tạo biên bản hóa đơn mới");

            return new ValidationResult(true);
        }
    }
}
