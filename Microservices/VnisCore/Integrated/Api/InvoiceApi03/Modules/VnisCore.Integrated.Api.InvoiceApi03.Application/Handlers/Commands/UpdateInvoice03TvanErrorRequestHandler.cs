using Core;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using MediatR;

using Microsoft.EntityFrameworkCore;

using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Handlers.Commands
{
    public class UpdateInvoice03TvanErrorRequestHandler : IRequestHandler<UpdateInvoice03TvanErrorRequestModel, UpdateInvoice03TvanErrorResponseModel>
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice03HeaderEntity, long> _repoInvoiceHeader;
        private readonly IRepository<Invoice03ErrorEntity, long> _repoInvoiceError;

        public UpdateInvoice03TvanErrorRequestHandler(
            IAppFactory appFactory,
            IRepository<Invoice03HeaderEntity, long> repoInvoiceHeader,
            IRepository<Invoice03ErrorEntity, long> repoInvoiceError
            )
        {
            _appFactory = appFactory;
            _repoInvoiceHeader = repoInvoiceHeader;
            _repoInvoiceError = repoInvoiceError;
        }

        public async Task<UpdateInvoice03TvanErrorResponseModel> Handle(UpdateInvoice03TvanErrorRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            var invoiceHeader = await _repoInvoiceHeader.AsNoTracking().FirstOrDefaultAsync(x => x.ErpId == request.ErpId && x.TenantId == tenantId);
            if (invoiceHeader == null)
            {
                throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {request.ErpId} để thực hiện sửa thông báo sai sót. Vui lòng kiểm tra lại");
            }

            if (invoiceHeader.SignStatus != (short)SignStatus.DaKy)
            {
                throw new UserFriendlyException($@"Hóa đơn có ErpId = {request.ErpId} chưa ký. Vui lòng kiểm tra lại.");
            }

            if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.DieuChinh)
            {
                throw new UserFriendlyException($@"Hóa đơn điều chỉnh: {invoiceHeader.TemplateNo} - {invoiceHeader.SerialNo} - {invoiceHeader.InvoiceNo} không thể lập TBSS. Vui lòng kiểm tra lại");
            }

            if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.XoaBo)
            {
                throw new UserFriendlyException($@"Hóa đơn xóa bỏ: {invoiceHeader.TemplateNo} - {invoiceHeader.SerialNo} - {invoiceHeader.InvoiceNo} không thể lập TBSS. Vui lòng kiểm tra lại");
            }

            if (request.AnnouncementDate.HasValue && (request.AnnouncementDate.Value.Date < invoiceHeader.InvoiceDate.Date))
            {
                throw new UserFriendlyException($@"Ngày thông báo hoá đơn có sai sót phải lớn hơn hoặc bằng ngày hoá đơn");
            }

            //if (invoiceHeader != null && (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinh
            //        || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiThayThe
            //        || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinhDinhDanh
            //        || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinhTangGiam
            //        ) && request.Action == TThaiTBao.Huy)
            //{
            //    var message = invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiThayThe ? "bị thay thế" : "bị điều chỉnh";
            //    throw new UserFriendlyException($@"Hóa đơn có ErpId = {request.ErpId} đã {message}. Vui lòng kiểm tra lại.");
            //}

            var invoiceError = await _repoInvoiceError.Where(x => x.TenantId == tenantId
                                                            && x.InvoiceHeaderId == invoiceHeader.Id
                                                            )
                                                        .OrderByDescending(x => x.Id)
                                                        .FirstOrDefaultAsync();

            if (invoiceError.SignStatus == (short)SignStatus.DaKy)
            {
                throw new UserFriendlyException($@"Không thể sửa hoá đơn có ErpId = {request.ErpId} đã ký. Vui lòng kiểm tra lại");
            }

            if (invoiceError.SignStatus == (short)SignStatus.Huy)
            {
                throw new UserFriendlyException($@"Hoá đơn có ErpId = {request.ErpId} đã bị Huỷ. Vui lòng kiểm tra lại");
            }

            invoiceError.InvoiceHeaderId = invoiceHeader.Id;
            invoiceError.AnnouncementDate = request.AnnouncementDate.HasValue ? request.AnnouncementDate.Value.Date : DateTime.Now.Date;
            invoiceError.BudgetUnitCode = request.BudgetUnitCode;
            invoiceError.VerificationCode = invoiceHeader.VerificationCode;
            invoiceError.Emails = request.Emails;
            invoiceError.CreatorId = userId;
            invoiceError.TenantId = tenantId;
            invoiceError.Reason = request.Reason;
            //invoiceError.Action = (short)request.Action.GetHashCode();
            invoiceError.LastModificationTime = DateTime.Now;

            await _repoInvoiceError.UpdateAsync(invoiceError, true);

            return new UpdateInvoice03TvanErrorResponseModel
            {
                ErpId = invoiceHeader.ErpId,
                InvoiceNo = invoiceHeader.InvoiceNo,
                SerialNo = invoiceHeader.SerialNo,
                TemplateNo = invoiceHeader.TemplateNo,
                AnnouncementDate = invoiceError.AnnouncementDate,
                Reason = request.Reason
            };
        }
    }
}
