using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Models;
using MediatR;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.InvoiceApi03.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi03.Application.Handlers.Queries
{
    public class DownloadUnOfficialInvoice03QueryHandler : IRequestHandler<DownloadUnOfficialInvoice03RequestModel, PrintApiModel>
    {
        private readonly IExportPdfHttpClient _exportPdfClient;
        private readonly IInvoiceHeaderRepository<Invoice03HeaderEntity> _repository;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;

        public DownloadUnOfficialInvoice03QueryHandler(IExportPdfHttpClient exportPdfHttpClient
                                                     , IInvoiceHeaderRepository<Invoice03HeaderEntity> repository
                                                     , IStringLocalizer<CoreLocalizationResource> localizer,
                                                        IExportPdfHttpClient exportPdfClient,
                                                        IAppFactory appFactory)
        {
            _exportPdfClient = exportPdfClient;
            _repository = repository;
            _localizer = localizer;
            _appFactory = appFactory;
        }

        public async Task<PrintApiModel> Handle(DownloadUnOfficialInvoice03RequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var invoice = await _repository.GetByErpIdAsync(tenantId ?? System.Guid.Empty, request.ErpId);

            if (invoice == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice03.Api.Intergration.InvoiceNotFound"]);

            var fileDto = await _exportPdfClient.UnOfficialsAsync(new List<long> { invoice.Id });

            return new PrintApiModel(invoice.ErpId, fileDto.FileBase64);
        }
    }
}
