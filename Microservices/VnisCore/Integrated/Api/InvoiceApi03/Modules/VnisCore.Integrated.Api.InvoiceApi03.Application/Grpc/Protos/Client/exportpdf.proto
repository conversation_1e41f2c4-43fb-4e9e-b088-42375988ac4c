// định nghĩa cú pháp 
syntax = "proto3";

// namespace
option csharp_namespace = "VnisCore.Export.Application";

// package
package GrpcExportPdf;

// định nghĩa các phương thức input, output giống interface
service GrpcExportPdf {
	// phương thức trả về dữ liệu một lần. giống phương thức thông thường
	rpc UnOfficial (PdfRequest) returns (PdfResponse) {}
	// phương thức trả dữ liệu về từng phần 
	//rpc UnOfficial (PdfRequest) returns (stream PdfResponse) {}
}

message PdfRequest {
	int32 invoiceType = 1;
	string ids = 2;
}
 
message PdfResponse {
	bytes Datas = 1;
	string ContentType = 2;
	string FileName = 3;
}