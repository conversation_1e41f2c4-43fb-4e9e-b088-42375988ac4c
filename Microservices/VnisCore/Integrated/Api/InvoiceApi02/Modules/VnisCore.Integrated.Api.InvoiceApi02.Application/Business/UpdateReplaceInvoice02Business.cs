using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Messages;

using Microsoft.Extensions.DependencyInjection;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Business
{
    public interface IUpdateReplaceInvoice02Business
    {
        Task<InvoiceCommandResponseModel> UpdateOracleAsync(InvoiceCommandRequestModel message);

        Task UpdateMongoAsync(UpdateReplaceInvoice02ApiRequestModel request);
    }

    public class UpdateReplaceInvoice02Business : IUpdateReplaceInvoice02Business
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;

        public UpdateReplaceInvoice02Business(
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository)
        {
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _serviceProvider = serviceProvider;
            _mongoInvoice02Repository = mongoInvoice02Repository;
        }

        public async Task UpdateMongoAsync(UpdateReplaceInvoice02ApiRequestModel request)
        {
            var invoices = await _mongoInvoice02Repository.GetByIdsAsync(new List<long> { request.Id });
            var indexesInvoiceDetails = request.InvoiceDetails.ToDictionary(x => x.Index, x => x);
            var indexsInvoiceHeaderExtras = request.InvoiceHeaderExtras.ToDictionary(x => x.FieldName, x => x);

            invoices.ForEach(item =>
            {
                item.BuyerCode = request.BuyerCode;
                item.BuyerFullName = request.BuyerFullName;
                item.BuyerLegalName = request.BuyerLegalName;
                item.BuyerTaxCode = request.BuyerTaxCode;
                item.BuyerAddressLine = request.BuyerAddressLine;
                item.BuyerDistrictName = request.BuyerDistrictName;
                item.BuyerCityName = request.BuyerCityName;
                item.BuyerCountryCode = request.BuyerCountryCode;
                item.BuyerPhoneNumber = request.BuyerPhoneNumber;
                item.BuyerFaxNumber = request.BuyerFaxNumber;
                item.BuyerEmail = request.BuyerEmail;
                item.BuyerBankName = request.BuyerBankName;
                item.BuyerBankAccount = request.BuyerBankAccount;

                item.Note = request.Note;
                item.InvoiceDate = request.InvoiceDate.ToLocalTime();
                item.PaymentDate = request.InvoiceDate;

                item.PaymentMethod = request.PaymentMethod;
                item.ToCurrency = request.Currency;
                item.ExchangeRate = request.ExchangeRate;
                item.TotalAmount = request.TotalAmount;
                item.TotalPaymentAmount = request.TotalPaymentAmount;
                item.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode();


                item.InvoiceDetails.ForEach(itemDetail =>
                {
                    if (indexesInvoiceDetails.ContainsKey(itemDetail.Index))
                    {
                        var rootDetail = indexesInvoiceDetails[itemDetail.Index];
                        itemDetail.PaymentAmount = rootDetail.PaymentAmount;
                        itemDetail.ProductCode = rootDetail.ProductCode;
                        itemDetail.ProductType = (short)rootDetail.ProductType;
                        itemDetail.ProductName = rootDetail.ProductName;
                        itemDetail.UnitName = rootDetail.UnitName;
                        itemDetail.UnitPrice = rootDetail.UnitPrice;
                        itemDetail.Quantity = rootDetail.Quantity;
                        itemDetail.Amount = rootDetail.Amount;
                        itemDetail.Note = rootDetail.Note;
                    }
                });
            });

            await _mongoInvoice02Repository.UpdateManyAsync(invoices);
        }

        public async Task<InvoiceCommandResponseModel> UpdateOracleAsync(InvoiceCommandRequestModel message)
        {
            var repoInvoice02Header = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice02HeaderEntity>>();
            var repoInvoice02Detail = _serviceProvider.GetService<IInvoiceDetailRepository<Invoice02DetailEntity>>();
            var request = JsonConvert.DeserializeObject<UpdateReplaceInvoice02ApiRequestModel>(CompressionExtensions.Unzip(message.Data));
            var invoiceStatus = InvoiceStatus.ThayThe;
            var signStatus = SignStatus.ChoKy;
            var approveStatus = await _invoiceService.GetApproveStatusAsync(message.TenantId);
            var tenantInfo = _appFactory.CurrentTenant;
            var template = await _invoiceService.GetTemplateRawAsync(message.TenantId, message.TemplateNo, message.SerialNo);
            var fromCurrency = await _invoiceService.GetCurrencyRawAsync(message.TenantId);
            var toCurrency = await _invoiceService.GetCurrencyRawAsync(message.TenantId, request.Currency);
            var products = await _invoiceService.GetProductsRawAsync(message.TenantId, request.InvoiceDetails.Select(x => x.ProductCode?.Trim()).Distinct().ToList());
            var productTypes = await _invoiceService.GetProductTypesRawAsync(products.Values.Where(x => x.ProductTypeId.HasValue).Select(x => x.ProductTypeId.Value).Distinct().ToList());
            var units = await _invoiceService.GetUnitsRawAsync(message.TenantId, request.InvoiceDetails.Select(x => x.UnitName?.Trim()).Distinct().ToList());
            var headerFields = (await _invoiceService.GetHeaderFieldsRawAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            var detailFields = (await _invoiceService.GetDetailFieldsRawAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            var rootInvoice = await repoInvoice02Header.GetByIdRawAsync(message.TenantId, message.InvoiceId.Value);
            var details = await repoInvoice02Detail.QueryByIdInvoiceHeaderRawAsync(message.TenantId, message.InvoiceId.Value);
            request.SerialNo = message.SerialNo;

            //Lưu hoá đơn ko số
            var invoice02service = _serviceProvider.GetService<IInvoice02Service>();
            await invoice02service.UpdateReplaceRawAsync(
                 request,
                 rootInvoice,
                 details,
                 signStatus,
                 approveStatus,
                 message.Resource,
                 message.TenantId,
                 message.UserId,
                 message.UserFullName,
                 message.UserName,
                 template,
                 fromCurrency,
                 toCurrency,
                 products,
                 productTypes,
                 units,
                 headerFields,
                 detailFields
             );

            var result = new InvoiceCommandResponseModel
            {
                //ProcessCode = message.ProcessCode.Value,
                Id = rootInvoice.Id,
                TenantId = message.TenantId,
                Type = message.Type,
                UserId = message.UserId,
                UserFullName = message.UserFullName,
                UserName = message.UserName,
                Method = HubMethod.UpdateInvoice,
                InvoiceStatus = invoiceStatus,
                SignStatus = signStatus,
                ApproveStatus = approveStatus,
                Number = rootInvoice.Number,
                InvoiceNo = rootInvoice.InvoiceNo,
                TemplateNo = message.TemplateNo,
                SerialNo = message.SerialNo,
                State = InvoiceActionState.UpdateReplace,
                ActionLogInvoice = ActionLogInvoice.Update,
                Action = InvoiceAction.UpdateReplace,
                Resource = message.Resource,
                ActionAt = DateTime.Now,
                ActionAtUtc = DateTime.UtcNow,
                ConnectionId = message.ConnectionId,
                InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                {
                    NewInvoiceDate = request.InvoiceDate,
                    NewTotalAmount = request.TotalAmount,
                    NewTotalPaymentAmount = request.TotalPaymentAmount
                }
            };

            //lấy dữ liệu bảng InvoiceReference để update invoiceDateReference
            var repoInvoiceReference = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<Invoice02ReferenceEntity>>();
            var invoiceRef = await repoInvoiceReference.GetByInvoiceReferenceIdRawAsync(rootInvoice.Id);
            if (invoiceRef != null)
            {
                await _invoiceService.UpdateDateInvoiceReference(invoiceRef, rootInvoice.InvoiceDate);
            }

            return result;
        }
    }
}
