using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;

using System;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.ApproveAndSign
{
    public class ApproveAndSignInvoice02ApiCheckStatusRule :
        IValidationRuleAsync<ApproveAndSignInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;


        public ApproveAndSignInvoice02ApiCheckStatusRule(IServiceProvider serviceProvider,
            IValidationContext validationContext,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository)
        {
            _serviceProvider = serviceProvider;
            _validationContext = validationContext;
            _localizer = localizier;
            _appFactory = appFactory;
            _mongoInvoice02Repository = mongoInvoice02Repository;
        }

        public async Task<ValidationResult> HandleAsync(ApproveAndSignInvoice02ApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var invoice = await _mongoInvoice02Repository.GetByErpId(input.ErpId, tenantId);
            
            if (invoice == null)
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice02HeaderEntity>>();
                var invoiceOracle = await repoInvoiceHeader.GetByErpIdAsync(tenantId, input.ErpId);
                if (invoiceOracle == null)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.Api.Intergration.InvoiceNotFound"]);

                invoice = new Core.MongoDB.Entities.Invoices.Invoice02.MongoInvoice02Entity
                {
                    Id = invoiceOracle.Id,
                    ErpId = invoiceOracle.ErpId,
                    InvoiceDate = invoiceOracle.InvoiceDate,
                    InvoiceNo = invoiceOracle.InvoiceNo,
                    SerialNo = invoiceOracle.SerialNo,
                    TemplateNo = invoiceOracle.TemplateNo,
                    TransactionId = invoiceOracle.TransactionId,
                    InvoiceStatus = invoiceOracle.InvoiceStatus,
                    SignStatus = invoiceOracle.SignStatus,
                    ApproveDeleteStatus = invoiceOracle.ApproveDeleteStatus,
                    ApproveStatus = invoiceOracle.ApproveStatus,
                    ApproveCancelStatus = invoiceOracle.ApproveCancelStatus
                };
            }
            //return new ValidationResult(false, "Vnis.BE.Invoice02.Api.Intergration.InvoiceNotFound");

            if (invoice.InvoiceStatus != InvoiceStatus.Goc.GetHashCode() && invoice.InvoiceStatus != InvoiceStatus.ThayThe.GetHashCode()
                && invoice.InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode() && invoice.InvoiceStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.Api.Intergration.Approve.CannotApproveInvoice"]);

            if (invoice.SignStatus == SignStatus.DaKy.GetHashCode() && invoice.ApproveDeleteStatus != ApproveStatus.ChoDuyet.GetHashCode())
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.Api.Intergration.Approve.InvoiceStatusIncorrect"]);

            if (invoice.SignStatus == SignStatus.DaKy.GetHashCode() && invoice.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode())
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice02.Api.Intergration.Approve.InvoiceStatusIncorrect"]);

            _validationContext.GetOrAddItem("Invoice", () =>
            {
                return invoice;
            });

            if (invoice.SignStatus == SignStatus.ChoKy.GetHashCode() && invoice.ApproveStatus == ApproveStatus.DaDuyet.GetHashCode())
                return new ValidationResult(false);

            return new ValidationResult(true);
        }
    }
}
