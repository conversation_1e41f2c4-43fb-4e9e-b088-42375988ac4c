using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;

using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;


namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Update
{
    public class UpdateInvoice02ApiPreProcess : IValidationRuleAsync<UpdateInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceHeaderRepository<Invoice02HeaderEntity> _repoInvoiceHeader;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IInvoice02Service _invoice02Service;


        public UpdateInvoice02ApiPreProcess(
            IInvoiceHeaderRepository<Invoice02HeaderEntity> repoInvoice,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext,
            IAppFactory appFactory,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IInvoice02Service invoice02Service)
        {
            _localizier = localizier;
            _repoInvoiceHeader = repoInvoice;
            _validationContext = validationContext;
            _appFactory = appFactory;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _invoice02Service = invoice02Service;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice02ApiRequestModel input)
        {
            var userId = _appFactory.CurrentUser.Id;
            var userName = _appFactory.CurrentUser.UserName;
            var userFullName = _appFactory.CurrentUser.Name;
            var tenantId = _appFactory.CurrentTenant.Id;

            await _validationContext.GetOrAddItemAsync("TenantId", async () =>
            {
                return tenantId;
            });

            await _validationContext.GetOrAddItemAsync("UserName", async () =>
            {
                return userName;
            });

            await _validationContext.GetOrAddItemAsync("UserFullName", async () =>
            {
                return userFullName;
            });

            await _validationContext.GetOrAddItemAsync("UserId", async () =>
            {
                return userId;
            });

            var invoice = await _validationContext.GetOrAddItemAsync("Invoice", async () =>
            {
                var data = await _mongoInvoice02Repository.GetByErpId(input.ErpId, tenantId.Value);

                return _appFactory.ObjectMapper.Map<MongoInvoice02Entity, Invoice02HeaderEntity>(data);
            });

            if (invoice == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.InvoiceNotFound"]);

            input.SerialNo = invoice.SerialNo;
            input.TemplateNo = invoice.TemplateNo;
            input.InvoiceNo = invoice.InvoiceNo;


            var inputProducts = input.InvoiceDetails.Select(x => x.ProductCode?.Trim()).Distinct().ToList();
            var inputUnits = input.InvoiceDetails.Select(x => x.UnitName?.Trim()).Distinct().ToList();

            var dataValidToCreate = await _invoice02Service.CreateInvoice02GetDataValid(tenantId.Value, new List<KeyValuePair<short, string>> { new KeyValuePair<short, string>(input.TemplateNo, input.SerialNo) }, new List<string> { input.Currency }, inputProducts, inputUnits);

            _validationContext.GetOrAddItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate", () =>
            {
                return dataValidToCreate;
            });

            return new ValidationResult(true);
        }
    }
}
