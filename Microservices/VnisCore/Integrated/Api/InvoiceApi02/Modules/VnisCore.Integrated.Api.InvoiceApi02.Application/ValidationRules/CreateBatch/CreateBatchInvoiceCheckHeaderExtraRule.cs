using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using System;
using System.Collections.Generic;
using System.Linq;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateBatch
{
    public class CreateBatchInvoiceCheckHeaderExtraRule : IValidationRule<CreateBatchInvoice02RequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateBatchInvoiceCheckHeaderExtraRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateBatchInvoice02RequestModel input)
        {
            var headerExtras = input.Datas.Select(x => x.Invoice).Where(x => x.InvoiceHeaderExtras != null).SelectMany(x => x.InvoiceHeaderExtras);
            if (!headerExtras.Any())
                return new ValidationResult(true);

            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var dataInfoToCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");
            var headerFieldNames = dataInfoToCreate.Where(x => x.Type == ValidateDataType.HeaderField.GetHashCode()).Select(x => x.FieldName).ToList();

            var commandHeaderFieldNames = headerExtras.Select(x => x.FieldName).ToList();
            var excepts = commandHeaderFieldNames.Except(headerFieldNames);

            if (excepts.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.InvoiceHeaderFieldNotFound", new string[] { string.Join(",", excepts) }]);

            var invoiceHasExtras = input.Datas.Where(x => x.Invoice.InvoiceHeaderExtras != null && x.Invoice.InvoiceHeaderExtras.Any()).ToList();

            foreach (var item in invoiceHasExtras)
            {
                item.Invoice.InvoiceHeaderExtras.RemoveAll(x => string.IsNullOrEmpty(x.FieldValue));
            }

            return new ValidationResult(true);
        }
    }
}
