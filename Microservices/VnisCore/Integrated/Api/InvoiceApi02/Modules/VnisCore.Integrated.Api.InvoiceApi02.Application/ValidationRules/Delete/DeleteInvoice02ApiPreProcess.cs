using Core.Shared.Factory;
using Core.Shared.Validations;
using Core.TenantManagement;

using System.Threading.Tasks;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.Delete
{
    public class DeleteInvoice02ApiPreProcess : IValidationRuleAsync<DeleteInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly ITenantRepository _repoTenant;

        public DeleteInvoice02ApiPreProcess(
            IValidationContext validationContext,
            IAppFactory appFactory,
            ITenantRepository repoTenant)
        {
            _validationContext = validationContext;
            _appFactory = appFactory;
            _repoTenant = repoTenant;
        }

        public async Task<ValidationResult> HandleAsync(DeleteInvoice02ApiRequestModel input)
        {
            var userId = _appFactory.CurrentUser.Id;
            var tenantId = _appFactory.CurrentTenant.Id;
            var userName = _appFactory.CurrentUser.UserName;
            var userFullName = _appFactory.CurrentUser.Name;

            await _validationContext.GetOrAddItemAsync("TenantId", async() =>
            {
                return tenantId;
            });

            await _validationContext.GetOrAddItemAsync("UserName", async () =>
            {
                return userName;
            });

            await _validationContext.GetOrAddItemAsync("UserFullName", async () =>
            {
                return userFullName;
            });

            await _validationContext.GetOrAddItemAsync("UserId", async() =>
            {
                return userId;
            });

            return new ValidationResult(true);
        }
    }
}
