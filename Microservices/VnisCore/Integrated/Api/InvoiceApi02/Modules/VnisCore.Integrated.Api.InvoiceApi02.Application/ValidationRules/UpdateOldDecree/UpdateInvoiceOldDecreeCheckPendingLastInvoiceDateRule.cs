using Core.Domain.Repositories;
using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateOldDecree
{
    public class UpdateInvoiceOldDecreeCheckPendingLastInvoiceDateRule : IValidationRuleAsync<UpdateInvoice02OldDecreeRequestModel, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public UpdateInvoiceOldDecreeCheckPendingLastInvoiceDateRule(
            IAppFactory appFactory, 
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext
            )
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice02OldDecreeRequestModel input)
        {
            var repoMonitor = _appFactory.Repository<MonitorInvoiceTemplateEntity, long>();

            var template = _validationContext.GetItem<CreateInvoice02GetDataValidDto>("Template");

            var monitor = await repoMonitor.FirstOrDefaultAsync(x => x.Id == template.TemplateId);

            if (input.InvoiceDate.Date < monitor.PendingLastInvoiceDate?.Date)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.Api.Intergration.PendingLastInvoiceDate", monitor.PendingLastInvoiceDate]);

            monitor.PendingLastInvoiceDate = input.InvoiceDate.Date;
            monitor.PendingInvoiceStatus = 2;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return new ValidationResult(true);
        }
    }
}
