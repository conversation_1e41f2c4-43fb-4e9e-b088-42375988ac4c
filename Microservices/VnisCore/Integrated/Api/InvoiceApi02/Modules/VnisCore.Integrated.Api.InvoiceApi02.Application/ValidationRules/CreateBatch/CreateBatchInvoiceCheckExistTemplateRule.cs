using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using System.Collections.Generic;
using System.Linq;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateBatch
{
    public class CreateBatchInvoiceCheckExistTemplateRule : IValidationRule<CreateBatchInvoice02RequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateBatchInvoiceCheckExistTemplateRule(
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateBatchInvoice02RequestModel input)
        {
            var dataInfoToCreate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataInfoToCreate");

            var templates = _validationContext.GetOrAddItem("Templates", () =>
            {
                return dataInfoToCreate.Where(x => x.Type == ValidateDataType.Template.GetHashCode()).ToList();
            });

            var templateInfos = templates.Select(x => new { x.TemplateNo, x.SerialNo }).ToList();
            var templateEntityInfos = templates.Select(x => new { x.TemplateNo, x.SerialNo }).Distinct().ToList();
            var excepts = templateInfos.Except(templateEntityInfos);

            if (excepts.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.CreateBatch.InvoiceTemplateNotFound", new string[] { string.Join(", ", excepts.Select(x => $"{x.TemplateNo}{x.SerialNo}")) }]);

            return new ValidationResult(true);
        }
    }
}
