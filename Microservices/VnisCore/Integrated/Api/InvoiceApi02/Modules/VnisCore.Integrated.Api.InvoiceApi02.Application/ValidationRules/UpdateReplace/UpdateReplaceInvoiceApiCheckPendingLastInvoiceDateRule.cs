using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateReplace
{
    public class UpdateReplaceInvoiceApiCheckPendingLastInvoiceDateRule : IValidationRuleAsync<UpdateReplaceInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public UpdateReplaceInvoiceApiCheckPendingLastInvoiceDateRule(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice02ApiRequestModel input)
        {
            var repoMonitor = _appFactory.Repository<MonitorInvoiceTemplateEntity, long>();

            var invoice = _validationContext.GetItem<Invoice02HeaderEntity>("Invoice");

            var monitor = await repoMonitor.FirstOrDefaultAsync(x => x.Id == invoice.InvoiceTemplateId);

            if (monitor.LastDocumentDate.HasValue && input.InvoiceDate.Date == monitor.LastDocumentDate.Value.Date)
            {
                if (input.InvoiceDate.Date > monitor.PendingLastInvoiceDate?.Date && monitor.PendingInvoiceStatus == 2)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.Api.Intergration.PendingLastInvoiceDate", monitor.PendingLastInvoiceDate]);

                monitor.PendingLastInvoiceDate = input.InvoiceDate.Date;
                monitor.PendingInvoiceStatus = 1;

                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            return new ValidationResult(true);
        }
    }
}
