using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using Serilog;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateReplace
{
    public class CreateReplaceInvoice02ApiCheckExistIdErpRule : IValidationRuleAsync<CreateReplaceInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IRepository<Invoice02HeaderEntity, long> _repoInvoice02Header;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IInvoice02ErpIdRepository _invoice02ErpIdRepository;
        private readonly IVnisCoreMongoInvoice02ErpIdRepository _mongoInvoice02ErpIdRepository;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;


        public CreateReplaceInvoice02ApiCheckExistIdErpRule(
            IAppFactory appFactory,
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IRepository<Invoice02HeaderEntity, long> repoInvoice02Header,
            IVnisCoreMongoInvoice02ErpIdRepository mongoInvoice02ErpIdRepository,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService,
            IInvoice02ErpIdRepository invoice02ErpIdRepository)
        {
            _localizier = localizier;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoInvoice02Header = repoInvoice02Header;
            _mongoInvoice02ErpIdRepository = mongoInvoice02ErpIdRepository;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _invoice02ErpIdRepository = invoice02ErpIdRepository;
            _invoiceService = invoiceService;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice02ApiRequestModel input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });

            var existErpInMongos = await _mongoInvoice02ErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
            if (existErpInMongos.Any(x => x.ErpId == input.ErpId && x.Status != ErpIdStatus.CreateInvoiceError.GetHashCode()))
            {
                //lấy ở mongo ra xem hóa đơn là gì
                var exist = await _mongoInvoice02Repository.GetByErpId(input.ErpId, tenantId);
                if (exist != null)
                {
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.DuplicateErpId", new string[] { "" }])
                    {
                        Data =
                        {
                            { "id", exist.Id },
                            { "serialNo", exist.SerialNo },
                            { "templateNo", exist.TemplateNo },
                            { "invoiceNo", exist.InvoiceNo },
                            { "transactionId", exist.TransactionId },
                            { "erpId", exist.ErpId },
                            { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(exist.InvoiceStatus) },
                            { "signStatus", EnumExtension.TryToEnum<SignStatus>(exist.SignStatus) }
                        }
                    };
                }
                else
                {
                    //query oracle xem có không
                    var existOracle = await _repoInvoice02Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == input.ErpId);
                    if (existOracle != null)
                    {
                        throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.DuplicateErpId", new string[] { "" }])
                        {
                            Data =
                            {
                                { "id", existOracle.Id },
                                { "serialNo", existOracle.SerialNo },
                                { "templateNo", existOracle.TemplateNo },
                                { "invoiceNo", existOracle.InvoiceNo },
                                { "transactionId", existOracle.TransactionId },
                                { "erpId", existOracle.ErpId },
                                { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(existOracle.InvoiceStatus) },
                                { "signStatus", EnumExtension.TryToEnum<SignStatus>(existOracle.SignStatus) }
                            }
                        };
                    }
                }

            }
            else
            {
                var existErpIdOracles = await _invoice02ErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
                if (existErpIdOracles.Any(x => x.ErpId == input.ErpId))
                {
                    var existOracle = await _repoInvoice02Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == input.ErpId);
                    if (existOracle != null)
                    {
                        throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.DuplicateErpId", new string[] { "" }])
                        {
                            Data =
                            {
                                { "id", existOracle.Id },
                                { "serialNo", existOracle.SerialNo },
                                { "templateNo", existOracle.TemplateNo },
                                { "invoiceNo", existOracle.InvoiceNo },
                                { "transactionId", existOracle.TransactionId },
                                { "erpId", existOracle.ErpId },
                                { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(existOracle.InvoiceStatus) },
                                { "signStatus", EnumExtension.TryToEnum<SignStatus>(existOracle.SignStatus) }
                            }
                        };
                    }
                }
            }

            var idErpId = await _invoiceService.GetSEQsNextVal(1, SequenceName.Invoice02ErpId);

            try
            {
                var erpIdExist = await _mongoInvoice02ErpIdRepository.InsertAsync(new Core.MongoDB.Entities.Invoices.Invoice02.MongoInvoice02ErpIdEntity
                {
                    Id = idErpId.FirstOrDefault(),
                    ErpId = input.ErpId,
                    Partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                    Source = (short)InvoiceSource.Api,
                    Status = (short)ErpIdStatus.UnProcess.GetHashCode(),
                    TenantGroup = _appFactory.CurrentTenant.Group,
                    TenantId = tenantId,
                    CreationTime = DateTime.Now
                });

                _validationContext.GetOrAddItem("InvoiceErpId", () =>
                {
                    return erpIdExist;
                });
            }
            catch (Exception ex)
            {
                Log.Error($"Insert mongodb ErpId error: {ex.Message}");

                // lấy ra những ErpId bị trùng trong Mongo
                var erpIdsTrung = await _mongoInvoice02ErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });

                // xóa những ErpId trùng đã tồn tại trong DB
                await _mongoInvoice02ErpIdRepository.DeleteManyByTenantIdAndErpIdAsync(tenantId, erpIdsTrung.Select(x => x.ErpId).ToList());

                // insert lại
                // đang tạm thời để giải pháp như này
                await _mongoInvoice02ErpIdRepository.InsertAsync(new Core.MongoDB.Entities.Invoices.Invoice02.MongoInvoice02ErpIdEntity
                {
                    Id = idErpId.FirstOrDefault(),
                    ErpId = input.ErpId,
                    Partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                    Source = (short)InvoiceSource.Api,
                    Status = (short)ErpIdStatus.UnProcess.GetHashCode(),
                    TenantGroup = _appFactory.CurrentTenant.Group,
                    TenantId = tenantId,
                    CreationTime = DateTime.Now
                });

                //throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.DuplicateErpId"]);
            }

            return new ValidationResult(true);
        }
    }
}
