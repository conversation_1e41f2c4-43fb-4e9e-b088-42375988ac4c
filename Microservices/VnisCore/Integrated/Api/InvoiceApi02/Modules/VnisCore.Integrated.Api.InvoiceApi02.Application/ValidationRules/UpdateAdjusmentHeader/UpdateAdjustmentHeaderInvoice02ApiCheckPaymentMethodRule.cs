using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.UpdateAdjustmentHeader
{
    public class UpdateAdjustmentHeaderInvoice02ApiCheckPaymentMethodRule : IValidationRuleAsync<UpdateAdjustmentHeaderInvoice02ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ISettingService _settingService;
        private readonly IAppFactory _appFactory;

        public UpdateAdjustmentHeaderInvoice02ApiCheckPaymentMethodRule(
            IValidationContext validationContext,
            ISettingService settingService,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
            _settingService = settingService;
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentHeaderInvoice02ApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());

            if (setting == null || string.IsNullOrEmpty(setting.Value))
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.PaymentMethodNotFound", new string[] { input.PaymentMethod }]);

            //cắt chuỗi value setting để lấy value của paymentmethod
            var values = setting.Value.Split(";");
            if (!values.Contains(input.PaymentMethod))
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice02.Api.Intergration.PaymentMethodNotFound", new string[] { input.PaymentMethod }]);

            return new ValidationResult(true);
        }
    }
}
