using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Extensions;
using Core.Shared.Validations;
using Core.Tvan.Constants;

using Microsoft.Extensions.Localization;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.ValidationRules.CreateOldDecree
{
    public class CreateInvoiceOldDecreeCheckInvoiceReferenceRule : IValidationRule<CreateInvoice02OldDecreeRequestModel, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public CreateInvoiceOldDecreeCheckInvoiceReferenceRule(
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _localizer = localizer;
        }

        public ValidationResult Handle(CreateInvoice02OldDecreeRequestModel input)
        {
            if (!int.TryParse(input.InvoiceReference.InvoiceNo, out int number))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.Api.Intergration.CreateOldDecree.InvoiceNoIncorrect"]);

            if (input.InvoiceReference.InvoiceDate > input.InvoiceDate)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.Api.Intergration.CreateOldDecree.InvoiceDateReferenceIncorrect", new string[] { input.InvoiceDate.ToString("dd/MM/yyyy") }]);

            if ((input.ReferenceInvoiceType == (short)LHDCLQuan.Loai2) || (input.ReferenceInvoiceType == (short)LHDCLQuan.Loai3))
            {
                if (!input.InvoiceReference.TemplateNo.IsTemplateNo32())
                    return new ValidationResult(false, $"{input.InvoiceReference.TemplateNo} định dạng dữ liệu mẫu số hóa đơn không đúng");

                if (!input.InvoiceReference.SerialNo.IsSerialNo32())
                    return new ValidationResult(false, $"{input.InvoiceReference.SerialNo} định dạng dữ liệu ký hiệu hóa đơn không đúng");

                if (!input.InvoiceReference.InvoiceNo.IsInvoiceNo32())
                    return new ValidationResult(false, $"{input.InvoiceReference.InvoiceNo} định dạng dữ liệu số hóa đơn không đúng");
            }
            else
            {
                if (!input.InvoiceReference.TemplateNo.IsTemplateNo())
                    return new ValidationResult(false, $"{input.InvoiceReference.TemplateNo} định dạng dữ liệu mẫu số hóa đơn không đúng");

                if (!input.InvoiceReference.SerialNo.IsSerialNo())
                    return new ValidationResult(false, $"{input.InvoiceReference.SerialNo} định dạng dữ liệu ký hiệu hóa đơn không đúng");

                if (!input.InvoiceReference.InvoiceNo.IsInvoiceNo())
                    return new ValidationResult(false, $"{input.InvoiceReference.InvoiceNo} định dạng dữ liệu số hóa đơn không đúng");
            }


            return new ValidationResult(true);
        }
    }

}
