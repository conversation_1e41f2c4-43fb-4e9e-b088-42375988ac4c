using Core.Shared.Attributes;
using Core.Tvan.Constants;

using MediatR;
using System;
using System.ComponentModel.DataAnnotations;

using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands.InvoiceError
{
    public class CreateInvoice02ErrorTvanRequestModel : IRequest<CreateInvoice02ErrorTvanResponseModel>
    {
        [Required(ErrorMessage = "ErpId không được để trống")]
        public string ErpId { get; set; }

        /// <summary>
        /// Lý do
        /// </summary>
        [MaxLength(255, ErrorMessage = "Lý do dài tối đa 255 ký tự")]
        public string Reason { get; set; }

        ///// <summary>
        ///// Tính chất thông báo
        ///// Hủy/thay thế/điều chỉnh/giải trình
        ///// </summary>
        //[Required(ErrorMessage = "Tính chất thông báo không được để trống")]
        //[Range(1, 4, ErrorMessage = "Tính chất thông báo phải có giá trị từ 1 tới 4")]
        //public TThaiTBao Action { get; set; }

        /// <summary>
        /// ngày thông báo
        /// </summary>
        [DataType(DataType.DateTime)]
        [LessThanCurrentDate(ErrorMessage = "Phải nhỏ hơn hoặc bằng ngày hiện tại")]
        public DateTime? AnnouncementDate { get; set; }


        [StringLength(100, ErrorMessage = "Email người mua chỉ được nhập tối đa 100 ký tự")]
        [Email]
        public string Emails { get; set; }

        /// <summary>
        /// Mã đơn vị quan hệ ngân sách(Mã số đơn vị có quan hệ với ngân sách của đơn vị bán tài sản công)
        /// chưa biết đặt tên ra sao cho hợp lý
        /// Bắt buộc (Đối với đơn vị bán tài sản công không có Mã số thuế ) 
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã đơn vị quan hệ ngân sách dài tối đa 7 ký tự")]
        public string BudgetUnitCode { get; set; }
    }
}
