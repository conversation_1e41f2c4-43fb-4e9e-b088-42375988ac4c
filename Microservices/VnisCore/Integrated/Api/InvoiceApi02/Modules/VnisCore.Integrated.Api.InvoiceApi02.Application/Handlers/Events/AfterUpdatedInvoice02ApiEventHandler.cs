using MediatR;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Events;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Handlers.Events
{
    public class AfterUpdatedInvoice02ApiEventHandler : INotificationHandler<AfterUpdatedInvoice02ApiEventModel>
    {
        public AfterUpdatedInvoice02ApiEventHandler()
        {
        }

        public Task Handle(AfterUpdatedInvoice02ApiEventModel notification, CancellationToken UpdatedlationToken)
        {
            //_rabbitService.Publish(new RabbitResponseModel<InvoiceCommandResponseModel>
            //{
            //    EventType = InvoiceProcessEvent.Commit.ToString(),
            //    Data = new InvoiceCommandResponseModel
            //    {
            //        IsInvoice = true,
            //        Resource = InvoiceSource.Api,
            //        SerialNo = notification.Data.SerialNo,
            //        TemplateNo = notification.Data.TemplateNo,
            //        TenantCode = notification.Data.TenantCode,
            //        UserCode = notification.Data.UserCode,
            //        UserFullName = notification.Data.UserFullName,
            //        UserName = notification.Data.UserName,
            //        Type = VnisType._02GTTT,
            //        State = InvoiceActionState.Update,
            //        Code = notification.Data.Code,
            //        Method = HubMethod.UpdateInvoice,
            //        ActionAtUtc = DateTime.UtcNow,
            //        ActionAt = DateTime.Now,
            //        ActionLogInvoice = ActionLogInvoice.Update,
            //        Action = InvoiceAction.Update,
            //    }
            //}, RabbitKey.Exchanges.Events, string.Format(RabbitMqKey.Routings.UpdateInvoice, "01"));

            return Task.CompletedTask;
        }
    }
}
