using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Messages;
using Core.Shared.RabbitMqConstants;
using Core.Shared.Validations;
using Core.TenantManagement;
using MediatR;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands;
using VnisCore.Integrated.Api.InvoiceApi02.Application.RabbitClient;
using Core.Shared.Factory;
using Microsoft.Extensions.Configuration;
using Core.Shared.MessageEventsData.IncreaseLicense;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Interfaces;
using Core.Shared.Services;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Handlers.Commands
{
    public class CreateAdjustmentDetailInvoice02ApiCommandHandler : IRequestHandler<CreateAdjustmentDetailInvoice02ApiRequestModel, CreateAdjustmentDetailInvoice02ApiResponseModel>
    {
        private readonly IInvoice02CommandHandler _invoice02Handler;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly IRabbitService _vnisBackgroundService;
        private readonly string _coreExchangeName;

        public CreateAdjustmentDetailInvoice02ApiCommandHandler(IInvoice02CommandHandler invoice02Handler,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext,
             IAppFactory appFactory,
             IRabbitService vnisBackgroundService,
            IConfiguration configuration)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _invoice02Handler = invoice02Handler;
            _appFactory = appFactory;
            _vnisBackgroundService = vnisBackgroundService;
            _coreExchangeName = configuration.GetSection("RabbitMQ:EventBus:ExchangeName")?.Value;
        }

        public async Task<CreateAdjustmentDetailInvoice02ApiResponseModel> Handle(CreateAdjustmentDetailInvoice02ApiRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var userId = _validationContext.GetItem<Guid>("UserId");
            var userName = _validationContext.GetItem<string>("UserName");
            var userFullName = _validationContext.GetItem<string>("UserFullName");

            var tenant = _validationContext.GetItem<Tenant>("Tenant");

            foreach (var item in request.InvoiceDetails)
            {
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var detailExtras = item.InvoiceDetailExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                    item.InvoiceDetailExtras = detailExtras;
                }
            }

            var rootInvoice = _validationContext.GetItem<Invoice02HeaderEntity>("InvoiceReference");

            var responseInvoice = await _invoice02Handler.PublishAsync(
               new InvoiceCommandRequestModel
               {
                   Resource = InvoiceSource.Api,
                   TenantId = tenantId,
                   UserId = userId,
                   UserName = userName,
                   UserFullName = userFullName,
                   Date = request.InvoiceDate,
                   TenantCode = tenant.TenantCode,
                   TaxCode = tenant.TaxCode,
                   Address = tenant.Address,
                   Country = tenant.Country,
                   District = tenant.District,
                   City = tenant.City,
                   Phones = tenant.Phones,
                   Fax = tenant.Fax,
                   Email = tenant.Emails,
                   LegalName = tenant.LegalName,
                   BankAccount = tenant.BankAccount,
                   BankName = tenant.BankName,
                   SellerFullName = tenant.FullNameVi,
                   SerialNo = request.SerialNo,
                   TemplateNo = request.TemplateNo,
                   Action = InvoiceAction.CreateAdjustmentDetail,
                   Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(request)),
                   Type = VnisType._02GTTT,
                   InvoiceRootId = rootInvoice?.Id
               },
               new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Commands, string.Format(RabbitMqKey.Routings.CreateAdjDetailInvoice, "02")),
               new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Rpc, string.Format(RabbitMqKey.Routings.GeneratedInvoice, "02"))
           );

            if (!responseInvoice.Succeeded)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Api.Intergration.CreateAdjDetail.CreateAdjDetailFail", new string[] { responseInvoice.Exception?.Message }]);

            var response = new CreateAdjustmentDetailInvoice02ApiResponseModel
            {
                Id = responseInvoice.Data.Id,
                ErpId = request.ErpId,
                TransactionId = rootInvoice.TransactionId,
                TemplateNo = request.TemplateNo,
                SerialNo = request.SerialNo,
                InvoiceNo = responseInvoice.Data.InvoiceNo,
                InvoiceStatus = responseInvoice.Data.InvoiceStatus,
                SignStatus = responseInvoice.Data.SignStatus
            };

            _vnisBackgroundService.Publish(new IncreaseLicenseEventSendData
            {
                Quantity = 1,
                TenantId = tenantId
            }, _coreExchangeName, RabbitMqKey.Routings.IncreaseLicense);

            return response;
        }
    }
}
