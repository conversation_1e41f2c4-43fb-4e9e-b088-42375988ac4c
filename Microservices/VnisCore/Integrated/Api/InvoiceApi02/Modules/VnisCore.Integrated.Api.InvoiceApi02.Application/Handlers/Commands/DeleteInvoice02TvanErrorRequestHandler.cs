using Core;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Factory;

using MediatR;

using Microsoft.EntityFrameworkCore;

using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Requests.Commands.InvoiceError;
using VnisCore.Integrated.Api.InvoiceApi02.Application.Models.Responses.Commands.InvoiceError;

namespace VnisCore.Integrated.Api.InvoiceApi02.Application.Handlers.Commands
{
    public class DeleteInvoice02TvanErrorRequestHandler : IRequestHandler<DeleteInvoice02TvanErrorRequestModel, DeleteInvoice02ErrorTvanResponse>
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice02HeaderEntity, long> _repoInvoiceHeader;
        private readonly IRepository<Invoice02ErrorEntity, long> _repoInvoiceError;

        public DeleteInvoice02TvanErrorRequestHandler(
            IAppFactory appFactory,
            IRepository<Invoice02HeaderEntity, long> repoInvoiceHeader,
            IRepository<Invoice02ErrorEntity, long> repoInvoiceError
            )
        {
            _appFactory = appFactory;
            _repoInvoiceHeader = repoInvoiceHeader;
            _repoInvoiceError = repoInvoiceError;
        }

        public async Task<DeleteInvoice02ErrorTvanResponse> Handle(DeleteInvoice02TvanErrorRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            var invoice02Header = await _repoInvoiceHeader.AsNoTracking()
                                                          .FirstOrDefaultAsync(x => x.ErpId == request.ErpId
                                                                                 && x.TenantId == tenantId);
            if (invoice02Header == null)
            {
                throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {request.ErpId}. Vui lòng kiểm tra lại.");
            }

            var invoiceError = await _repoInvoiceError.Where(x => x.TenantId == tenantId && x.InvoiceHeaderId == invoice02Header.Id)
                                                        .OrderByDescending(x => x.Id)
                                                        .FirstOrDefaultAsync();

            if (invoiceError == null)
            {
                throw new UserFriendlyException($"Không tìm thấy hóa đơn có ErpId = {request.ErpId} gửi thông báo sai sót. Vui lòng kiểm tra lại.");
            }

            if (invoiceError.SignStatus == (short)SignStatus.DaKy.GetHashCode())
            {
                throw new UserFriendlyException("Thông báo sai sót đã ký không thể xóa. Vui lòng kiểm tra lại.");
            }

            await _repoInvoiceError.DeleteAsync(invoiceError.Id);

            return new DeleteInvoice02ErrorTvanResponse
            {
                ErpId = invoice02Header.ErpId,
                InvoiceNo = invoice02Header.InvoiceNo,
                SerialNo = invoice02Header.SerialNo,
                TemplateNo = invoice02Header.TemplateNo,
            };
        }
    }
}
