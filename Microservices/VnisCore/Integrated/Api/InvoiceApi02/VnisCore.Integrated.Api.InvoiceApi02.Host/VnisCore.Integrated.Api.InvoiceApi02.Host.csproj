<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Version>5.31.4</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="RabbitMQ.Client" Version="6.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.EventBus.RabbitMQ\Core.EventBus.RabbitMQ.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.Swashbuckle\Core.Swashbuckle.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Host.Shared\Core.Host.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.VaultSharp\Core.VaultSharp.csproj" />
	<ProjectReference Include="..\Modules\VnisCore.Integrated.Api.InvoiceApi02.Application\VnisCore.Integrated.Api.InvoiceApi02.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Logs\" />
  </ItemGroup>

</Project>
