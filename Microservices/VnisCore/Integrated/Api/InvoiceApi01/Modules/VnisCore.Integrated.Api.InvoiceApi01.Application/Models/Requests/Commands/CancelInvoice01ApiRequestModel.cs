using MediatR;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Responses.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands
{
    public class CancelInvoice01ApiRequestModel : IRequest<CancelInvoice01ApiResponseModel>
    {
        public string ErpId { get; set; }

        public short TemplateNo { get; set; }

        /// <summary>
        /// <PERSON><PERSON> hiệu hóa đơn
        /// </summary>
        public string SerialNo { get; set; }

        /// Số hóa đơn
        /// </summary>
        public string InvoiceNo { get; set; }
    }
}
