using System.ComponentModel.DataAnnotations;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Dto
{
    public class InvoicePrintNoteDto
    {
        /// <summary>
        /// Nội dung ghi chú in hóa đơn
        /// </summary>
        [MaxLength(255, ErrorMessage ="Nội dung ghi chú điều chỉnh/thay thế tối đa 255 kí tự")]
        public string Note { get; set; }

        /// <summary>
        /// Hiển thị nội dung ghi chú không
        /// 1: Hiển thị
        /// 0: Ẩn
        /// </summary>
        public bool IsShowNote { get; set; } = false;
    }
}
