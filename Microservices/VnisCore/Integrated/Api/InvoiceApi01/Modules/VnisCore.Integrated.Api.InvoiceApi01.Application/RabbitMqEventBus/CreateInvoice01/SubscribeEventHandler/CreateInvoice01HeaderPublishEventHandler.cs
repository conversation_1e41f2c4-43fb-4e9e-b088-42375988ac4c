using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Serilog;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitMqEventBus.CreateInvoice01.MessageEventData;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitMqEventBus.CreateInvoice01.SubscribeEventHandler
{
    public class CreateInvoice01HeaderPublishEventHandler : IDistributedEventHandler<CreateInvoice01HeaderEventSendData>, ITransientDependency
    {
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;

        public CreateInvoice01HeaderPublishEventHandler(
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository)
        {
            _mongoInvoice01Repository = mongoInvoice01Repository;
        }

        public async Task HandleEventAsync(CreateInvoice01HeaderEventSendData eventData)
        {
            try
            {
                await _mongoInvoice01Repository.InsertManyAsync(eventData.Invoices);
            }
            catch (Exception e)
            {
                Log.Error(e.Message);
                Log.Error(e.StackTrace);
                Log.Error(eventData.Invoices.First().BatchId.ToString());
            }
        }
    }
}
