using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Messages;
using Core.Shared.Services;
using Core.TenantManagement;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Abstractions;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Services
{
    public class CreateRootOldDecreeInvoice01Service : IInvoiceHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ITaxService _taxService;
        private readonly IAppFactory _appFactory;
        private readonly INumberService _numberService;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateRootOldDecreeInvoice01Service(
            IServiceProvider serviceProvider,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ITaxService taxService,
            INumberService numberService,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService)
        {
            _numberService = numberService;
            _taxService = taxService;
            _localizier = localizer;
            _serviceProvider = serviceProvider;
            _invoiceService = invoiceService;
            _appFactory = appFactory;
        }


        public async Task<InvoiceCommandResponseModel> HandleRequestAsync(InvoiceCommandRequestModel message)
        {
            var request = JsonConvert.DeserializeObject<CreateInvoice01OldDecreeRequestModel>(CompressionExtensions.Unzip(message.Data));
            var signStatus = SignStatus.ChoKy;
            var approveStatus = await _invoiceService.GetApproveStatusAsync(message.TenantId);
            var tenantInfo = _appFactory.CurrentTenant;
            var template = await _invoiceService.GetTemplateRawAsync(message.TenantId, message.TemplateNo, message.SerialNo);
            var fromCurrency = await _invoiceService.GetCurrencyRawAsync(message.TenantId);
            var toCurrency = await _invoiceService.GetCurrencyRawAsync(message.TenantId, request.Currency);
            var taxes = await _taxService.GetTaxesRawQueryAsync(message.TenantId);
            var products = await _invoiceService.GetProductsRawAsync(message.TenantId, request.InvoiceDetails.Select(x => x.ProductCode?.Trim()).Distinct().ToList());
            var productTypes = await _invoiceService.GetProductTypesRawAsync(products.Values.Where(x => x.ProductTypeId.HasValue).Select(x => x.ProductTypeId.Value).Distinct().ToList());
            var units = await _invoiceService.GetUnitsRawAsync(message.TenantId, request.InvoiceDetails.Select(x => x.UnitName?.Trim()).Distinct().ToList());
            var headerFields = (await _invoiceService.GetHeaderFieldsRawAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            var detailFields = (await _invoiceService.GetDetailFieldsRawAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);

            request.SerialNo = message.SerialNo;
            request.TemplateNo = message.TemplateNo;
            var tenant = new TenantInfoModel
            {
                TenantCode = message.TenantCode,
                TaxCode = message.TaxCode,
                Address = message.Address,
                Country = message.Country,
                District = message.District,
                City = message.City,
                Phones = message.Phones,
                Fax = message.Fax,
                Email = message.Email,
                LegalName = message.LegalName,
                BankAccount = message.BankAccount,
                BankName = message.BankName,
                FullNameVi = message.SellerFullName
            };

            //Lưu hoá đơn ko số
            var invoice01service = _serviceProvider.GetService<IInvoice01Service>();
            var entity = await invoice01service.CreateOldDecreeRawAsync(
                 request,
                 tenant,
                 message.Resource,
                 message.TenantId,
                 message.UserId,
                 message.UserFullName,
                 message.UserName,
                 signStatus,
                 approveStatus,
                 template,
                 fromCurrency,
                 toCurrency,
                 taxes,
                 products,
                 productTypes,
                 units,
                 headerFields,
                 detailFields
             );

            var result = new InvoiceCommandResponseModel
            {
                //ProcessCode = message.ProcessCode.Value,
                Id = entity.Id,
                TenantId = message.TenantId,
                Type = message.Type,
                UserId = message.UserId,
                UserFullName = message.UserFullName,
                UserName = message.UserName,
                Method = HubMethod.CreateInvoice,
                InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(request.InvoiceStatus),
                SignStatus = signStatus,
                ApproveStatus = approveStatus,
                //Number = number.Number,
                //InvoiceNo = number.InvoiceNo,
                TemplateNo = message.TemplateNo,
                SerialNo = message.SerialNo,
                State = InvoiceActionState.CreateRoot,
                ActionLogInvoice = ActionLogInvoice.Create,
                Action = InvoiceAction.CreateRoot,
                Resource = message.Resource,
                ActionAt = DateTime.Now,
                ActionAtUtc = DateTime.UtcNow,
                ConnectionId = message.ConnectionId,
                InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                {
                    NewInvoiceDate = request.InvoiceDate,
                    NewTotalAmount = request.TotalAmount,
                    NewTotalPaymentAmount = request.TotalPaymentAmount,
                    NewTotalVatAmount = request.TotalVatAmount
                }
            };

            var number = await _numberService.GenerateAsync(message.TenantId, message.TemplateNo, message.SerialNo, message.Date);
            if (number == null || number.Number == 0)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.GenerateNumberFail"]);

            //Lưu số
            await _invoiceService.SaveNumberAsync(entity.Id, number);

            result.Number = number.Number;
            result.InvoiceNo = number.InvoiceNo;

            return result;
        }
    }

}
