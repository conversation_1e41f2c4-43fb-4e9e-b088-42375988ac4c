using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Messages;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.DependencyInjection;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Abstractions;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;
using Newtonsoft.Json;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using Core.Shared.Services;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models;
using Dapper;
using System.Globalization;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.RabbitClient.Services
{
    public class UpdateReplaceInvoice01Service : IInvoiceHandler
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAppFactory _appFactory;
        private readonly IModel Channel;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ITaxService _taxService;

        public UpdateReplaceInvoice01Service(IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IRabbitBusClient rabbitBusClient,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            ITaxService taxService)
        {
            _localizier = localizier;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            Channel = rabbitBusClient.GetChannel();
            _serviceProvider = serviceProvider;
            _taxService = taxService;
        }

        public async Task<InvoiceCommandResponseModel> HandleRequestAsync(InvoiceCommandRequestModel message)
        {
            var repoInvoice01Header = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice01HeaderEntity>>();
            var repoInvoice01Detail = _serviceProvider.GetService<IInvoiceDetailRepository<Invoice01DetailEntity>>();
            var repoInvoice01Tax = _serviceProvider.GetService<IInvoice01TaxBreakdownRepository>();
            var request = JsonConvert.DeserializeObject<UpdateReplaceInvoice01ApiRequestModel>(CompressionExtensions.Unzip(message.Data));
            var invoiceStatus = InvoiceStatus.ThayThe;
            var signStatus = SignStatus.ChoKy;
            var approveStatus = await _invoiceService.GetApproveStatusAsync(message.TenantId);
            var tenantInfo = _appFactory.CurrentTenant;
            var template = await _invoiceService.GetTemplateRawAsync(message.TenantId, message.TemplateNo, message.SerialNo);
            var fromCurrency = await _invoiceService.GetCurrencyRawAsync(message.TenantId);
            var toCurrency = await _invoiceService.GetCurrencyRawAsync(message.TenantId, request.Currency);
            var taxes = await _taxService.GetTaxesRawQueryAsync(message.TenantId);
            var products = await _invoiceService.GetProductsRawAsync(message.TenantId, request.InvoiceDetails.Select(x => x.ProductCode?.Trim()).Distinct().ToList());
            var productTypes = await _invoiceService.GetProductTypesRawAsync(products.Values.Where(x => x.ProductTypeId.HasValue).Select(x => x.ProductTypeId.Value).Distinct().ToList());
            var units = await _invoiceService.GetUnitsRawAsync(message.TenantId, request.InvoiceDetails.Select(x => x.UnitName?.Trim()).Distinct().ToList());
            var headerFields = (await _invoiceService.GetHeaderFieldsRawAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            var detailFields = (await _invoiceService.GetDetailFieldsRawAsync(message.TenantId)).ToDictionary(x => x.FieldName, x => x);
            var rootInvoice = await repoInvoice01Header.GetByIdRawAsync(message.TenantId, message.InvoiceId.Value);
            var details = await repoInvoice01Detail.QueryByIdInvoiceHeaderRawAsync(message.TenantId, message.InvoiceId.Value);
            var taxBreakdowns = await repoInvoice01Tax.QueryByInvoiceHeaderIdRawAsync(message.InvoiceId.Value);

            request.SerialNo = message.SerialNo;

            //Lưu hoá đơn ko số
            var invoice01service = _serviceProvider.GetService<IInvoice01Service>();
            await invoice01service.UpdateReplaceRawAsync(
                 request,
                 rootInvoice,
                 details,
                 taxBreakdowns,
                 signStatus,
                 approveStatus,
                 message.Resource,
                 message.TenantId,
                 message.UserId,
                 message.UserFullName,
                 message.UserName,
                 template,
                 fromCurrency,
                 toCurrency,
                 taxes,
                 products,
                 productTypes,
                 units,
                 headerFields,
                 detailFields
             );

            var result = new InvoiceCommandResponseModel
            {
                //ProcessCode = message.ProcessCode.Value,
                Id = rootInvoice.Id,
                TenantId = message.TenantId,
                Type = message.Type,
                UserId = message.UserId,
                UserFullName = message.UserFullName,
                UserName = message.UserName,
                Method = HubMethod.UpdateInvoice,
                InvoiceStatus = invoiceStatus,
                SignStatus = signStatus,
                ApproveStatus = approveStatus,
                Number = rootInvoice.Number,
                InvoiceNo = rootInvoice.InvoiceNo,
                TemplateNo = message.TemplateNo,
                SerialNo = message.SerialNo,
                State = InvoiceActionState.UpdateReplace,
                ActionLogInvoice = ActionLogInvoice.Update,
                Action = InvoiceAction.UpdateReplace,
                Resource = message.Resource,
                ActionAt = DateTime.Now,
                ActionAtUtc = DateTime.UtcNow,
                ConnectionId = message.ConnectionId,
                InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                {
                    NewInvoiceDate = request.InvoiceDate,
                    NewTotalAmount = request.TotalAmount,
                    NewTotalPaymentAmount = request.TotalPaymentAmount,
                    NewTotalVatAmount = request.TotalVatAmount
                }
            };
            
            //lấy dữ liệu bảng InvoiceReference để update invoiceDateReference
            var repoInvoiceReference = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<Invoice01ReferenceEntity>>();
            var invoiceRef = await repoInvoiceReference.GetByInvoiceReferenceIdRawAsync(rootInvoice.Id);
            if (invoiceRef != null)
            {
                await _invoiceService.UpdateDateInvoiceReference(invoiceRef, rootInvoice.InvoiceDate);
            }

            return result;
        }
    }
}
