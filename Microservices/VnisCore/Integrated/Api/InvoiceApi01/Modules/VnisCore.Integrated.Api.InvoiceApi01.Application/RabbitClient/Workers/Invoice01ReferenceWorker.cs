//using Microsoft.Extensions.DependencyInjection;
//using Microsoft.Extensions.Logging;
//using RabbitMQ.Client.Events;
//using System;
//using System.Collections.Generic;
//using System.Threading.Tasks;

//namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Workers
//{
//    public class Invoice01ReferenceWorker : RabbitClient<RabbitResponseModel<InvoiceCommandResponseModel>, RabbitResponseModel<InvoiceCommandResponseModel>>
//    {
//        private readonly IServiceProvider _serviceProvider;
//        private readonly ILogger<Invoice01ReferenceWorker> _logger;

//        public Invoice01ReferenceWorker(
//            IServiceProvider serviceProvider,
//            ILogger<Invoice01ReferenceWorker> logger,
//            IRabbitBus rabbitChannel) : base(rabbitChannel)
//        {
//            _serviceProvider = serviceProvider;
//            _logger = logger;

//            rabbitChannel.InitChannel("ReferenceInvoice01");
//            BasicQos();
//            InitQueue(queueName: "reference-invoice01");
//            InitBinding(RabbitKey.Exchanges.Events, new List<string>
//            {
//                string.Format(RabbitMqKey.Routings.CreateAdjDetailInvoice, "01"),
//                string.Format(RabbitMqKey.Routings.CreateAdjHeaderInvoice, "01"),
//                string.Format(RabbitMqKey.Routings.CreateReplaceInvoice, "01"),
//                string.Format(RabbitMqKey.Routings.UpdateAdjustmentDetailInvoice, "01"),
//                string.Format(RabbitMqKey.Routings.UpdateAdjustmentHeaderInvoice, "01"),
//                string.Format(RabbitMqKey.Routings.UpdateReplaceInvoice, "01"),
//                string.Format(RabbitMqKey.Routings.AddInvoiceNo, "01")
//            });
//        }

//        public override async Task HandleAsync(BasicDeliverEventArgs ea, RabbitResponseModel<InvoiceCommandResponseModel> request)
//        {
//            try
//            {
//                Console.WriteLine($"Invoice01ReferenceWorker - Received Invoice: {request.Data.Code}");
//                _logger.LogDebug($"Invoice01ReferenceWorker - Received Invoice: {request.Data.Code}");

//                using (var scope = _serviceProvider.CreateScope())
//                {
//                    var invoice01UoW = _serviceProvider.GetService<IInvoice01UnitOfWork>();
//                    var repoInvoiceHeader = invoice01UoW.GetRepository<IInvoiceHeaderRepository<Invoice01Header>>();
//                    var repoInvoiceReference = invoice01UoW.GetRepository<IInvoiceReferenceRepository<Invoice01Reference>>();

//                    Invoice01Reference invoiceReference = null;
//                    var signStatus = request.Data.SignStatus;
//                    var approveStatus = request.Data.ApproveStatus;
//                    var invoiceNo = request.Data.InvoiceNo;
//                    var templateNo = request.Data.TemplateNo;
//                    var serialNo = request.Data.SerialNo;

//                    // nếu là sửa hóa đơn thay thế/điều chỉnh thì update dữ liệu hóa đơn thay thế/điều chỉnh vào bảng reference
//                    //hoặc là điều chỉnh định danh không sinh số
//                    if (ea.RoutingKey.StartsWith("add-invoiceno.invoice"))
//                    {
//                        //trường hợp là CẤP SỐ (Phát hành) thay thế/điều chỉnh
//                        var invoice01Reference = await repoInvoiceReference.GetByCodeInvoiceHeaderAsync(request.Data.Code);
//                        if (invoice01Reference != null)
//                        {
//                            var rootInvoice = await GetInvoiceByCodeAsync(scope, invoice01Reference.InvoiceReferenceCode);
//                            if (rootInvoice == null)
//                                throw new Exception("Không tìm thấy hóa đơn gốc");

//                            var replaceOrAdjInvoice = await GetInvoiceByCodeAsync(scope, request.Data.Code);
//                            if (replaceOrAdjInvoice == null)
//                                throw new Exception("Không tìm thấy hóa đơn điều chỉnh/thay thế");

//                            // update thông tin hóa đơn gốc
//                            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.ThayThe.GetHashCode())
//                                repoInvoiceHeader.UpdateFields(rootInvoice,
//                                          rootInvoice.Set(x => x.InvoiceStatus, InvoiceStatus.BiThayThe.GetHashCode()));

//                            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
//                                repoInvoiceHeader.UpdateFields(rootInvoice,
//                                          rootInvoice.Set(x => x.InvoiceStatus, InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()));

//                            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
//                                repoInvoiceHeader.UpdateFields(rootInvoice,
//                                          rootInvoice.Set(x => x.InvoiceStatus, InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()));

//                            signStatus = EnumExtension.ToEnum<SignStatus>(rootInvoice.SignStatus);
//                            approveStatus = EnumExtension.ToEnum<ApproveStatus>(rootInvoice.ApproveStatus);
//                            invoiceNo = rootInvoice.InvoiceNo;
//                            templateNo = rootInvoice.TemplateNo;
//                            serialNo = rootInvoice.SerialNo;

//                            // insert thông tin hóa đơn gốc vào bảng reference
//                            invoiceReference = new Invoice01Reference
//                            {
//                                CreatedAt = DateTime.Now,
//                                CreatedAtUtc = DateTime.UtcNow,
//                                InvoiceDateReference = replaceOrAdjInvoice.InvoiceDate,
//                                InvoiceCode = rootInvoice.Code,
//                                InvoiceNoReference = replaceOrAdjInvoice.InvoiceNo,
//                                InvoiceReferenceCode = replaceOrAdjInvoice.Code,
//                                NumberReference = replaceOrAdjInvoice.Number ?? 0,
//                                SerialNoReference = replaceOrAdjInvoice.SerialNo,
//                                TemplateNoReference = replaceOrAdjInvoice.TemplateNo,
//                                InvoiceStatus = rootInvoice.InvoiceStatus,
//                                TenantCode = rootInvoice.TenantCode,
//                                Partition = long.Parse(rootInvoice.InvoiceDate.ToString($"yyyyMMddHHmm"))
//                            };
//                            await repoInvoiceReference.InsertAsync(invoiceReference);
//                        }
//                    }
//                    else
//                    {
//                        if (!request.Data.CodeInvoiceReference.HasValue)
//                        {
//                            //trường hợp là điều chỉnh định danh không sinh số hoặc là sửa

//                            var invoiceHeader = await GetInvoiceByCodeAsync(scope, request.Data.Code);
//                            if (invoiceHeader == null)
//                                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceNotFound"]);

//                            //nếu là tạo hóa đơn điều chỉnh định danh k sinh số
//                            if (ea.RoutingKey.StartsWith("create-adj-header.invoice"))
//                            {
//                                invoiceReference = new Invoice01Reference
//                                {
//                                    CreatedAt = DateTime.Now,
//                                    CreatedAtUtc = DateTime.UtcNow,
//                                    InvoiceCode = request.Data.Code,
//                                    InvoiceStatus = request.Data.InvoiceStatus.GetHashCode(),
//                                    TenantCode = request.Data.TenantCode,
//                                    InvoiceDateReference = invoiceHeader.InvoiceDate,
//                                    InvoiceNoReference = invoiceHeader.InvoiceNo,
//                                    InvoiceReferenceCode = invoiceHeader.Code,
//                                    NumberReference = invoiceHeader.Number ?? 0,
//                                    SerialNoReference = invoiceHeader.SerialNo,
//                                    TemplateNoReference = invoiceHeader.TemplateNo
//                                };

//                                await repoInvoiceReference.InsertAsync(invoiceReference);
//                            }
                           
//                            else
//                            {
//                                //nếu là sửa hóa đơn
//                                invoiceReference = await repoInvoiceReference.GetByInvoiceReferenceCodeAsync(request.Data.Code);
//                                if (invoiceReference == null)
//                                    throw new Exception("Không tìm thấy thông tin hóa đơn liên quan");

//                                repoInvoiceReference.UpdateFields(invoiceReference, invoiceReference.Set(x => x.InvoiceDateReference, invoiceHeader.InvoiceDate));
//                            }
//                        }
//                        else
//                        {
//                            Invoice01Header rootInvoice = null;
//                            Invoice01Header replaceOrAdjInvoice = null;


//                            //trường hợp là tạo thay thế/điều chỉnh
//                            rootInvoice = await GetInvoiceByCodeAsync(scope, request.Data.CodeInvoiceReference.Value);
//                            if (rootInvoice == null)
//                                throw new Exception("Không tìm thấy hóa đơn gốc");

//                            replaceOrAdjInvoice = await GetInvoiceByCodeAsync(scope, request.Data.Code);
//                            if (replaceOrAdjInvoice == null)
//                                throw new Exception("Không tìm thấy hóa đơn điều chỉnh/thay thế");

//                            if (string.IsNullOrEmpty(replaceOrAdjInvoice.InvoiceNo))
//                                throw new Exception($"Hóa đơn điều chỉnh/thay thế cho hóa đơn {rootInvoice.SellerTaxCode}-{rootInvoice.TemplateNo}-{rootInvoice.SerialNo}-{rootInvoice.InvoiceNo} CHƯA CÓ SỐ HÓA ĐƠN");

//                            // update thông tin hóa đơn gốc
//                            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.ThayThe.GetHashCode())
//                                repoInvoiceHeader.UpdateFields(rootInvoice,
//                                          rootInvoice.Set(x => x.InvoiceStatus, InvoiceStatus.BiThayThe.GetHashCode()));

//                            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
//                                repoInvoiceHeader.UpdateFields(rootInvoice,
//                                          rootInvoice.Set(x => x.InvoiceStatus, InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()));

//                            if (replaceOrAdjInvoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
//                                repoInvoiceHeader.UpdateFields(rootInvoice,
//                                          rootInvoice.Set(x => x.InvoiceStatus, InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()));

//                            signStatus = EnumExtension.ToEnum<SignStatus>(rootInvoice.SignStatus);
//                            approveStatus = EnumExtension.ToEnum<ApproveStatus>(rootInvoice.ApproveStatus);
//                            invoiceNo = rootInvoice.InvoiceNo;
//                            templateNo = rootInvoice.TemplateNo;
//                            serialNo = rootInvoice.SerialNo;

//                            // insert thông tin hóa đơn gốc vào bảng reference
//                            invoiceReference = new Invoice01Reference
//                            {
//                                CreatedAt = DateTime.Now,
//                                CreatedAtUtc = DateTime.UtcNow,
//                                InvoiceDateReference = replaceOrAdjInvoice.InvoiceDate,
//                                InvoiceCode = rootInvoice.Code,
//                                InvoiceNoReference = replaceOrAdjInvoice.InvoiceNo,
//                                InvoiceReferenceCode = replaceOrAdjInvoice.Code,
//                                NumberReference = replaceOrAdjInvoice.Number ?? 0,
//                                SerialNoReference = replaceOrAdjInvoice.SerialNo,
//                                TemplateNoReference = replaceOrAdjInvoice.TemplateNo,
//                                InvoiceStatus = rootInvoice.InvoiceStatus,
//                                TenantCode = rootInvoice.TenantCode,
//                                Partition = long.Parse(rootInvoice.InvoiceDate.ToString($"yyyyMMddHHmm"))
//                            };
//                            await repoInvoiceReference.InsertAsync(invoiceReference);
//                        }
//                    }

//                    await invoice01UoW.CommitAsync();

//                    if (invoiceReference != null)
//                    {
//                        Publish(new RabbitResponseModel<InvoiceCommandResponseModel>
//                        {
//                            Data = new InvoiceCommandResponseModel
//                            {
//                                Code = invoiceReference.InvoiceCode,
//                                Type = VnisType._01GTKT,
//                                InvoiceNo = invoiceNo,
//                                TemplateNo = templateNo,
//                                SerialNo = serialNo,
//                                TenantCode = invoiceReference.TenantCode,
//                                InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoiceReference.InvoiceStatus),
//                                ApproveStatus = approveStatus,
//                                SignStatus = signStatus,
//                                UserFullName = request.Data.UserFullName,
//                                UserName = request.Data.UserName,
//                                UserCode = request.Data.UserCode,
//                                ConnectionId = request.Data.ConnectionId
//                            }
//                        }, RabbitKey.Exchanges.Events, string.Format(RabbitMqKey.Routings.UpdatedInvoiceReference, "01"));
//                    }
//                }

//                Console.WriteLine($"Invoice01ReferenceWorker - Success Invoice: {request.Data.Code}");
//                _logger.LogDebug($"Invoice01ReferenceWorker - Success Invoice: {request.Data.Code}");
//            }
//            catch (Exception ex)
//            {
//                Console.WriteLine(ex.Message);
//                _logger.LogError(ex, ex.Message);
//            }
//            finally
//            {
//                BasicAck(ea);
//            }
//        }

//        public async Task<Invoice01Header> GetInvoiceByCodeAsync(IServiceScope scope, Guid invoiceCode)
//        {
//            var invoice01UoW = scope.ServiceProvider.GetService<IInvoice01UnitOfWork>();
//            var repoInvoice01Header = invoice01UoW.GetRepository<IInvoiceHeaderRepository<Invoice01Header>>();
//            return await repoInvoice01Header.GetByCodeAsync(invoiceCode);
//        }
//    }
//}
