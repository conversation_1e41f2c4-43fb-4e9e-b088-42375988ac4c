using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateOldDecree
{
    /// <summary>
    /// Validate tiền tệ
    /// </summary>
    public class CreateInvoiceOldDecreeCheckExistCurrencyRule : IValidationRule<CreateInvoice01OldDecreeRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateInvoiceOldDecreeCheckExistCurrencyRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateInvoice01OldDecreeRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var dataInfoToCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");

            //Nguyên tệ
            var fromCurrency = dataInfoToCreate.FirstOrDefault(x => x.Type == ValidateDataType.FromCurrency.GetHashCode());
            if (fromCurrency == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.FromCurrencyNotFound"]);

            _validationContext.AddItem<CurrencyEntity>("FromCurrency", new CurrencyEntity
            {
                CurrencyCode = fromCurrency.FromCurrencyCode,
                Rounding = fromCurrency.ToCurrencyRounding,
                NameVi = fromCurrency.NameVi,
                MinimumNameVi = fromCurrency.MinimumNameVi,
                NameEn = fromCurrency.NameEn,
                MinimumNameEn = fromCurrency.MinimumNameEn
            });

            //Tiền tệ
            var toCurrency = dataInfoToCreate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
            if (toCurrency == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.ToCurrencyNotFound"]);

            _validationContext.AddItem<CurrencyEntity>("ToCurrency", new CurrencyEntity
            {
                CurrencyCode = toCurrency.ToCurrencyCode,
                Rounding = toCurrency.ToCurrencyRounding,
                Conversion = toCurrency.ToCurrencyConversion,
                NameVi = toCurrency.NameVi,
                MinimumNameVi = toCurrency.MinimumNameVi,
                NameEn = toCurrency.NameEn,
                MinimumNameEn = toCurrency.MinimumNameEn
            });

            return new ValidationResult(true);
        }
    }
}
