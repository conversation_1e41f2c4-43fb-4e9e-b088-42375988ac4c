using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using System.Collections.Generic;
using System.Linq;

using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateOldDecree
{
    /// <summary>
    /// kiểm tra product type. Trường hợp k có chiết khấu nhưng product type = 3 (ChietKhauThuongMai) thì báo lỗi
    /// </summary>
    public class UpdateInvoiceOldDecreeCheckProductTypeRule : IValidationRule<UpdateInvoice01OldDecreeRequestModel, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateInvoiceOldDecreeCheckProductTypeRule(IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
        }

        public ValidationResult Handle(UpdateInvoice01OldDecreeRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            var errorDetailDiscounts = new List<string>();

            foreach (var item in input.InvoiceDetails)
            {
                if (item.ProductType != (short)ProductType.GhiChuDienGiai.GetHashCode())
                    if (string.IsNullOrEmpty(item.UnitName))
                        return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.UnitNameIsNotNull", new string[] { item.Index.ToString() }]);
            }

            return new ValidationResult(true);
        }
    }
}
