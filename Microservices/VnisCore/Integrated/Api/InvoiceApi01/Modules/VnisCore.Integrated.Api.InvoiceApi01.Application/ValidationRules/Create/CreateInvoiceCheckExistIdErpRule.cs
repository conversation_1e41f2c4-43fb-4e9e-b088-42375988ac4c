using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Create
{

    /// <summary>
    /// check trung iderp
    /// </summary>
    public class CreateInvoiceCheckExistIdErpRule : IValidationRuleAsync<CreateInvoice01RequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IInvoice01ErpIdRepository _invoice01ErpIdRepository;
        private readonly IVnisCoreMongoInvoice01ErpIdRepository _mongoInvoice01ErpIdRepository;
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly ILockInvoiceService _lockInvoiceService;

        public CreateInvoiceCheckExistIdErpRule(
            IValidationContext validationContext,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header,
            IVnisCoreMongoInvoice01ErpIdRepository mongoInvoice01ErpIdRepository,
            IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            IInvoice01ErpIdRepository invoice01ErpIdRepository,
            ILockInvoiceService lockInvoiceService)
        {
            _validationContext = validationContext;
            _repoInvoice01Header = repoInvoice01Header;
            _localizier = localizier;
            _mongoInvoice01ErpIdRepository = mongoInvoice01ErpIdRepository;
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _invoice01ErpIdRepository = invoice01ErpIdRepository;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _lockInvoiceService = lockInvoiceService;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice01RequestModel input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });

            _lockInvoiceService.CheckDuplicateErpId(input.ErpId, _appFactory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);

            var existErpInMongos = await _mongoInvoice01ErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
            if (existErpInMongos.Any(x => x.ErpId == input.ErpId && x.Status != ErpIdStatus.CreateInvoiceError.GetHashCode()))
            {
                await ReturnExistedErpIdAsync(input.ErpId, tenantId);
            }
            else
            {
                var existErpIdOracles = await _invoice01ErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
                if (existErpIdOracles.Any(x => x.ErpId == input.ErpId))
                {
                    var existOracle = await _repoInvoice01Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == input.ErpId);
                    if (existOracle != null)
                    {
                        throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.DuplicateErpId", new string[] { "" }])
                        {
                            Data =
                            {
                                { "id", existOracle.Id },
                                { "serialNo", existOracle.SerialNo },
                                { "templateNo", existOracle.TemplateNo },
                                { "invoiceNo", existOracle.InvoiceNo },
                                { "transactionId", existOracle.TransactionId },
                                { "erpId", existOracle.ErpId },
                                { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(existOracle.InvoiceStatus) },
                                { "signStatus", EnumExtension.TryToEnum<SignStatus>(existOracle.SignStatus) }
                            }
                        };
                    }
                }
            }

            var idErpId = await _invoiceService.GetSEQsNextVal(1, SequenceName.Invoice01ErpId);

            try
            {
                var erpIdExist = await _mongoInvoice01ErpIdRepository.InsertAsync(new Core.MongoDB.Entities.Invoices.Invoice01.MongoInvoice01ErpIdEntity
                {
                    Id = idErpId.FirstOrDefault(),
                    ErpId = input.ErpId,
                    Partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                    Source = (short)InvoiceSource.Api,
                    Status = (short)ErpIdStatus.UnProcess.GetHashCode(),
                    TenantGroup = _appFactory.CurrentTenant.Group,
                    TenantId = tenantId,
                    CreationTime = DateTime.Now
                });

                _validationContext.GetOrAddItem("InvoiceErpId", () =>
                {
                    return erpIdExist;
                });
            }
            catch (Exception ex) //TODO: catch đúng exception
            {
                Log.Error($"Insert mongodb ErpId error: {ex.Message}");
                await ReturnExistedErpIdAsync(input.ErpId, tenantId);
                
                // lấy ra những ErpId bị trùng trong Mongo
                var erpIdsTrung = await _mongoInvoice01ErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });

                // xóa những ErpId trùng đã tồn tại trong DB
                await _mongoInvoice01ErpIdRepository.DeleteManyByTenantIdAndErpIdsAsync(tenantId, erpIdsTrung.Select(x => x.ErpId).ToList());

                // insert lại
                // đang tạm thời để giải pháp như này
                await _mongoInvoice01ErpIdRepository.InsertAsync(new Core.MongoDB.Entities.Invoices.Invoice01.MongoInvoice01ErpIdEntity
                {
                    Id = idErpId.FirstOrDefault(),
                    ErpId = input.ErpId,
                    Partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                    Source = (short)InvoiceSource.Api,
                    Status = (short)ErpIdStatus.UnProcess.GetHashCode(),
                    TenantGroup = _appFactory.CurrentTenant.Group,
                    TenantId = tenantId,
                    CreationTime = DateTime.Now
                });

                //throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.InsertErpIdError"]);
            }

            return new ValidationResult(true);
        }

        private async Task ReturnExistedErpIdAsync(string erpId, Guid tenantId)
        {
            //lấy ở mongo ra xem hóa đơn là gì
            var exist = await _mongoInvoice01Repository.GetByErpId(erpId, tenantId);
            if (exist != null)
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.DuplicateErpId", new string[] { "" }])
                {
                    Data =
                        {
                            { "id", exist.Id },
                            { "serialNo", exist.SerialNo },
                            { "templateNo", exist.TemplateNo },
                            { "invoiceNo", exist.InvoiceNo },
                            { "transactionId", exist.TransactionId },
                            { "erpId", exist.ErpId },
                            { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(exist.InvoiceStatus) },
                            { "signStatus", EnumExtension.TryToEnum<SignStatus>(exist.SignStatus) }
                        }
                };
            }
            else
            {
                //query oracle xem có không
                var existOracle = await _repoInvoice01Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == erpId);
                if (existOracle != null)
                {
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice01.Api.Intergration.DuplicateErpId", new string[] { "" }])
                    {
                        Data =
                            {
                                { "id", existOracle.Id },
                                { "serialNo", existOracle.SerialNo },
                                { "templateNo", existOracle.TemplateNo },
                                { "invoiceNo", existOracle.InvoiceNo },
                                { "transactionId", existOracle.TransactionId },
                                { "erpId", existOracle.ErpId },
                                { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(existOracle.InvoiceStatus) },
                                { "signStatus", EnumExtension.TryToEnum<SignStatus>(existOracle.SignStatus) }
                            }
                    };
                }
            }
        }
    }
}
