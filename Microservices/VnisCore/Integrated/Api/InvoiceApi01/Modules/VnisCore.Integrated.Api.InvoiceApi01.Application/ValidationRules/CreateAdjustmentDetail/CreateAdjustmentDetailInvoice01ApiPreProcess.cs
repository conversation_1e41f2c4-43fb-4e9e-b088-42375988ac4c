using Core.Shared.Factory;
using Core.Shared.Validations;
using Core.TenantManagement;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentDetail
{
    public class CreateAdjustmentDetailInvoice01ApiPreProcess : IValidationRuleAsync<CreateAdjustmentDetailInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly ITenantRepository _repoTenant;

        public CreateAdjustmentDetailInvoice01ApiPreProcess(
            IValidationContext validationContext,
            IAppFactory appFactory,
            ITenantRepository repoTenant)
        {
            _validationContext = validationContext;
            _appFactory = appFactory;
            _repoTenant = repoTenant;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustmentDetailInvoice01ApiRequestModel input)
        {
            var userId = _appFactory.CurrentUser.Id;
            var userName = _appFactory.CurrentUser.UserName;
            var userFullName = _appFactory.CurrentUser.Name;
            var tenantId = _appFactory.CurrentTenant.Id;

            await _validationContext.GetOrAddItemAsync("UserName", async () =>
            {
                return userName;
            });

            await _validationContext.GetOrAddItemAsync("UserFullName", async () =>
            {
                return userFullName;
            });

            await _validationContext.GetOrAddItemAsync("UserId", async () =>
            {
                return userId;
            });

            await _validationContext.GetOrAddItemAsync<Tenant>("Tenant", async () =>
            {
                return await _repoTenant.GetAsync(tenantId ?? System.Guid.Empty);
            });

            return new ValidationResult(true);
        }
    }
}
