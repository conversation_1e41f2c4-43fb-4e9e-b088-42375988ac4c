using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Update
{
    /// <summary>
    /// kiểm tra field name detail Extra
    /// </summary>
    public class UpdateInvoice01ApiCheckDetailExtraRule : IValidationRuleAsync<UpdateInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IInvoice01DetailFieldRepository _repoInvoice01DetailField;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateInvoice01ApiCheckDetailExtraRule(IInvoice01DetailFieldRepository repoInvoice01DetailField,
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _repoInvoice01DetailField = repoInvoice01DetailField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice01ApiRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            var commandDetailExtras = input.InvoiceDetails
                                            .Where(x => x.InvoiceDetailExtras != null)
                                            .SelectMany(x => x.InvoiceDetailExtras)
                                            .ToList();
            if (!commandDetailExtras.Any())
                return new ValidationResult(true);


            var commandDetailFieldNames = commandDetailExtras.Select(x => x.FieldName).Distinct();

            if (!commandDetailFieldNames.Any())
                return new ValidationResult(true);

            var tenantUd = _validationContext.GetItem<Guid>("TenantId");

            //lấy các detail field
            var detailFieldNames = await _repoInvoice01DetailField.GetFieldNameByTenantIdAsync(tenantUd);

            var expects = commandDetailFieldNames.Except(detailFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceDetailFieldNotFound", new string[] { string.Join(",", expects) }]);

            input.InvoiceDetails.Where(x => x.InvoiceDetailExtras != null).SelectMany(x => x.InvoiceDetailExtras).ToList().RemoveAll(t => string.IsNullOrEmpty(t.FieldValue));

            return new ValidationResult(true);
        }
    }
}
