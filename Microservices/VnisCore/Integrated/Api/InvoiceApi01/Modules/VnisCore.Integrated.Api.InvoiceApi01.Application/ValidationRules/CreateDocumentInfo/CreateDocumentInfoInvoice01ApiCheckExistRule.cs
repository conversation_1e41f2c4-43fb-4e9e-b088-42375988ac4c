using Core.Shared.Factory;
using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateDocumentInfo
{
    /// <summary>
    /// check đã tồn tại biên bản hóa đơn hay chưa
    /// </summary>
    public class CreateDocumentInfoInvoice01ApiCheckExistRule : IValidationRuleAsync<CreateDocumentInfoInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceDocumentRepository<Invoice01DocumentEntity> _repoInvoice01Document;
        private readonly IAppFactory _appFactory;

        public CreateDocumentInfoInvoice01ApiCheckExistRule(IValidationContext validationContext,
            IInvoiceDocumentRepository<Invoice01DocumentEntity> repoInvoice01Document,
            IAppFactory appFactory)
        {
            _validationContext = validationContext;
            _repoInvoice01Document = repoInvoice01Document;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateDocumentInfoInvoice01ApiRequestModel input)
        {
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

            var invoiceDocument = await _validationContext.GetOrAddItemAsync("InvoiceDocument", async () =>
            {
                return await _repoInvoice01Document.GetByIdInvoiceHeaderAsync(tenantId, invoice.Id);
            });

            if (invoiceDocument != null)
                return new ValidationResult(false, "Biên bản hóa đơn đã tồn tại, hãy xóa biên bản cũ để tạo biên bản hóa đơn mới");

            return new ValidationResult(true);
        }
    }
}
