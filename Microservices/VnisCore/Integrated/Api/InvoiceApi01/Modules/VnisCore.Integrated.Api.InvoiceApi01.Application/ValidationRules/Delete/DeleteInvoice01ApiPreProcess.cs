using Core.Shared.Factory;
using Core.Shared.Validations;

using System.Threading.Tasks;

using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.Delete
{
    public class DeleteInvoice01ApiPreProcess : IValidationRuleAsync<DeleteInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;

        public DeleteInvoice01ApiPreProcess(
            IValidationContext validationContext,
            IAppFactory appFactory)
        {
            _validationContext = validationContext;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(DeleteInvoice01ApiRequestModel input)
        {
            var userId = _appFactory.CurrentUser.Id;
            var tenantId = _appFactory.CurrentTenant.Id;
            var userName = _appFactory.CurrentUser.UserName;
            var userFullName = _appFactory.CurrentUser.Name;

            await _validationContext.GetOrAddItemAsync("TenantId", async() =>
            {
                return tenantId;
            });

            await _validationContext.GetOrAddItemAsync("UserName", async () =>
            {
                return userName;
            });

            await _validationContext.GetOrAddItemAsync("UserFullName", async () =>
            {
                return userFullName;
            });

            await _validationContext.GetOrAddItemAsync("UserId", async() =>
            {
                return userId;
            });

            return new ValidationResult(true);
        }
    }
}
