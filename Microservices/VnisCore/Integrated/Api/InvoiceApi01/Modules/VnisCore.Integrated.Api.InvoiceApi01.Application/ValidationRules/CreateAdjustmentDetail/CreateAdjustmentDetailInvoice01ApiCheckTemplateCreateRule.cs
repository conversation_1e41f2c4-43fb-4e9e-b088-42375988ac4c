using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentDetail
{
    /// <summary>
    /// kiểm tra mẫu của hóa đơn tài khoản hiện tại được phân quyền tạo không
    /// </summary>
    public class CreateAdjustmentDetailInvoice01ApiCheckTemplateCreateRule : IValidationRuleAsync<CreateAdjustmentDetailInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateAdjustmentDetailInvoice01ApiCheckTemplateCreateRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustmentDetailInvoice01ApiRequestModel input)
        {
            var userId = _validationContext.GetItem<Guid>("UserId");
            var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
            var dataInfoToCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");

            var permissions = dataInfoToCreate.Where(x => x.TemplateId == template.TemplateId && x.UserId.HasValue && x.UserId == userId);

            if (!permissions.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.CreateAdjDetail.UserCannotCreate"]);

            return new ValidationResult(true);
        }
    }
}
