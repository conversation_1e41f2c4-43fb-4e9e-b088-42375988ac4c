using Core.Domain.Repositories;
using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateAdjustmentHeader
{
    public class UpdateAdjustmentHeaderInvoiceApiCheckPendingLastInvoiceDateRule : IValidationRuleAsync<UpdateAdjustmentHeaderInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public UpdateAdjustmentHeaderInvoiceApiCheckPendingLastInvoiceDateRule(
            IAppFactory appFactory, 
            IStringLocalizer<CoreLocalizationResource> localizer, 
            IValidationContext validationContext
            )
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentHeaderInvoice01ApiRequestModel input)
        {
            var repoMonitor = _appFactory.Repository<MonitorInvoiceTemplateEntity, long>();

            var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

            var monitor = await repoMonitor.FirstOrDefaultAsync(x => x.Id == invoice.InvoiceTemplateId);

            if (monitor.LastDocumentDate.HasValue && input.InvoiceDate.Date == monitor.LastDocumentDate.Value.Date)
            {
                if (input.InvoiceDate.Date > monitor.PendingLastInvoiceDate?.Date && monitor.PendingInvoiceStatus == 2)
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.Api.Intergration.PendingLastInvoiceDate", monitor.PendingLastInvoiceDate]);

                monitor.PendingLastInvoiceDate = input.InvoiceDate.Date;
                monitor.PendingInvoiceStatus = 1;

                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
            }

            return new ValidationResult(true);
        }
    }
}
