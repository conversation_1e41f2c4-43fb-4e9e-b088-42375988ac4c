using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentHeader
{
    /// <summary>
    /// kiểm tra mẫu hóa đơn
    /// </summary>
    public class CreateAdjustmentHeaderInvoice01ApiCheckExistTemplateRule : IValidationRuleAsync<CreateAdjustmentHeaderInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public CreateAdjustmentHeaderInvoice01ApiCheckExistTemplateRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustmentHeaderInvoice01ApiRequestModel input)
        {
            var dataInfoToCreate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate");

            var template = _validationContext.GetOrAddItem("Template", () =>
            {
                return dataInfoToCreate.FirstOrDefault(x => x.Type == ValidateDataType.Template.GetHashCode());
            });

            if (template == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.InvoiceTemplateNotFound"]);

            return new ValidationResult(true);
        }
    }
}
