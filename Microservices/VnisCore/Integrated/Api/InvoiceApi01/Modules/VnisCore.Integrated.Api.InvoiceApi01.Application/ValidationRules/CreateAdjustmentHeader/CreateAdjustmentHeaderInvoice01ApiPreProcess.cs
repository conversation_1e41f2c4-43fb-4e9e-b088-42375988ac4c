using Core.Dto.Shared;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Core.TenantManagement;

using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Integrated.Api.InvoiceApi01.Application.Interfaces;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjustmentHeader
{
    public class CreateAdjustmentHeaderInvoice01ApiPreProcess : IValidationRuleAsync<CreateAdjustmentHeaderInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly ITenantRepository _repoTenant;
        private readonly IInvoice01Service _invoice01Service;

        public CreateAdjustmentHeaderInvoice01ApiPreProcess(
            IValidationContext validationContext,
            IAppFactory appFactory,
            ITenantRepository repoTenant,
            IInvoice01Service invoice01Service)
        {
            _validationContext = validationContext;
            _appFactory = appFactory;
            _repoTenant = repoTenant;
            _invoice01Service = invoice01Service;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustmentHeaderInvoice01ApiRequestModel input)
        {
            var userId = _appFactory.CurrentUser.Id;
            var userName = _appFactory.CurrentUser.UserName;
            var userFullName = _appFactory.CurrentUser.Name;
            var tenantId = _appFactory.CurrentTenant.Id;

            await _validationContext.GetOrAddItemAsync("UserName", async () =>
            {
                return userName;
            });

            await _validationContext.GetOrAddItemAsync("UserFullName", async () =>
            {
                return userFullName;
            });

            await _validationContext.GetOrAddItemAsync("UserId", async () =>
            {
                return userId;
            });

            await _validationContext.GetOrAddItemAsync<Tenant>("Tenant", async () =>
            {
                return await _repoTenant.GetAsync(tenantId ?? System.Guid.Empty);
            });

            var inputProducts = new List<string>();
            var inputUnits = new List<string>();

            var dataValidToCreate = await _invoice01Service.CreateInvoice01GetDataValid(tenantId.Value, new List<KeyValuePair<short, string>> { new KeyValuePair<short, string>(input.TemplateNo, input.SerialNo) }, new List<string>(), inputProducts, inputUnits);

            _validationContext.GetOrAddItem<List<CreateInvoice01GetDataValidDto>>("DataInfoToCreate", () =>
            {
                return dataValidToCreate;
            });

            return new ValidationResult(true);
        }
    }
}
