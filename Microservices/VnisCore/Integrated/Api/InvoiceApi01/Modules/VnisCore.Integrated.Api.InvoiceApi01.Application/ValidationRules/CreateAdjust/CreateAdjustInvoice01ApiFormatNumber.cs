using Core.Shared.Validations;
using System.Collections.Generic;
using System.Globalization;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.CreateAdjust
{
    /// <summary>
    /// format lại số
    /// </summary>
    public class CreateAdjustInvoice01ApiFormatNumber : IValidationRule<CreateAdjustInvoice01ApiRequestModel, ValidationResult>
    {
        public ValidationResult Handle(CreateAdjustInvoice01ApiRequestModel input)
        {
            //tỷ giá
            var exchangeRate = input.ExchangeRate.ToString("#0.##", CultureInfo.InvariantCulture);
            input.ExchangeRate = decimal.Parse(exchangeRate, CultureInfo.InvariantCulture);

            //tổng thành tiền
            var totalAmount = input.TotalAmount.ToString("#0.######", CultureInfo.InvariantCulture);
            input.TotalAmount = decimal.Parse(totalAmount, CultureInfo.InvariantCulture);

            //tổng tiền thanh toán
            var totalPaymentAmount = input.TotalPaymentAmount.ToString("#0.######", CultureInfo.InvariantCulture);
            input.TotalPaymentAmount = decimal.Parse(totalPaymentAmount, CultureInfo.InvariantCulture);

            //tổng tiền thuế
            var totalVatAmount = input.TotalVatAmount.ToString("#0.######", CultureInfo.InvariantCulture);
            input.TotalVatAmount = decimal.Parse(totalVatAmount, CultureInfo.InvariantCulture);

            //tổng tiền chiết khấu
            var totalDiscountAmountBeforeTax = input.TotalDiscountAmountBeforeTax.ToString("#0.######", CultureInfo.InvariantCulture);
            input.TotalDiscountAmountBeforeTax = decimal.Parse(totalDiscountAmountBeforeTax, CultureInfo.InvariantCulture);

            if (!input.InvoiceDetails.IsNullOrEmpty())
            {

                foreach (var item in input.InvoiceDetails)
                {
                    var amount = item.Amount.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.Amount = decimal.Parse(amount, CultureInfo.InvariantCulture);

                    var paymentAmount = item.PaymentAmount.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.PaymentAmount = decimal.Parse(paymentAmount, CultureInfo.InvariantCulture);

                    var vatAmount = item.VatAmount.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.VatAmount = decimal.Parse(vatAmount, CultureInfo.InvariantCulture);

                    var discountAmountBeforeTax = item.DiscountAmountBeforeTax.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.DiscountAmountBeforeTax = decimal.Parse(discountAmountBeforeTax, CultureInfo.InvariantCulture);

                    var discountPercentBeforeTax = item.DiscountPercentBeforeTax.ToString("#0.##", CultureInfo.InvariantCulture);
                    item.DiscountPercentBeforeTax = decimal.Parse(discountPercentBeforeTax, CultureInfo.InvariantCulture);

                    var vatPercent = item.VatPercent.ToString("#0.##", CultureInfo.InvariantCulture);
                    item.VatPercent = decimal.Parse(vatPercent, CultureInfo.InvariantCulture);

                    var unitPrice = item.UnitPrice.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.UnitPrice = decimal.Parse(unitPrice, CultureInfo.InvariantCulture);

                    var quantity = item.Quantity.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.Quantity = decimal.Parse(quantity, CultureInfo.InvariantCulture);
                }
            }
            if (!input.InvoiceTaxBreakdowns.IsNullOrEmpty())
            {
                foreach (var item in input.InvoiceTaxBreakdowns)
                {
                    var vatAmount = item.VatAmount.ToString("#0.######", CultureInfo.InvariantCulture);
                    item.VatAmount = decimal.Parse(vatAmount, CultureInfo.InvariantCulture);

                    var vatPercent = item.VatPercent.ToString("#0.##", CultureInfo.InvariantCulture);
                    item.VatPercent = decimal.Parse(vatPercent, CultureInfo.InvariantCulture);
                }
            }

            return new ValidationResult(true);
        }
    }
}
