using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories;


namespace VnisCore.Integrated.Api.InvoiceApi01.Application.ValidationRules.UpdateAdjustmentDetail
{
    /// <summary>
    /// check ngày biên bản
    /// </summary>
    public class UpdateAdjustmentDetailInvoice01ApiCheckDocumentDateRule : IValidationRuleAsync<UpdateAdjustmentDetailInvoice01ApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateAdjustmentDetailInvoice01ApiCheckDocumentDateRule(IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IServiceProvider serviceProvider)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _serviceProvider = serviceProvider;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentDetailInvoice01ApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

            var repoInvoiceReference = _serviceProvider.GetService<IInvoiceReferenceRepository<Invoice01ReferenceEntity>>();
            var reference = await repoInvoiceReference.GetByIdInvoiceHeaderAsync(invoice.Id);

            var repoInvoiceFile = _serviceProvider.GetService<IInvoiceDocumentInfoRepository<Invoice01DocumentInfoEntity>>();
            var file = await repoInvoiceFile.GetByIdInvoiceHeaderAsync(tenantId, invoice.Id);

            if (file != null && (file.DocumentDate < reference.InvoiceDateReference || file.DocumentDate > input.InvoiceDate))
                return new ValidationResult(false, _localizier["Vnis.BE.Invoice01.Api.Intergration.UpdateAdjDetail.InvoiceDocumentDateRange"]);

            return new ValidationResult(true);
        }
    }
}
