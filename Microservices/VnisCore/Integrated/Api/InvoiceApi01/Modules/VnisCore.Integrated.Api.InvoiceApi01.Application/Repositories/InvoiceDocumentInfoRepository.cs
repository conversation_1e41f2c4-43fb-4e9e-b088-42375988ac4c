using Core.Domain.Repositories;
using Core.Shared.Factory;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories
{
    public interface IInvoiceDocumentInfoRepository<T> : IBasicRepository<T, long> where T : BaseInvoiceDocumentInfo
    {
        Task<T> GetByIdInvoiceHeaderAsync(Guid tenantId, long idInvoice);

        Task<List<T>> GetByIdInvoiceHeadersAsNoTrackingAsync(Guid tenantId, List<long> idInvoices);
    }

    public class InvoiceDocumentInfoRepository<T> : BaseRepository<T, long>, IInvoiceDocumentInfoRepository<T> where T : BaseInvoiceDocumentInfo
    {
        private readonly IAppFactory _appFactory;

        public InvoiceDocumentInfoRepository(IAppFactory appFactory) : base(appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<T> GetByIdInvoiceHeaderAsync(Guid tenantId, long idInvoice)
        {
            return await _appFactory.Repository<T, long>().FirstOrDefaultAsync(x => x.InvoiceHeaderId == idInvoice && x.TenantId == tenantId);
        }

        public async Task<List<T>> GetByIdInvoiceHeadersAsNoTrackingAsync(Guid tenantId, List<long> idInvoices)
        {
            return await _appFactory.Repository<T, long>().Where(x => idInvoices.Contains(x.InvoiceHeaderId) && x.TenantId == tenantId).AsNoTracking().AsQueryable().ToListAsync();
        }
    }
}
