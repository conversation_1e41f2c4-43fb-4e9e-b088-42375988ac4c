using Core.Domain.Repositories;
using Core.Shared.Factory;
using Dapper;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories
{
    public interface IInvoiceReferenceRepository<T> : IBasicRepository<T, long> where T : BaseInvoiceReference
    {
        Task<T> GetByInvoiceReferenceIdAsync(long invoiceReferenceId);

        Task<T> GetByIdInvoiceHeaderAsync(long idInvoice);

        Task<List<T>> GetByIdInvoiceHeadersAsync(List<long> idInvoices);

        Task<Invoice01ReferenceEntity> GetInvoiceReferenceRawAsync(long IdInvoice);

        Task<Invoice01ReferenceEntity> GetByInvoiceReferenceIdRawAsync(long invoiceReferenceId);
    }


    public class InvoiceReferenceRepository<T> : BaseRepository<T, long>, IInvoiceReferenceRepository<T> where T : BaseInvoiceReference
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<T, long> _repo;

        public InvoiceReferenceRepository(IAppFactory appFactory,
            IRepository<T, long> repo) 
            : base(appFactory)
        {
            _appFactory = appFactory;
            _repo = repo;
        }

        public async Task<T> GetByIdInvoiceHeaderAsync(long idInvoice)
        {
            return await _repo.FirstOrDefaultAsync(x => x.InvoiceHeaderId == idInvoice);
        }
        
        public async Task<List<T>> GetByIdInvoiceHeadersAsync(List<long> idInvoices)
        {
            return await _repo.Where(x => idInvoices.Contains(x.InvoiceHeaderId)).ToListAsync();
        }

        public async Task<T> GetByInvoiceReferenceIdAsync(long invoiceReferenceId)
        {
            return await _repo.FirstOrDefaultAsync(x => x.InvoiceReferenceId == invoiceReferenceId);
        }

        public async Task<Invoice01ReferenceEntity> GetInvoiceReferenceRawAsync(long IdInvoice)
        {
            var query = $"Select * from \"Invoice01Reference\" where \"InvoiceHeaderId\" = {IdInvoice}";
            return await _appFactory.VnisCoreOracle.Connection.QuerySingleOrDefaultAsync<Invoice01ReferenceEntity>(query);
        }
        public async Task<Invoice01ReferenceEntity> GetByInvoiceReferenceIdRawAsync(long invoiceReferenceId)
        {
            var query = $"Select * from \"Invoice01Reference\" where \"InvoiceReferenceId\" = {invoiceReferenceId}";
            return await _appFactory.VnisCoreOracle.Connection.QuerySingleOrDefaultAsync<Invoice01ReferenceEntity>(query);
        }
    }
}
