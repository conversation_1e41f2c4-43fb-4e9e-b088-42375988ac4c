using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core.Domain.Repositories;
using Core.Shared.Factory;

namespace VnisCore.Integrated.Api.InvoiceApi01.Application.Repositories
{
    public interface IInvoiceHeaderExtraRepository<T> where T : BaseInvoiceHeaderExtra
    {
        Task<List<T>> QueryByIdInvoiceHeaderAsync(long idInvoiceHeader);

        Task<List<T>> QueryByIdInvoiceHeadersAsNoTrackingAsync(List<long> idInvoiceHeaders);

        Task<List<T>> QueryByValuesAsNoTrackingAsync(Dictionary<long, string> indexRequestHeaderField);
    }

    public class InvoiceHeaderExtraRepository<T> : IInvoiceHeaderExtraRepository<T> where T : BaseInvoiceHeaderExtra
    {
        private readonly IAppFactory _appFactory;

        public InvoiceHeaderExtraRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<List<T>> QueryByIdInvoiceHeaderAsync(long idInvoiceHeader)
        {
            return await _appFactory.Repository<T, long>().Where(x => x.InvoiceHeaderId == idInvoiceHeader).AsQueryable().ToListAsync();
        }

        public async Task<List<T>> QueryByIdInvoiceHeadersAsNoTrackingAsync(List<long> idInvoiceHeaders)
        {
            return await _appFactory.Repository<T, long>().Where(x => idInvoiceHeaders.Contains(x.InvoiceHeaderId)).AsNoTracking().AsQueryable().ToListAsync();
        }

        public async Task<List<T>> QueryByValuesAsNoTrackingAsync(Dictionary<long, string> indexRequestHeaderField)
        {
            var predicate = PredicateBuilder.New<T>();

            if (indexRequestHeaderField != null && indexRequestHeaderField.Any())
            {
                foreach (var item in indexRequestHeaderField)
                {
                    if (!string.IsNullOrEmpty(item.Value))
                        predicate = predicate.Or(x => x.InvoiceHeaderFieldId == item.Key && x.FieldValue.Contains(item.Value));
                }
            }

            return await _appFactory.Repository<T, long>().Where(predicate).AsNoTracking().AsQueryable().ToListAsync();
        }
    }
}
