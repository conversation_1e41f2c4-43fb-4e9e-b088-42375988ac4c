using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.IncreaseLicense;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Messages;
using Core.Shared.RabbitMqConstants;
using Core.Shared.Validations;
using Core.TenantManagement;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Integrated.Api.TicketApi.Application.Handlers.Rpc;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands.Message;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.RabbitClient;

namespace VnisCore.Integrated.Api.TicketApi.Application.Handlers.Commands
{
    public class CreateTicketCommandHandler : IRequestHandler<CreateTicketRequestModel, CreateTicketResponseModel>
    {
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly ITicketCommandHandler _ticketHandler;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IRabbitService _vnisBackgroundService;
        private readonly string _coreExchangeName;
        private readonly ITicketCacheBusiness _ticketCacheBusiness;
        private readonly ICachingCatalogBusiness _cachingCatalogBusiness;

        public CreateTicketCommandHandler(
            IValidationContext validationContext,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            ITicketCommandHandler ticketCommandHandler,
            IRabbitService vnisBackgroundService,
            IConfiguration configuration,
            ITicketCacheBusiness ticketCacheBusiness,
            ICachingCatalogBusiness cachingCatalogBusiness)
        {
            _localizier = localizier;
            _ticketHandler = ticketCommandHandler;
            _validationContext = validationContext;
            _appFactory = appFactory;
            _vnisBackgroundService = vnisBackgroundService;
            _coreExchangeName = configuration.GetSection("RabbitMQ:EventBus:ExchangeName")?.Value;
            _cachingCatalogBusiness = cachingCatalogBusiness;
            _ticketCacheBusiness = ticketCacheBusiness;
        }

        public async Task<CreateTicketResponseModel> Handle(CreateTicketRequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var userId = _validationContext.GetItem<Guid>("UserId");
            var userName = _validationContext.GetItem<string>("UserName");
            var userFullName = _validationContext.GetItem<string>("UserFullName");

            var tenant = _validationContext.GetItem<Tenant>("Tenant");
            var template = _validationContext.GetItem<InvoiceTemplateEntity>("Template");
            var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
            var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");
            var detailFields = _validationContext.GetItem<List<TicketDetailFieldEntity>>("DetailField");
            var headerFields = _validationContext.GetItem<List<TicketHeaderFieldEntity>>("HeaderField");
            var taxes = _validationContext.GetItem<Dictionary<decimal, Tuple<string, string>>>("Taxes");

            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                var headerExtras = request.InvoiceHeaderExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                request.InvoiceHeaderExtras = headerExtras;
            }

            foreach (var item in request.InvoiceDetails)
            {
                if (item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any())
                {
                    var detailExtras = item.InvoiceDetailExtras.Where(x => !string.IsNullOrWhiteSpace(x.FieldValue)).ToList();
                    item.InvoiceDetailExtras = detailExtras;
                }
            }

            CreateRootTicketMessage message = new CreateRootTicketMessage();
            message.Invoice = request;
            NeedInfomation needInfomation = new NeedInfomation();
            needInfomation.Template = template;
            needInfomation.HeaderFields = !headerFields.IsNullOrEmpty() ? headerFields.ToDictionary(x => x.FieldName, x => x) : new Dictionary<string, TicketHeaderFieldEntity>();
            needInfomation.DetailFields = !detailFields.IsNullOrEmpty() ? detailFields.ToDictionary(x => x.FieldName, x => x) : new Dictionary<string, TicketDetailFieldEntity>();
            needInfomation.FromCurrency = fromCurrency;
            needInfomation.ToCurrency = toCurrency;
            needInfomation.Taxes = taxes;

            #region Thông tin hàng hóa
            // Lấy thông tin hàng hóa, loại hàng hóa
            // Ko cache do sản phẩm có nhiều dữ liệu
            if (!request.InvoiceDetails.IsNullOrEmpty()
                && request.InvoiceDetails.Where(x => !x.ProductCode.IsNullOrEmpty()).Any())
            {
                var productCodes = request.InvoiceDetails.Where(x => !x.ProductCode.IsNullOrEmpty()).Select(x => x.ProductCode);
                var products = await _cachingCatalogBusiness.GetProductsAsync(tenantId, productCodes.ToList());

                if (!products.IsNullOrEmpty())
                {
                    needInfomation.Products = products.ToDictionary(x => x.ProductCode, x => x);
                    // Lấy thông tin đơn vị tính
                    var productTypeId = products.Select(x => x.ProductTypeId);

                    productTypeId = productTypeId.Where(x => x.HasValue && x != 0);
                    if (productTypeId.Any())
                    {
                        var productTypeIds = productTypeId.Select(x => (long)x).ToList();
                        var productTypes = await _cachingCatalogBusiness.GetProductTypesAsync(tenantId, productTypeIds);
                        needInfomation.ProductTypes = productTypes.ToDictionary(x => x.Id, x => x);
                    }
                }
            }
            // 
            var units = await _cachingCatalogBusiness.GetOrSetCachedUnitAsync(tenantId);
            if (!units.IsNullOrEmpty())
            {
                var unitNames = request.InvoiceDetails.Select(x => x.UnitName);
                var existUnit = units.Where(x => unitNames.Contains(x.Name)).ToList();
                if (existUnit.Any())
                {
                    needInfomation.Units = existUnit.Select(x=> new UnitEntity()
                    {
                        Id = x.Id,
                        Name = x.Name,
                        Rounding = x.Rounding,
                    }).ToDictionary(x=>x.Name, x=>x);
                }
            }
            #endregion

            message.NeedInfomation = needInfomation;
            //var abc = CompressionExtensions.Zip(JsonConvert.SerializeObject(message));
            //HandleTicket
            var responseInvoice = await _ticketHandler.PublishAsync(
                new InvoiceCommandRequestModel
                {
                    Action = InvoiceAction.CreateRoot,
                    Resource = InvoiceSource.Api,
                    TenantId = tenantId,
                    UserId = userId,
                    UserName = userName,
                    UserFullName = userFullName,
                    TenantCode = tenant.TenantCode,
                    TaxCode = tenant.TaxCode,
                    Address = tenant.Address,
                    Country = tenant.Country,
                    District = tenant.District,
                    City = tenant.City,
                    Phones = tenant.Phones,
                    Fax = tenant.Fax,
                    Email = tenant.Emails,
                    LegalName = tenant.LegalName,
                    BankName = tenant.BankName,
                    BankAccount = tenant.BankAccount,
                    SellerFullName = tenant.FullNameVi,
                    Date = request.InvoiceDate,
                    SerialNo = request.SerialNo,
                    TemplateNo = request.TemplateNo,
                    Data = CompressionExtensions.Zip(JsonConvert.SerializeObject(message)),
                    Type = VnisType._05TVDT
                },
                new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Commands, string.Format(RabbitMqKey.Routings.CreateRootInvoice, "05")),
                new PublicationAddress(ExchangeType.Topic, RabbitKey.Exchanges.Rpc, string.Format(RabbitMqKey.Routings.GeneratedInvoice, "05")));

            if (!responseInvoice.Succeeded)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.Create.CreateInvoiceFail", new string[] { responseInvoice.Exception?.Message }]);

            var response = new CreateTicketResponseModel
            {
                Id = responseInvoice.Data.Id,
                ErpId = request.ErpId,
                TransactionId = request.TransactionId,
                TemplateNo = request.TemplateNo,
                SerialNo = request.SerialNo,
                InvoiceNo = responseInvoice.Data.InvoiceNo,
                InvoiceStatus = responseInvoice.Data.InvoiceStatus,
                SignStatus = responseInvoice.Data.SignStatus,
            };

            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
           
            _vnisBackgroundService.Publish(new IncreaseLicenseEventSendData
            {
                Quantity = 1,
                TenantId = tenantId
            }, _coreExchangeName, RabbitMqKey.Routings.IncreaseLicense);

            //sync Customer
            await distributedEventBus.PublishAsync(new SyncCustomerEventSendData(new SyncCustomerRequestModel
            {
                IdInvoice = responseInvoice.Data.Id,
                TenantId = tenantId,
                Type = VnisType._05TVDT,
            }));

            //sync Unit
            await distributedEventBus.PublishAsync(new SyncUnitEventSendData(new SyncUnitRequestModel
            {
                IdInvoice = responseInvoice.Data.Id,
                TenantId = tenantId,
                Type = VnisType._05TVDT,
            }));

            return response;
        }
    }
}
