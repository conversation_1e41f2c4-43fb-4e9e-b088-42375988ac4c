using Core.Application.Dtos;
using Core.Shared.Factory;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Queries;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Queries;

namespace VnisCore.Integrated.Api.TicketApi.Application.Handlers.Queries
{
    public class PagingTicketQueryHandler : IRequestHandler<PagingTicketRequestModel, PagedResultDto<PagingTicketResponseModel>>
    {
        private readonly IAppFactory _factory;
        private readonly ITicketService _ticketService;

        public PagingTicketQueryHandler(
            IAppFactory factory,
            ITicketService ticketService)
        {
            _factory = factory;
            _ticketService = ticketService;
        }

        public async Task<PagedResultDto<PagingTicketResponseModel>> Handle(PagingTicketRequestModel request, CancellationToken cancellationToken)
        {
            //var context = new ContextModel
            //{
            //    TenantId = _factory.CurrentTenant.Id ?? Guid.Empty,
            //    UserId = _factory.CurrentUser.Id ?? Guid.Empty
            //};

            //List<long> ids = await GetInvoiceHeaderIdByHeaderExtra(context.TenantId, request.HeaderExtras);

            //var paged = await GetInvoiceHeader(request, context, ids);

            //await MappingIdFileDocument(context, paged.Data);

            //await MappingInvoiceHeaderExtra(context, paged.Data);

            var tenantId = _factory.CurrentTenant.Id ?? Guid.Empty;
            var userId = _factory.CurrentUser.Id ?? Guid.Empty;
            return await _ticketService.GetListAsync(tenantId, userId, request);
        }

        //private async Task MappingIdFileDocument(ContextModel context, IEnumerable<PagingInvoice01ResponseModel> invoices)
        //{
        //    var ids = invoices.Select(x => x.Id).ToList();
        //    var invoiceDocumentInfos = await _repoInvoice01DocumentInfo.GetByIdInvoiceHeadersAsNoTrackingAsync(context.TenantId, ids);

        //    var groupFiles = invoiceDocumentInfos.GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.FirstOrDefault());

        //    foreach (var item in invoices)
        //    {
        //        if (!groupFiles.ContainsKey(item.Id))
        //            continue;

        //        item.FileDocumentId = groupFiles[item.Id].FileId;
        //    }
        //}

        /// <summary>
        /// Mapping HeaderExtra vào từng InvoiceHeader
        /// </summary>
        /// <param name="context"></param>
        /// <param name="invoices"></param>
        /// <returns></returns>
        //private async Task MappingInvoiceHeaderExtra(ContextModel context, IEnumerable<PagingInvoice01ResponseModel> invoices)
        //{
        //    var settingHeaderExtra = await GetHeaderExtraAsync(context.TenantId);
        //    if (!settingHeaderExtra.Any())
        //        return;

        //    //Lấy HeaderExtra theo Code bảng InvoiceHeader
        //    var ids = invoices.Select(x => x.Id).ToList();
        //    var headerExtras = await _repoInvoice01HeaderExtra.QueryByIdInvoiceHeadersAsNoTrackingAsync(ids);

        //    var groupHeaderExtras = headerExtras
        //        .GroupBy(x => x.InvoiceHeaderId)
        //        .ToDictionary(x => x.Key, x => x.ToList());

        //    //Mapping HeaderExtra
        //    foreach (var item in invoices)
        //    {
        //        if (!groupHeaderExtras.ContainsKey(item.Id))
        //            continue;

        //        item.InvoiceHeaderExtras = groupHeaderExtras[item.Id]
        //            .Where(x => settingHeaderExtra.ContainsKey(x.InvoiceHeaderFieldId))
        //            .Select(x => new PagingInvoice01ResponseModel.PagingInvoice01HeaderExtraResponseModel
        //            {
        //                FieldName = settingHeaderExtra[x.InvoiceHeaderFieldId],
        //                FieldValue = x.FieldValue
        //            }).ToList();
        //    }
        //}

        /// <summary>
        /// Lấy InvoiceHeader
        /// </summary>
        /// <param name="request"></param>
        /// <param name="context"></param>
        /// <param name="ids">Danh sách Code của bảng InvoiceHeader nếu search theo HeaderExtra</param>
        /// <returns></returns>
        //private async Task<Paged<PagingInvoice01ResponseModel>> GetInvoiceHeader(PagingInvoice01RequestModel request, ContextModel context, List<long> ids)
        //{
        //    var datas = new List<PagingInvoice01ResponseModel>();

        //    var allReadTemplates = await _invoiceService.GetReadInvoiceTemplatesAsync(context.TenantId, context.UserId, VnisType._05TVDT);
        //    if (request.TemplateNo != null)
        //        allReadTemplates = allReadTemplates.Where(x => x.TemplateNo == request.TemplateNo).ToList();

        //    var paged = await _repoInvoice01Header.PagingAsync(context.TenantId, new PagingInvoiceModel
        //    {
        //        TransactionId = request.TransactionId,
        //        ErpId = request.ErpId,
        //        UserNameCreatorErp = request.UserNameCreatorErp,
        //        InvoiceTemplateIds = request.InvoiceTemplateIds,
        //        AllReadTemplateIds = allReadTemplates.Select(x => x.Id).ToList(),
        //        InvoiceNo = request.InvoiceNo,
        //        InvoiceStatuses = request.InvoiceStatuses,
        //        SignStatuses = request.SignStatuses,
        //        ApproveStatuses = request.ApproveStatuses,
        //        CreateFromDate = request.CreateFromDate,
        //        CreateToDate = request.CreateToDate,
        //        CancelFromDate = request.CancelFromDate,
        //        CancelToDate = request.CancelToDate,
        //        Customers = request.Customers,
        //        FromNumber = request.FromNumber,
        //        ToNumber = request.ToNumber,
        //        UserNameCreator = request.UserNameCreator,
        //        IsNullInvoice = request.IsNullInvoice,
        //        IssuedTime = request.IssuedTime,
        //        Ids = ids, //Nếu query theo HeaderExtra
        //        Q = request.Q,
        //        Page = request.Page,
        //        Search = request.Search,
        //        Size = request.Size,
        //        Sort = request.Sort,
        //        Columns = request.Columns
        //    });

        //    foreach (var item in paged.Data)
        //    {
        //        PagingInvoice01ResponseModel model = new PagingInvoice01ResponseModel
        //        {
        //            Id = item.Id,
        //            ErpId = item.ErpId,
        //            TransactioId = item.TransactionId,
        //            TemplateNo = item.TemplateNo,
        //            SerialNo = item.SerialNo,
        //            InvoiceNo = item.InvoiceNo,
        //            Number = item.Number,
        //            InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(item.InvoiceStatus),
        //            SignStatus = EnumExtension.ToEnum<SignStatus>(item.SignStatus),
        //            ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(item.ApproveStatus),
        //            InvoiceDate = item.InvoiceDate,
        //            TotalAmount = item.TotalAmount,
        //            TotalPaymentAmount = item.TotalPaymentAmount,
        //            TotalVatAmount = item.TotalVatAmount,
        //            BuyerFullName = item.BuyerFullName,
        //            BuyerEmail = item.BuyerEmail,
        //            BuyerCode = item.BuyerCode,
        //            PrintedTime = item.PrintedTime,
        //            UserNameCreator = item.UserNameCreator,
        //            CreatorErp = item.CreatorErp,
        //            Note = item.Note,
        //            Source = EnumExtension.ToEnum<InvoiceSource>(item.Source),
        //            IsViewed = item.IsViewed,
        //            IsOpened = item.IsOpened,
        //            VerificationCode = item.VerificationCode
        //        };

        //        if (item.ExtraProperties != null)
        //        {
        //            var extraProperties = item.ExtraProperties.ToDictionary(x => x.Key, x => (string)x.Value);
        //            var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
        //            if (extraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
        //            {
        //                headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"]);
        //            }

        //            if (headerExtraProperties.Any())
        //            {
        //                model.InvoiceHeaderExtras = headerExtraProperties.Select(x => new PagingInvoice01HeaderExtraResponseModel
        //                {
        //                    FieldValue = x.FieldValue,
        //                    FieldName = x.FieldName
        //                }).ToList();
        //            }
        //        }

        //        datas.Add(model);
        //    }

        //    var result = new Paged<PagingInvoice01ResponseModel>
        //    {
        //        Data = datas,
        //        Page = paged.Page,
        //        Q = paged.Q,
        //        Size = paged.Size,
        //        TotalItems = paged.TotalItems,
        //        TotalPages = paged.TotalPages
        //    };
        //    return result;
        //}

        /// <summary>
        /// Lấy Code bảng InvoiceHeader theo HeaderExtra
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="requestHeaderExtras"></param>
        /// <returns></returns>
        //private async Task<List<long>> GetInvoiceHeaderIdByHeaderExtra(Guid tenantId, List<string> requestHeaderExtras)
        //{
        //    if (requestHeaderExtras == null || !requestHeaderExtras.Any())
        //        return null;

        //    //lấy headerField
        //    var headerFields = await _repoInvoice01HeaderField.QueryByTenantIdAsync(tenantId);
        //    var indexHeaderFields = headerFields.ToDictionary(x => x.FieldName, x => x);
        //    var invoiceHeaderFieldIds = headerFields.Select(x => x.Id).ToList();

        //    var indexRequestHeaderField = new Dictionary<long, string>();
        //    //lấy những headerField được query
        //    foreach (var item in requestHeaderExtras)
        //    {
        //        var split = item.Split("-");
        //        var headerFieldName = split.FirstOrDefault();
        //        if (indexHeaderFields.ContainsKey(headerFieldName))
        //        {
        //            indexRequestHeaderField.Add(indexHeaderFields[headerFieldName].Id, item.Substring(headerFieldName.Length + 1, item.Length - headerFieldName.Length - 1));
        //        }
        //    }

        //    //query các headerExtra
        //    var invoiceHeaderExtras = await _repoInvoice01HeaderExtra.QueryByValuesAsNoTrackingAsync(indexRequestHeaderField);
        //    var groupInvoiceHeaderExtras = invoiceHeaderExtras
        //        .GroupBy(x => x.InvoiceHeaderId)
        //        .Where(x => x.Select(x => x.InvoiceHeaderFieldId).Distinct().Count() >= indexRequestHeaderField.Count())
        //        .ToDictionary(x => x.Key, x => x.ToList());

        //    return groupInvoiceHeaderExtras.Keys.ToList();
        //}

        ///// <summary>
        ///// Lấy các HeaderExtra được hiển thị theo Setting
        ///// </summary>
        ///// <param name="tenantId"></param>
        ///// <returns></returns>
        //private async Task<Dictionary<long, string>> GetHeaderExtraAsync(Guid tenantId)
        //{
        //    var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.ShowHeaderExtra01.ToString());

        //    //Lấy các HeaderField theo Setting
        //    var result = new Dictionary<long, string>();
        //    if (setting != null && !string.IsNullOrEmpty(setting.Value))
        //    {
        //        var headerFields = await _repoInvoice01HeaderField.QueryByFieldNamesAsNoTrackingAsync(tenantId, setting.Value.Split(';'));
        //        result = headerFields.ToDictionary(x => x.Id, x => x.FieldName);
        //    }

        //    return result;
        //}
    }
}
