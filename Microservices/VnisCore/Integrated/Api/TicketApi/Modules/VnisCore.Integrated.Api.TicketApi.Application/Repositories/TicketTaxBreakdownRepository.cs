using Core.Domain.Repositories;
using Core.Shared.Factory;
using Dapper;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;

namespace VnisCore.Integrated.Api.TicketApi.Application.Repositories
{
    public interface ITicketTaxBreakdownRepository
    {
        Task<List<TicketTaxBreakdownEntity>> QueryByInvoiceHeaderIdAsync(long idInvoiceHeader);

        Task<List<TicketTaxBreakdownEntity>> QueryByInvoiceHeaderIdRawAsync(long idInvoiceHeader);
        //void Delete(TicketTaxBreakdownEntity item);
    }

    public class TicketTaxBreakdownRepository : ITicketTaxBreakdownRepository
    {
        private readonly IRepository<TicketTaxBreakdownEntity, long> _repoInvoiceTaxBreakdown;
        private readonly IAppFactory _appFactory;

        public TicketTaxBreakdownRepository(
            IRepository<TicketTaxBreakdownEntity, long> repoInvoiceTaxBreakdown,
            IAppFactory appFactory
            )
        {
            _repoInvoiceTaxBreakdown = repoInvoiceTaxBreakdown;
            _appFactory = appFactory;
        }

        public async Task<List<TicketTaxBreakdownEntity>> QueryByInvoiceHeaderIdAsync(long idInvoiceHeader)
        {
            return await _repoInvoiceTaxBreakdown.Where(x => x.InvoiceHeaderId == idInvoiceHeader).AsQueryable().ToListAsync();
        }

        public async Task<List<TicketTaxBreakdownEntity>> QueryByInvoiceHeaderIdRawAsync(long idInvoiceHeader)
        {
            var query = $@"SELECT * FROM ""TicketTaxBreakdown"" WHERE ""InvoiceHeaderId"" = {idInvoiceHeader}";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketTaxBreakdownEntity>(query);

            return result.ToList();
        }
    }
}
