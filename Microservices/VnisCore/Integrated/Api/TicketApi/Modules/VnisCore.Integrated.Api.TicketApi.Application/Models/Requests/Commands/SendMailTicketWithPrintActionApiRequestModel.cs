using MediatR;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands
{
    public class SendMailTicketWithPrintActionApiRequestModel : IRequest
    {
        /// <summary>
        /// Số hóa đơn
        /// </summary>
        [JsonIgnore]
        public string PrintAction { get; set; }

        [Required(ErrorMessage = "ErpId không được để trống")]
        public string ErpId { get; set; }

        /// <summary>
        ///  Mail
        /// </summary>
        [Required(ErrorMessage = "Email không được để trống")]
        public string Mail { get; set; }
    }
}
