using Core.Shared.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Commands
{
    public class CreateAndUnOfficialTicketResponseModel
    {
        // <summary>
        /// Id bản ghi hóa đơn
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// IdErp bản ghi hóa đơn
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// Mã đơn hàng, giao dịch ....
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Ký hiệu mẫu hóa đơn
        /// </summary>
        /// <value></value>
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu
        /// </summary>
        /// <value></value>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn (Goc, DieuChinhTangGiam)
        /// </summary>
        public InvoiceStatus InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái ký (ChoDuyet, ChoKy, DaKy, DaBaoCao)
        /// </summary>
        public SignStatus SignStatus { get; set; }

        public string FileUnOfficial { get; set; }
    }
}
