using Core.Shared.Constants;

namespace VnisCore.Integrated.Api.TicketApi.Application.Models.Responses.Commands
{
    public class UpdateAdjustTicketApiResponseModel
    {
        /// <summary>
        /// Id bản ghi hóa đơn
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// IdErp bản ghi hóa đơn
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// Mã đơn hàng, giao dịch ....
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// Mẫu số
        /// </summary>
        /// <value></value>
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu
        /// </summary>
        /// <value></value>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn (Goc, DieuChinhTangGiam)
        /// </summary>
        public InvoiceStatus InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái ký (ChoDuyet, ChoKy, DaKy, DaBaoCao)
        /// </summary>
        public SignStatus SignStatus { get; set; }
    }
}
