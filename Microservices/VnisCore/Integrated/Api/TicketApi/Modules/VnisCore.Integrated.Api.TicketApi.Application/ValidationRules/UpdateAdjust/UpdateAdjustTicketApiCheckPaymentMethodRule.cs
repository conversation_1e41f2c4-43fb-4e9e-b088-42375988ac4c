using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjust
{
    public class UpdateAdjustTicketApiCheckPaymentMethodRule : IValidationRuleAsync<UpdateAdjustTicketApiRequestModel, ValidationResult>
    {
        private readonly ISettingService _settingService;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateAdjustTicketApiCheckPaymentMethodRule(ISettingService settingService,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext)
        {
            _localizier = localizier;
            _settingService = settingService;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustTicketApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());

            if (setting == null || string.IsNullOrEmpty(setting.Value))
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.PaymentMethodNotFound", new string[] { input.PaymentMethod }]);

            //cắt chuỗi value setting để lấy value của paymentmethod
            var values = setting.Value.Split(";");
            if (!values.Contains(input.PaymentMethod))
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.PaymentMethodNotFound", new string[] { input.PaymentMethod }]);

            return new ValidationResult(true);
        }
    }
}
