using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjustmentHeader
{
    /// <summary>
    /// validate trạng thái ký và trạng thái hóa đơn xem có được sửa hóa đơn thay thế không
    /// </summary>
    class UpdateAdjustmentHeaderTicketApiCheckStatusRule : IValidationRuleAsync<UpdateAdjustmentHeaderTicketApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;

        public UpdateAdjustmentHeaderTicketApiCheckStatusRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _invoiceService = invoiceService;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentHeaderTicketApiRequestModel input)
        {
            var invoice = _validationContext.GetItem<TicketHeaderEntity>("Invoice");

            if (invoice.InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.UpdateAdjHeader.CannotUpdateInvoiceNotAdjHeader"]);

            //kiểm tra trạng thái ký
            if (invoice.SignStatus > SignStatus.ChoKy.GetHashCode())
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.UpdateAdjHeader.CannotUpdateInvoiceSignStatus"]);

            //var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            if (await _invoiceService.HasApproveAsync(tenantId) && invoice.ApproveStatus == (short)ApproveStatus.DaDuyet)
            {
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.UpdateAdjHeader.CannotUpdateInvoiceApproveStatus"]);
            }

            //ToDo: đợi khi nào có thêm bảng TicketApproveEntity
            //if ((invoice.Source != InvoiceSource.ShareDbAdapter.GetHashCode() && await _invoiceService.HasApproveAsync(tenantCode))
            //    || (invoice.Source == InvoiceSource.ShareDbAdapter.GetHashCode() && await _invoiceService.HasAdapterHasApprove(tenantCode)))
            //{
            //    var repoInvoiceApprove = _appFactory.Repository<TicketApproveEntity, long>();
            //    var approve = await repoInvoiceApprove.GetLastByInvoiceCodeAsync(invoice.Code);

            //    if (invoice.InvoiceStatus != SignStatus.KyLoi.GetHashCode()
            //        && (invoice.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode()
            //            || (approve != null && approve.Action != ApproveAction.HuyDuyet.GetHashCode())))
            //        return new ValidationResult(false, "Không được sửa hóa đơn");
            //}
            //else
            //{
            //    if (invoice.ApproveStatus != ApproveStatus.KhongQuyTrinhDuyet.GetHashCode())
            //        return new ValidationResult(false, "Không được sửa hóa đơn");
            //}

            return new ValidationResult(true);
        }
    }
}
