using Core.Shared.Validations;
using System.Linq;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Create
{
    /// <summary>
    /// kiểm tra Index của detail
    /// </summary>
    public class CreateInvoiceCheckIndexDetailRule : IValidationRule<CreateTicketRequestModel, ValidationResult>
    {
        public CreateInvoiceCheckIndexDetailRule()
        {
        }

        public ValidationResult Handle(CreateTicketRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            foreach (var detail in input.InvoiceDetails)
            {
                if (detail.Index > 9999)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa {detail.ProductName} phải nhỏ hơn hoặc bằng 9999");
                }
                if (detail.Index <= 0)
                {
                    return new ValidationResult(false, $"Số thứ tự hàng hóa {detail.ProductName} phải lớn hơn 0");
                }
            }

            return new ValidationResult(true);
        }
    }
}
