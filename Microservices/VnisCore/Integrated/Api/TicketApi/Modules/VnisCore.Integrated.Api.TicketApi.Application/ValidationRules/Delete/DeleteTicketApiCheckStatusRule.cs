using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Invoice.Services;
using Core.Shared.Services;
using Core.Shared.Validations;
using Core.Tvan.Constants;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;

namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Delete
{
    public class DeleteTicketApiCheckStatusRule : IValidationRuleAsync<DeleteTicketApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceHeaderRepository<TicketHeaderEntity> _repoInvoiceHeader;
        private readonly IInvoiceReferenceRepository<TicketReferenceEntity> _repoInvoiceRefernce;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ISettingService _settingService;
        private readonly ITaxReport01Repository _taxReport01Repository;

        public DeleteTicketApiCheckStatusRule(
            ISettingService settingService,
            IValidationContext validationContext,
            IInvoiceHeaderRepository<TicketHeaderEntity> repoInvoiceHeader,
            IInvoiceReferenceRepository<TicketReferenceEntity> repoInvoiceRefernce,
            ITaxReport01Repository taxReport01Repository,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoInvoiceHeader = repoInvoiceHeader;
            _repoInvoiceRefernce = repoInvoiceRefernce;
            _settingService = settingService;
            _taxReport01Repository = taxReport01Repository;
        }

        public async Task<ValidationResult> HandleAsync(DeleteTicketApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");
            var invoice = await _repoInvoiceHeader.GetByErpIdAsync(tenantId, input.ErpId);
            if (invoice == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.InvoiceNotFound"]);
            
            if (invoice.InvoiceStatus != InvoiceStatus.Goc.GetHashCode())
                return new ValidationResult(false, _localizier["Vnis.BE.Api.Intergration.Delete.InvoiceIsNotOnlyRoot"]);

            // if (invoice.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode())
            //     return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.Delete.InvoiceIsDeleted"]);

            if (invoice.SignStatus != SignStatus.DaKy.GetHashCode())
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.Delete.CannotDeleteUnSign"]);

            // hóa đơn đã ký mà có trạng thái chờ duyệt => chờ duyệt xóa bỏ ko thể xóa bỏ
            if (invoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode())
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.Delete.InvoiceStatusIncorrect"]);

            // if (invoice.InvoiceStatus == InvoiceStatus.BiThayThe.GetHashCode()
            //    || invoice.InvoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
            //    || invoice.InvoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode())
            //     return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.Delete.CannotDeleteReplaceOrAdj"]);

            //kiểm tra xem hóa đơn đã bị thay thế/điều chỉnh bởi hóa đơn khác chưa vì có thể hóa đơn thay thế/điều chỉnh chưa sinh số nên hóa đơn hiện tại chưa bị chuyển trạng thái
            //tìm kiếm trong bảng InvoiceReference đã có bản ghi nào có InvoiceReferenceCode = Code hóa đơn không
            //vì khi tạo hóa đơn thay thế/điều chỉnh đã insert sẵn 1 bản ghi vào bảng InvoiceRefererence có InvoiceReferenceCode = Code hóa đơn gốc (kể cả dcdd k sinh số)
            // if (invoice.InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
            //     && invoice.InvoiceStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
            //     && invoice.InvoiceStatus != InvoiceStatus.ThayThe.GetHashCode())
            // {
            //     var invoiceReference = await _repoInvoiceRefernce.GetByInvoiceReferenceIdAsync(invoice.Id);
            //     if (invoiceReference != null)
            //         return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.Delete.CannotDeleteReplaceOrAdjBy", new string[] { EnumExtension.ToEnum<InvoiceStatus>(invoiceReference.InvoiceStatus).GetName() }]);
            // }

            #region Kiểm tra trạng thái hóa đơn theo ND70, nếu dữ liệu đã gửi lên TVAN thì không được xóa

            // Nếu hóa đơn có trạng thái TvanStatus khác UnSent thì không được xóa
            if (invoice.StatusTvan != TvanStatus.UnSent.GetHashCode())
            {
                return new ValidationResult(false, $"Không được phép xóa bỏ hóa đơn đã gửi CQT");
            }

            // Nếu hóa đơn có trạng thái TvanStatus là UnSent thì kiểm tra theo logic sau
            if (invoice.StatusTvan == TvanStatus.UnSent.GetHashCode())
            {
                List<TaxReport01DetailMappingEntity> taxReport01DetailMappingEntities = new List<TaxReport01DetailMappingEntity>();

                var detailMappings = await _taxReport01Repository.GetAllDetailMappingByDate(tenantId, invoice.InvoiceDate, VnisType._05TVDT);
                taxReport01DetailMappingEntities.AddRange(detailMappings);

                // Kiểm tra xem có hóa đơn nào đã được báo cáo trong tháng/quý đó không

                var lstSignStatusCannotUpdate = new List<int>
                {
                    SignStatus.KyLoi.GetHashCode(),
                    SignStatus.DaKy.GetHashCode(),
                    SignStatus.DangKy.GetHashCode()
                };

                List<TaxReport01DetailMappingEntity> lstEntityDetailMappings = new List<TaxReport01DetailMappingEntity>();
                // Lấy ra danh sách detail mapping thuộc danh sách hóa đơn đang xóa
                if (taxReport01DetailMappingEntities.Any())
                {
                    foreach (var item in taxReport01DetailMappingEntities)
                    {
                        if (!string.IsNullOrEmpty(item.InvoiceIds))
                        {
                            // Lấy danh sách id hóa đơn thuộc bảng tổng hợp
                            var invoiceIds = item.InvoiceIds?.Split(',').Select(long.Parse).ToList() ?? new List<long>();

                            // Nếu id hóa đơn thuộc bảng tổng hợp đã tồn tại trong danh sách hóa đơn đang xóa
                            if (invoiceIds.Any(x => x == invoice.Id))
                            {
                                if (lstSignStatusCannotUpdate.Contains(item.SignStatus))
                                {
                                    return new ValidationResult(false, $"Không được phép xóa bỏ hóa đơn đã tồn tại trong bảng tổng hợp đã ký");
                                }
                                lstEntityDetailMappings.Add(item);
                            }
                        }
                    }
                }

                _validationContext.GetOrAddItem<List<TaxReport01DetailMappingEntity>>("TaxReport01DetailMappings", () =>
                {
                    return lstEntityDetailMappings;
                });
            }

            #endregion

            await _validationContext.GetOrAddItemAsync("Invoice", async () =>
            {
                return invoice;
            });

            return new ValidationResult(true);
        }
    }
}
