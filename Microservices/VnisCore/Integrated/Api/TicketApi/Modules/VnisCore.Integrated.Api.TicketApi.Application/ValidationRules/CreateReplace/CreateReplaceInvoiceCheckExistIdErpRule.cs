using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Validations;

using Microsoft.Extensions.Localization;

using Serilog;

using VnisCore.Core.MongoDB.IRepository.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;

namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateReplace
{
    public class CreateReplaceInvoiceCheckExistIdErpRule : IValidationRuleAsync<CreateReplaceTicketApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IRepository<TicketHeaderEntity, long> _repoTicketHeader;
        private readonly IVnisCoreMongoTicketErpIdRepository _mongoTicketErpIdRepository;
        private readonly ITicketErpIdRepository _ticketErpIdRepository;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly IRedisCacheService _redisCacheService;

        public CreateReplaceInvoiceCheckExistIdErpRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IRepository<TicketHeaderEntity, long> repoTicketHeader,
            IVnisCoreMongoTicketErpIdRepository mongoTicketErpIdRepository,
            ITicketErpIdRepository ticketErpIdRepository, IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            IAppFactory appFactory,
            IRedisCacheService redisCacheService)
        {
            _validationContext = validationContext;
            _repoTicketHeader = repoTicketHeader;
            _mongoTicketErpIdRepository = mongoTicketErpIdRepository;
            _ticketErpIdRepository = ticketErpIdRepository;
            _localizier = localizier;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _redisCacheService = redisCacheService;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceTicketApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });

            // Thực hiện insert ErpId vào Redis
            // Nếu đã tồn tại trên redis thì báo lỗi
            // Nếu không kiểm tra trên tiếp luồng cũ
            var key = CacheKeyBuilder.BuildCacheKeyWithPath(CacheKeyPath.t, _appFactory.CurrentTenant.TaxCode.ToString(), CacheKeyPath.Ticket, CacheKeyPath.DuplicateErpId, input.ErpId);
            // Thiết lập TTL key = 5 phút
            var cacheExpiration = new TimeSpan(0, 5, 0);
            var isExistErpId = _redisCacheService.SetWithWhen<bool>(key, true, cacheExpiration, StackExchange.Redis.When.NotExists);
            if (!isExistErpId)
            {
                throw new UserFriendlyException($"ErpId {input.ErpId} đã tồn tại");
            }

            var existErpInMongos = await _mongoTicketErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
            var existErpInOracle = await _ticketErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });

            // check mongo
            if (existErpInMongos.Any(x => x.ErpId == input.ErpId && x.Status != ErpIdStatus.CreateInvoiceError.GetHashCode()))
            {
                //var existErpIdOracles = await _ticketErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
                //if (existErpIdOracles.Any(x => x.ErpId == input.ErpId))
                //{
                var exist = await _repoTicketHeader.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == input.ErpId && !string.IsNullOrEmpty(x.InvoiceNo));
                if (exist != null)
                {
                    //return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.DuplicateErpId"], result);

                    throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.DuplicateErpId"])
                    {
                        Data =
                            {
                                { "id", exist.Id },
                                { "serialNo", exist.SerialNo },
                                { "templateNo", exist.TemplateNo },
                                { "invoiceNo", exist.InvoiceNo },
                                { "transactionId", exist.TransactionId },
                                { "erpId", exist.ErpId },
                                { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(exist.InvoiceStatus) },
                                { "signStatus", EnumExtension.TryToEnum<SignStatus>(exist.SignStatus) }
                            }
                    };
                    //}
                }
            }

            // check bảng TicketErpId (oracle)
            if (existErpInOracle.Any(x => x.ErpId == input.ErpId && x.Status != ErpIdStatus.CreateInvoiceError.GetHashCode()))
            {
                //var existErpIdOracles = await _ticketErpIdRepository.QueryErpIdsAsync(tenantId, new List<string> { input.ErpId });
                //if (existErpIdOracles.Any(x => x.ErpId == input.ErpId))
                //{
                var exist = await _repoTicketHeader.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.ErpId == input.ErpId && !string.IsNullOrEmpty(x.InvoiceNo));
                if (exist != null)
                {
                    //return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.DuplicateErpId"], result);

                    throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.DuplicateErpId"])
                    {
                        Data =
                            {
                                { "id", exist.Id },
                                { "serialNo", exist.SerialNo },
                                { "templateNo", exist.TemplateNo },
                                { "invoiceNo", exist.InvoiceNo },
                                { "transactionId", exist.TransactionId },
                                { "erpId", exist.ErpId },
                                { "invoiceStatus", EnumExtension.TryToEnum<InvoiceStatus>(exist.InvoiceStatus) },
                                { "signStatus", EnumExtension.TryToEnum<SignStatus>(exist.SignStatus) }
                            }
                    };
                    //}
                }
            }

            var idErpId = await _invoiceService.GetSEQsNextVal(1, SequenceName.TicketErpId);

            try
            {
                var erpIdExist = await _mongoTicketErpIdRepository.InsertAsync(new Core.MongoDB.Entities.Invoices.Ticket.MongoTicketErpIdEntity
                {
                    Id = idErpId.FirstOrDefault(),
                    ErpId = input.ErpId,
                    Partition = long.Parse(input.InvoiceDate.ToString(FormatDate._yyyyMMddHHmm)),
                    Source = (short)InvoiceSource.Api,
                    Status = (short)ErpIdStatus.UnProcess.GetHashCode(),
                    TenantGroup = _appFactory.CurrentTenant.Group,
                    TenantId = tenantId,
                    CreationTime = DateTime.Now
                });

                _validationContext.GetOrAddItem("InvoiceErpId", () =>
                {
                    return erpIdExist;
                });
            }
            catch (Exception ex) //TODO: catch đúng exception
            {
                Log.Error($"Insert mongodb ErpId error: {ex.Message}");

                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.Api.Intergration.InsertErpIdError"]);
            }

            return new ValidationResult(true);
        }
    }
}
