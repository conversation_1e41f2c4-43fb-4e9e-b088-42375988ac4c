using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;


namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateAdjust
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class CreateAdjustInvoiceCheckHeaderExtraRule : IValidationRuleAsync<CreateAdjustTicketApiRequestModel, ValidationResult>
    {
        private readonly ITicketHeaderFieldRepository _repoTicketHeaderField;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly ITicketCacheBusiness _ticketCacheBusiness;

        public CreateAdjustInvoiceCheckHeaderExtraRule(ITicketHeaderFieldRepository repoTicketHeaderField,
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory,
            ITicketCacheBusiness ticketCacheBusiness)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoTicketHeaderField = repoTicketHeaderField;
            _appFactory = appFactory;
            _ticketCacheBusiness = ticketCacheBusiness;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustTicketApiRequestModel input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            // Lấy thông tin từ cache
            var fieldCaches = await _ticketCacheBusiness.GetHeaderFieldsFromCacheAsync(tenantId);
            var headerFieldNames = fieldCaches.Select(x => x.FieldName);

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.InvoiceHeaderFieldNotFound", new string[] { string.Join(",", expects) }]);

            input.InvoiceHeaderExtras.RemoveAll(x => string.IsNullOrEmpty(x.FieldValue));

            _validationContext.AddItem("HeaderField", fieldCaches);

            return new ValidationResult(true);
        }
    }
}
