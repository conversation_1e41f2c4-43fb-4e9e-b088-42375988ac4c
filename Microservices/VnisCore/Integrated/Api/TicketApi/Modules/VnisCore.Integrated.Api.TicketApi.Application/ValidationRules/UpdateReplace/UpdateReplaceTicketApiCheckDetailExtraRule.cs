using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Integrated.Api.TicketApi.Application.Interfaces;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;


namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateReplace
{
    /// <summary>
    /// kiểm tra field name detail Extra
    /// </summary>
    public class UpdateReplaceTicketApiCheckDetailExtraRule : IValidationRuleAsync<UpdateReplaceTicketApiRequestModel, ValidationResult>
    {
        private readonly ITicketDetailFieldRepository _repoTicketDetailField;
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ITicketCacheBusiness _ticketCacheBusiness;

        public UpdateReplaceTicketApiCheckDetailExtraRule(ITicketDetailFieldRepository repoTicketDetailField,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext,
            ITicketCacheBusiness ticketCacheBusiness)
        {
            _localizier = localizier;
            _repoTicketDetailField = repoTicketDetailField;
            _validationContext = validationContext;
            _ticketCacheBusiness = ticketCacheBusiness;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceTicketApiRequestModel input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            var commandDetailExtras = input.InvoiceDetails
                                            .Where(x => x.InvoiceDetailExtras != null)
                                            .SelectMany(x => x.InvoiceDetailExtras)
                                            .ToList();
            if (!commandDetailExtras.Any())
                return new ValidationResult(true);


            var commandDetailFieldNames = commandDetailExtras.Select(x => x.FieldName).Distinct();

            if (!commandDetailFieldNames.Any())
                return new ValidationResult(true);

            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các detail field
            // Lấy thông tin từ cache
            var fieldCaches = await _ticketCacheBusiness.GetDetailFieldsFromCacheAsync(tenantId);
            var detailFieldNames = fieldCaches.Select(x => x.FieldName);

            var expects = commandDetailFieldNames.Except(detailFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.InvoiceDetaiFieldNotFound", new string[] { string.Join(",", expects) }]);

            input.InvoiceDetails.Where(x => x.InvoiceDetailExtras != null).SelectMany(x => x.InvoiceDetailExtras).ToList().RemoveAll(t => string.IsNullOrEmpty(t.FieldValue));

            _validationContext.AddItem("DetailField", fieldCaches);

            return new ValidationResult(true);
        }
    }
}
