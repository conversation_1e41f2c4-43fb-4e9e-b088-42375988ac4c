using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;

namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.Update
{
    /// <summary>
    /// kiểm tra exchangerate có đúng giá trị không
    /// nếu mã tiền tệ = nguyên tệ => exchangerate = 1
    /// </summary>
    public class UpdateTicketApiCheckExchangeRateRule : IValidationRule<UpdateTicketApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public UpdateTicketApiCheckExchangeRateRule(
            IStringLocalizer<CoreLocalizationResource> localizier,
            IValidationContext validationContext)
        {
            _localizier = localizier;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(UpdateTicketApiRequestModel input)
        {
            var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
            var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");

            if (fromCurrency.CurrencyCode == toCurrency.CurrencyCode && input.ExchangeRate != 1)
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.ExchangeRateIsOne"]);

            return new ValidationResult(true);
        }
    }
}
