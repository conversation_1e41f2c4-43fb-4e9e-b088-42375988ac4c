using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;


namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.UpdateAdjust
{
    public class UpdateAdjustTicketApiCheckExistTaxRule : IValidationRuleAsync<UpdateAdjustTicketApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly ITaxService _taxService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly ICachingCatalogBusiness _cachingCatalogBusiness;

        public UpdateAdjustTicketApiCheckExistTaxRule(
            IValidationContext validationContext,
            IStringLocalizer<CoreLocalizationResource> localizier,
            ITaxService taxService,
            IAppFactory appFactory,
            ICachingCatalogBusiness cachingCatalogBusiness)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _taxService = taxService;
            _appFactory = appFactory;
            _cachingCatalogBusiness = cachingCatalogBusiness;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustTicketApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var taxes = await _validationContext.GetOrAddItemAsync<Dictionary<decimal, Tuple<string, string>>>("Taxes", async () =>
            {
                // Lấy thông tin từ cache
                var taxCache = await _cachingCatalogBusiness.GetOrSetCachedTaxsAsync(tenantId);
                var taxEntities = _appFactory.ObjectMapper.Map<List<TaxCachingDto>, List<TaxEntity>>(taxCache);
                var taxes = new Dictionary<decimal, Tuple<string, string>>();
                foreach (var item in taxEntities)
                {
                    taxes.Add(item.Value, new Tuple<string, string>(item.Name, item.Display));
                }

                return taxes;
            });

            if (taxes.Count == 0)
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.TaxNotFound"]);

            // Check thuế suất ở detail
            var vatPercentInput = input.InvoiceDetails.Select(x => x.VatPercent).ToList();
            var exceptTaxsInDetails = vatPercentInput.Except(taxes.Select(x => x.Key).ToList()).ToList();
            if (exceptTaxsInDetails.Any())
            {
                return new ValidationResult(false, $@"Thuế suất {string.Join(", ", exceptTaxsInDetails)} của chi tiết hóa đơn không tồn tại trong Danh mục thuế suất");
            }

            // Check thuế suất ở TaxBreakDown
            var vatPercentTaxBreakDownInput = input.InvoiceTaxBreakdowns.Select(x => x.VatPercent).ToList();
            var exceptTaxsInTaxBreakDown = vatPercentTaxBreakDownInput.Except(taxes.Select(x => x.Key).ToList()).ToList();
            if (exceptTaxsInTaxBreakDown.Any())
            {
                return new ValidationResult(false, @$"Thuế suất {string.Join(", ", exceptTaxsInDetails)} của chi tiết thuế suất không tồn tại trong Danh mục thuế suất");
            }

            return new ValidationResult(true);
        }
    }
}
