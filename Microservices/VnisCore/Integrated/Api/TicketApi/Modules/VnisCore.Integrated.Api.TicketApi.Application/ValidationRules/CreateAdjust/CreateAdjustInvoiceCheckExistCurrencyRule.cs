using Core.Localization.Resources.AbpLocalization;
using Core.ObjectMapping;
using Core.Shared.Cached;
using Core.Shared.Cached.Dtos;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Integrated.Api.TicketApi.Application.Models.Requests.Commands;
using VnisCore.Integrated.Api.TicketApi.Application.Repositories;

namespace VnisCore.Integrated.Api.TicketApi.Application.ValidationRules.CreateAdjust
{
    /// <summary>
    /// Validate tiền tệ
    /// </summary>
    public class CreateAdjustInvoiceCheckExistCurrencyRule : IValidationRuleAsync<CreateAdjustTicketApiRequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly ICurrencyRepository _repoCurrency;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly ICachingCatalogBusiness _cachingCatalogBusiness;

        public CreateAdjustInvoiceCheckExistCurrencyRule(
            IValidationContext validationContext,
            ICurrencyRepository repoCurrency,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory,
            ICachingCatalogBusiness cachingCatalogBusiness)
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoCurrency = repoCurrency;
            _appFactory = appFactory;
            _cachingCatalogBusiness = cachingCatalogBusiness;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustTicketApiRequestModel input)
        {
            var tenantId = _validationContext.GetItem<Guid>("TenantId");

            // Lấy thông tin từ cache
            var currencyCache = await _cachingCatalogBusiness.GetOrSetCachedCurrencyAsync(tenantId);
            var currencies = _appFactory.ObjectMapper.Map<List<CurrencyCachingDto>, List<CurrencyEntity>>(currencyCache);

            //Nguyên tệ
            var fromCurrency = currencies.Where(x => x.IsDefault == true).FirstOrDefault();
            if (fromCurrency == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.FromCurrencyNotFound"]);

            _validationContext.AddItem<CurrencyEntity>("FromCurrency", fromCurrency);

            //Tiền tệ
            var toCurrency = currencies.Where(x => x.CurrencyCode == input.Currency).FirstOrDefault();
            if (toCurrency == null)
                return new ValidationResult(false, _localizier["Vnis.BE.Ticket.Api.Intergration.ToCurrencyNotFound"]);

            _validationContext.AddItem<CurrencyEntity>("ToCurrency", toCurrency);

            return new ValidationResult(true);
        }
    }
}
