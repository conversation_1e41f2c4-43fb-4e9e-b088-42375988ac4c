using Microsoft.AspNetCore.Http;
using System;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.System.Application.User.Model.Request
{
    public class ImportUserRequestModel //: IRequest
    {
        [Required(ErrorMessage = "File excel không được để trống")]
        public IFormFile File { get; set; }
        public Guid currentTenantId { get; set; }

        /// <summary>
        /// định dạng là json của 1 dictionary(string,string)
        /// </summary>
        //public string Parameters { get; set; }

        //public string Service { get; set; }

        //public bool? AutoRounding { get; set; }
    }
}
