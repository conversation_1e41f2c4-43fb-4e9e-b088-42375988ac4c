namespace VnisCore.System.Application.User.Dto
{
    public class ImportUserDto
    {
        public string EmployeeCode { get; set; }
        public bool IsEmployeeCode { get; set; }
        public string UserLogin { get; set; }
        public bool IsUserLogin { get; set; }
        public string ActionText { get; set; }
        public bool IsActionText { get; set; }
        public short Action { get; set; }
        public bool IsAction { get; set; }
        public string TenantCode { get; set; }
        public bool IsTenantCode { get; set; }
        public string DepartmentCode { get; set; }
        public bool IsDepartmentCode { get; set; }
        public string FullName { get; set; }
        public bool IsFullName { get; set; }
        public string Role { get; set; }
        public bool IsRole { get; set; }
        public string CashierCode { get; set; }
        public bool IsCashierCode { get; set; }
        public string Email { get; set; }
        public bool IsEmail { get; set; }
        public bool IsExists { get; set; }
        public bool? IsError { get; set; }
        public string ErrorMessage { get; set; }
    }
    
}