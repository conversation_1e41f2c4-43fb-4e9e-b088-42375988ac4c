using MediatR;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.System.Application.Setting.G4Setting.Dtos
{
    public class UpdateSettingG4Dto : IRequest<G4SettingResponseDto>
    {
        public long Id { get; set; }
        // header setting
        [Required(ErrorMessage = "Mã cấu hình không được để trống")]
        public string Code { get; set; }

        [Required(ErrorMessage = "Độ ưu tiên không được để trống")]
        public short Order { get; set; }

        // detail setting
        public List<SettingG4DetailUpdate> SettingG4Details { get; set; }
    }

    public class SettingG4DetailUpdate
    {
        public long Id { get; set; }

        public long Index { get; set; }
        /// <summary>
        /// điều kiện gộp
        /// </summary>
        [Required(ErrorMessage = "Điều kiện gộp không được để trống")]
        public string MergeCondition { get; set; }

        /// <summary>
        /// chu kì gộp: ngày/tuần/tháng
        /// </summary>
        [Required(ErrorMessage = "Chu kỳ gộp không được để trống")]
        public short MergeCycle { get; set; }

        /// <summary>
        /// ngày gộp
        /// </summary>
        [Required(ErrorMessage = "Ngày gộp không được để trống")]
        public short MergeDate { get; set; }

        /// <summary>
        /// điều kiện bổ sung
        /// </summary>
        public string AdditonalCondition { get; set; }
    }
}
