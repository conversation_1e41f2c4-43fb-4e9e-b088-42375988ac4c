using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using MediatR;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.SettingG4;
using VnisCore.System.Application.Setting.G4Setting.Dtos;

namespace VnisCore.System.Application.Setting.G4Setting.Handlers
{
    public class CreateSettingG4RequestHandler : IRequestHandler<CreateSettingG4Dto, G4SettingResponseDto>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public CreateSettingG4RequestHandler(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _appFactory = appFactory;
            _localizer = localizer;
        }

        public async Task<G4SettingResponseDto> Handle(CreateSettingG4Dto request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            var settingG4HeaderRepos = _appFactory.Repository<SettingG4HeaderEntity, long>();
            var settingG4DetailRepos = _appFactory.Repository<SettingG4DetailEntity, long>();

            var settingG4HeaderByCode = await settingG4HeaderRepos.AnyAsync(x => x.Code == request.Code && x.TenantId == tenantId);
            if (settingG4HeaderByCode)
                //throw new UserFriendlyException("Cấu hình tích hợp G4 đã tồn tại");
                throw new UserFriendlyException(_localizer["Vnis.BE.System.Setting.G4ConfigExist"]);

            var settingG4HeaderEntity = new SettingG4HeaderEntity
            {
                Code = request.Code,
                Order = request.Order,
                TenantId = tenantId,
                IsActive = true
            };

            await settingG4HeaderRepos.InsertAsync(settingG4HeaderEntity, cancellationToken: cancellationToken);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync(cancellationToken: cancellationToken);

            if (request.SettingDetail.Count > 0)
            {
                var details = new List<SettingG4DetailEntity>();

                request.SettingDetail.ForEach((item) =>
                {
                    var detailEntity = new SettingG4DetailEntity
                    {
                        TenantId = tenantId,
                        MergeCondition = item.MergeCondition,
                        MergeCycle = item.MergeCycle,
                        MergeDate = item.MergeDate,
                        SettingHeaderId = settingG4HeaderEntity.Id,
                        AdditonalCondition = item.AdditonalCondition
                    };

                    details.Add(detailEntity);
                });

                await settingG4DetailRepos.InsertManyAsync(details);
            }

            return new G4SettingResponseDto
            {
                Id = settingG4HeaderEntity.Id,
                Code = settingG4HeaderEntity.Code,
                Order = settingG4HeaderEntity.Order
            };
        }
    }
}
