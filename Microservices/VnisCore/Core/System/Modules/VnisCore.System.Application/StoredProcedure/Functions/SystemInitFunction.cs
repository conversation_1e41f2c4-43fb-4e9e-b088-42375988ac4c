using Microsoft.EntityFrameworkCore;
using VnisCore.AuthDatabase.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.System.Application.StoredProcedure.Functions
{
    public static class SystemInitFunction
    {
        public static void CreateFunctionGuidToRaw(VnisCoreAuthDatabaseOracleDbContext dbContext)
		{
			var spQuery = $@"create or replace NONEDITIONABLE function {SystemFunctionName.GuidToRaw}( guid in varchar2 ) return raw
                            is
                                hex varchar2(32);
                            begin

                                hex := substr(guid, 7, 2)
                                ||     substr(guid, 5, 2)
                                ||     substr(guid, 3, 2)
                                ||     substr(guid, 1, 2)
                                --
                                ||     substr(guid, 12, 2)
                                ||     substr(guid, 10, 2)
                                --
                                ||     substr(guid, 17, 2)
                                ||     substr(guid, 15, 2)
                                -- 
                                ||     substr(guid, 20, 2)
                                ||     substr(guid, 22, 2)
                                -- 
                                ||     substr(guid, 25, 12);

                                return hextoraw(hex);

                            end;";
			dbContext.Database.ExecuteSqlRaw(spQuery);
		}

        public static void CreateFunctionRawToGuid(VnisCoreAuthDatabaseOracleDbContext dbContext)
        {
            var spQuery = $@"create or replace NONEDITIONABLE function {SystemFunctionName.RawToGuid}( raw_guid in raw ) return varchar2
                            is
                              hex varchar2(32);
                            begin

                              hex := rawtohex(raw_guid);

                              return substr(hex, 7, 2) 
                                  || substr(hex, 5, 2) 
                                  || substr(hex, 3, 2) 
                                  || substr(hex, 1, 2) 
                                  || '-'
                                  || substr(hex, 11, 2) 
                                  || substr(hex, 9, 2) 
                                  || '-'
                                  || substr(hex, 15, 2) 
                                  || substr(hex, 13, 2) 
                                  || '-'
                                  || substr(hex, 17, 4) 
                                  || '-'
                                  || substr(hex, 21, 12);

                            end;";
            dbContext.Database.ExecuteSqlRaw(spQuery);
        }

    }
} 