using Core.Domain.Repositories;
using Core.TenantManagement;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VnisCore.System.Infrastructure.IRepository
{
    public interface IEFTenantRepository : IBasicRepository<Tenant, Guid>
    {
        Task<TenantDto> GetById(
            Guid id
        );

        Task<List<TenantDto>> GetList(GetTenantsInput input, Guid tenantId);

        Task<int> Count(GetTenantsInput input, Guid tenantId);
    }
}
