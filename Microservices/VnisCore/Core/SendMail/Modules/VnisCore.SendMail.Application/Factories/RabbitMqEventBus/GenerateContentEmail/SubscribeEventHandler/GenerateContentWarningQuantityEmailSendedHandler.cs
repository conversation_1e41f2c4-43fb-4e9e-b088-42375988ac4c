//using Core;
//using Core.Caching;
//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Localization.Resources.AbpLocalization;
//using Core.MultiTenancy;
//using Core.SettingManagement;
//using Core.Shared.Constants;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using Core.Shared.MessageEventsData.GenerateContentEmail;
//using Core.Shared.Services;

//using Dapper;

//using Microsoft.Extensions.Caching.Distributed;
//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.DependencyInjection;
//using Microsoft.Extensions.Localization;

//using Serilog;

//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Net;
//using System.Threading.Tasks;

//using VnisCore.Core.MongoDB.Entities;
//using VnisCore.Core.MongoDB.IRepository;
//using VnisCore.Core.Oracle.Domain.Entities.Catalog;
//using VnisCore.SendMail.Application.Factories.Models;
//using VnisCore.SendMail.Application.Factories.Services;

//namespace VnisCore.SendMail.Application.Factories.RabbitMqEventBus.GenerateContentEmail.SubscribeEventHandler
//{
//    public class GenerateContentWarningQuantityEmailSendedHandler : IDistributedEventHandler<GenerateContentWarningQuantityEmailSendedSendData>, ITransientDependency
//    {
//        private readonly IAppFactory _appFactory;
//        private readonly IServiceProvider _serviceProvider;
//        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
//        private readonly IConfiguration _configuration;
//        private readonly IVnisCoreMongoEmailRepository _vnisCoreMongoEmailRepository;

//        public GenerateContentWarningQuantityEmailSendedHandler(
//             IAppFactory appFactory,
//            IServiceProvider serviceProvider,
//            IStringLocalizer<CoreLocalizationResource> localizer,
//            IConfiguration configuration,
//            IVnisCoreMongoEmailRepository vnisCoreMongoEmailRepository
//            )
//        {
//            _appFactory = appFactory;
//            _serviceProvider = serviceProvider;
//            _localizer = localizer;
//            _configuration = configuration;
//            _vnisCoreMongoEmailRepository = vnisCoreMongoEmailRepository;
//        }

//        public async Task HandleEventAsync(GenerateContentWarningQuantityEmailSendedSendData eventData)
//        {
//            Log.Information($"GenerateContentWarningQuantityEmailSendedHandler: Nhận mess gửi mail cảnh báo");
//            try
//            {
//                await SendMailAsync(eventData);
//            }
//            catch (Exception ex)
//            {
//                Log.Error(ex, ex.Message);
//            }
//        }

//        private async Task SendMailAsync(GenerateContentWarningQuantityEmailSendedSendData eventData)
//        {
//            try
//            {
//                int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
//                var smtpOption = await GetConfigAsync(eventData.TenantId);
//                var emailSender = new EmailSender(smtpOption, _localizer);
//                if (eventData == null)
//                    throw new UserFriendlyException(_localizer["Vnis.BE.SendMail.EmailInvoice.DataIsNull"]);

//                // vì là mẫu mail thông báo hết hạn bên ứng dụng tự quản lý nên sẽ chỉ cần insert 1 mẫu mail theo tenant 00000
//                var emailTemplate = await GetEmailTemplateAsync(Guid.Empty, eventData.Action);
//                if (emailTemplate == null)
//                    throw new UserFriendlyException(_localizer["Vnis.BE.SendMail.EmailInvoice.EmailTemplateNotFound"]);

//                var content = GetContent(emailTemplate, eventData);

//                var cc = string.IsNullOrEmpty(emailTemplate.Cc) ? null : emailTemplate.Cc.Split(';');
//                var bcc = string.IsNullOrEmpty(emailTemplate.Bcc) ? null : emailTemplate.Bcc.Split(';');

//                try
//                {
//                    await emailSender.SendWithManyTosAsync(emailTemplate.Subject, content, eventData.NotifyEmails, null, bcc, null);
//                    await InsertEmailAsync(emailTemplate, eventData.NotifyEmails, string.IsNullOrEmpty(emailTemplate.Cc) ? null : emailTemplate.Cc, emailTemplate.Bcc, content, null, MailStatus.Success, eventData.TenantId);
//                }
//                catch (Exception ex)
//                {
//                    Log.Error(ex, ex.Message);
//                    await InsertEmailAsync(emailTemplate, eventData.NotifyEmails, string.IsNullOrEmpty(emailTemplate.Cc) ? null : emailTemplate.Cc, emailTemplate.Bcc, content, null, MailStatus.Error, eventData.TenantId);
//                }
//            }
//            catch (Exception ex)
//            {
//                Log.Error(ex, ex.Message);
//            }
//        }

//        public async Task<SmtpOption> GetConfigAsync(Guid tenantId)
//        {
//            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<TenantSetting>>>();
//            var cacheKey = SettingCacheItem.CalculateCacheKey($"{tenantId}:{GroupSettingKey.Smtp.ToString()}", null, null);

//            var settingCacheValue = await cache.GetAsync(cacheKey);

//            if (settingCacheValue != null && settingCacheValue.Any())
//            {
//                var fromName = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpFromName.ToString());
//                return new SmtpOption
//                {
//                    Host = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpHost.ToString()).Value,
//                    Port = string.IsNullOrEmpty(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpPort.ToString()).Value) ? 0 : int.Parse(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpPort.ToString()).Value),
//                    Socket = (string.IsNullOrEmpty(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString()).Value) || settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString()).Value == "#") ? 0 : int.Parse(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString()).Value),
//                    UserName = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpUserName.ToString()).Value,
//                    Password = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpPassword.ToString()).Value,
//                    From = settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpFrom.ToString()).Value,
//                    FromName = fromName == null ? "" : fromName.Value,
//                    Authentication = !string.IsNullOrEmpty(settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpAuthentication.ToString()).Value) &&
//                                    settingCacheValue.FirstOrDefault(x => x.Code == SettingKey.SmtpAuthentication.ToString()).Value == "1",
//                };
//            }

//            var repoSetting = _serviceProvider.GetService<ISettingService>();

//            var settings = repoSetting.GetByGroupCodeAsync(tenantId, GroupSettingKey.Smtp.ToString()).Result;

//            var setting = new TenantSetting();

//            var host = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpHost.ToString());
//            if (host != null)
//                settings.Add(host);

//            var authentication = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpAuthentication.ToString());
//            if (authentication != null && !string.IsNullOrEmpty(authentication.Value) && authentication.Value == "1")
//                settings.Add(authentication);

//            var port = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpPort.ToString());
//            if (port != null)
//                settings.Add(port);

//            var socket = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpSocket.ToString());
//            if (socket != null)
//                settings.Add(socket);
//            //setting.Socket = string.IsNullOrEmpty(socket.Value) ? 0 : int.Parse(socket.Value);

//            var username = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpUserName.ToString());
//            if (username != null)
//                settings.Add(username);
//            //setting.UserName = username.Value;

//            var password = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpPassword.ToString());
//            if (password != null)
//                settings.Add(password);
//            //setting.Password = password.Value;

//            var from = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpFrom.ToString());
//            if (from != null)
//                settings.Add(from);
//            //setting.From = from.Value;

//            var fromname = settings.FirstOrDefault(x => x.Code == SettingKey.SmtpFromName.ToString());
//            if (fromname != null)
//                settings.Add(fromname);
//            //setting.FromName = fromname.Value;

//            await cache.SetAsync(cacheKey,
//                settings,
//                new DistributedCacheEntryOptions().SetSlidingExpiration(TimeSpan.FromMinutes(10)));

//            return new SmtpOption
//            {
//                Host = host.Value,
//                Port = string.IsNullOrEmpty(port.Value) ? 0 : int.Parse(port.Value),
//                Socket = (string.IsNullOrEmpty(socket.Value) || socket.Value == "#") ? 0 : int.Parse(socket.Value),
//                UserName = username.Value,
//                Password = password.Value,
//                From = from.Value,
//                FromName = fromname == null ? "" : fromname.Value,
//                Authentication = !string.IsNullOrEmpty(authentication.Value) && authentication.Value == "1",
//            };
//        }

//        private string GetContent(EmailTemplateEntity emailTemplate, GenerateContentWarningQuantityEmailSendedSendData eventData)
//        {
//            var content = WebUtility.HtmlDecode(emailTemplate.Content);
//            var now = DateTime.Now.ToString("dd/MM/yyyy");

//            content = content.Replace("@SMTPFrom@", eventData.SmtpFrom);
//            content = content.Replace("@SoLuongMailDaGui@", eventData.QuantityEmailsSended.ToString());
//            content = content.Replace("@NgayThongBao@", now);

//            return content;
//        }

//        private async Task<EmailTemplateEntity> GetEmailTemplateAsync(Guid tenantId, string action)
//        {
//            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

//            var query = $@" SELECT * 
//                            FROM ""EmailTemplate"" 
//                            WHERE ""TenantId"" = '{rawTenantId}' 
//                                AND ""Action"" = '{action}' 
//                                AND ""IsDeleted"" = 0";

//            var emailTemplate = await _appFactory.VnisCoreOracle.Connection.QuerySingleOrDefaultAsync<EmailTemplateEntity>(query);

//            return emailTemplate;
//        }

//        public virtual async Task<Guid> InsertEmailAsync(EmailTemplateEntity emailTemplate, string to, string cc, string bcc, string content, string attachments, MailStatus mailStatus, Guid tenantId)
//        {
//            var email = new MongoEmailEntity
//            {
//                To = to,
//                Cc = cc,
//                Bcc = bcc,
//                ConcurrencyStamp = DateTime.Now,
//                Content = content,
//                EmailAction = emailTemplate.Action,
//                Attachments = attachments,
//                Subject = emailTemplate.Subject,
//                TenantId = tenantId,
//                Status = (short)mailStatus.GetHashCode()
//            };

//            email = await _vnisCoreMongoEmailRepository.InsertAsync(email);

//            return email.Id;
//        }
//    }
//}
