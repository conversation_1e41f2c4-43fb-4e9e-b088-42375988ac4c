using Core.Shared.Attributes;
using Core.Shared.Constants;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.SendMail.Application.Mail.Dto
{
    public class EmailInvoiceErrDto
    {
        [Required(ErrorMessage = "GroupCode không được để trống")]
        [MinLength(1, ErrorMessage = "Phải có ít nhất thông tin 1 thông tin TBSS (GroupCode)")]
        public List<string> GroupCodes { get; set; }

        /// <summary>
        /// email người mua
        /// </summary>
        [Required(ErrorMessage = "Emails không được để trống")]
        [StringLength(500, ErrorMessage = "Email người mua chỉ được nhập tối đa 500 ký tự")]
        [EmailAttribute(ErrorMessage = "Email người mua không đúng định dạng email")]
        public string Emails { get; set; }

        /// <summary>
        /// loại hóa đơn
        /// </summary>
        [Required(ErrorMessage = "Loại hóa đơn không được để trống")]
        public VnisType InvoiceType { get; set; }

        [JsonIgnore]
        public SendMailSource? SendMailSource { get; set; }

        [JsonIgnore]
        public string Action { get; set; }

        [JsonIgnore]
        public string PrintAction { get; set; }

        [JsonIgnore]
        public Guid? TenantId { get; set; }
    }
}
