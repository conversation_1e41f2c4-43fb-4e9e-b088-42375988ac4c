using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Core.Caching;
using Core.MultiTenancy;
using Core.SettingManagement;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Shared.Services;
using Dapper;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Serilog;
using VnisCore.Core.MongoDB.Entities;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.SendMail.GenerateContent.Factories.Interfaces;
using VnisCore.SendMail.GenerateContent.Factories.Models.GenerateContents;

namespace VnisCore.SendMail.GenerateContent.Services
{
    public interface IGenerateInvoice02NoCodeMailContentService
    {
        Task GenerateContentMail(List<decimal> grpTenants, List<int> grpInvs);
    }

    public class GenerateInvoice02NoCodeMailContentService : IGenerateInvoice02NoCodeMailContentService
    {
        private readonly IAppFactory _appFactory;
        private readonly IVnisCoreMongoInvoice02Repository _mongoInvoice02Repository;
        private readonly ISettingService _settingService;
        private readonly IVnisCoreMongoEmailInvoiceRepository _mongoEmailInvoiceRepository;

        public int TAKE_GENERATE_CONTENT_INVOICE02_SIZE = 100;

        public GenerateInvoice02NoCodeMailContentService(
            IAppFactory appFactory,
            IVnisCoreMongoInvoice02Repository mongoInvoice02Repository,
            ISettingService settingService,
            IConfiguration configuration,
            IVnisCoreMongoEmailInvoiceRepository mongoEmailInvoiceRepository)
        {
            _appFactory = appFactory;
            _mongoInvoice02Repository = mongoInvoice02Repository;
            _settingService = settingService;
            _mongoEmailInvoiceRepository = mongoEmailInvoiceRepository;

            int.TryParse(configuration.GetSection("TakeSize:GenerateContentInvoice02").Value, out TAKE_GENERATE_CONTENT_INVOICE02_SIZE);
        }

        public async Task GenerateContentMail(List<decimal> grpTenants, List<int> grpInvs)
        {
            List<MongoInvoice02Entity> headers = null;
            do
            {
                try
                {
                    var exist = await _mongoInvoice02Repository.AnyCreateContentEmailInvoiceNoCodeAsync(grpTenants, grpInvs);
                    if (!exist)
                    {
                        break;
                    }

                    headers = await _mongoInvoice02Repository.GetListCreateContentEmailInvoiceNoCodeWithCursorAsync(TAKE_GENERATE_CONTENT_INVOICE02_SIZE, grpTenants, grpInvs);
                    if (!headers.Any())
                    {
                        Log.Information($@"Không tìm thấy hóa đơn để tạo content gửi mail!");
                        break;
                    }

                    // TH HD gốc theo logic cũ
                    // TH HD thay thế, điều chỉnh => Gửi mail theo mẫu thay thế điều chỉnh
                    try
                    {
                        // Lọc các hóa đơn có trường Email người mua = null
                        // Ko gửi mail với các mail trên
                        var sucessValidateEmailInvoices = headers.Where(x => !x.BuyerEmail.IsNullOrEmpty()).ToList();
                        var emptyEmailInvoices = headers.Except(sucessValidateEmailInvoices).ToList();

                        if (emptyEmailInvoices != null && emptyEmailInvoices.Any())
                            await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(emptyEmailInvoices, -2);

                        // Làm 2 loại hd đơn
                        var GocInvoices = sucessValidateEmailInvoices.Where(x => x.InvoiceStatus == (short)InvoiceStatus.Goc).ToList();
                        var KhacGocInvoices = sucessValidateEmailInvoices.Where(x => x.InvoiceStatus != (short)InvoiceStatus.Goc).ToList();

                        if (!GocInvoices.IsNullOrEmpty())
                        {
                            await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(GocInvoices, 1);

                            var dictByTenant = GocInvoices.GroupBy(x => x.TenantId).ToDictionary(x => x.Key, y => y.ToList());

                            var emailFactory = _appFactory.GetServiceDependency<IGenerateContentEmailFactory>();
                            var emailService = emailFactory.GetService(EmailAction.SendInvoice.ToString(), VnisType._02GTTT);

                            foreach (var item in dictByTenant)
                            {
                                if (!(await SendMailAfterSignAsync(item.Key)))
                                {
                                    Log.Information($@"Không có cấu hình gửi mail sau khi ký hóa đơn - TenantId={item.Key} - Action={EmailAction.SendInvoice.ToString()}");
                                    return;
                                }

                                var emailTemplate = await GetEmailTemplateAsync(item.Key, EmailAction.SendInvoice.ToString());
                                if (emailTemplate == null)
                                {
                                    Log.Error($@"Không tìm thấy mẫu email - TenantId={item.Key} - Action={EmailAction.SendInvoice.ToString()}");
                                    await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(item.Value, -1);
                                    return;
                                }

                                var lstEmailEntity = new List<MongoEmailInvoiceEntity>();
                                var lstErrorCreateContentEmail = new List<MongoInvoice02Entity>();

                                foreach (var invoice in item.Value)
                                {
                                    try
                                    {
                                        var model = new BaseGenerateContentMaillModel
                                        {
                                            TenantId = item.Key,
                                            //UserId = invoice.CreatorId.Value,
                                            Emails = invoice.BuyerEmail,
                                            Type = VnisType._02GTTT,
                                            SendMailSource = SendMailSource.Auto,
                                            InvoiceHeaderId = invoice.Id,
                                            SignStatus = (SignStatus)invoice.SignStatus,
                                            InvoiceStatus = invoice.InvoiceStatus
                                        };

                                        var contentEmail = await emailService.GenerateContentAsync(model, emailTemplate);

                                        var emailEntity = await MappingToMongoEmailEntityAsync(model, emailTemplate, contentEmail, invoice.TenantGroup, invoice.InvoiceGroup);

                                        lstEmailEntity.Add(emailEntity);
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.Error(ex, ex.Message, ex.StackTrace);

                                        lstErrorCreateContentEmail.Add(invoice);
                                    }
                                }

                                try // TODO: làm chặt hơn bằng transaction
                                {
                                    if (lstEmailEntity != null && lstEmailEntity.Any())
                                        await _mongoEmailInvoiceRepository.InsertManyAsync(lstEmailEntity);

                                    if (lstErrorCreateContentEmail != null && lstErrorCreateContentEmail.Any())
                                        await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(lstErrorCreateContentEmail, -1);
                                }
                                catch (Exception ex)
                                {
                                    Log.Error(ex, ex.Message, ex.StackTrace);
                                }
                            }
                        }

                        if (!KhacGocInvoices.IsNullOrEmpty())
                        {
                            await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(KhacGocInvoices, 1);

                            var dictByTenant = KhacGocInvoices.GroupBy(x => x.TenantId).ToDictionary(x => x.Key, y => y.ToList());

                            var emailFactory = _appFactory.GetServiceDependency<IGenerateContentEmailFactory>();
                            var emailService = emailFactory.GetService(EmailAction.SendInvoiceReplaceAdjustment.ToString(), VnisType._02GTTT);

                            foreach (var item in dictByTenant)
                            {
                                if (!(await SendMailAfterSignAsync(item.Key)))
                                {
                                    Log.Information($@"Không có cấu hình gửi mail sau khi ký hóa đơn - TenantId={item.Key} - Action={EmailAction.SendInvoiceReplaceAdjustment.ToString()}");
                                    return;
                                }

                                var emailTemplate = await GetEmailTemplateAsync(item.Key, EmailAction.SendInvoiceReplaceAdjustment.ToString());
                                if (emailTemplate == null)
                                {
                                    Log.Error($@"Không tìm thấy mẫu email - TenantId={item.Key} - Action={EmailAction.SendInvoiceReplaceAdjustment.ToString()}");
                                    await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(item.Value, -1);
                                    return;
                                }

                                var lstEmailEntity = new List<MongoEmailInvoiceEntity>();
                                var lstErrorCreateContentEmail = new List<MongoInvoice02Entity>();

                                foreach (var invoice in item.Value)
                                {
                                    try
                                    {
                                        var model = new BaseGenerateContentMaillModel
                                        {
                                            TenantId = item.Key,
                                            //UserId = invoice.CreatorId.Value,
                                            Emails = invoice.BuyerEmail,
                                            Type = VnisType._02GTTT,
                                            SendMailSource = SendMailSource.Auto,
                                            InvoiceHeaderId = invoice.Id,
                                            SignStatus = (SignStatus)invoice.SignStatus,
                                            InvoiceStatus = invoice.InvoiceStatus
                                        };

                                        var contentEmail = await emailService.GenerateContentAsync(model, emailTemplate);

                                        var emailEntity = await MappingToMongoEmailEntityAsync(model, emailTemplate, contentEmail, invoice.TenantGroup, invoice.InvoiceGroup);

                                        lstEmailEntity.Add(emailEntity);
                                    }
                                    catch (Exception ex)
                                    {
                                        Log.Error(ex, ex.Message, ex.StackTrace);

                                        lstErrorCreateContentEmail.Add(invoice);
                                    }
                                }

                                try // TODO: làm chặt hơn bằng transaction
                                {
                                    if (lstEmailEntity != null && lstEmailEntity.Any())
                                        await _mongoEmailInvoiceRepository.InsertManyAsync(lstEmailEntity);

                                    if (lstErrorCreateContentEmail != null && lstErrorCreateContentEmail.Any())
                                        await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(lstErrorCreateContentEmail, -1);
                                }
                                catch (Exception ex)
                                {
                                    Log.Error(ex, ex.Message, ex.StackTrace);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message, ex.StackTrace);

                        await _mongoInvoice02Repository.UpdateManyStatusCreateContentEmailAsync(headers, -1);
                        break;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, ex.Message, ex.StackTrace);
                    break;
                }
            }
            while (headers != null && headers.Any());
        }

        private async Task<bool> SendMailAfterSignAsync(Guid tenantId)
        {
            //var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
            //var settingAutoSendMail = await repoSetting.GetByCodeRawAsync(tenantId, SettingKey.SendMailAfterSign.ToString());
            //if (settingAutoSendMail != null && settingAutoSendMail.Value == "1")
            //    return true;

            //return false;

            //var settingAutoSendMail = await GetByCodeRawAsync(tenantId, SettingKey.SendMailAfterSign.ToString());
            var settingAutoSendMail = await _settingService.GetByCodeAsync(tenantId, SettingKey.SendMailAfterSign.ToString());
            if (settingAutoSendMail != null && settingAutoSendMail.Value == "1")
                return true;

            return false;
        }

        private async Task<bool> SendMailInvoiceChangeAsync(Guid tenantId)
        {
            var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
            var settingAutoSendMail = await repoSetting.GetByCodeAsync(tenantId, SettingKey.SendMailInvoiceChangeAfterSign.ToString());
            if (settingAutoSendMail != null && settingAutoSendMail.Value == "1")
                return true;

            return false;
        }

        private async Task<bool> SendMailInvoiceReplateAdjustmentAsync(Guid tenantId)
        {
            var repoSetting = _appFactory.GetServiceDependency<ISettingService>();
            var settingAutoSendMail = await repoSetting.GetByCodeAsync(tenantId, SettingKey.SendInvoiceReplaceAdjustment.ToString());
            if (settingAutoSendMail != null && settingAutoSendMail.Value == "1")
                return true;

            return false;
        }

        public async Task<TenantSetting> GetByCodeRawAsync(Guid tenantId, string code)
        {
            //lấy ở cache
            var cache = _appFactory.GetServiceDependency<IDistributedCache<TenantSetting>>();
            var cacheKey = SettingCacheItem.CalculateCacheKey($"{tenantId}:{code}", null, null);

            //lấy cache theo tenant
            var setting = await cache.GetAsync(cacheKey);
            if (setting != null)
                return setting;

            //cache k có => lấy ở currentTenant
            setting = _appFactory.CurrentTenant.Settings?.FirstOrDefault(x => x.Code == code);
            if (setting != null)
                return setting;

            //TODO: thêm hàm ở repoSetting 
            var entity = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<Setting>($@"
                                                    SELECT 
                                                        ""Id"",
                                                        ""ParentId"",
                                                        ""GroupCode"",
                                                        ""Name"",
                                                        ""Code"",
                                                        ""Value"",
                                                        ""ProviderName"",
                                                        ""ProviderKey"",
                                                        ""TenantId"",
                                                        ""Type"",
                                                        ""Description"",
                                                        ""Options"",
                                                        ""IsReadOnly""
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""Code"" = '{code}' 
                                                    AND ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(tenantId)}' And ""IsDeleted"" = 0");
            if (entity == null)
            {
                //lấy cache theo tenant Guid.Empty
                cacheKey = SettingCacheItem.CalculateCacheKey($"{Guid.Empty}:{code}", null, null);
                setting = await cache.GetAsync(cacheKey);
                if (setting != null)
                    return setting;

                entity = (await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<Setting>($@"
                                                    SELECT 
                                                        ""Id"",
                                                        ""ParentId"",
                                                        ""GroupCode"",
                                                        ""Name"",
                                                        ""Code"",
                                                        ""Value"",
                                                        ""ProviderName"",
                                                        ""ProviderKey"",
                                                        ""TenantId"",
                                                        ""Type"",
                                                        ""Description"",
                                                        ""Options"",
                                                        ""IsReadOnly""
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""Code"" = '{code}' 
                                                    AND ""TenantId"" = '{OracleExtension.ConvertGuidToRaw(Guid.Empty)}' And ""IsDeleted"" = 0"));
            }

            if (entity != null)
            {
                setting = new TenantSetting
                {
                    Id = entity.Id,
                    ParentId = entity.ParentId,
                    GroupCode = entity.GroupCode,
                    Name = entity.Name,
                    Code = entity.Code,
                    Value = entity.Value,
                    ProviderName = entity.ProviderName,
                    ProviderKey = entity.ProviderKey,
                    TenantId = entity.TenantId,
                    Options = entity.Options,
                    Type = entity.Type,
                    Description = entity.Description,
                    IsReadOnly = entity.IsReadOnly
                };

                await cache.SetAsync(cacheKey, setting, new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(15)
                });
            }

            return setting;
        }

        /// <summary>
        /// lấy mẫu email
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<EmailTemplateEntity> GetEmailTemplateAsync(Guid tenantId, string action)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var query = $@" SELECT * FROM ""EmailTemplate"" WHERE ""TenantId"" = '{rawTenantId}' AND ""Action"" = '{action}' AND ""IsDeleted"" = 0";
            var emailTemplate = await _appFactory.VnisCoreOracle.Connection.QuerySingleOrDefaultAsync<EmailTemplateEntity>(query);
            return emailTemplate;
        }

        private async Task<MongoEmailInvoiceEntity> MappingToMongoEmailEntityAsync(BaseGenerateContentMaillModel model, EmailTemplateEntity emailTemplate, ContentEmailModel emailContent, decimal groupTenant, int groupInvoice)
        {
            return new MongoEmailInvoiceEntity
            {
                To = emailContent.To,
                Cc = emailContent.Cc,
                Bcc = emailContent.Bcc,
                Content = emailContent.Content,
                FullNameCreator = string.IsNullOrEmpty(model.FullName) ? emailContent.FullName : model.FullName,
                InvoiceHeaderId = emailContent.InvoiceId,
                InvoiceNo = emailContent.InvoiceNo,
                SerialNo = emailContent.SerialNo,
                TemplateNo = emailContent.TemplateNo,
                ConcurrencyStamp = DateTime.UtcNow,
                Status = MailStatus.UnSent.GetHashCode(),
                Subject = emailTemplate.Subject,
                TenantId = model.TenantId,
                Attachments = emailContent.Attachments,
                EmailAction = EmailAction.SendInvoice.ToString(),
                InvoiceType = model.Type.GetName(),
                TenantGroup = groupTenant,
                InvoiceGroup = groupInvoice,
                SendMailSource = (int)model.SendMailSource
            };
        }
    }
}
