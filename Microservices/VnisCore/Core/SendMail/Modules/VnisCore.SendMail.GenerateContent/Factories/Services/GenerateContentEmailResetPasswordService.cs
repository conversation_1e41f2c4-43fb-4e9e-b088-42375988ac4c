using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Uow;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.SendMail.GenerateContent.Factories.Abstractions;
using VnisCore.SendMail.GenerateContent.Factories.Interfaces;
using VnisCore.SendMail.GenerateContent.Factories.Models.GenerateContents;

namespace VnisCore.SendMail.GenerateContent.Factories.Services
{
    public class GenerateContentEmailResetPasswordService : BaseGenerateContentEmailService, IGenerateContentEmailService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public GenerateContentEmailResetPasswordService(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IUnitOfWorkManager unitOfWorkManager) : base(appFactory, localizer, unitOfWorkManager)
        {
            _appFactory = appFactory;
            _localizer = localizer;
        }

        public override async Task<ContentEmailModel> DoworkAsync(BaseGenerateContentMaillModel model, EmailTemplateEntity emailTemplate)
        {
            if (model == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.SendMail.EmailInvoice.ResetPasswordInfoNotFound"]);


            var user = _appFactory.CurrentUser;
            if (user == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.SendMail.EmailInvoice.UserNotFound"]);

            if (string.IsNullOrEmpty(model.Emails))
                throw new UserFriendlyException(_localizer["Vnis.BE.SendMail.EmailInvoice.EmailResetPassswordNotFound"]);

            var content = GetContent(emailTemplate, model.UserName, model.FullName, model.Password);

            var emails = model.Emails.Split(';').ToList();
            var to = emails.FirstOrDefault();

            var cc = new List<string>();
            emails.RemoveAt(0);
            cc.AddRange(emails);

            if (!string.IsNullOrEmpty(emailTemplate.Cc))
                cc.AddRange(emailTemplate.Cc.Split(';'));

            var bcc = string.IsNullOrEmpty(emailTemplate.Bcc) ? null : emailTemplate.Bcc.Split(';');

            return new ContentEmailModel
            {
                Bcc = bcc == null ? null : string.Join(';', bcc),
                Cc = cc == null ? null : string.Join(';', cc),
                Attachments = null,
                To = to,
                Content = content
            };
        }

        private string GetContent(EmailTemplateEntity emailTemplate, string userName, string fullName, string password)
        {
            var content = WebUtility.HtmlDecode(emailTemplate.Content);
            content = content.Replace("@FullName@", fullName);
            content = content.Replace("@UserName@", userName);
            content = content.Replace("@PassWord@", password);

            return content;
        }
    }
}
