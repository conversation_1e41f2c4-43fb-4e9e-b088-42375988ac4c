using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Core.Localization.Resources.AbpLocalization;
using Core.ObjectMapping;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Uow;
using Dapper;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.SendMail.GenerateContent.Factories.Abstractions;
using VnisCore.SendMail.GenerateContent.Factories.Dtos;
using VnisCore.SendMail.GenerateContent.Factories.Interfaces;

namespace VnisCore.SendMail.GenerateContent.Factories.Services
{
    public class GenerateContentEmailInvoice01Service : GenerateContentEmailInvoiceService<Invoice01HeaderEntity, Invoice01UnOfficialEntity>, IGenerateContentEmailService
    {
        private readonly IAppFactory _appFactory;
        protected IObjectMapper _objectMapper { get; }

        public GenerateContentEmailInvoice01Service(IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IUnitOfWorkManager unitOfWorkManager,
            IObjectMapper objectMapper) : base(appFactory, localizer, unitOfWorkManager)
        {
            _appFactory = appFactory;
            _objectMapper = objectMapper;
        }

        public override async Task<string> GenerateContentAsync(Invoice01HeaderEntity invoice, EmailTemplateEntity emailTemplate)
        {
            var content = WebUtility.HtmlDecode(emailTemplate.Content);
           
            //Thông tin người mua
            content = content.Replace("@MA_KHACH_HANG@", invoice.BuyerCode);
            content = content.Replace("@TEN_KHACH_HANG@", invoice.BuyerFullName);
            content = content.Replace("@TEN_CONG_TY@", invoice.BuyerFullName);
            content = content.Replace("@MA_SO_THUE@", invoice.BuyerTaxCode);
            content = content.Replace("@DIA_CHI@", invoice.BuyerAddressLine);
            content = content.Replace("@QUAN_HUYEN@", invoice.BuyerDistrictName);
            content = content.Replace("@THANH_PHO@", invoice.BuyerCityName);
            content = content.Replace("@MA_QUOC_GIA@", invoice.BuyerCountryCode);
            content = content.Replace("@SO_DIEN_THOAI@", invoice.BuyerPhoneNumber);
            content = content.Replace("@FAX@", invoice.BuyerFaxNumber);
            content = content.Replace("@EMAIL@", invoice.BuyerEmail);
            content = content.Replace("@NGAN_HANG@", invoice.BuyerBankName);
            content = content.Replace("@SO_TAI_KHOAN@", invoice.BuyerBankAccount);
            content = content.Replace("@THOI_DIEM_KY@", invoice.BuyerSignedAt?.ToLocalTime().ToString("dd/MM/yyyy"));
            content = content.Replace("@TEN_NGUOI_KY@", invoice.BuyerFullNameSigned);

            //Thông tin người bán
            content = content.Replace("@TEN_NGUOI_BAN@", invoice.SellerLegalName);
            content = content.Replace("@TEN_CONG_TY_NGUOI_BAN@", invoice.SellerFullName);
            content = content.Replace("@MA_SO_THUE_NGUOI_BAN@", invoice.SellerTaxCode);
            content = content.Replace("@DIA_CHI_NGUOI_BAN@", invoice.SellerAddressLine);
            content = content.Replace("@MA_QUOC_GIA_NGUOI_BAN@", invoice.SellerCountryCode);
            content = content.Replace("@QUAN_HUYEN_NGUOI_BAN@", invoice.SellerDistrictName);
            content = content.Replace("@THANH_PHO_NGUOI_BAN@", invoice.SellerCityName);
            content = content.Replace("@SO_DIEN_THOAI_NGUOI_BAN@", invoice.SellerPhoneNumber);
            content = content.Replace("@FAX_NGUOI_BAN@", invoice.SellerFaxNumber);
            content = content.Replace("@EMAIL_NGUOI_BAN@", invoice.SellerEmail);
            content = content.Replace("@NGAN_HANG_NGUOI_BAN@", invoice.SellerBankName);
            content = content.Replace("@SO_TAI_KHOAN_NGUOI_BAN@", invoice.SellerBankAccount);
            content = content.Replace("@THOI_DIEM_NGUOI_BAN_KY@", invoice.SellerSignedTime?.ToLocalTime().ToString("dd/MM/yyyy"));
            content = content.Replace("@TEN_NGUOI_BAN_KY@", invoice.SellerFullNameSigned);

            //Thông tin hóa đơn
            content = content.Replace("@MAU_SO_HOA_DON@", invoice.TemplateNo.ToString());
            content = content.Replace("@KY_HIEU_HOA_DON@", invoice.SerialNo);
            content = content.Replace("@SO_HOA_DON@", invoice.InvoiceNo);
            content = content.Replace("@NGAY_HOA_DON@", invoice.InvoiceDate.ToLocalTime().ToString("dd/MM/yyyy"));
            content = content.Replace("@MA_BAO_MAT@", invoice.TransactionId.ToString());
            content = content.Replace("@IDERP@", invoice.ErpId);
            content = content.Replace("@TRANG_THAI_HOA_DON@", (EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus)).GetName());
            content = content.Replace("@TRANG_THAI_KY@", (EnumExtension.ToEnum<SignStatus>(invoice.SignStatus)).GetName());
            content = content.Replace("@TRANG_THAI_DUYET@", (EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveStatus)).GetName());
            content = content.Replace("@GHI_CHU_HOA_DON@", invoice.Note ?? string.Empty);

            //Thông tin thanh toán
            content = content.Replace("@TONG_CONG@", invoice.TotalPaymentAmount.ToString("#,##0.####"));
            content = content.Replace("@HINH_THUC_THANH_TOAN@", invoice.PaymentMethod);
            content = content.Replace("@TONG_TIEN_THUE@", invoice.TotalVatAmount.ToString("#,##0.####"));
            content = content.Replace("@TONG_TIEN_CHIET_KHAU_TRUOC_THUE@", invoice.TotalDiscountAmountBeforeTax.ToString("#,##0.####"));
            content = content.Replace("@TONG_PHAN_TRAM_CHIET_KHAU_SAU_THUE@", invoice.TotalDiscountPercentAfterTax.ToString("#,##0.####"));
            content = content.Replace("@TONG_TIEN_CHIET_KHAU_SAU_THUE@", invoice.TotalDiscountAmountAfterTax.ToString("#,##0.####"));
            content = content.Replace("@SO_TIEN_BANG_CHU@", invoice.PaymentAmountWords);
            content = content.Replace("@NGUYEN_TE@", invoice.FromCurrency);
            content = content.Replace("@NGOAI_TE@", invoice.ToCurrency);
            content = content.Replace("@TONG_TIEN_CHUA_THUE@", invoice.TotalAmount.ToString("#,##0.####"));
            content = content.Replace("@NGAY_THANH_TOAN@", invoice.PaymentDate?.ToLocalTime().ToString("dd/MM/yyyy"));

            //Thông tin mở rộng
            if (invoice.ExtraProperties != null && invoice.ExtraProperties.Any())
            {
                var extraProperties = invoice.ExtraProperties.ToDictionary(x => x.Key, x => x.Value.ToString());

                var headerExtraProperties = new Dictionary<string, string>();
                if (invoice.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(extraProperties["invoiceHeaderExtras"]))
                {
                    headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(extraProperties["invoiceHeaderExtras"])
                                                        .GroupBy(x => x.FieldName)
                                                        .ToDictionary(x => x.Key, y => y.First().FieldValue);
                }

                if (headerExtraProperties.Any())
                {
                    //check xem có chứa biến của headerExtra không
                    var patternRegexHeaderExtra = @"\@(EXTRA_HEADER.[a-zA-Z0-9-_]{1,250})\@";
                    var headerExtraParams = Regex.Matches(content, patternRegexHeaderExtra);

                    if (headerExtraParams.Any())
                    {
                        var indexHeaderExtraParams = headerExtraParams
                                                    .GroupBy(x => x.Value)
                                                    .ToDictionary(x => x.Key, x => x.FirstOrDefault().Value.Substring(14, x.FirstOrDefault().Value.Length - 15));

                        foreach (var match in indexHeaderExtraParams)
                        {
                            //lấy extra tương ứng
                            if (headerExtraProperties.ContainsKey(match.Value))
                            {
                                content = content.Replace(match.Key, headerExtraProperties[match.Value]);
                            }
                        }

                        ////lấy headerField
                        ////var headerFields = await GetHeaderFieldsAsync(invoice.TenantId, indexHeaderExtraParams.Values.ToList());

                        //if (headerFields.Any())
                        //{
                        //    var indexHeaderField = headerFields.ToDictionary(x => x.FieldName, x => x.Id);
                        //    //lấy extra của hóa đơn ra
                        //    var indexHeaderExtras = headerExtraProperties.ToDictionary(x => x.InvoiceHeaderFieldId, x => x.FieldValue);
                        //    if (indexHeaderExtras.Any())
                        //    {
                        //        foreach (var match in indexHeaderExtraParams)
                        //        {
                        //            //lấy extra tương ứng
                        //            if (indexHeaderField.ContainsKey(match.Value) && indexHeaderExtras.ContainsKey(indexHeaderField[match.Value]))
                        //            {
                        //                content = content.Replace(match.Key, indexHeaderExtras[indexHeaderField[match.Value]]);
                        //            }
                        //        }
                        //    }
                        //}
                    }
                }
            }

            return content;
        }

        public override async Task<Invoice01HeaderEntity> GetInvoiceAsync(Guid tenantId, long id)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var query = $@" SELECT * FROM ""Invoice01Header"" WHERE ""TenantId"" = '{rawTenantId}' AND ""Id"" = {id}";
            var result = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice01HeaderDto>(query);
            var invoice01 = _objectMapper.Map<Invoice01HeaderDto, Invoice01HeaderEntity>(result);
            return invoice01;
        }

        private async Task<List<Invoice01HeaderFieldEntity>> GetHeaderFieldsAsync(Guid tenantId, List<string> fieldNames)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var query = $@" SELECT * FROM ""Invoice01HeaderField"" where ""TenantId"" = '{rawTenantId}' AND ""FieldName"" IN ({string.Join(", ", fieldNames.Select(x => $@"'{x}'"))})";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderFieldEntity>(query);
            return result.ToList();
        }
    }
}
