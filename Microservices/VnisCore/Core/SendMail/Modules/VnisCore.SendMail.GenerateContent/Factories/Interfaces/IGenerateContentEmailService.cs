using System.Threading.Tasks;
using Core.Shared.Models;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.SendMail.GenerateContent.Factories.Models.GenerateContents;

namespace VnisCore.SendMail.GenerateContent.Factories.Interfaces
{
    public interface IGenerateContentEmailService
    {
        Task<ContentEmailModel> GenerateContentAsync(BaseGenerateContentMaillModel model, EmailTemplateEntity template);
    }
}
