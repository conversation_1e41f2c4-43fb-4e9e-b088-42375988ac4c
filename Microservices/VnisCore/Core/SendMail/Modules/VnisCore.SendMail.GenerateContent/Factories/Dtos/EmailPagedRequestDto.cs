using System;
using Core.Shared.Dto;
using Newtonsoft.Json;

namespace VnisCore.SendMail.GenerateContent.Factories.Dtos
{
    public class EmailPagedRequestDto : PagedFullRequestDto
    {
        /// <summary>
        /// gửi từ ngày
        /// </summary>
        public DateTime? CreateFromDate { get; set; }

        /// <summary>
        /// gửi đến ngày
        /// </summary>
        public DateTime? CreateToDate { get; set; }

        [JsonIgnore]
        public Guid? TenantId { get; set; }
    }
}
