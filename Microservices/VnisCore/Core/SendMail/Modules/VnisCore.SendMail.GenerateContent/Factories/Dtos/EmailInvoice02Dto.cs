using System;
using System.Text.Json.Serialization;
using Core.Application.Dtos;

namespace VnisCore.SendMail.GenerateContent.Factories.Dtos
{
    class EmailInvoice02Dto : EntityDto<long>
    {
        public new long Id { get; set; }

        public bool IsActive { get; set; }

        public string Attachments { get; set; }

        public string Bcc { get; set; }

        public string Cc { get; set; }

        public string Content { get; set; }

        public string From { get; set; }


        public int Status { get; set; }

        public string Subject { get; set; }

        public string To { get; set; }

        /// <summary>
        /// code của hóa đơn
        /// </summary>
        public long? InvoiceHeaderId { get; set; }

        /// <summary>
        /// Loại hóa đơn
        /// </summary>
        public string InvoiceType { get; set; }

        public short TemplateNo { get; set; }

        public string SerialNo { get; set; }

        public string InvoiceNo { get; set; }

        public string FullNameCreator { get; set; }

        /// <summary>
        /// action của email
        /// </summary>
        public string EmailAction { get; set; }

        public DateTime ConcurrencyStamp { get; set; }

        /// <summary>
        /// số lần gửi lại
        /// </summary>
        public int TimeResend { get; set; }

        [JsonIgnore]
        public Guid TenantId { get; set; }

        [JsonIgnore]
        public Guid CreatorId { get; set; }

        [JsonIgnore]
        public DateTime CreationTime { get; set; }

        [JsonIgnore]
        public DateTime? LastModificationTime { get; set; }

        [JsonIgnore]
        public Guid? LastModifierId { get; set; }
    }
}
