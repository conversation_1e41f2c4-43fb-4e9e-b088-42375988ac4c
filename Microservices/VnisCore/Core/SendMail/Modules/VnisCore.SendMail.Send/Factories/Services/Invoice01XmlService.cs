using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Core;
using Core.Shared.Authentication;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using Serilog;
using VnisCore.SendMail.Send.Factories.Interfaces;

namespace VnisCore.SendMail.Send.Factories.Services
{
    public class Invoice01XmlService : IInvoiceMediaService
    {
        private readonly string URL_API_INVOICE_DOWNLOAD_XML = "api/ExportPdf/InvoiceDownload/Xml/1";
        private readonly string URL_API_INVOICE_DOWNLOAD_XML_HASCODE = "api/ExportPdf/InvoiceDownload/Xml/HasVerificationCode/1";
        private readonly string URL_API_INVOICE_DOWNLOAD_XML_MULTI_HASCODE = "api/ExportPdf/InvoiceDownload/Xml/HasVerificationCode/multi/1";
        private readonly string BASE_ADDRESS;
        private readonly string USERNAME;
        private readonly string PASSWORD;
        private readonly TimeSpan TIMEOUT;
        private readonly short IS_AUTHENTICATION_NOT_HTTPCONTEXT;

        private readonly IHttpClientFactory _httpClient;
        private readonly IAppFactory _appFactory;
        private readonly IConfiguration _configuration;
        private readonly IAuthenticationShared _authenticationShared;

        public Invoice01XmlService(
            IHttpClientFactory httpClient,
            IAppFactory appFactory,
            IConfiguration configuration,
            IAuthenticationShared authenticationShared)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _appFactory = appFactory;
            _authenticationShared = authenticationShared;

            BASE_ADDRESS = _configuration.GetSection("Microservices:ExportPdf:Endpoint").Value;
            USERNAME = _configuration.GetSection("Microservices:ExportPdf:UserName").Value;
            PASSWORD = _configuration.GetSection("Microservices:ExportPdf:Password").Value;
            TIMEOUT = TimeSpan.FromMinutes(int.Parse(_configuration.GetSection("Microservices:ExportPdf:Timeout").Value));
            IS_AUTHENTICATION_NOT_HTTPCONTEXT = short.Parse(_configuration.GetSection("Microservices:ExportPdf:IsAuthenticationNotHttpContext").Value);
        }

        public async Task<Tuple<string, byte[]>> DownloadAsync(long invoiceId, string serialNo, string printAction = null)
        {
            try
            {
                var file = await DownloadXmlAsync(invoiceId, serialNo);
                if (file == null) return null;

                //var file = await repoInvoiceMedia.Where(x => x.InvoiceHeaderId == invoiceId).OrderByDescending(x => x.CreationTime).FirstOrDefaultAsync();

                //var pathFileMinio = $"{MediaFileType.Invoice01Xml}/{file.TenantId}/{file.CreationTime.Year}/{file.CreationTime.Month:00}/{file.CreationTime.Day:00}/{file.CreationTime.Hour:00}/{file.PhysicalFileName}";

                return new Tuple<string, byte[]>(file.FileName, file.FileBytes);
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message, ex.StackTrace);
                throw new UserFriendlyException(ex.Message);
            }
        }

        private async Task<FileDto> DownloadXmlAsync(long idInvoiceHeader, string serialNo)
        {
            //if (serialNo.StartsWith("K"))
            //{
                var uri = $"{BASE_ADDRESS}{URL_API_INVOICE_DOWNLOAD_XML}";
                return await DownloadAsync(idInvoiceHeader, uri);
            //}
            //else
            //{
            //    try
            //    {
            //        var uri = $"{BASE_ADDRESS}{URL_API_INVOICE_DOWNLOAD_XML_MULTI_HASCODE}";
            //        return await DownloadAsync(idInvoiceHeader, uri);

            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error(ex, ex.Message);
            //        var uri = $"{BASE_ADDRESS}{URL_API_INVOICE_DOWNLOAD_XML}";
            //        return await DownloadAsync(idInvoiceHeader, uri);
            //    }
            //}
        }

        private async Task<FileDto> DownloadAsync(long invoiceHeaderId, string uri)
        {
            if (IS_AUTHENTICATION_NOT_HTTPCONTEXT == 1)
            {
                var token = await _authenticationShared.Login("", USERNAME, PASSWORD);
                if (token.IsError)
                {
                    Log.Error(token.HttpErrorReason);
                    throw new UserFriendlyException($"Không authentication được api {URL_API_INVOICE_DOWNLOAD_XML}");
                }

                var client = _authenticationShared.GetClient(token.AccessToken);
                client.Timeout = TIMEOUT;

                var reqContent = new StringContent(JsonConvert.SerializeObject(new List<long> { invoiceHeaderId }), Encoding.UTF8, "application/json");

                var result = await client.PostAsync(uri, reqContent);
                if (result.StatusCode != HttpStatusCode.OK)
                {
                    Log.Error(await result.Content.ReadAsStringAsync());
                    throw new UserFriendlyException($"Có lỗi trong quá trình lấy file xml: {await result.Content.ReadAsStringAsync()}");
                }

                var resContent = await result.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<FileDto>(resContent);
            }
            else
            {
                var httpContextAccessor = _appFactory.GetServiceDependency<IHttpContextAccessor>();
                var token = httpContextAccessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, uri);
                request.Content = new StringContent(JsonConvert.SerializeObject(new List<long> { invoiceHeaderId }), Encoding.UTF8, "application/json");
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

                var client = _httpClient.CreateClient();
                var result = await client.SendAsync(request);
                if (result.StatusCode != HttpStatusCode.OK)
                {
                    Log.Error(await result.Content.ReadAsStringAsync());
                    throw new UserFriendlyException($"Có lỗi trong quá trình lấy file xml: {await result.Content.ReadAsStringAsync()}");
                }

                var resContent = await result.Content.ReadAsStringAsync();

                return JsonConvert.DeserializeObject<FileDto>(resContent);
            }
        }
    }
}
