using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Repositories;
using VnisCore.Invoice01.Application.Invoice01Document.Models.Requests;

namespace VnisCore.Invoice01.Application.Invoice01Document.Handlers
{
    public class DeleteInvoice01DocumentCommandHandler : AsyncRequestHandler<DeleteInvoice01DocumentRequestModel>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly ILogger<DeleteInvoice01DocumentCommandHandler> _logger;

        public DeleteInvoice01DocumentCommandHandler(
            IFileService fileService,
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            ILogger<DeleteInvoice01DocumentCommandHandler> logger)
        {
            _fileService = fileService;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _appFactory = appFactory;
        }

        protected override async Task Handle(DeleteInvoice01DocumentRequestModel request, CancellationToken cancellationToken)
        {
            try
            {
                var repoInvoiceDocument = _serviceProvider.GetService<IInvoiceDocumentRepository<Invoice01DocumentEntity>>();
                var document = await repoInvoiceDocument.GetByIdAsync(request.Id);

                if (document != null)
                {
                    document.IsDeleted = true;
                    document.DeletionTime = DateTime.Now;
                    document.DeleterId = _appFactory.CurrentUser.Id ?? Guid.Empty;
                    await _appFactory.CurrentUnitOfWork.SaveChangesAsync(cancellationToken);

                    // xóa file trên minio
                    var pathFileMinio = $"{MediaFileType.Invoice01Document}/{document.TenantId}/{document.CreationTime.Year}/{document.CreationTime.Month:00}/{document.CreationTime.Day:00}/{document.CreationTime.Hour:00}/{document.PhysicalFileName}";
                    await _fileService.DeleteAsync(pathFileMinio);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }
    }
}
