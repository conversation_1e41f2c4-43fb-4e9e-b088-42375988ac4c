using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models;
using static VnisCore.Invoice01.Application.Invoice01.Dto.CreateInvoice01HeaderDto;

namespace VnisCore.Invoice01.Application.Invoice01.Services
{
    /// <summary>
    /// mô tả: Service import excel hoá đơn 01GTKT mở rộng cùng setting tuỳ chỉnh
    /// task liên quan: 1781
    /// link task: https://gitlab.com/hoangnh/einvoice/-/issues/1781
    /// </summary>
    public class ImportInvoice01WithSettingService : BaseImport01Service, IImport01Service
    {
        private readonly ILogger<ImportInvoice01WithSettingService> _logger;
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;
        private readonly IVnisCoreMongoInvoice01BatchIdRepository _mongoInvoice01BatchIdRepository;
        private readonly IConfiguration _configuration;
        private readonly string _EMPTY_VALUE = "#";
        private readonly string[] _CollumnExcel = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "AA", "AB", "AC", "AD", "AE", "AF", "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP", "AQ", "AR", "AS", "AT", "AU", "AV", "AW", "AX", "AY", "AZ", "BA", "BB", "BC", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BK", "BL", "BM", "BN", "BO", "BP", "BQ", "BR", "BS", "BT", "BU", "BV", "BW", "BX", "BY", "BZ" };

        public ImportInvoice01WithSettingService(
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            ILogger<ImportInvoice01WithSettingService> logger,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            IVnisCoreMongoInvoice01BatchIdRepository mongoInvoice01BatchIdRepository,
            IConfiguration configuration)
            : base(logger, appFactory, serviceProvider, distributedEventBus, localizer, invoiceService, mongoInvoice01BatchIdRepository, configuration)
        {
            _invoiceService = invoiceService;
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _localizer = localizer;
            _mongoInvoice01BatchIdRepository = mongoInvoice01BatchIdRepository;
            _configuration = configuration;
        }

        public override async Task<List<ImportInvoice01Model>> ReadExcelAsync(Guid tenantId, ExcelPackage package, Dictionary<string, string> parameters)
        {
            var worksheet = package.Workbook.Worksheets[0];
            if (worksheet?.Dimension == null || worksheet.Dimension.End.Row < 4)
                //throw new UserFriendlyException("Không có sheet dữ liệu hoặc định dạng file không đúng");
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.EmptyDataImportOrDataFormatIncorrect"]);

            var repoCurrency = _appFactory.Repository<CurrencyEntity, long>();
            var fromCurrency = await repoCurrency.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.IsDefault && !x.IsDeleted);
            if (fromCurrency == null)
                //throw new UserFriendlyException("Đơn vị bán hàng chưa cài đặt nguyên tệ");
            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.FromCurrencyIsNotYet"]);

            var repoInvoice01 = _appFactory.Repository<Invoice01HeaderEntity, long>();
            var repoInvoice01ErpId = _appFactory.Repository<Invoice01ErpIdEntity, long>();
            //var duplicates = repoInvoice01.Where(x => x.TenantId == tenantId && !x.IsDeleted)
            //    .Select(x => x.ErpId)
            //    .Distinct()
            //    .AsEnumerable()
            //    .ToList();

            var invoices = new List<ImportInvoice01Model>();
            var idErps = new List<string>();
            var regexCode = new Regex("^[a-zA-Z0-9][a-zA-Z0-9-_/.]{0,48}[a-zA-Z0-9]{1,49}$|^[a-zA-Z0-9]{1}$");
            var repoCustomer = _appFactory.Repository<CustomerEntity, long>();
            var customers = await repoCustomer.Where(x => x.TenantId == tenantId).ToListAsync();
            var exitedCustomers = customers.ToDictionary(x => x.CustomerCode, x => x.TaxCode);

            var settingService = _serviceProvider.GetService<ISettingService>();
            var setting = await settingService.GetByGroupCodeAsync(tenantId, GroupSettingKey.ImportExcelInvoice01.ToString());
            var valueStartOfData = setting.FirstOrDefault(m => m.Code == SettingKey.ImportExcel01SettingStartOfData.ToString());
            int startOfData = !string.IsNullOrEmpty(valueStartOfData?.Value) && valueStartOfData?.Value != _EMPTY_VALUE  ? Convert.ToInt32(valueStartOfData.Value) : 4;

            var settingPaymentMethod = await settingService.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());
            if (settingPaymentMethod == null || string.IsNullOrEmpty(settingPaymentMethod.Value))
                //throw new UserFriendlyException("Chưa có cấu hình phương thức thanh toán");
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PaymentMethodNotFound"]);

            var paymentMethods = settingPaymentMethod.Value.Split(";").ToList();

            var settingStartDataOfDetail = setting.FirstOrDefault(m => m.Code == SettingKey.ImportExcel01SettingDataOfDetail.ToString());
            bool startDataOfDetail = !string.IsNullOrEmpty(settingStartDataOfDetail?.Value) && settingStartDataOfDetail?.Value != _EMPTY_VALUE ? Convert.ToBoolean(int.Parse(settingStartDataOfDetail.Value)) : true;

            int indexTemplateNo = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTemplateNo.ToString(), out string defaultTemplateNo);
            int indexSerialNo = GetIndexExcel(setting, SettingKey.ImportExcel01SettingSerialNo.ToString(), out string defaultSerialNo);
            int indexIdERP = GetIndexExcel(setting, SettingKey.ImportExcel01SettingIdERP.ToString(), out string defaultIdERP);
            int indexInvoiceDate = GetIndexExcel(setting, SettingKey.ImportExcel01SettingInvoiceDate.ToString(), out string defaultInvoiceDate);
            int indexPaymentMethod = GetIndexExcel(setting, SettingKey.ImportExcel01SettingPaymentMethod.ToString(), out string defaultPaymentMethod);
            int indexPaymentDate = GetIndexExcel(setting, SettingKey.ImportExcel01SettingPaymentDate.ToString(), out string defaultPaymentDate);
            int indexCurrency = GetIndexExcel(setting, SettingKey.ImportExcel01SettingCurrency.ToString(), out string defaultCurrency);
            int indexExchangeRate = GetIndexExcel(setting, SettingKey.ImportExcel01SettingExchangeRate.ToString(), out string defaultExchangeRate);
            int indexBuyerCode = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerId.ToString(), out string defaultBuyerCode);
            int indexBuyerLegalName = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerLegalName.ToString(), out string defaultBuyerLegalName);
            int indexBuyerFullName = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerFullName.ToString(), out string defaultBuyerFullName);
            int indexBuyerTaxCode = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerTaxCode.ToString(), out string defaultBuyerTaxCode);
            int indexBuyerAddressLine = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerAddressLine.ToString(), out string defaultBuyerAddressLine);
            int indexBuyerBankAccount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerBankAccount.ToString(), out string defaultBuyerBankAccount);
            int indexBuyerBankName = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerBankName.ToString(), out string defaultBuyerBankName);
            int indexBuyerEmail = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerEmail.ToString(), out string defaultBuyerEmail);
            int indexBuyerPhone = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerPhone.ToString(), out string defaultBuyerPhone);
            int indexBuyerFax = GetIndexExcel(setting, SettingKey.ImportExcel01SettingBuyerFax.ToString(), out string defaultBuyerFax);
            int indexNote = GetIndexExcel(setting, SettingKey.ImportExcel01SettingNote.ToString(), out string defaultNote);
            int indexTotalAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTotalAmount.ToString(), out string defaultTotalAmount);
            int indexTotalDiscountPercent = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTotalDiscountPercent.ToString(), out string defaultTotalDiscountPercent);
            int indexTotalDiscountAmountAfterTax = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTotalDiscountAmountAfterTax.ToString(), out string defaultTotalDiscountAmountAfterTax);
            int indexTotalDiscountAmountBeforeTax = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTotalDiscountAmountBeforeTax.ToString(), out string defaultTotalDiscountAmountBeforeTax);
            int indexTotalVatAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTotalVatAmount.ToString(), out string defaultTotalVatAmount);
            int indexTotalPaymentAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingTotalPaymentAmount.ToString(), out string defaultTotalPaymentAmount);
            int indexProductCode = GetIndexExcel(setting, SettingKey.ImportExcel01SettingProductId.ToString(), out string defaultProductCode);
            int indexProductName = GetIndexExcel(setting, SettingKey.ImportExcel01SettingProductName.ToString(), out string defaultProductName);
            int indexDetailNote = GetIndexExcel(setting, SettingKey.ImportExcel01SettingDetailNote.ToString(), out string defaultDetailNote);
            int indexUnitName = GetIndexExcel(setting, SettingKey.ImportExcel01SettingUnitName.ToString(), out string defaultUnitName);
            int indexQuantity = GetIndexExcel(setting, SettingKey.ImportExcel01SettingQuantity.ToString(), out string defaultQuantity);
            int indexUnitPrice = GetIndexExcel(setting, SettingKey.ImportExcel01SettingUnitPrice.ToString(), out string defaultUnitPrice);
            int indexDiscountPercent = GetIndexExcel(setting, SettingKey.ImportExcel01SettingDiscountPercent.ToString(), out string defaultDiscountPercent);
            int indexDiscountAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingDiscountAmount.ToString(), out string defaultDiscountAmount);
            int indexAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingAmount.ToString(), out string defaultAmount);
            int indexVatPercent = GetIndexExcel(setting, SettingKey.ImportExcel01SettingVatPercent.ToString(), out string defaultVatPercent);
            int indexVatAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingVatAmount.ToString(), out string defaultVatAmount);
            int indexPaymentAmount = GetIndexExcel(setting, SettingKey.ImportExcel01SettingPaymentAmount.ToString(), out string defaultPaymentAmount);
            int indexProductType = GetIndexExcel(setting, SettingKey.ImportExcel01SettingProductType.ToString(), out string defaultProductType);

            //extra
            var settingHeaderExtras = setting.FirstOrDefault(m => m.Code == SettingKey.ImportExcel01SettingHeaderExtras.ToString());
            Dictionary<string, Tuple<int, string>> dicHeaderExtras = new Dictionary<string, Tuple<int, string>>(); //lưu dạng: HeaderField-số cột- giá trị mặc định
            if (settingHeaderExtras != null && !String.IsNullOrEmpty(settingHeaderExtras.Value) && settingHeaderExtras.Value.Contains(':'))
            {
                if (settingHeaderExtras.Value.Contains('|'))
                {
                    foreach (var item in settingHeaderExtras.Value.Split('|'))
                    {
                        var indexExtra = GetIndexExtraExcel(item, out string defaultValueHeaderExtra);
                        dicHeaderExtras.Add(item.Split(':')[1].Trim(), new Tuple<int, string>(indexExtra, defaultValueHeaderExtra));
                    }
                }
                else
                {
                    var indexExtra = GetIndexExtraExcel(settingHeaderExtras.Value, out string defaultValueHeaderExtra);
                    dicHeaderExtras.Add(settingHeaderExtras.Value.Split(':')[1].Trim(), new Tuple<int, string>(indexExtra, defaultValueHeaderExtra));
                }
            }

            var settingDetailExtras = setting.FirstOrDefault(m => m.Code == SettingKey.ImportExcel01SettingDetailExtras.ToString());
            Dictionary<string, Tuple<int, string>> dicDetailExtras = new Dictionary<string, Tuple<int, string>>();
            if (settingDetailExtras != null && !String.IsNullOrEmpty(settingDetailExtras.Value) && settingDetailExtras.Value.Contains(':'))
            {
                if (settingDetailExtras.Value.Contains('|'))
                {
                    foreach (var item in settingDetailExtras.Value.Split('|'))
                    {
                        var indexExtra = GetIndexExtraExcel(item, out string defaultValueDetailExtra);
                        dicDetailExtras.Add(item.Split(':')[1].Trim(), new Tuple<int, string>(indexExtra, defaultValueDetailExtra));
                    }
                }
                else
                {
                    var indexExtra = GetIndexExtraExcel(settingDetailExtras.Value, out string defaultValueDetailExtra);
                    dicDetailExtras.Add(settingDetailExtras.Value.Split(':')[1].Trim(), new Tuple<int, string>(indexExtra, defaultValueDetailExtra));
                }
            }

            //var formatDate = setting.FirstOrDefault(m => m.Key == SettingKey.ImportExcel01SettingFormatDate.ToString());

            var currIndex = "";
            var prevIndex = "";

            for (var i = startOfData; i <= worksheet.Dimension.End.Row; i++)
            {
                var invoice = new ImportInvoice01Model();
                #region lấy các trường từ file excel

                //header
                var templateNo = GetValueCell(worksheet, i, indexTemplateNo, defaultTemplateNo);
                var serialNo = GetValueCell(worksheet, i, indexSerialNo, defaultSerialNo);
                var idErp = GetValueCell(worksheet, i, indexIdERP, defaultIdERP);
                var invoiceDate = GetValueCell(worksheet, i, indexInvoiceDate, defaultInvoiceDate);
                var paymentMethod = GetValueCell(worksheet, i, indexPaymentMethod, defaultPaymentMethod);
                var paymentDate = GetValueCell(worksheet, i, indexPaymentDate, defaultPaymentDate);
                var currency = GetValueCell(worksheet, i, indexCurrency, defaultCurrency);
                var exchangeRate = GetValueCell(worksheet, i, indexExchangeRate, defaultExchangeRate);
                var buyerId = GetValueCell(worksheet, i, indexBuyerCode, defaultBuyerCode);
                var buyerLegalName = GetValueCell(worksheet, i, indexBuyerLegalName, defaultBuyerLegalName);
                var buyerFullName = GetValueCell(worksheet, i, indexBuyerFullName, defaultBuyerFullName);
                var buyerTaxCode = GetValueCell(worksheet, i, indexBuyerTaxCode, defaultBuyerTaxCode);
                var buyerAddressLine = GetValueCell(worksheet, i, indexBuyerAddressLine, defaultBuyerAddressLine);
                var buyerBankAccount = GetValueCell(worksheet, i, indexBuyerBankAccount, defaultBuyerBankAccount);
                var buyerBankName = GetValueCell(worksheet, i, indexBuyerBankName, defaultBuyerBankName);
                var buyerEmail = GetValueCell(worksheet, i, indexBuyerEmail, defaultBuyerEmail);
                var buyerPhone = GetValueCell(worksheet, i, indexBuyerPhone, defaultBuyerPhone);
                var buyerFax = GetValueCell(worksheet, i, indexBuyerFax, defaultBuyerFax);
                var note = GetValueCell(worksheet, i, indexNote, defaultNote);
                var totalAmount = GetValueCell(worksheet, i, indexTotalAmount, defaultTotalAmount);
                var totalDiscountPercent = GetValueCell(worksheet, i, indexTotalDiscountPercent, defaultTotalDiscountPercent);
                var totalDiscountAmountAfterTax = GetValueCell(worksheet, i, indexTotalDiscountAmountAfterTax, defaultTotalDiscountAmountAfterTax);
                var totalDiscountAmountBeforeTax = GetValueCell(worksheet, i, indexTotalDiscountAmountBeforeTax, defaultTotalDiscountAmountBeforeTax);
                var totalVatAmount = GetValueCell(worksheet, i, indexTotalVatAmount, defaultTotalVatAmount);
                var totalPaymentAmount = GetValueCell(worksheet, i, indexTotalPaymentAmount, defaultTotalPaymentAmount);

                //detail
                var productId = GetValueCell(worksheet, i, indexProductCode, defaultProductCode);
                var productName = GetValueCell(worksheet, i, indexProductName, defaultProductName);
                var detailNote = GetValueCell(worksheet, i, indexDetailNote, defaultDetailNote);
                var unitName = GetValueCell(worksheet, i, indexUnitName, defaultUnitName);
                var quantity = GetValueCell(worksheet, i, indexQuantity, defaultQuantity);
                var unitPrice = GetValueCell(worksheet, i, indexUnitPrice, defaultUnitPrice);
                var discountPercent = GetValueCell(worksheet, i, indexDiscountPercent, defaultDiscountPercent);
                var discountAmount = GetValueCell(worksheet, i, indexDiscountAmount, defaultDiscountAmount);
                var amount = GetValueCell(worksheet, i, indexAmount, defaultAmount);
                var vatPercent = GetValueCell(worksheet, i, indexVatPercent, defaultVatPercent);
                var vatAmount = GetValueCell(worksheet, i, indexVatAmount, defaultVatAmount);
                var paymentAmount = GetValueCell(worksheet, i, indexPaymentAmount, defaultPaymentAmount);
                var productType = GetValueCell(worksheet, i, indexProductType, defaultProductType);

                if (string.IsNullOrEmpty(idErp) && string.IsNullOrEmpty(invoiceDate) && string.IsNullOrEmpty(paymentMethod))
                    break;

                //validate iderp
                if (string.IsNullOrEmpty(idErp) && string.IsNullOrEmpty(templateNo) && string.IsNullOrEmpty(serialNo)
                       && string.IsNullOrEmpty(buyerId) && string.IsNullOrEmpty(productId))
                    continue;

                // số chứng từ - not null
                if (!string.IsNullOrEmpty(idErp))
                {
                    if (idErp.Length > 450)
                        ThrowErrorImport("Số chứng từ hóa đơn", indexIdERP, i, "không được dài quá 450 kí tự");

                    if (await repoInvoice01ErpId.AnyAsync(x => x.TenantId == tenantId && x.ErpId == idErp))
                        ThrowErrorImport("Số chứng từ hóa đơn", indexIdERP, i, "đã tồn tại trong hệ thống");
                }
                else
                {
                    idErp = $"{templateNo}-{serialNo}-{StringExtension.Random(8)}";
                }

                //check xem số chứng từ có trùng không
                //nếu trùng với dòng trước thì bỏ qua, nếu không trùng với dòng trước mà đã có trong list thì báo lỗi
                if (invoices.Count() > 0 && invoices.Last().ErpId != idErp && idErps.Any(x => x == idErp))
                    ThrowErrorImport("Số chứng từ hóa đơn", indexIdERP, i, "đã bị trùng trong file excel");

                //hình thức thanh toán -not null
                if (string.IsNullOrEmpty(paymentMethod))
                    ThrowErrorImport("Hình thức thanh toán", indexPaymentMethod, i, "không được để trống");

                if (paymentMethod.Length > 50)
                    ThrowErrorImport("Hình thức thanh toán", indexPaymentMethod, i, "dài tối đa 50 ký tự");

                if (!paymentMethods.Contains(paymentMethod))
                    ThrowErrorImport("Hình thức thanh toán", indexPaymentMethod, i, "chưa có cấu hình");

                idErps.Add(idErp);
                invoice.ErpId = idErp;

                #endregion
                currIndex = idErp;
                //đọc và validate dữ liệu header
                if (startDataOfDetail || (!startDataOfDetail && currIndex != prevIndex)) //dữ liệu detal cùng dòng dữ liệu header
                {
                    //đọc như bt, đọc tất cả
                    #region validate headers
                    // mẫu số - not null - match regex
                    if (string.IsNullOrEmpty(templateNo) || !templateNo.IsTemplateNo())
                        ThrowErrorImport("Mẫu số hóa đơn", indexTemplateNo, i, "không được để trống và phải đúng định dạng");

                    if (templateNo.Length > 20)
                        ThrowErrorImport("Mẫu số hóa đơn", indexTemplateNo, i, "không được dài quá 20 ký tự");

                    // ký hiệu - not null - match regex
                    if (string.IsNullOrEmpty(serialNo) || !serialNo.IsSerialNo())
                        ThrowErrorImport("Ký hiệu hóa đơn", indexSerialNo, i, "không được để trống và phải đúng định dạng");

                    // ngày hóa đơn - not null
                    if (string.IsNullOrEmpty(invoiceDate))
                        ThrowErrorImport("Ngày hóa đơn", indexInvoiceDate, i, "không được để trống");

                    var dateInvoice = ParseDate(invoiceDate);//, formatDate!= null ? formatDate.Value : null
                    if (!dateInvoice.HasValue)
                        ThrowErrorImport("Ngày hóa đơn", indexInvoiceDate, i, "không đúng kiểu dữ liệu DateTime");

                    if (dateInvoice.Value.Date > DateTime.Now.Date)
                        ThrowErrorImport("Ngày hóa đơn", indexInvoiceDate, i, "không được lớn hơn ngày hiện tại");



                    var outPaymentDate = new DateTime();
                    // ngày thanh toán
                    if (!string.IsNullOrEmpty(paymentDate) && !DateTime.TryParse(paymentDate, out outPaymentDate))
                        ThrowErrorImport("Ngày thanh toán", indexPaymentDate, i, "không đúng kiểu dữ liệu DateTime");

                    // tiền tệ - not null
                    if (string.IsNullOrEmpty(currency))
                        ThrowErrorImport("Tiền tệ", indexCurrency, i, "không được để trống");

                    if (currency.Length > 3)
                        ThrowErrorImport("Tiền tệ", indexCurrency, i, "không được quá 3 ký tự");

                    // tỷ giá - not null
                    if (string.IsNullOrEmpty(exchangeRate))
                        ThrowErrorImport("Tỷ giá chuyển đổi", indexExchangeRate, i, "không được để trống");

                    if (!decimal.TryParse(exchangeRate, out decimal numberExchangeRate))
                        ThrowErrorImport("Tỷ giá chuyển đổi", indexExchangeRate, i, "không đúng định dạng số thập phân Double");

                    if (numberExchangeRate <= 0)
                        ThrowErrorImport("Tỷ giá chuyển đổi", indexExchangeRate, i, "phải lớn hơn 0");

                    if (currency == fromCurrency.CurrencyCode && numberExchangeRate != 1)
                        ThrowErrorImport("Tỷ giá tại", indexExchangeRate, i, "có tiền tệ bằng với nguyên tệ nên tỷ giá phải bằng 1");

                    // mã người mua hàng - not null
                    if (string.IsNullOrEmpty(buyerId))
                        ThrowErrorImport("Mã người mua", indexBuyerCode, i, "không được để trống");

                    if (buyerId.Length > 50)
                        ThrowErrorImport("Mã người mua", indexBuyerCode, i, "không được quá 50 ký tự");

                    if (!regexCode.IsMatch(buyerId))
                        ThrowErrorImport("Mã người mua", indexBuyerCode, i, "không được bắt đầu hoặc kết thúc bằng khoảng trắng hoặc kí tự đặc biệt và chỉ bao gồm các kí tự a-z, A-Z, 0-9, -, _, /");

                    // tên người mua
                    //if (string.IsNullOrEmpty(buyerLegalName))
                    //    ThrowErrorImport("Tên người mua", indexBuyerLegalName, i, "không được để trống");

                    if (!string.IsNullOrEmpty(buyerLegalName) && buyerLegalName.Length > 400)
                        ThrowErrorImport("Tên người mua", indexBuyerLegalName, i, "dài tối đa 400 ký tự");

                    // tên đơn vị mua hàng - not null
                    if (string.IsNullOrEmpty(buyerFullName))
                        ThrowErrorImport("Tên đơn vị mua hàng", indexBuyerFullName, i, "không được để trống");

                    if (buyerFullName.Length > 400)
                        ThrowErrorImport("Tên đơn vị mua hàng", indexBuyerFullName, i, "dài tối đa 400 ký tự");

                    // mã số thuế người mua
                    string buyerCode = "";
                    if (!string.IsNullOrEmpty(buyerTaxCode))
                    {
                        if (buyerTaxCode.Length > 14)
                            ThrowErrorImport("Mã số thuế đơn vị mua hàng", indexBuyerTaxCode, i, "dài tối đa 14 ký tự");

                        //check có phải mst không
                        if (!buyerTaxCode.IsTaxCode())
                            ThrowErrorImport($"Mã số thuế đơn vị mua hàng", indexBuyerTaxCode, i, "không đúng định dạng mã số thuế");

                        //2. check mst trong file import đã có trong danh mục chưa => nếu có thì lấy invoiceHeader.BuyerId = Customer.CustomerId => hết
                        //TH trùng taxcode và cuscode
                        if (exitedCustomers.ContainsValue(buyerTaxCode))
                        {
                            buyerCode = exitedCustomers.FirstOrDefault(x => x.Value == buyerTaxCode).Key;
                        } //TH mã khách hàng trùng với mã số thuế import thì sẽ tự sinh mkh
                        else if (exitedCustomers.ContainsKey(buyerTaxCode))
                        {
                            buyerCode = buyerTaxCode;
                        }
                    }
                    //TH MST null
                    else
                    {
                        buyerCode = StringExtension.Random(8);
                    }
                    // địa chỉ người mua - not null
                    if (string.IsNullOrEmpty(buyerAddressLine))
                        ThrowErrorImport("Địa chỉ người mua", indexBuyerAddressLine, i, "không được để trống");

                    if (buyerAddressLine.Length > 400)
                        ThrowErrorImport("Địa chỉ người mua", indexBuyerAddressLine, i, "dài tối đa 400 ký tự");

                    //tài khoản ngân hàng người mua
                    if (!string.IsNullOrEmpty(buyerBankAccount) && buyerBankAccount.Length > 30)
                        ThrowErrorImport("Tài khoản ngân hàng người mua", indexBuyerBankAccount, i, "dài tối đa 30 ký tự");

                    // chi nhánh ngân hàng người mua
                    if (!string.IsNullOrEmpty(buyerBankName) && buyerBankName.Length > 400)
                        ThrowErrorImport("Tên ngân hàng người mua", indexBuyerBankName, i, "dài tối đa 400 ký tự");

                    // email người mua
                    if (!string.IsNullOrEmpty(buyerEmail) && buyerEmail.Length > 500)
                        ThrowErrorImport("Email người mua", indexBuyerEmail, i, "dài tối đa 500 ký tự");
                    // số điện thoại người mua
                    if (!string.IsNullOrEmpty(buyerPhone)
                        && buyerPhone.Length > 20)
                        ThrowErrorImport("Số điện thoại người mua", indexBuyerPhone, i, "dài tối đa 20 ký tự");

                    // fax người mua
                    if (!string.IsNullOrEmpty(buyerFax) && buyerFax.Length > 20)
                        ThrowErrorImport("Số fax người mua", indexBuyerFax, i, "dài tối đa 20 ký tự");

                    // nội dung hóa đơn
                    if (!string.IsNullOrEmpty(note) && note.Length > 500)
                        ThrowErrorImport("Nội dung hóa đơn", indexNote, i, "dài tối đa 500 ký tự");

                    // % chiết khấu toàn hóa đơn
                    double totalDiscountPercentOut = 0;
                    if (!string.IsNullOrEmpty(totalDiscountPercent) && !double.TryParse(totalDiscountPercent, out totalDiscountPercentOut))
                        ThrowErrorImport("Phần trăm chiếu khấu toàn hóa đơn", indexTotalDiscountPercent, i, "không đúng định dạng số thập phân Double");

                    //header tự tính
                    // tổng tiền 
                    decimal totalAmountOut = 0;
                    if (indexTotalAmount == 0 && string.IsNullOrEmpty(defaultTotalAmount))
                    {
                        totalAmountOut = -1;
                    } //k phải tự tính
                    else
                    {
                        if (string.IsNullOrEmpty(totalAmount))
                            ThrowErrorImport("Tổng thành tiền", indexTotalAmount, i, "không được để trống");

                        if (!decimal.TryParse(totalAmount, out totalAmountOut))
                            ThrowErrorImport("Tổng thành tiền", indexTotalAmount, i, "không đúng định dạng số thập phân Decimal");
                    }


                    // Tổng tiền chiết khấu cho từng mặt hàng 
                    decimal totalDiscountAmountBeforeTaxOut = 0;
                    if (indexTotalDiscountAmountBeforeTax == 0 && string.IsNullOrEmpty(defaultTotalDiscountAmountBeforeTax))
                    {
                        totalDiscountAmountBeforeTaxOut = -1;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(totalDiscountAmountBeforeTax)
                            && !decimal.TryParse(totalDiscountAmountBeforeTax, out totalDiscountAmountBeforeTaxOut))
                            ThrowErrorImport("Tổng tiền chiết khấu cho từng mặt hàng", indexTotalDiscountAmountBeforeTax, i, "không đúng định dạng số thập phân Decimal");
                    }

                    // Tiền chiết khấu toàn hóa đơn
                    decimal totalDiscountAmountAfterTaxOut = 0;
                    if (indexTotalDiscountAmountAfterTax == 0 && string.IsNullOrEmpty(defaultTotalDiscountAmountAfterTax))
                    {
                        totalDiscountAmountAfterTaxOut = -1;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(totalDiscountAmountAfterTax)
                            && !decimal.TryParse(totalDiscountAmountAfterTax, out totalDiscountAmountAfterTaxOut))
                            ThrowErrorImport("Tiền chiết khấu toàn hóa đơn", indexTotalDiscountAmountAfterTax, i, "không đúng định dạng số thập phân Decimal");
                    }

                    // tổng tiền thuế - not null
                    decimal totalVatAmountOut = 0;
                    if (indexTotalVatAmount == 0 && string.IsNullOrEmpty(defaultTotalVatAmount))
                    {
                        totalVatAmountOut = -1;
                    }
                    else//k phải tự tính
                    {
                        if (!string.IsNullOrEmpty(totalVatAmount)
                            && !decimal.TryParse(totalVatAmount, out totalVatAmountOut))
                            ThrowErrorImport("Tổng tiền thuế", indexTotalVatAmount, i, "không đúng định dạng số thập phân Decimal");
                    }

                    // tổng tiền thanh toán - not null
                    decimal totalPaymentAmountOut = 0;
                    if (indexTotalPaymentAmount == 0 && string.IsNullOrEmpty(defaultTotalPaymentAmount))
                    {
                        totalPaymentAmountOut = -1;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(totalPaymentAmount))
                            ThrowErrorImport("Tổng tiền thanh toán", indexTotalPaymentAmount, i, "không được để trống");

                        if (!decimal.TryParse(totalPaymentAmount, out totalPaymentAmountOut))
                            ThrowErrorImport("Tổng tiền thanh toán", indexTotalPaymentAmount, i, "không đúng định dạng số thập phân Decimal");
                    }

                   
                    #endregion

                    //extra
                    Dictionary<string, string> valueHeaderExtra = new Dictionary<string, string>();
                    if (dicHeaderExtras.Count > 0)
                    {
                        foreach (var item in dicHeaderExtras)
                        {
                            if (item.Value.Item1 != 0 && string.IsNullOrEmpty(item.Value.Item2))
                            {
                                var value = worksheet.Cells[i, item.Value.Item1].GetValue<string>()?.Trim();
                                if (!string.IsNullOrWhiteSpace(value))
                                    valueHeaderExtra.Add(item.Key, value);
                            }
                            else
                                valueHeaderExtra.Add(item.Key, item.Value.Item2);
                        }
                    }

                    if (valueHeaderExtra.Count > 0)
                    {
                        invoice.MetadataHeaderExtra = valueHeaderExtra;
                    }

                    invoice.TemplateNo = short.Parse(templateNo);
                    invoice.SerialNo = serialNo;
                    invoice.ErpId = idErp;
                    invoice.InvoiceDate = dateInvoice.Value;
                    invoice.PaymentMethod = paymentMethod;
                    invoice.PaymentDate = string.IsNullOrEmpty(paymentDate) ? dateInvoice.Value : outPaymentDate;
                    invoice.Currency = currency;
                    invoice.ExchangeRate = numberExchangeRate;
                    invoice.BuyerCode = buyerId;
                    invoice.BuyerFullName = buyerFullName;
                    invoice.BuyerLegalName = buyerLegalName;
                    invoice.BuyerTaxCode = buyerTaxCode;
                    invoice.BuyerAddressLine = buyerAddressLine;
                    invoice.BuyerBankAccount = buyerBankAccount;
                    invoice.BuyerBankName = buyerBankName;
                    invoice.BuyerEmail = buyerEmail;
                    invoice.BuyerPhone = buyerPhone;
                    invoice.BuyerFax = buyerFax;
                    invoice.Note = note;
                    invoice.TotalAmount = totalAmountOut;
                    invoice.TotalDiscountPercent = totalDiscountPercentOut;
                    invoice.TotalDiscountAmountBeforeTax = totalDiscountAmountBeforeTaxOut;
                    invoice.TotalDiscountAmountAfterTax = totalDiscountAmountAfterTaxOut;
                    invoice.TotalVatAmount = totalVatAmountOut;
                    invoice.TotalPaymentAmount = totalPaymentAmountOut; //tính sau cùng;
                }

                if (startDataOfDetail || (!startDataOfDetail && currIndex == prevIndex))
                {

                    #region validate details
                    // mã hàng - not null
                    if (string.IsNullOrEmpty(productId) && !string.IsNullOrEmpty(defaultProductCode))
                        ThrowErrorImport("Mã hàng hóa", indexProductCode, i, "không được để trống");

                    if (productId.Length > 50)
                        ThrowErrorImport("Mã hàng hóa", indexProductCode, i, "chỉ có thể dài tối đa 50 ký tự");

                    // tên hàng - not null
                    if (string.IsNullOrEmpty(productName))
                        ThrowErrorImport("Tên hàng hóa", indexProductName, i, "không được để trống");

                    if (productName.Length > 500)
                        ThrowErrorImport("Tên hàng hóa", indexProductName, i, "không được dài quá 500 kí tự");

                    // nội dung hàng hóa 
                    if (!string.IsNullOrEmpty(detailNote) && detailNote.Length > 2000)
                        ThrowErrorImport("Nội dung hàng hóa", indexDetailNote, i, "không được dài quá 2000 kí tự");

                    // đơn vị tính - not null

                    if (string.IsNullOrEmpty(unitName))
                        ThrowErrorImport("Đơn vị tính", indexUnitName, i, "không được để trống");

                    if (unitName.Length > 50)
                        ThrowErrorImport("Đơn vị tính", indexUnitName, i, "không được dài quá 50 kí tự");

                    // số lượng
                    decimal quantityOut = 0; //phần này k tự tính, chỉ có thể lấy giá trị mặc định
                    if (!string.IsNullOrEmpty(quantity) && !decimal.TryParse(quantity, out quantityOut))
                        ThrowErrorImport("Số lượng", indexQuantity, i, "không đúng định dạng số");

                    // đơn giá
                    decimal unitPriceOut = 0; //phần này k tự tính, chỉ có thể lấy giá trị mặc định
                    if (!string.IsNullOrEmpty(unitPrice) && !decimal.TryParse(unitPrice, out unitPriceOut))
                        ThrowErrorImport("Đơn giá", indexUnitPrice, i, "không đúng định dạng số");

                    //% chiết khấu trước thuế chi tiết hóa đơn
                    decimal discountPercentOut = 0; //phần này k tự tính, chỉ có thể lấy giá trị mặc định
                    if (!string.IsNullOrEmpty(discountPercent) && !decimal.TryParse(discountPercent, out discountPercentOut))
                        ThrowErrorImport("Phần trăm chiết khấu", indexDiscountPercent, i, "không đúng định dạng số thập phân double");

                    //% thuế - not null
                    if (string.IsNullOrEmpty(vatPercent))
                        ThrowErrorImport("Thuế suất", indexVatPercent, i, "không được để trống");

                    if (!Int32.TryParse(vatPercent, out Int32 vatPercentOut))
                        ThrowErrorImport("Thuế suất", indexVatPercent, i, "không đúng định dạng số nguyên Int");

                    //các phần tự tính
                    // thành tiền trước thuế - not null
                    decimal amountOut = 0;
                    if (indexAmount == 0 && string.IsNullOrEmpty(defaultAmount))
                    {
                        //tự tính
                        amountOut = (decimal)quantityOut * unitPriceOut;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(amount))
                            ThrowErrorImport("Thành tiền trước thuế", indexAmount, i, "không được để trống");

                        if (!decimal.TryParse(amount, out amountOut))
                            ThrowErrorImport("Thành tiền trước thuế", indexAmount, i, "không đúng định dạng số thập phân Decimal");
                    }

                    //tiền chiết khấu trước thuế chi tiết hóa đơn
                    decimal discountAmountOut = 0;
                    if (indexDiscountAmount == 0 && string.IsNullOrEmpty(defaultDiscountAmount))
                    {
                        //tự tính
                        discountAmountOut = (decimal)discountPercentOut * amountOut;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(discountAmount) && !decimal.TryParse(discountAmount, out discountAmountOut))
                            ThrowErrorImport("Tiền chiết khấu", indexDiscountAmount, i, "không đúng định dạng số thập phân Decimal");
                    }

                    //tiền thuế từng mặt hàng - not null
                    decimal vatAmountOut = 0;
                    if (indexVatAmount == 0 && string.IsNullOrEmpty(defaultVatAmount))
                    {
                        //tự tính
                        vatAmountOut = (amountOut * vatPercentOut) / 100;
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(vatAmount))
                            ThrowErrorImport("Tiền thuế", indexVatAmount, i, "không đúng định dạng số thập phân Decimal");

                        if (!decimal.TryParse(vatAmount, out vatAmountOut))
                            ThrowErrorImport("Tiền thuế", indexVatAmount, i, "không đúng định dạng số thập phân Decimal");
                    }
                    //tính lại amount: = số lượng x đơn giá - chiết khấu
                    amountOut -= discountAmountOut;

                    //thành tiền từng mặt hàng sau thuế
                    decimal paymentAmountOut = 0;
                    if (indexPaymentAmount == 0 && string.IsNullOrEmpty(defaultPaymentAmount))
                    {
                        //tự tính
                        paymentAmountOut = amountOut + vatAmountOut;
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(paymentAmount) && !decimal.TryParse(paymentAmount, out paymentAmountOut))
                            ThrowErrorImport("Thành tiền sau thuế", indexPaymentAmount, i, "không đúng định dạng số thập phân Decimal");
                    }

                    if (string.IsNullOrEmpty(productType))
                        ThrowErrorImport("Tính chất hàng hóa", indexProductType, i, "không được để trống");

                    var productTypes = new List<short>() { 1, 2, 3, 4 };
                    if (!productTypes.Contains(short.Parse(productType)))
                        ThrowErrorImport("Tính chất hàng hóa", indexProductType, i, "không đúng");

                    #endregion

                    Dictionary<string, string> valueDetailExtra = new Dictionary<string, string>();
                    if (dicDetailExtras.Count > 0)
                    {
                        foreach (var item in dicDetailExtras)
                        {
                            if (item.Value.Item1 != 0 && string.IsNullOrEmpty(item.Value.Item2))
                            {
                                var value = worksheet.Cells[i, item.Value.Item1].GetValue<string>()?.Trim();
                                if (!string.IsNullOrWhiteSpace(value))
                                    valueDetailExtra.Add(item.Key, value);
                            }
                            else
                                valueDetailExtra.Add(item.Key, item.Value.Item2); //mặc định
                        }
                    }
                    if (valueDetailExtra.Count > 0)
                    {
                        invoice.MetadataDetailExtra = valueDetailExtra;
                    }

                    invoice.ProductCode = productId;
                    invoice.ProductName = productName;
                    invoice.DetailNote = detailNote;
                    invoice.UnitName = unitName;
                    invoice.Quantity = quantityOut;
                    invoice.UnitPrice = unitPriceOut;
                    invoice.DiscountPercent = discountPercentOut;
                    invoice.DiscountAmount = discountAmountOut;
                    invoice.Amount = amountOut;
                    invoice.VatPercent = vatPercentOut;
                    invoice.VatAmount = vatAmountOut;
                    invoice.PaymentAmount = paymentAmountOut;
                    invoice.ProductType = short.Parse(productType);
                }

                //validate các trường

                prevIndex = currIndex;

                invoices.Add(invoice);
            }

            return invoices;
        }

        public override async Task<List<CreateInvoice01HeaderDto>> ToEntitiesAsync(Guid tenantId, Guid createBy, string userFullName, string userName, string cashierCode, List<ImportInvoice01Model> excelModels)
        {
            var tenantInfo = _appFactory.CurrentTenant;
            if (tenantInfo == null)
                //throw new UserFriendlyException("Không tìm thấy thông tin công ty");
                throw new UserFriendlyException(_localizer["Vnis.BE.Export.Invoice01.TenantNotFound"]);

            //Tiền tệ
            var repoCurrency = _appFactory.Repository<CurrencyEntity, long>();
            var currencies = await repoCurrency.Where(x => x.TenantId == tenantId).ToListAsync();
            var fromCurrency = currencies.FirstOrDefault(x => x.IsDefault);
            if (fromCurrency == null)
                //throw new UserFriendlyException("Đơn vị bán hàng chưa cài đặt nguyên tệ");
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.FromCurrencyIsNotYet"]);

            //thue
            var taxService = _serviceProvider.GetService<ITaxService>();
            var existedTaxes = await taxService.GetTaxesAsync(tenantId);
            var taxes = excelModels.GroupBy(x => x.VatPercent);
            foreach (var item in taxes)
            {
                var tax = item.First();
                if (!existedTaxes.ContainsKey(tax.VatPercent))
                    //throw new UserFriendlyException($"Không tồn tại thuế suất {tax.VatPercent}");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.TaxNotExist"]);
            }

            //tao/sua thong tin kh va tao tai khoan khách hàng
            #region khách hàng
            var repoCustomer = _appFactory.Repository<CustomerEntity, long>();
            var customers = excelModels.Where(x => !string.IsNullOrEmpty(x.BuyerCode)).GroupBy(x => x.BuyerId);
            //var tenantInfoMetadata = _invoiceService.GetTenantInfoMetadata(tenantInfo);
            //lấy dữ liệu thông tin doanh nghiệp khác
            //var sellerBankAccount = tenantInfoMetadata.ContainsKey(TenantMetaData.BankAccount) ? tenantInfoMetadata[TenantMetaData.BankAccount].Value : null;
            //var sellerBankName = tenantInfoMetadata.ContainsKey(TenantMetaData.BankName) ? tenantInfoMetadata[TenantMetaData.BankName].Value : null;
            //var sellerPhoneNumber = tenantInfoMetadata.ContainsKey(TenantMetaData.Phone) ? tenantInfoMetadata[TenantMetaData.Phone].Value : null;
            //var sellerFax = tenantInfoMetadata.ContainsKey(TenantMetaData.Fax) ? tenantInfoMetadata[TenantMetaData.Fax].Value : null;
            //var sellerEmail = tenantInfoMetadata.ContainsKey(TenantMetaData.Email) ? tenantInfoMetadata[TenantMetaData.Email].Value : null;
            //var sellerLegalName = tenantInfoMetadata.ContainsKey(TenantMetaData.LegalName) ? tenantInfoMetadata[TenantMetaData.LegalName].Value : null;
            #endregion

            //extra
            var repoHeaderField = _appFactory.Repository<Invoice01HeaderFieldEntity, long>();
            var headerFields = repoHeaderField.Where(x => x.TenantId == tenantId)
                                                           .ToDictionary(x => x.FieldName, x => x.Id);

            var repoDetailField = _appFactory.Repository<Invoice01DetailFieldEntity, long>();
            var detailFields = repoDetailField.Where(x => x.TenantId == tenantId)
                                                           .ToDictionary(x => x.FieldName, x => x.Id);

            //tim cac mau/loai hoa don trong csdl
            var commonInvoice01Service = _serviceProvider.GetService<ICommonInvoice01Service>();
            var invoiceTemplates = (await commonInvoice01Service.GetRegisterAvailabilities(tenantId, createBy, 1))
                                    .ToDictionary(x => $"{x.TemplateNo}|{x.SerialNo}", x => x);


            var dictionaryTax = existedTaxes.ToDictionary(x => x.Key, x => x.Value.Item1);

            var invoiceHeaders = new List<CreateInvoice01HeaderDto>();
            var tempInvoiceHeaders = excelModels.GroupBy(x => x.ErpId);

            //kiểm tra xem dữ liệu file excel có trùng với csdl không
            foreach (var item in tempInvoiceHeaders)
            {
                //loc theo tung mau hoa don
                var invoiceHeader = new CreateInvoice01HeaderDto();
                var groupInvoice = item.ToList();
                var firstKey = groupInvoice.FirstOrDefault();

                if (firstKey == null)
                    continue;

                if (!invoiceTemplates.ContainsKey($"{firstKey.TemplateNo}|{firstKey.SerialNo}"))
                    //throw new UserFriendlyException("Không tìm thấy mẫu hóa đơn để TẠO. Vui lòng kiểm tra thông tin mẫu hóa đơn hoặc phân quyền tạo hóa đơn");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.Create.InvoiceTemplateNotFound"]);

                var invoiceTemplate = invoiceTemplates[$"{firstKey.TemplateNo}|{firstKey.SerialNo}"];

                invoiceHeader.TemplateNo = short.Parse(invoiceTemplate.TemplateNo);

                var currency = currencies.FirstOrDefault(x => x.CurrencyCode == firstKey.Currency);
                if (currency == null)
                    //throw new UserFriendlyException($"Không tìm thấy tiền tệ mã {firstKey.Currency}");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.Create.InvoiceTemplateNotFound", firstKey.Currency]);

                AddInvoiceHeader(tenantId, createBy, userFullName, userName, cashierCode, firstKey, invoiceHeader);
                var invoiceHeaderExtras = new List<CreateInvoice01HeaderExtraModel>();
                var invoiceDetails = new List<CreateInvoice01DetailModel>();
                var invoiceTaxBreakDown = new List<Invoice01TaxBreakdownEntity>();
                //tự tính các phần tiền của tổng hóa đơn
                decimal totalAmount = 0;
                decimal totalVatAmount = 0;
                decimal totalDiscountAmountBeforeTax = 0;

                var index = 1;
                foreach (var detailExcel in groupInvoice)
                {
                    invoiceHeaderExtras = new List<CreateInvoice01HeaderExtraModel>();
                    //add header extra
                    if (detailExcel.MetadataHeaderExtra != null)
                    {
                        foreach (var field in detailExcel.MetadataHeaderExtra)
                        {
                            invoiceHeaderExtras.Add(new CreateInvoice01HeaderExtraModel
                            {
                                FieldValue = field.Value,
                                FieldName = field.Key,
                            });
                        }
                    }

                    if (string.IsNullOrEmpty(detailExcel.ProductCode)) //bỏ qua nếu k có hàng hóa
                        continue;

                    var detail = ConvertToInvoiceDetail(detailExcel);
                    detail.Index = index;
                    var invoiceDetailExtras = new List<CreateInvoice01DetailExtraModel>();
                    //add extra cho detail
                    if (detailExcel.MetadataDetailExtra != null)
                    {
                        foreach (var field in detailExcel.MetadataDetailExtra)
                        {
                            invoiceDetailExtras.Add(new CreateInvoice01DetailExtraModel
                            {
                                FieldName = field.Key,
                                FieldValue = field.Value,
                            });
                        }
                    }
                    detail.InvoiceDetailExtras = invoiceDetailExtras;
                    invoiceDetails.Add(detail);
                    index++;
                    if (!invoiceTaxBreakDown.Any(x => x.VatPercent == detail.VatPercent))
                    {
                        invoiceTaxBreakDown.Add(new Invoice01TaxBreakdownEntity
                        {
                            Name = existedTaxes[detail.VatPercent].Item2,
                            VatAmount = detail.VatAmount,
                            VatPercent = detail.VatPercent,
                            VatAmountBackUp = 0,
                            TenantId = tenantId,
                            VatPercentDisplay = existedTaxes[detail.VatPercent].Item1,
                            InvoiceHeaderId = invoiceHeader.Id
                        });
                    }
                    else
                    {
                        var taxBreakDown = invoiceTaxBreakDown.First(x => x.VatPercent == detail.VatPercent);
                        taxBreakDown.VatAmount += detail.VatAmount;
                    }

                    totalAmount += detailExcel.Amount;
                    totalVatAmount += detailExcel.VatAmount;
                    totalDiscountAmountBeforeTax += detailExcel.DiscountAmount;
                }

                invoiceHeader.TotalVatAmount = invoiceHeader.TotalVatAmount == -1 ? totalVatAmount : invoiceHeader.TotalVatAmount;
                invoiceHeader.TotalAmount = invoiceHeader.TotalAmount == -1 ? totalAmount : invoiceHeader.TotalAmount;
                invoiceHeader.TotalDiscountAmountBeforeTax = invoiceHeader.TotalDiscountAmountBeforeTax == -1 ? totalDiscountAmountBeforeTax : invoiceHeader.TotalDiscountAmountBeforeTax;
                invoiceHeader.TotalDiscountAmountAfterTax = invoiceHeader.TotalDiscountAmountAfterTax == -1 ? (invoiceHeader.TotalAmount * (decimal)invoiceHeader.TotalDiscountPercentAfterTax / 100) : invoiceHeader.TotalDiscountAmountAfterTax;
                invoiceHeader.TotalPaymentAmount = invoiceHeader.TotalPaymentAmount == -1 ? totalAmount + totalVatAmount - invoiceHeader.TotalDiscountAmountAfterTax : invoiceHeader.TotalPaymentAmount;

                invoiceHeader.InvoiceTaxBreakdowns = invoiceTaxBreakDown.Select(x => new CreateInvoice01TaxBreakdownModel
                {
                    VatAmount = x.VatAmount,
                    VatPercent = x.VatPercent,
                    VatAmountBackUp = x.VatAmountBackUp
                }).ToList();

                invoiceHeader.InvoiceDetails = invoiceDetails;
                invoiceHeader.InvoiceHeaderExtras = invoiceHeaderExtras;

                invoiceHeaders.Add(invoiceHeader);
            }

            return invoiceHeaders;
        }

        private void ThrowErrorImport(string error, int indexCellInExcel, int indexRow, string message)
        {
            if(indexCellInExcel == 0)
                throw new UserFriendlyException($"{error} {message}");

            throw new UserFriendlyException($"{error} tại {_CollumnExcel.ElementAt(indexCellInExcel - 1)}{indexRow} {message}");
        }

        private string GetValueCell(ExcelWorksheet worksheet, int indexRow, int indexCol, string defaultValue)
        {
            if (indexCol == 0)
                return defaultValue;

            var valueExcel = worksheet.Cells[indexRow, indexCol].GetValue<string>()?.Trim();

            if (string.IsNullOrEmpty(valueExcel))
            {
                return defaultValue;
            }
            else
            {
                return valueExcel;
            }
            //if (string.IsNullOrEmpty(defaultValue))
            //{
            //    return worksheet.Cells[indexRow, indexCol].GetValue<string>()?.Trim();
            //}
            //else
            //    return defaultValue;
        }

        private int GetIndexExcel(IList<TenantSetting> lstSetting, string key, out string defaultValue)
        {
            var setting = lstSetting.FirstOrDefault(m => m.Code == key);
            if (setting == null)
                //throw new UserFriendlyException($"Chưa có cấu hình key: {key} import excel");
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.KeyConfigImportExcelNotFound"]);

            defaultValue = null;
            //cắt chuỗi
            if (string.IsNullOrEmpty(setting.Value) || setting.Value == _EMPTY_VALUE)
                return 0;

            //xem có phải là có giá trị mặc đinh không
            if (setting.Value.Contains("#"))
            {
                if (setting.Value.Contains(":"))
                {
                    //có chia ra có giá trị mặc định. định dạng kiểu #:123. 123 là giá trị mặc định. Cột nào cũng k quan trọng nữa
                    var indexOfSeperate = setting.Value.IndexOf(':');

                    defaultValue = setting.Value.Substring(indexOfSeperate + 1);

                    return 0;
                }
                else
                {
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.SettingInvalid", setting.Value]);
                }
            }
            else
            {
                if (!_CollumnExcel.Contains(setting.Value))
                    //throw new UserFriendlyException($"Không có cột {setting.Value } trong file excel");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.ColumnNotExist", setting.Value]);

                return (Array.IndexOf(_CollumnExcel, setting.Value) + 1);
            } 
        }

        /// <summary>
        /// lấy ra cột extra, định dạng kiểu; TenCot:TenFieldExtra:GiaTRiMacDinh
        /// </summary>
        /// <param name="settingValue"></param>
        /// <param name="defaultValue"></param>
        /// <returns></returns>
        private int GetIndexExtraExcel(string settingValue, out string defaultValue)
        {
            defaultValue = null;

            //xem có phải là có giá trị mặc đinh không
            if (!settingValue.Contains(":"))
                //throw new UserFriendlyException($"Không có cột mở rộng hóa đơn trong file excel");
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.ColumnExtendNotExist"]);

            var values = settingValue.Split(':');

            if (values.First() == _EMPTY_VALUE)
            {
                //lấy giá trị mặc định
                defaultValue = values.Last();
                return 0;
            }

            //đọc giá trị từ excel
            if (!_CollumnExcel.Contains(values.First()))
                //throw new UserFriendlyException($"Không có cột {values.ElementAt(1)} trong file excel");
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.ImportExcel.ColumnNotExist",values.ElementAt(1)]);

            return (Array.IndexOf(_CollumnExcel, values.First()) + 1);
        }

        private DateTime? ParseDate(string input)
        {
            try
            {
                // if(String.IsNullOrEmpty(format))
                // {
                //     format = "MM/dd/yyyy";
                // }
                string format1 = "dd/MM/yyyy";
                string format2 = "dd-MM-yyyy";
                DateTime datetime;
                if (input.Contains("/"))
                {
                    DateTime.TryParseExact(input, format1, System.Globalization.CultureInfo.InvariantCulture,
                                                            System.Globalization.DateTimeStyles.None, out datetime);
                    return datetime;
                }
                if (input.Contains("-"))
                {
                    DateTime.TryParseExact(input, format2, System.Globalization.CultureInfo.InvariantCulture,
                                                            System.Globalization.DateTimeStyles.None, out datetime);
                    return datetime;
                }

                return null;
            }
            catch (UserFriendlyException)
            {
                return null;
            }
        }
    }

}
