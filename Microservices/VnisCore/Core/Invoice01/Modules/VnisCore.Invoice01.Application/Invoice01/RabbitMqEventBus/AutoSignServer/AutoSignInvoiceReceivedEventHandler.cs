//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Shared.Constants;
//using Core.Shared.Services;

//using Microsoft.Extensions.Configuration;

//using Newtonsoft.Json;

//using Serilog;

//using System.Collections.Generic;
//using System.Net;
//using System.Net.Http;
//using System.Text;
//using System.Threading.Tasks;

//namespace VnisCore.Invoice01.Application.Invoice01.RabbitMqEventBus.AutoSignServer
//{
//    public class AutoSignInvoiceReceivedEventHandler : IDistributedEventHandler<AutoSignInvoice01EventSendData>, ITransientDependency
//    {
//        private readonly IConfiguration _configure;
//        private readonly IHttpClientFactory _httpClient;
//        private readonly ISettingService _settingService;

//        public AutoSignInvoiceReceivedEventHandler(IHttpClientFactory httpClient,
//                                                IConfiguration configure,
//                                                ISettingService settingService)
//        {
//            _httpClient = httpClient;
//            _configure = configure;
//            _settingService = settingService;
//        }

//        public async Task HandleEventAsync(AutoSignInvoice01EventSendData eventData)
//        {
//            var invoiceHasApprove = await _settingService.GetByCodeAsync(eventData.TenantId, SettingKey.InvoiceHasApprove.ToString());
//            if (invoiceHasApprove != null && invoiceHasApprove.Value == "1")
//                return;

//            var autoSignServerNSHN = await _settingService.GetByCodeAsync(eventData.TenantId, SettingKey.AutoSignServerNuocSachHaNoi.ToString());
//            var autoSignServer = await _settingService.GetByCodeAsync(eventData.TenantId, SettingKey.AutoSignServer.ToString());
//            if ((eventData.Source != InvoiceSource.Api && autoSignServer != null && autoSignServer.Value != "1")
//                || (eventData.Source == InvoiceSource.Api && autoSignServerNSHN != null && autoSignServerNSHN.Value != "1"))
//                return;

//            var idsHeader = new List<long>();
//            idsHeader.Add(eventData.Id);

//            var client = _httpClient.CreateClient();
//            var httpContent = new StringContent(JsonConvert.SerializeObject(idsHeader), Encoding.UTF8, "application/json");
//            var host = _configure.GetSection("Microservices:Sign:Endpoint").Value;
           
//            string apiName = $"api/sign/SignInvoice01";

//            HttpRequestMessage clientRequest = new HttpRequestMessage(HttpMethod.Post, host + apiName)
//            {
//                Content = httpContent
//            };

//            clientRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", eventData.Token);

//            var response = await client.SendAsync(clientRequest);
//            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.NoContent)
//            {
//                Log.Error("***** CÓ LỖI TRONG QUÁ TRÌNH KÝ *****");
//                Log.Error($"***** {response.Content} *****");
//            }
//        }
//    }
//}
