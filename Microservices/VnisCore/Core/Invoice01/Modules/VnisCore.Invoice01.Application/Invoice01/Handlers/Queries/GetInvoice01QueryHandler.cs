using Core.Shared.Constants;
using Core.Shared.Extensions;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Models.Handlers.Queries;
using VnisCore.Invoice01.Application.Invoice01.Models.Responses.Queries;

namespace VnisCore.Invoice01.Application.Invoice01.Handlers.Queries
{
    public class GetInvoice01QueryHandler : IRequestHandler<GetInvoice01Query, GetInvoice01Response>
    {
        private readonly IInvoiceHeaderRepository<Invoice01HeaderEntity> _repoInvoiceHeader;
        private readonly IInvoiceHeaderExtraRepository<Invoice01HeaderExtraEntity> _repoInvoiceHeaderExtra;
        private readonly IInvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity> _repoInvoiceHeaderField;
        private readonly IInvoiceDetailRepository<Invoice01DetailEntity> _repoInvoiceDetail;
        private readonly IInvoiceDetailExtraRepository<Invoice01DetailExtraEntity> _repoInvoiceDetailExtra;
        private readonly IInvoiceDetailFieldRepository<Invoice01DetailFieldEntity> _repoInvoiceDetailField;
        private readonly IInvoiceReferenceRepository<Invoice01ReferenceEntity> _repoInvoiceReference;
        private readonly IInvoiceDocumentInfoRepository<Invoice01DocumentInfoEntity> _repoInvoiceDocumentInfo;
        private readonly IInvoice01TaxBreakdownRepository _repoTaxBreakdown;

        public GetInvoice01QueryHandler(
            IInvoiceHeaderRepository<Invoice01HeaderEntity> repoInvoiceHeader,
            IInvoiceHeaderExtraRepository<Invoice01HeaderExtraEntity> repoInvoiceHeaderExtra,
            IInvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity> repoInvoiceHeaderField,
            IInvoiceDetailRepository<Invoice01DetailEntity> repoInvoiceDetail,
            IInvoiceDetailExtraRepository<Invoice01DetailExtraEntity> repoInvoiceDetailExtra,
            IInvoiceDetailFieldRepository<Invoice01DetailFieldEntity> repoInvoiceDetailField,
            IInvoiceReferenceRepository<Invoice01ReferenceEntity> repoInvoiceReference,
            IInvoiceDocumentInfoRepository<Invoice01DocumentInfoEntity> repoInvoiceDocumentInfo,
            IInvoice01TaxBreakdownRepository repoTaxBreakdown
            )
        {
            _repoInvoiceHeader = repoInvoiceHeader;
            _repoInvoiceHeaderExtra = repoInvoiceHeaderExtra;
            _repoInvoiceHeaderField = repoInvoiceHeaderField;
            _repoInvoiceDetail = repoInvoiceDetail;
            _repoInvoiceDetailExtra = repoInvoiceDetailExtra;
            _repoInvoiceDetailField = repoInvoiceDetailField;
            _repoInvoiceReference = repoInvoiceReference;
            _repoInvoiceDocumentInfo = repoInvoiceDocumentInfo;
            _repoTaxBreakdown = repoTaxBreakdown;
        }

        public async Task<GetInvoice01Response> Handle(GetInvoice01Query input, CancellationToken cancellationToken)
        {
            //Header
            var header = await _repoInvoiceHeader.GetByIdAsync(input.InvoiceId, input.TenantId);
            if (header == null)
                return null;

            var invoice = new GetInvoice01Response
            {
                Id = header.Id,
                ErpId = header.ErpId,
                TransactionId = header.TransactionId,
                InvoiceTemplateId = header.InvoiceTemplateId,
                TemplateNo = header.TemplateNo,
                SerialNo = header.SerialNo,
                InvoiceNo = header.InvoiceNo,
                Note = header.Note,
                InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(header.InvoiceStatus),
                SignStatus = EnumExtension.ToEnum<SignStatus>(header.SignStatus),
                InvoiceDate = header.InvoiceDate.ToLocalTime(),
                TotalAmount = header.TotalAmount,
                TotalVatAmount = header.TotalVatAmount,
                TotalDiscountAmountBeforeTax = header.TotalDiscountAmountBeforeTax,
                TotalDiscountPercentAfterTax = header.TotalDiscountPercentAfterTax,
                TotalDiscountAmountAfterTax = header.TotalDiscountAmountAfterTax,
                FromCurrency = header.FromCurrency,
                Currency = header.ToCurrency,
                RoundingCurrency = header.RoundingCurrency,
                CurrencyConversion = header.CurrencyConversion,
                ExchangeRate = header.ExchangeRate,
                PaymentMethod = header.PaymentMethod,
                PaymentDate = header.PaymentDate.Value.ToLocalTime(),
                PaymentAmountWords = header.PaymentAmountWords,
                PaymentAmountWordsEn = header.PaymentAmountWordsEn,
                TotalPaymentAmount = header.TotalPaymentAmount,
                BuyerCode = header.BuyerCode,
                BuyerId = header.BuyerId,
                BuyerErpId = header.BuyerErpId,
                BuyerEmail = header.BuyerEmail,
                BuyerFullName = header.BuyerFullName,
                BuyerLegalName = header.BuyerLegalName,
                BuyerTaxCode = header.BuyerTaxCode,
                BuyerAddressLine = header.BuyerAddressLine,
                BuyerDistrictName = header.BuyerDistrictName,
                BuyerCityName = header.BuyerCityName,
                BuyerCountryCode = header.BuyerCountryCode,
                BuyerPhoneNumber = header.BuyerPhoneNumber,
                BuyerFaxNumber = header.BuyerFaxNumber,
                BuyerBankName = header.BuyerBankName,
                BuyerBankAccount = header.BuyerBankAccount,
                SellerId = header.SellerId,
                SellerLegalName = header.SellerLegalName,
                SellerTaxCode = header.SellerTaxCode,
                SellerAddressLine = header.SellerAddressLine,
                SellerCountryCode = header.SellerCountryCode,
                SellerDistrictName = header.SellerDistrictName,
                SellerCityName = header.SellerCityName,
                SellerPhoneNumber = header.SellerPhoneNumber,
                SellerFaxNumber = header.SellerFaxNumber,
                SellerEmail = header.SellerEmail,
                SellerBankName = header.SellerBankName,
                SellerBankAccount = header.SellerBankAccount,
                SellerFullName = header.SellerFullName,
                SellerSignedTime = header.SellerSignedTime,
                SellerFullNameSigned = header.SellerFullNameSigned,
                SellerSignedId = header.SellerSignedId,
                PrintedTime = header.PrintedTime,
                FullNamePrinter = header.FullNamePrinter,
                PrintedId = header.PrintedId,
                //IdFileDocument = header.IdFileDocument,
                //DocumentDate = header.DocumentDate,
                //DocumentNo = header.DocumentNo,
                //DocumentReason = header.DocumentReason,
                //IdInvoiceReference = header.IdInvoiceReference,
                //TransactionData = header.TransactionData
            };

            //Detail
            await AddInvoiceDetails(invoice);

            //DetailExtra
            await AddInvoiceDetailExtras(header.TenantId, invoice);

            //HeaderExtra
            await AddInvoiceHeaderExtras(header.TenantId, invoice);

            //TaxBreakdown
            await AddInvoiceTaxBreakdowns(invoice);

            //FileDocument
            await AddDocument(header.TenantId, invoice);

            //Document name
            //await AddFileNameOfDocument(invoice);

            //InvoiceReference
            await AddInvoiceReference(invoice);

            return invoice;
        }

        private async Task AddDocument(Guid tenantId, GetInvoice01Response invoice)
        {
            var fileDocument = await _repoInvoiceDocumentInfo.GetByCodeInvoiceHeaderAsync(tenantId, invoice.Id);
            if (fileDocument != null)
            {
                invoice.IdFileDocument = fileDocument.FileId;
                invoice.DocumentDate = fileDocument.DocumentDate;
                invoice.DocumentNo = fileDocument.DocumentNo;
                invoice.DocumentReason = fileDocument.DocumentReason;
            }
        }

        //private async Task AddFileNameOfDocument(GetInvoice01Response invoice)
        //{
        //    var name = "";
        //    if (invoice.IdFileDocument.HasValue)
        //    {
        //        //lấy thông tin biên bản
        //        var repoFile = _invoice01UoW.GetRepository<IInvoiceMediaRepository<Invoice01Document>>();
        //        var fileOfRecord = await repoFile.GetByIdAsync(invoice.IdFileDocument.Value);
        //        if (fileOfRecord != null)
        //            name = fileOfRecord.FileName;
        //    }
        //    invoice.FileNameOfDocument = name;
        //}

        private async Task AddInvoiceTaxBreakdowns(GetInvoice01Response invoice)
        {
            var invoiceTaxbBreakDowns = await _repoTaxBreakdown.QueryByInvoiceHeaderCodeAsync(invoice.Id);

            invoice.InvoiceTaxBreakdowns = invoiceTaxbBreakDowns.Select(x => new GetInvoice01Response.GetInvoice01TaxBreakdownResponseModel
            {
                Id = x.Id,
                Name = x.Name,
                VatAmount = x.VatAmount,
                VatPercent = x.VatPercent,
                VatAmountBackUp = x.VatAmountBackUp
            }).ToList();
        }

        private async Task AddInvoiceHeaderExtras(Guid tenantId, GetInvoice01Response invoice)
        {
            //lấy các header field
            var headerFields = (await _repoInvoiceHeaderField.QueryByTenantCodeAsync(tenantId))
                                .ToDictionary(x => x.Id, x => x.FieldName);

            var invoiceHeaderExtras = await _repoInvoiceHeaderExtra.QueryByCodeInvoiceHeaderAsync(invoice.Id);
            invoice.InvoiceHeaderExtras = invoiceHeaderExtras.Select(x => new GetInvoice01Response.GetInvoice01HeaderExtraResponseModel
            {
                Id = x.Id,
                FieldName = headerFields[x.InvoiceHeaderFieldId],
                FieldValue = x.FieldValue,
            }).ToList();
        }

        private async Task AddInvoiceDetailExtras(Guid tenantId, GetInvoice01Response invoice)
        {
            if (invoice.InvoiceDetails.Any())
            {
                //lấy các detail field
                var detailFields = (await _repoInvoiceDetailField.QueryByTenantCodeAsync(tenantId))
                                    .ToDictionary(x => x.Id, x => x.FieldName);

                var invoiceDetailIds = invoice.InvoiceDetails.Select(x => x.Id).ToList();
                
                var detailExtras = (await _repoInvoiceDetailExtra.QueryByInvoiceDetailIdsAsync(invoiceDetailIds))
                    .GroupBy(x => x.InvoiceDetailId)
                    .ToDictionary(x => x.Key, x => x.ToList());

                foreach (var item in invoice.InvoiceDetails)
                {
                    item.InvoiceDetailExtras = new List<GetInvoice01Response.GetInvoice01DetailExtraResponseModel>();
                    if (!detailExtras.ContainsKey(item.Id))
                        continue;

                    item.InvoiceDetailExtras = detailExtras[item.Id].Select(x => new GetInvoice01Response.GetInvoice01DetailExtraResponseModel
                    {
                        Id = x.Id,
                        FieldValue = x.FieldValue,
                        FieldName = detailFields[x.InvoiceDetailFieldId]
                    }).ToList();
                }
            }
        }

        private async Task<List<Invoice01DetailEntity>> AddInvoiceDetails(GetInvoice01Response invoice)
        {
            var invoiceDetails = await _repoInvoiceDetail.QueryByCodeInvoiceHeaderAsync(invoice.Id);
            invoice.InvoiceDetails = invoiceDetails.Select(x => new GetInvoice01Response.GetInvoice01DetailResponseModel
            {
                Id = x.Id,
                Index = x.Index,
                DiscountAmountBeforeTax = x.DiscountAmountBeforeTax,
                DiscountPercentBeforeTax = x.DiscountPercentBeforeTax,
                PaymentAmount = x.PaymentAmount,
                ProductCode = x.ProductCode,
                ProductId = x.ProductId,
                ProductName = x.ProductName,
                UnitName = x.UnitName,
                UnitId = x.UnitId,
                UnitPrice = x.UnitPrice,
                RoundingUnit = x.RoundingUnit,
                Quantity = x.Quantity,
                Amount = x.Amount,
                VatPercent = x.VatPercent,
                VatAmount = x.VatAmount,
                Note = x.Note,
                HideQuantity = x.HideQuantity,
                HideUnit = x.HideUnit,
                HideUnitPrice = x.HideUnitPrice
            })
            .OrderBy(x => x.Index)
            .ToList();
            return invoiceDetails;
        }

        private async Task AddInvoiceReference(GetInvoice01Response invoice)
        {
            var invoiceReference = await _repoInvoiceReference.GetByCodeInvoiceHeaderAsync(invoice.Id);
            if (invoiceReference != null)
            {
                invoice.InvoiceReferenceId = invoiceReference.InvoiceReferenceId;
                invoice.TransactionData = $"{invoice.SellerTaxCode}|{invoiceReference.TemplateNoReference}|{invoiceReference.SerialNoReference}|{invoiceReference.InvoiceNoReference}|{invoiceReference.InvoiceDateReference:yyyy-MM-dd}";
                invoice.InvoiceDateReference = invoiceReference.InvoiceDateReference;
                invoice.Content = invoiceReference.Note;
            }
        }
    }
}
