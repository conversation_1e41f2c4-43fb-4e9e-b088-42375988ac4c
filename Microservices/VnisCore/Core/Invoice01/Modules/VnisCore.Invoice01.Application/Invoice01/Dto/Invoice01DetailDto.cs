using Core.Application.Dtos;
using System;
using System.Collections.Generic;

namespace VnisCore.Invoice01.Application.Invoice01.Dto
{
    public class Invoice01DetailDto : EntityDto<long>
    {
        public new long Id { get; set; }
        /// <summary>
        /// Thứ tự
        /// </summary>
        public int Index { get; set; }

        public long InvoiceHeaderId { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        public Guid TenantId { get; set; }

        #region Thông tin hàng hóa
        /// <summary>
        /// Id bản ghi hàng hóa
        /// </summary>
        public long ProductId { get; set; }

        /// <summary>
        /// Tính chất hàng hóa (Không phải nhóm hàng hóa)
        /// 1 - Hàng hóa, dịch vụ
        /// 2 - Khuyến mại
        /// 3 - Chiết khấu thương mại (Trong trường hợp muốn thể hiện thông tin chiết khấu theo dòng)
        /// 4 - <PERSON><PERSON> chú/diễn giải
        /// </summary>
        public short ProductType { get; set; }

        /// <summary>
        /// Mã hàng hóa
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// Tên hàng hóa
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Id bản ghi bảng Đơn vị tính (Unit)
        /// </summary>
        public long UnitId { get; set; }

        /// <summary>
        /// Tên Đơn vị tính
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Đơn giá hàng hóa
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal Quantity { get; set; }

        /// <summary>
        /// Tổng tiền trước thuế / trước chiết khấu
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// Tổng tiền sau thuế
        /// </summary>
        public decimal PaymentAmount { get; set; }
        #endregion

        public long Partition { get; set; }

        public int RoundingUnit { get; set; }

        /// <summary>
        /// Nếu là mặt hàng khuyến mãi thì set TRUE
        /// </summary>
        public bool IsPromotion { get; set; }

        /// <summary>
        /// Tiền chiết khấu
        /// </summary>
        public decimal DiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        public decimal DiscountPercentBeforeTax { get; set; }

        /// <summary>
        /// Phần trăm thuế
        /// </summary>
        public decimal VatPercent { get; set; }

        /// <summary>
        /// Giá trị hiển thị phần trăm thuế
        /// </summary>
        public string VatPercentDisplay { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal VatAmount { get; set; }

       

        /// <summary>
        /// có ấn số lương không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideQuantity { get; set; }

        /// <summary>
        /// có ấn đơn vị tính không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideUnit { get; set; }

        /// <summary>
        /// có ấn đơn giá không
        /// true: ẩn, false/null: hiện
        /// </summary>
        public bool HideUnitPrice { get; set; }

        public Dictionary<string, string> ExtraProperties { get; set; }
    }
}
