using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.Update
{
    /// <summary>
    /// Validate thuế suất đã tồn tại
    /// </summary>
    public class UpdateInvoiceCheckExistTaxRule : IValidationRuleAsync<UpdateInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly ITaxService _taxService;

        public UpdateInvoiceCheckExistTaxRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            ITaxService taxService
            )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _taxService = taxService;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice01HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataValidate");
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var taxes = _validationContext.GetOrAddItem("Taxes", () =>
            {
                //return await _taxService.GetTaxesAsync(tenantId);
                return dataValidate.Where(x => x.Type == ValidateDataType.Tax.GetHashCode()).ToDictionary(x => x.TaxValue, x => new Tuple<string, string>(x.TaxName, x.TaxDisplay));
            });

            if (taxes.Count == 0)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.TaxConfigNotExist"]);

            return new ValidationResult(true);
        }
    }
}
