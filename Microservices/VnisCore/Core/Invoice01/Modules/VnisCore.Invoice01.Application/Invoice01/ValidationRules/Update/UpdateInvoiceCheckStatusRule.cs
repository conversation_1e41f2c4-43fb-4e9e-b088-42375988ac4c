using System.Threading.Tasks;
using Core.Shared.Validations;
using Core.Shared.Constants;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Services;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using Core.Shared.Factory;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.Update
{
    /// <summary>
    /// validate trạng thái ký và trạng thái hóa đơn xem có được sửa hóa đơn không
    /// </summary>
    class UpdateInvoiceCheckStatusRule : IValidationRuleAsync<UpdateInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;

        public UpdateInvoiceCheckStatusRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _invoiceService = invoiceService;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice01HeaderDto input)
        {
            var invoice = _validationContext.GetItem<Invoice01HeaderEntity>("Invoice");

            //kiểm tra trạng thái hóa đơn
            if (invoice.InvoiceStatus != InvoiceStatus.Goc.GetHashCode())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.Update.CannotUpdateNonOriginalInvoice"]);

            //kiểm tra trạng thái ký
            if (invoice.SignStatus > SignStatus.ChoKy.GetHashCode())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CannotUpdateInvoice"]);

            var tenantId = _appFactory.CurrentTenant.Id.Value;

            if (await _invoiceService.HasApproveAsync(tenantId) && invoice.ApproveStatus == (short)ApproveStatus.DaDuyet)
            {
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CannotUpdateInvoiceApproveStatus"]);
            }

            return new ValidationResult(true);
        }
    }
}
