using Core.Domain.Repositories;
using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateReplace
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class UpdateReplaceInvoiceCheckHeaderExtraRule : IValidationRuleAsync<UpdateReplaceInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice01HeaderFieldEntity, long> _repoInvoice01HeaderField;
        private readonly IValidationContext _validationContext;

        public UpdateReplaceInvoiceCheckHeaderExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IRepository<Invoice01HeaderFieldEntity, long> repoInvoice01HeaderField,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoice01HeaderField = repoInvoice01HeaderField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice01HeaderDto input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataValidate");
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            //var headerFieldNames = await _repoInvoice01HeaderField.Where(x => x.TenantId == tenantId)
            //                                                      .Select(x => x.FieldName)
            //                                                      .AsQueryable()
            //                                                      .ToListAsync();

            var headerFieldNames = dataValidate.Where(x => x.Type == ValidateDataType.HeaderField.GetHashCode()).Select(x => x.FieldName).ToList();
            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.HeaderExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
