using Core.Domain.Repositories;
using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateReplace
{
    public class CreateReplaceInvoiceCheckPendingLastInvoiceDateRule : IValidationRuleAsync<CreateReplaceInvoice01Dto, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public CreateReplaceInvoiceCheckPendingLastInvoiceDateRule(
            IAppFactory appFactory, 
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext
            )
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice01Dto input)
        {
            var repoMonitor = _appFactory.Repository<MonitorInvoiceTemplateEntity, long>();

            var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");

            var monitor = await repoMonitor.FirstOrDefaultAsync(x => x.Id == template.TemplateId);

            if (input.InvoiceDate.Date < monitor.PendingLastInvoiceDate?.Date)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.PendingLastInvoiceDate", monitor.PendingLastInvoiceDate]);

            monitor.PendingLastInvoiceDate = input.InvoiceDate.Date;
            monitor.PendingInvoiceStatus = 2;

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            return new ValidationResult(true);
        }
    }
}
