using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using Core.Domain.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;
using Core.Dto.Shared;
using System.Collections.Generic;
using System.Linq;
using Core.Shared.Constants;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.Update
{
    /// <summary>
    /// validate tiền tệ
    /// </summary>
    public class UpdateInvoiceCheckExistCurrencyRule : IValidationRuleAsync<UpdateInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<CurrencyEntity, long> _repoCurrencyEntity;

        public UpdateInvoiceCheckExistCurrencyRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IRepository<CurrencyEntity, long> repoCurrencyEntity
            )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoCurrencyEntity = repoCurrencyEntity;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice01HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataValidate");
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //Nguyên tệ
            //var fromCurrency = await _repoCurrencyEntity.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.IsDefault && !x.IsDeleted);
            var fromCurrency = dataValidate.FirstOrDefault(x => x.Type == ValidateDataType.FromCurrency.GetHashCode());
            if (fromCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.FromCurrencyNotSet"]);

            _validationContext.AddItem("FromCurrency", new CurrencyEntity
            {
                CurrencyCode = fromCurrency.FromCurrencyCode,
                Rounding = fromCurrency.ToCurrencyRounding,
                NameVi = fromCurrency.NameVi,
                MinimumNameVi = fromCurrency.MinimumNameVi,
            });

            //Tiền tệ
            //var toCurrency = await _repoCurrencyEntity.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.CurrencyCode == input.ToCurrency && !x.IsDeleted);
            var toCurrency = dataValidate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
            if (toCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ToCurrencyIncorrect"]);

            _validationContext.AddItem("ToCurrency", new CurrencyEntity
            {
                CurrencyCode = toCurrency.ToCurrencyCode,
                Conversion = toCurrency.ToCurrencyConversion,
                Rounding = toCurrency.ToCurrencyRounding,
                NameVi = toCurrency.NameVi,
                MinimumNameVi = toCurrency.MinimumNameVi,
            });

            return new ValidationResult(true);
        }
    }
}
