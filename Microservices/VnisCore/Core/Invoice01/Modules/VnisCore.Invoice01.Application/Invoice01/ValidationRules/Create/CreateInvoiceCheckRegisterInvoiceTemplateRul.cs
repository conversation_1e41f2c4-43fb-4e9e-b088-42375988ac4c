using Core.Shared.Validations;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using Core.Domain.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Invoice01.Application.Factories.Services;
using Core;
using Core.Dto.Shared;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.Create
{
    /// <summary>
    /// Kiểm tra đăng ký phát hành có mã không mã
    /// </summary>
    public class CreateInvoiceCheckRegisterInvoiceTemplateRul : IValidationRuleAsync<CreateInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly ICommonInvoice01Service _commonInvoice01Service;

        public CreateInvoiceCheckRegisterInvoiceTemplateRul(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            ICommonInvoice01Service commonInvoice01Service)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _commonInvoice01Service = commonInvoice01Service;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice01HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
            var registrations = await _commonInvoice01Service.GetRegisterAvailabilities(tenantId, userId, input.TemplateNo);

            if (!registrations.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.NoRegisterInvoiceTemplate"]);

            var idsRegistration = registrations.Select(x => x.Id).ToList();

            if (!idsRegistration.Contains(template.TemplateId))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceTemplateNotRegister"]);
            return new ValidationResult(true);
        }
    }
}
