using System.Threading.Tasks;
using Core.Shared.Validations;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;
using System.Linq;
using System.Collections.Generic;
using VnisCore.Invoice01.Infrastructure.IRepository;
using IInvoice01HeaderRepository = VnisCore.Invoice01.Infrastructure.IRepository.IInvoice01HeaderRepository;
using Core.Tvan.Constants;
using System;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateAdjustmentHeader
{
    /// <summary>
    /// kiểm tra điều kiện của hóa đơn GỐC có đủ điều kiện để bị điều chỉnh định danh không
    /// </summary>
    public class CreateInvoiceAdjustCheckReferenceInvoiceRule : IValidationRuleAsync<CreateAdjustInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IInvoice01HeaderRepository _repoInvoiceHeader;
        private readonly IInvoice01ReferenceRepository _invoice01ReferenceRepository;
        private readonly Factories.Services.IInvoice01Service _invoice01Service;
        private readonly IInvoice01ErrorRepository _invoice01ErrorRepository;
        private readonly IInvoice01DetailRepository _invoice01DetailRepository;

        public CreateInvoiceAdjustCheckReferenceInvoiceRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IInvoice01HeaderRepository repoInvoiceHeader,
            IInvoice01ReferenceRepository invoice01ReferenceRepository,
            Factories.Services.IInvoice01Service invoice01Service,
            IInvoice01ErrorRepository invoice01ErrorRepository,
            IInvoice01DetailRepository invoice01DetailRepository
            )
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoInvoiceHeader = repoInvoiceHeader;
            _invoice01ReferenceRepository = invoice01ReferenceRepository;
            _invoice01Service = invoice01Service;
            _invoice01ErrorRepository = invoice01ErrorRepository;
            _invoice01DetailRepository = invoice01DetailRepository;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustInvoice01HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var rootInvoice = await _repoInvoiceHeader.GetByIdAsync(input.InvoiceReferenceId, tenantId);

            if (rootInvoice == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.OriginalInvoiceNotFound"]);

            if (rootInvoice.TenantId != tenantId)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustHeader.CannotAdjustInvoiceForOtherTenant"]);

            var signStatus = EnumExtension.ToEnum<SignStatus>(rootInvoice.SignStatus).GetName();
            var approveStatus = EnumExtension.ToEnum<ApproveStatus>(rootInvoice.ApproveStatus).GetName();

            //check điều kiện hóa đơn bị thay thế có phải là hóa đơn gốc không, nếu không thì không được thay thế/điều chỉnh
            if (rootInvoice.InvoiceStatus != InvoiceStatus.Goc.GetHashCode() 
                && rootInvoice.InvoiceStatus != InvoiceStatus.BiDieuChinh.GetHashCode() 
                && rootInvoice.InvoiceStatus != InvoiceStatus.BiDieuChinhTangGiam.GetHashCode() 
                && rootInvoice.InvoiceStatus != InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode())
            {
                return new ValidationResult(false, $"Hóa đơn sai sót cần điều chỉnh phải ở trạng thái: Gốc, Bị Điều chỉnh, Bị Điều chỉnh định danh hoặc Bị Điều chỉnh tăng giảm. Vui lòng kiểm tra lại");
            } 

            if (rootInvoice.SignStatus != SignStatus.DaKy.GetHashCode())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustHeader.CannotAdjustUnsignedInvoice"]);

            // hóa đơn đã ký mà có trạng thái chờ duyệt => chờ duyệt xóa bỏ ko thể điều chỉnh
            if (rootInvoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.CreateAdjustHeader.CannotAdjustApproveDeleteInvoice"]);

            if (rootInvoice.ApproveDeleteStatus == ApproveStatus.ChoDuyet.GetHashCode())
                return new ValidationResult(false, $"Hóa đơn sai sót số {rootInvoice.InvoiceNo}, ký hiệu {rootInvoice.SerialNo} đang chờ duyệt xóa bỏ. Vui lòng kiểm tra lại");

            if (rootInvoice.ApproveDeleteStatus == ApproveStatus.DaDuyet.GetHashCode())
                return new ValidationResult(false, $"Hóa đơn sai sót số {rootInvoice.InvoiceNo}, ký hiệu {rootInvoice.SerialNo} đã bị xóa bỏ. Vui lòng kiểm tra lại");

            if (input.SerialNo.Substring(0, 1) == "C")
            {
                if (rootInvoice.VerificationCode == null)
                {
                    // TH HD gốc chưa được câp mã
                    return new ValidationResult(false, $"Hóa đơn sai sót số {rootInvoice.InvoiceNo}, ký hiệu {rootInvoice.SerialNo} chưa được cấp mã. Vui lòng kiểm tra lại");
                }
            }

            if(input.InvoiceDate.Date < rootInvoice.InvoiceDate.Date)
            {
                return new ValidationResult(false, "Ngày hóa đơn không được nhỏ hơn ngày hóa đơn sai sót");
            }

            if(rootInvoice.InvoiceStatus == InvoiceStatus.BiDieuChinh.GetHashCode() || rootInvoice.InvoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode() || rootInvoice.InvoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode())
            {
                // Nếu có quy trình duyệt, HĐ sai sót gốc cần đảm bảo không có HĐ điều chỉnh đã lập ở trạng thái chờ duyệt / Chờ ký / Ký lỗi(Tham chiếu logic thông báo lỗi tại màn hình DS hóa đơn)
                // Lấy các hóa đơn điều chỉnh
                var referenceInfos = await _invoice01ReferenceRepository.getInvoiceReferenceById(rootInvoice.Id, tenantId);

                var referenceInvoices = await _repoInvoiceHeader.getInvoiceByIds(referenceInfos.Select(x => x.InvoiceReferenceId).ToList(), tenantId);

                if (referenceInvoices.Where(x=>x.SignStatus != (short)SignStatus.DaKy.GetHashCode()).Any())
                {
                    return new ValidationResult(false, $"Hóa đơn sai sót số {rootInvoice.InvoiceNo}, ký hiệu {rootInvoice.SerialNo} đã tạo hóa đơn điều chỉnh chưa được ký số. Vui lòng kiểm tra lại");
                }

                if (input.SerialNo.Substring(0, 1) == "C")
                {
                    // Lấy các hóa đơn liên quan chưa được cấp mã và Bị Từ chối
                    var unSentReferenceInvoices = referenceInvoices.Where(invoice => !(invoice.StatusTvan == TvanStatus.TCTReject.GetHashCode() || 
                                        (invoice.StatusTvan == TvanStatus.TCTAccept.GetHashCode() && !invoice.VerificationCode.IsNullOrEmpty()))).ToList();
                    if (unSentReferenceInvoices.Any())
                    {
                        // TH HD liên quan chưa được câp mã
                        return new ValidationResult(false, $"Hóa đơn đã lập hóa đơn điều chỉnh chưa nhận được phản hồi của CQT. Vui lòng kiểm tra lại");
                    }
                }

                if(referenceInvoices.Where(x=>x.InvoiceDate.Date > input.InvoiceDate).Any())
                {
                    var lastAdjustInvoice = referenceInvoices.Where(x => x.InvoiceDate > input.InvoiceDate).OrderBy(x => x.Id).FirstOrDefault();
                    return new ValidationResult(false, $"Ngày hóa đơn phải không được nhỏ hơn ngày hóa đơn điều chỉnh cuối cùng (là ngày {lastAdjustInvoice.InvoiceDate.ToString("dd/MM/yyyy")}) có số {rootInvoice.InvoiceNo}, ký hiệu {rootInvoice.SerialNo}. Vui lòng kiểm tra lại");
                }

                #region Kiểm tra hóa đơn điều chỉnh các giá trị số lượng, thành tiền 
                // kiểm tra index của hd điều chỉnh có thuộc index của hd gốc không
                if (!input.InvoiceDetails.IsNullOrEmpty())
                {
                    // Từ hóa đơn gốc lấy DS các hóa đơn liên quan
                    var referenceInvoiceIds = referenceInfos.Select(x => x.InvoiceReferenceId).ToList();
                    // Loại bỏ các hóa đơn TCT từ chối
                    var unAcceptInvoiceIds = referenceInvoices.Where(x => x.StatusTvan == TvanStatus.TCTReject.GetHashCode()).Select(x => x.Id);
                    if (unAcceptInvoiceIds.Any())
                    {
                        referenceInvoiceIds.RemoveAll(unAcceptInvoiceIds);
                        referenceInvoices = referenceInvoices.Where(x => x.StatusTvan != TvanStatus.TCTReject.GetHashCode()).ToList();
                    }

                    if (referenceInvoices.Where(x => x.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()).Any())
                    {
                        // Không lấy detail của HD điều chỉnh định danh
                        var dinhDanhIds = referenceInvoices.Where(x => x.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()).Select(x => x.Id);
                        referenceInvoiceIds.RemoveAll(dinhDanhIds);
                    }
                    referenceInvoiceIds.Add(rootInvoice.Id);
                    var invoiceDetails = await _invoice01DetailRepository.getInvoiceByIds(referenceInvoiceIds, tenantId);

                    var rootInvoiceDetails = invoiceDetails.Where(x => x.InvoiceHeaderId == rootInvoice.Id);
                    var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);

                    foreach (var detail in input.InvoiceDetails)
                    {
                        if (!indexes.ContainsKey(detail.Index))
                            throw new Exception(_localizer["Vnis.BE.Invoice01.IndexInvoiceDetailIncorrect"]);

                        // Kiểm tra Số lượng, Đơn giá, Tỷ lệ chiết khấu, Số tiền CK, Thành tiền, Số tiền thuế
                        // Giá trị hiện tại = giá trị gốc + các lần điều chỉnh tăng - các lần điều chỉnh giảm
                        var indexDetails = invoiceDetails.Where(x => x.Index == detail.Index);
                        if (detail.Quantity < 0 && Math.Abs(detail.Quantity) > indexDetails.Sum(x => x.Quantity))
                        {
                            return new ValidationResult(false, $"Số lượng đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.Quantity)}");
                        }

                        if (detail.UnitPrice < 0 && Math.Abs(detail.UnitPrice) > indexDetails.Sum(x => x.UnitPrice))
                        {
                            return new ValidationResult(false, $"Đơn giá đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.UnitPrice)}");
                        }

                        if (detail.DiscountAmountBeforeTax < 0 && Math.Abs(detail.DiscountAmountBeforeTax) > indexDetails.Sum(x => x.DiscountAmountBeforeTax))
                        {
                            return new ValidationResult(false, $"Tiền chiết khấu đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.DiscountAmountBeforeTax)}");
                        }

                        if (detail.Amount < 0 && Math.Abs(detail.Amount) > indexDetails.Sum(x => x.Amount))
                        {
                            return new ValidationResult(false, $"Thành tiền đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.Amount)}");
                        }

                        if (detail.VatAmount < 0 && Math.Abs(detail.VatAmount) > indexDetails.Sum(x => x.VatAmount))
                        {
                            return new ValidationResult(false, $"Tiền thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.VatAmount)}");
                        }
                    }

                }
                #endregion

                #region Kiểm tra tổng tiền hóa đơn
                if (!referenceInvoices.Where(x => x.Id == rootInvoice.Id).Any())
                {
                    referenceInvoices.Add(rootInvoice);
                }
                var hoaDonLienQuanKhacDinhDanh = referenceInvoices.Where(x => x.InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode()).ToList();
                if (input.TotalPaymentAmount < 0 && Math.Abs(input.TotalPaymentAmount) > Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalPaymentAmount)))
                {
                    return new ValidationResult(false, $"Tổng tiền phải trả đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalPaymentAmount))}");
                }

                if (input.TotalAmount < 0 && Math.Abs(input.TotalAmount) > Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalAmount)))
                {
                    return new ValidationResult(false, $"Tổng tiền hàng trước thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalAmount))}");
                }

                if (input.TotalVatAmount < 0 && Math.Abs(input.TotalVatAmount) > Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalVatAmount)))
                {
                    return new ValidationResult(false, $"Tổng tiền thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalVatAmount))}");
                }

                if (input.TotalDiscountAmountBeforeTax < 0 && Math.Abs(input.TotalDiscountAmountBeforeTax) > Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalDiscountAmountBeforeTax)))
                {
                    return new ValidationResult(false, $"Tổng tiền chiết khấu trước thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(hoaDonLienQuanKhacDinhDanh.Sum(x => x.TotalDiscountAmountBeforeTax))}");
                }
                #endregion
            }
            else if (rootInvoice.InvoiceStatus == InvoiceStatus.Goc.GetHashCode())
            {
                #region Kiểm tra hóa đơn điều chỉnh các giá trị số lượng, thành tiền 
                // kiểm tra index của hd điều chỉnh có thuộc index của hd gốc không
                if (!input.InvoiceDetails.IsNullOrEmpty())
                {
                    // Từ hóa đơn gốc lấy DS các hóa đơn liên quan
                    var referenceInvoiceIds = new List<long>() { rootInvoice.Id };
                    var invoiceDetails = await _invoice01DetailRepository.getInvoiceByIds(referenceInvoiceIds, tenantId);

                    var rootInvoiceDetails = invoiceDetails.Where(x => x.InvoiceHeaderId == rootInvoice.Id);
                    var indexes = rootInvoiceDetails.ToDictionary(x => x.Index, x => x);
                    foreach (var detail in input.InvoiceDetails)
                    {
                        if (!indexes.ContainsKey(detail.Index))
                            return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.IndexInvoiceDetailIncorrect"]);

                        // Kiểm tra Số lượng, Đơn giá, Tỷ lệ chiết khấu, Số tiền CK, Thành tiền, Số tiền thuế
                        // Giá trị hiện tại = giá trị gốc + các lần điều chỉnh tăng - các lần điều chỉnh giảm
                        var indexDetails = invoiceDetails.Where(x => x.Index == detail.Index);
                        if (detail.Quantity < 0 && Math.Abs(detail.Quantity) > indexDetails.Sum(x => x.Quantity))
                        {
                            return new ValidationResult(false, $"Số lượng đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.Quantity)}");
                        }

                        if (detail.UnitPrice < 0 && Math.Abs(detail.UnitPrice) > indexDetails.Sum(x => x.UnitPrice))
                        {
                            return new ValidationResult(false, $"Đơn giá đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.UnitPrice)}");
                        }

                        if (detail.DiscountAmountBeforeTax < 0 && Math.Abs(detail.DiscountAmountBeforeTax) > indexDetails.Sum(x => x.DiscountAmountBeforeTax))
                        {
                            return new ValidationResult(false, $"Tiền chiết khấu đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.DiscountAmountBeforeTax)}");
                        }

                        if (detail.Amount < 0 && Math.Abs(detail.Amount) > indexDetails.Sum(x => x.Amount))
                        {
                            return new ValidationResult(false, $"Thành tiền đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.Amount)}");
                        }

                        if (detail.VatAmount < 0 && Math.Abs(detail.VatAmount) > indexDetails.Sum(x => x.VatAmount))
                        {
                            return new ValidationResult(false, $"Tiền thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-indexDetails.Sum(x => x.VatAmount)}");
                        }
                    }

                }
                #endregion

                #region Kiểm tra tổng tiền hóa đơn
                if (input.TotalPaymentAmount < 0 && Math.Abs(input.TotalPaymentAmount) > Math.Abs(rootInvoice.TotalPaymentAmount))
                {
                    return new ValidationResult(false, $"Tổng tiền phải trả đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(rootInvoice.TotalPaymentAmount)}");
                }

                if (input.TotalAmount < 0 && Math.Abs(input.TotalAmount) > Math.Abs(rootInvoice.TotalAmount))
                {
                    return new ValidationResult(false, $"Tổng tiền hàng trước thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(rootInvoice.TotalAmount)}");
                }

                if (input.TotalVatAmount < 0 && Math.Abs(input.TotalVatAmount) > Math.Abs(rootInvoice.TotalVatAmount))
                {
                    return new ValidationResult(false, $"Tổng tiền thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(rootInvoice.TotalVatAmount)}");
                }

                if (input.TotalDiscountAmountBeforeTax < 0 && Math.Abs(input.TotalDiscountAmountBeforeTax) > Math.Abs(rootInvoice.TotalDiscountAmountBeforeTax))
                {
                    return new ValidationResult(false, $"Tổng tiền chiết khấu trước thuế đang điều chỉnh giảm vượt quá giá trị cho phép. Giá trị giảm tối đa: {-Math.Abs(rootInvoice.TotalDiscountAmountBeforeTax)}");
                }
                #endregion
            }

            //// Kiểm tra TBSS
            //var invoiceError = await _invoice01ErrorRepository.GetInvoiceByInvoiceHeader(input.InvoiceReferenceId, (short) VnisType._01GTKT.GetHashCode(), tenantId);
            //if(invoiceError != null)
            //{
            //    // Kiểm tra trạng thái của TBSS
            //    // TH TBSS bị TCT từ chối thì vẫn có thể tạo HD điều chỉnh
            //    // -2: Tuong duong TCT từ chỗi
            //    // 2: Tuong duong TCT chap nhan
            //    if (invoiceError.Action == TThaiTBao.Huy.GetHashCode() && invoiceError.TvanStatus != TvanStatus.TvanReject.GetHashCode())
            //    {
            //        return new ValidationResult(false, "Hóa đơn đã thêm vào thông báo sai sót với tính chất Hủy. Vui lòng kiểm tra lại");
            //    }
            //}

            _validationContext.GetOrAddItem("InvoiceReference", () =>
            {
                return rootInvoice;
            });

            var dataValidate = await _invoice01Service.CreateInvoice01GetDataValid(tenantId, new List<KeyValuePair<short, string>> { new KeyValuePair<short, string>(input.TemplateNo, input.SerialNo) }, new List<string> { rootInvoice.ToCurrency }, null, null);

            _validationContext.GetOrAddItem("DataValidate", () =>
            {
                return dataValidate;
            });

            return new ValidationResult(true);
        }
    }
}
