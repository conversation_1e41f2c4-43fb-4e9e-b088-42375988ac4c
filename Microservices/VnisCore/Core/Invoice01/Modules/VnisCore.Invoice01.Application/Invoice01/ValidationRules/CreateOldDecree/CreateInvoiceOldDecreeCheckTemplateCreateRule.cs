using Core.Shared.Validations;
using System.Linq;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using Core.Domain.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;
using Core.Dto.Shared;
using System.Collections.Generic;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateOldDecree
{
    /// <summary>
    /// kiểm tra mẫu của hóa đơn tài khoản hiện tại được phân quyền tạo không
    /// </summary>
    public class CreateInvoiceOldDecreeCheckTemplateCreateRule : IValidationRule<CreateInvoice01HeaderOldDecreeDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        //private readonly IRepository<AccountTokenTemplateEntity> _repoAccountTokenTemplate;

        public CreateInvoiceOldDecreeCheckTemplateCreateRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext)
            //IRepository<AccountTokenTemplateEntity> repoAccountTokenTemplate)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            //_repoAccountTokenTemplate = repoAccountTokenTemplate;
        }

        public ValidationResult Handle(CreateInvoice01HeaderOldDecreeDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var userId = _validationContext.GetItem<Guid>("UserId");
            var template = _validationContext.GetItem<CreateInvoice01GetDataValidDto>("Template");
            var dataValidate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataValidate");

            //var permissions = _repoAccountTokenTemplate.Where(x => x.UserId == userId && x.TemplateId.HasValue).ToList();
            var permissions = dataValidate.Where(x => x.TemplateId == template.TemplateId && x.UserId.HasValue && x.UserId == userId);

            //if (!permissions.Any(x => x.TemplateId == template.Id))
            if (!permissions.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.AccountNotPermissionForCreateTemplateNo"]);

            return new ValidationResult(true);
        }
    }
}
