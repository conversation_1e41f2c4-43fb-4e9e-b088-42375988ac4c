using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.UpdateAdjustmentDetail
{
    public class UpdateAdjustmentDetailInvoicePreProcess : IValidationRuleAsync<UpdateAdjustmentDetailInvoice01Request, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice01HeaderEntity> _repoInvoice01Header;

        public UpdateAdjustmentDetailInvoicePreProcess(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext,
            IRepository<Invoice01HeaderEntity> repoInvoice01Header,
            IAppFactory appFactory)
        {
            _localizer = localizer;
            _validationContext = validationContext;
            _repoInvoice01Header = repoInvoice01Header;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentDetailInvoice01Request input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });

            _validationContext.GetOrAddItem("UserCode", () =>
            {
                return userId;
            });

            var invoice = await _repoInvoice01Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == input.TemplateNo && x.SerialNo == input.SerialNo && x.InvoiceNo == input.InvoiceNo);
            if (invoice == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.InvoiceNotFound"]);

            _validationContext.GetOrAddItem("Invoice", () =>
            {
                return invoice;
            });

            return new ValidationResult(true);
        }
    }
}
