using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Factories.Repositories;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Invoice01.ValidationRule.CreateReplace
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class CreateReplaceInvoiceCheckHeaderExtraRule : IValidationRuleAsync<CreateReplaceInvoice01Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        //private readonly IInvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity> _repoInvoiceHeaderField;
        private readonly IValidationContext _validationContext;

        public CreateReplaceInvoiceCheckHeaderExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            //IInvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity> repoInvoiceHeaderField,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            //_repoInvoiceHeaderField = repoInvoiceHeaderField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice01Dto input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataValidate");
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            //var headerFieldNames = await _repoInvoiceHeaderField.GetFieldNameByTenantCodeAsync(tenantId);

            var headerFieldNames = dataValidate.Where(x => x.Type == ValidateDataType.HeaderField.GetHashCode()).Select(x => x.FieldName).ToList();

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.HeaderExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
