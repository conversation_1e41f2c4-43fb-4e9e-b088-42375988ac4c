using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using VnisCore.Invoice01.Application.Factories.Models;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Queries;
using VnisCore.Invoice01.Application.Invoice01.Models.Responses.Queries;
using Core.Application.Services;
using Core.Application.Dtos;
using VnisCore.Invoice01.Application.Invoice01.Models.Responses.Commands;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Invoice01.Application.Invoice01
{
    public interface IInvoice01Service : IApplicationService
    {
        Task<PagedResultDto<PagingInvoice01Response>> GetListAsync(PagingInvoice01Request input);

        Task<GetInvoice01Response> GetByIdAsync([FromRoute] long id);

        Task<GetInfomationsForCreateInvoiceResponse> GetInfomationsForCreateInvoice(GetInfomationsForCreateInvoiceRequest input);

        Task<PagedResultDto<PagingCustomerResponse>> GetCustomers(PagingCustomerRequest input);

        Task<PagedResultDto<PagingProductResponse>> GetProducts(PagingProductRequest input);

        Task<CreateInvoice01Response> CreateAsync([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, CreateInvoice01HeaderDto input);

        Task<CreateInvoice01ByGroupCustomerResponse> CreateByGroupCustomer([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateInvoice01ByGroupCustomerDto input);

        Task<CreateReplaceInvoice01Response> CreateReplace([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateReplaceInvoice01Dto input);

        Task<CreateInvoice01AdjustmentHeaderResponse> CreateAdjustmentHeader([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateAdjustmentHeaderInvoice01HeaderDto input);

        Task<CreateInvoice01AdjustmentDetailResponse> CreateAdjustmentDetail([FromQuery] CreateInvoice01InfoRequestModel infoRequestModel, [FromBody] CreateInvoice01AdjustmentDetailRequest input);

        Task<UpdateInvoice01Response> UpdateAsync([FromQuery] UpdateInvoice01InfoRequestModel infoRequestModel, UpdateInvoice01HeaderDto input);

        Task<UpdateReplaceInvoice01Response> UpdateReplace([FromQuery] UpdateInvoice01InfoRequestModel infoRequestModel, [FromBody] UpdateReplaceInvoice01HeaderDto input);

        Task<UpdateAdjustmentHeaderInvoice01Response> UpdateAdjustmnetHeader([FromQuery] UpdateInvoice01InfoRequestModel infoRequestModel, [FromBody] UpdateAdjustmentHeaderInvoice01Dto input);

        Task<UpdateAdjustmentDetailInvoice01Response> UpdateAdjustmentDetail([FromQuery] UpdateInvoice01InfoRequestModel infoRequestModel, [FromBody] UpdateAdjustmentDetailInvoice01Request input);

        Task<CancelInvoice01Response> Cancel(CancelInvoice01HeaderDto input);

        Task<DeleteInvoice01Response> Delete(DeleteInvoice01HeaderDto input);

        //Task<GetCustomerModel> CustomerAsync(string buyerCode);
        Task<GetCustomerModel> GetCustomerEmailAsync([FromBody] string buyerCode);

        /// <summary>
        /// send mail hóa đơn
        /// </summary>
        /// <param name="invoiceHeader"></param>
        /// <param name="mail"></param>
        /// <returns></returns>
        Task<SendMailInvoice01ResponseModel> SendMailAsync(Invoice01HeaderEntity invoiceHeader, string mail, string printAction = null);
    }
}
