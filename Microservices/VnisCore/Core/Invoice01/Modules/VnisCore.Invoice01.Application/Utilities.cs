using Core.SettingManagement;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.TenantManagement;
using System;

namespace VnisCore.Invoice01.Application
{
    public static class Utilities
    {
        public const string ApiUrlBase = "api/Invoice01/[controller]/";
        public const string ApiUrlActionBase = "api/Invoice01/[controller]/[action]";

        public static TenantInfo TenantInfo = new TenantInfo
        {
            Id = Guid.Parse("62b896a6-9db4-e146-863b-208987d27dd9"),
            Address = "88 Láng Hạ",
            FullNameVi = "Vnis",
            HostName = "dev.com",
            Name = "Vnis",
            City = "Hn",
            Country = "Việt Nam",
            District = "Đống Đa",
            Emails = "",
            FullNameEn = "Vnis",
            LegalName = "Vnis",
            TaxCode = "0101352495-999",
        };

        public static Guid CreatorId = Guid.Parse("3FA85F64-5717-4562-B3FC-2C963F66AFA6");
        public static Guid TenantId = Guid.Parse("62b896a6-9db4-e146-863b-208987d27dd9");
        
        public static Setting SettingShowHeaderExtra01 = new Setting(SettingKey.ShowHeaderExtra01.ToString(), GroupSettingKey.Invoice.ToString(), SettingKey.ShowHeaderExtra01.ToDisplayName(), "0");
        public static Setting SettingReadMoneyAdjDetail = new Setting(SettingKey.ReadMoneyAdjDetail.ToString(), GroupSettingKey.Invoice.ToString(), SettingKey.ReadMoneyAdjDetail.ToDisplayName(), "1");
        public static Setting SettingInvoiceHasApprove = new Setting(SettingKey.InvoiceHasApprove.ToString(), GroupSettingKey.Invoice.ToString(), SettingKey.InvoiceHasApprove.ToDisplayName(), "1");
        public static Setting SettingPaymentMethod = new Setting(SettingKey.PaymentMethod.ToString(), GroupSettingKey.Invoice.ToString(), SettingKey.PaymentMethod.ToDisplayName(), "Tiền mặt");
    }

    public class TenantInfo
    {
        public Guid Id { get; set; }

        public string Code { get; set; }

        public string Address { get; set; }

        public string FullNameVi { get; set; }

        public string FullNameEn { get; set; }

        public string HostName { get; set; }

        public string Name { get; set; }

        public string City { get; set; }

        public string Country { get; set; }

        public string District { get; set; }

        public string Emails { get; set; }

        public string LegalName { get; set; }

        public string TaxCode { get; set; }

        public string Metadata { get; set; }

        public string Phone { get; set; }

        public string Fax { get; set; }

        public string BankName { get; set; }

        public string BankAccount { get; set; }
    }
}
