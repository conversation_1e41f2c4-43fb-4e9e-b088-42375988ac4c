using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Invoice01.Application.Tbss.Dto
{
    public class CreateTbssDto : TbssHeaderDto
    {
        /// <summary>
        /// thông tin các hóa đơn sai sót
        /// </summary>
        [Required(ErrorMessage = "Thông tin các hóa đơn sai sót không được để trống")]
        [MinLength(1, ErrorMessage = "Thông tin các hóa đơn sai sót phải có ít nhất 1 hóa đơn")]
        public List<TbssDetailDto> TbssDetailDto { get; set; }
    }
}
