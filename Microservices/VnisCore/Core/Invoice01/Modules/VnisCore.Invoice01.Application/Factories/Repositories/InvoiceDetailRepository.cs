using Core.Domain.Repositories;
using Core.ObjectMapping;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01.Dto;

namespace VnisCore.Invoice01.Application.Factories.Repositories
{
    public class InvoiceDetailRepository<TDetail> : IInvoiceDetailRepository<TDetail>
       where TDetail : BaseInvoiceDetail
    {
        private readonly IRepository<TDetail, long> _repoDetail;
        private readonly IAppFactory _appFactory;
        protected IObjectMapper _objectMapper { get; }

        public InvoiceDetailRepository(IRepository<TDetail, long> repoDetail,
            IAppFactory appFactory,
            IObjectMapper objectMapper)
        {
            _repoDetail = repoDetail;
            _appFactory = appFactory;
            _objectMapper = objectMapper;
        }

        public async Task<List<TDetail>> QueryByCodeInvoiceHeaderAsync(long id)
        {
            return await _repoDetail
                .Where(x => x.InvoiceHeaderId == id)
                .ToListAsync();
        }

        public async Task<List<Invoice01DetailEntity>> GetInvoiceDetailRawAsync(Guid tenantId, long IdRootInvoice)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var query = $"Select * from \"Invoice01Detail\" where \"TenantId\" = '{rawTenantId}' and \"InvoiceHeaderId\" = {IdRootInvoice}";
            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01DetailDto>(query);
            var details = result.Select(x => _objectMapper.Map<Invoice01DetailDto, Invoice01DetailEntity>(x)).ToList();

            return details;
        }

    }
}
