using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core.Domain.Repositories;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Invoice01.Application.Factories.Repositories
{
    public interface IInvoiceDetailFieldRepository<T> where T : BaseInvoiceDetailField
    {

        /// <summary>
        /// lấy tất cả các detail field trong 1 cty
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <returns></returns>
        Task<List<T>> QueryByTenantCodeAsync(Guid tenantCode);

        Task<List<Invoice01DetailFieldEntity>> QueryByTenantCodeRawAsync(Guid tenantId);
    }
}
