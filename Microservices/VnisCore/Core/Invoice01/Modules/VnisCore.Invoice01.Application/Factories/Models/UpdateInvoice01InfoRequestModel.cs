using Core.Shared.Attributes;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Invoice01.Application.Factories.Models
{
    public class UpdateInvoice01InfoRequestModel
    {
        /// <summary>
        /// Mẫu số của hóa đơn 
        /// </summary>
        [Required(ErrorMessage = "Mẫu số hóa đơn không được để trống")]
        //[TemplateNo(ErrorMessage = "Định dạng mẫu số hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn 
        /// </summary>
        [Required(ErrorMessage = "Ký hiệu hóa đơn không được để trống")]
        [SerialNo(ErrorMessage = "Ký hiệu hóa đơn không bao gồm các ký tự O, J, <PERSON>, <PERSON> và phần số gồm 2 chữ số")]
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn 
        /// </summary>
        [Required(ErrorMessage = "Số hóa đơn của hóa đơn không được để trống")]
        [InvoiceNo(ErrorMessage = "Số hóa đơn của hóa đơn không hợp lệ")]
        public string InvoiceNo { get; set; }
    }
}
