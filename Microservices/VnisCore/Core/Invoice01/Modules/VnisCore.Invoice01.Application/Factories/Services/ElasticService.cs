//using Core;
//using Core.Shared.Constants;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using Microsoft.EntityFrameworkCore;
//using Nest;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Threading.Tasks;
//using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
//using VnisCore.Invoice01.Application.Factories.Repositories;
//using VnisCore.Invoice01.Application.Invoice01.Models;
//using Core.Shared.Models;
//using Microsoft.Extensions.Localization;
//using Core.Localization.Resources.AbpLocalization;

//namespace VnisCore.Invoice01.Application.Factories.Services
//{
//    public class ElasticService : IElasticService
//    {
//        private readonly IElasticClient _elasticService;
//        private readonly IAppFactory _appFactory;
//        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

//        public ElasticService(IElasticClient elasticClient,
//            IAppFactory appFactory, 
//            IStringLocalizer<CoreLocalizationResource> localizer)
//        {
//            _elasticService = elasticClient;
//            _appFactory = appFactory;
//            _localizer = localizer;
//        }

//        public async Task SyncInvoice01Elastic(Guid tenantId, long idInvoice)
//        {
//            var invoice01HeaderService = _appFactory.GetServiceDependency<IInvoiceHeaderRepository<Invoice01HeaderEntity>>();
//            var header = await invoice01HeaderService.GetInvoiceHeaderRawAsync(tenantId, idInvoice);
//            if (header == null)
//            {
//                //throw new UserFriendlyException("Không tìm thấy hóa đơn");
//                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.InvoiceNotFound"]);
//            }

//            //var elasticInvoices = new List<ElastichSearchInvoiceModel>();

//            var invoice = new ElastichSearchInvoiceModel
//            {
//                Id = header.Id,
//                TenantId = header.TenantId,
//                ErpId = header.ErpId,
//                TransactionId = header.TransactionId,
//                TemplateId = header.InvoiceTemplateId,
//                TemplateNo = header.TemplateNo,
//                SerialNo = header.SerialNo,
//                InvoiceNo = header.InvoiceNo,
//                //Note = header.Note,
//                InvoiceStatus = header.InvoiceStatus,
//                InvoiceStatusName = EnumExtension.ToEnum<InvoiceStatus>(header.InvoiceStatus).GetName(),
//                SignStatus = header.SignStatus,
//                InvoiceDate = header.InvoiceDate,
//                CreatorErp = header.CreatorErp,
//                TotalAmount = header.TotalAmount,
//                TotalVatAmount = header.TotalVatAmount,
//                //TotalDiscountAmountBeforeTax = header.TotalDiscountAmountBeforeTax,
//                //TotalDiscountPercentAfterTax = header.TotalDiscountPercentAfterTax,
//                //TotalDiscountAmountAfterTax = header.TotalDiscountAmountAfterTax,
//                //FromCurrency = header.FromCurrency,
//                ToCurrency = header.ToCurrency,
//                RoundingCurrency = header.RoundingCurrency,
//                CurrencyConversion = header.CurrencyConversion,
//                ExchangeRate = header.ExchangeRate,
//                PaymentMethod = header.PaymentMethod,
//                PaymentDate = header.PaymentDate.Value,
//                PaymentAmountWords = header.PaymentAmountWords,
//                PaymentAmountWordsEn = header.PaymentAmountWordsEn,
//                TotalPaymentAmount = header.TotalPaymentAmount,
//                UserNameCreator = header.UserNameCreator,
//                FullNameCreator = header.FullNameCreator,
//                IssuedTime = header.IssuedTime,
//                BuyerCode = header.BuyerCode,
//                BuyerId = header.BuyerId,
//                BuyerErpId = header.BuyerErpId,
//                BuyerEmail = header.BuyerEmail,
//                BuyerFullName = header.BuyerFullName,
//                BuyerLegalName = header.BuyerLegalName,
//                BuyerTaxCode = header.BuyerTaxCode,
//                BuyerAddressLine = header.BuyerAddressLine,
//                BuyerDistrictName = header.BuyerDistrictName,
//                BuyerCityName = header.BuyerCityName,
//                BuyerCountryCode = header.BuyerCountryCode,
//                BuyerPhoneNumber = header.BuyerPhoneNumber,
//                BuyerFaxNumber = header.BuyerFaxNumber,
//                BuyerBankName = header.BuyerBankName,
//                BuyerBankAccount = header.BuyerBankAccount,
//                ApprovedTime = header.ApprovedTime,
//                CreationTime = header.CreationTime,
//                Number = header.Number,
//                BuyerFullNameSigned = header.BuyerFullNameSigned,
//                ApproveStatus = header.ApproveStatus,
//                ApproveDeleteStatus = header.ApproveDeleteStatus,
//                ApproveCancelStatus = header.ApproveCancelStatus,
//                FullNameApprover = header.FullNameApprover,
//                BuyerSignedTime = header.BuyerSignedAt,
//                DeletionTime = header.DeletionTime,
//                IsOpened = header.IsOpened,
//                //RegistrationDetailId = header.RegistrationDetailId,
//                //RegistrationHeaderId = header.RegistrationHeaderId,
//                SellerId = header.SellerId,
//                SellerLegalName = header.SellerLegalName,
//                SellerTaxCode = header.SellerTaxCode,
//                SellerAddressLine = header.SellerAddressLine,
//                SellerCountryCode = header.SellerCountryCode,
//                SellerDistrictName = header.SellerDistrictName,
//                SellerCityName = header.SellerCityName,
//                SellerPhoneNumber = header.SellerPhoneNumber,
//                SellerFaxNumber = header.SellerFaxNumber,
//                SellerEmail = header.SellerEmail,
//                SellerBankName = header.SellerBankName,
//                SellerBankAccount = header.SellerBankAccount,
//                SellerFullName = header.SellerFullName,
//                SellerSignedTime = header.SellerSignedTime,
//                SellerFullNameSigned = header.SellerFullNameSigned,
//                SellerSignedId = header.SellerSignedId,
//                CancelTime = header.CancelTime,
//                CancelId = header.CancelId,
//                BatchId = header.BatchId,
//                IsViewed = header.IsViewed,
//                ViewedTime = header.ViewedTime,
//                OpenedTime = header.OpenedTime,
//                Partition = header.Partition,
//                LastModifierId = header.LastModifierId,
//                UpdatedTime = header.LastModificationTime,
//                PrintedTime = header.PrintedTime,
//                FullNamePrinter = header.FullNamePrinter,
//                Source = header.Source,
//                ExtraProperties = header.ExtraProperties != null ? header.ExtraProperties.ToDictionary(x => x.Key, x => (string)x.Value) : new Dictionary<string, string>()
//            };

//            //Detail
//            //await AddInvoiceDetails(header.TenantId, invoice);

//            //DetailExtra
//            //await AddInvoiceDetailExtras(header.TenantId, invoice);

//            //HeaderExtra
//            await AddInvoiceHeaderExtras(header.TenantId, invoice);

//            //TaxBreakdown
//            //await AddInvoiceTaxBreakdowns(invoice);

//            //InvoiceReference
//            //await AddInvoiceReference(invoice);

//            //elasticInvoices.Add(invoice);

//            var indexResponse = await _elasticService.IndexDocumentAsync(invoice);
//            if (!indexResponse.IsValid)
//            {
//                //throw new UserFriendlyException("Có lỗi trong quá trình đồng bộ Elastic");
//                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.SynchProcessElasticError"]);
//            }
//        }

//        private async Task AddInvoiceDetails(Guid tenantId, ElastichSearchInvoiceModel invoice)
//        {
//            var invoice01DetailService = _appFactory.GetServiceDependency<IInvoiceDetailRepository<Invoice01DetailEntity>>();
//            var invoiceDetails = await invoice01DetailService.GetInvoiceDetailRawAsync(tenantId, invoice.Id);
//            invoice.InvoiceDetails = invoiceDetails.Select(x => new ElastichSearchInvoiceModel.ElasticGetInvoice01DetailResponseModel
//            {
//                Id = x.Id,
//                Index = x.Index,
//                DiscountAmountBeforeTax = x.DiscountAmountBeforeTax,
//                DiscountPercentBeforeTax = x.DiscountPercentBeforeTax,
//                PaymentAmount = x.PaymentAmount,
//                ProductCode = x.ProductCode,
//                ProductId = x.ProductId,
//                ProductName = x.ProductName,
//                UnitName = x.UnitName,
//                UnitId = x.UnitId,
//                UnitPrice = x.UnitPrice,
//                RoundingUnit = x.RoundingUnit,
//                Quantity = x.Quantity,
//                Amount = x.Amount,
//                VatPercent = x.VatPercent,
//                VatAmount = x.VatAmount,
//                Note = x.Note,
//                HideQuantity = x.HideQuantity,
//                HideUnit = x.HideUnit,
//                HideUnitPrice = x.HideUnitPrice,
//                ExtraProperties = x.ExtraProperties != null ? x.ExtraProperties.ToDictionary(x => x.Key, x => (string)x.Value) : new Dictionary<string, string>(),
//            })
//            .OrderBy(x => x.Index)
//            .ToList();
//        }


//        private async Task AddInvoiceDetailExtras(Guid tenantId, ElastichSearchInvoiceModel invoice)
//        {
//            //var invoice01DetailExtraService = _appFactory.GetServiceDependency<IInvoiceDetailExtraRepository<Invoice01DetailExtraEntity>>();
//            //var invoice01DetailFieldService = _appFactory.GetServiceDependency<IInvoiceDetailFieldRepository<Invoice01DetailFieldEntity>>();
           
//            if (invoice.InvoiceDetails.Any())
//            {
//                //lấy các detail field
//                //var detailFields = (await invoice01DetailFieldService.QueryByTenantCodeRawAsync(tenantId))
//                //                    .ToDictionary(x => x.Id, x => x.FieldName);

//                //var idInvoiceDetails = invoice.InvoiceDetails.Select(x => x.Id).ToList();
//                //var detailExtras = (await invoice01DetailExtraService.GetInvoiceDetailExtraRawAsync(tenantId, idInvoiceDetails))
//                //    .GroupBy(x => x.InvoiceDetailId)
//                //    .ToDictionary(x => x.Key, x => x.ToList());

//                foreach (var item in invoice.InvoiceDetails)
//                {
//                    item.InvoiceDetailExtras = new List<ElastichSearchInvoiceModel.ElasticGetInvoice01DetailExtraResponseModel>();
//                    //if (!detailExtras.ContainsKey(item.Id))
//                    //    continue;
//                    if(item.ExtraProperties != null && item.ExtraProperties.Any())
//                    {
//                        var extraProperties = new List<InvoiceDetailExtraModel>();
//                        if (item.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceDetailExtras"]))
//                        {
//                            extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(item.ExtraProperties["invoiceDetailExtras"]);
//                        }
//                        if (extraProperties.Any())
//                        {
//                            var detailExtras = extraProperties.GroupBy(x => x.InvoiceDetailId).ToDictionary(x => x.Key, x => x.ToList());
//                            item.InvoiceDetailExtras = detailExtras[item.Id].Select(x => new ElastichSearchInvoiceModel.ElasticGetInvoice01DetailExtraResponseModel
//                            {
//                                FieldName = x.FieldName,
//                                FieldValue = x.FieldValue,
//                                InvoiceDetailFieldId = x.InvoiceDetailFieldId
//                            }).ToList();
//                        }
//                    }
                    
//                }
//            }
//        }


//        private async Task AddInvoiceHeaderExtras(Guid tenantId, ElastichSearchInvoiceModel invoice)
//        {
//            //var invoice01headerExtra01Service = _appFactory.GetServiceDependency<IInvoiceHeaderExtraRepository<Invoice01HeaderExtraEntity>>();
//            //var invoice01headerField01Service = _appFactory.GetServiceDependency<IInvoiceHeaderFieldRepository<Invoice01HeaderFieldEntity>>();

//            ////lấy các header field
//            //var headerFields = (await invoice01headerField01Service.QueryByTenantCodeRawAsync(tenantId))
//            //                    .ToDictionary(x => x.Id, x => x.FieldName);

//            //var invoiceHeaderExtras = await invoice01headerExtra01Service.GetInvoiceHeaderExtraRawAsync(tenantId, invoice.Id);
//            if (invoice.ExtraProperties != null && invoice.ExtraProperties.Any())
//            {
//                var extraProperties = new List<InvoiceHeaderExtraModel>();
//                if (invoice.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(invoice.ExtraProperties["invoiceHeaderExtras"]))
//                {
//                    extraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(invoice.ExtraProperties["invoiceHeaderExtras"]);
//                }

//                if (extraProperties.Any())
//                {
//                    invoice.InvoiceHeaderExtras = extraProperties.Select(x => new ElastichSearchInvoiceModel.ElasticGetInvoice01HeaderExtraResponseModel
//                    {
//                        FieldValue = x.FieldValue,
//                        FieldName = x.FieldName
//                    }).ToList();
//                }
//            }
            
//        }

//        private async Task AddInvoiceTaxBreakdowns(ElastichSearchInvoiceModel invoice)
//        {
//            var invoice01TaxBreakdownService = _appFactory.GetServiceDependency<IInvoice01TaxBreakdownRepository>();

//            var invoiceTaxbBreakDowns = await invoice01TaxBreakdownService.GetInvoiceTaxBreakDownRawAsync(invoice.TenantId, invoice.Id);

//            invoice.InvoiceTaxBreakdowns = invoiceTaxbBreakDowns.Select(x => new ElastichSearchInvoiceModel.ElasticGetInvoice01TaxBreakdownResponseModel
//            {
//                Id = x.Id,
//                Name = x.Name,
//                VatAmount = x.VatAmount,
//                VatPercent = x.VatPercent,
//                VatAmountBackUp = x.VatAmountBackUp
//            }).ToList();
//        }


//        private async Task AddInvoiceReference(ElastichSearchInvoiceModel invoice)
//        {
//            var invoice01ReferenceService = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<Invoice01ReferenceEntity>>();

//            var invoiceReference = await invoice01ReferenceService.GetInvoiceReferenceRawAsync(invoice.Id);
//            if (invoiceReference != null)
//            {
//                invoice.InvoiceReferenceId = invoiceReference.InvoiceReferenceId;
//                invoice.TransactionData = $"{invoice.SellerTaxCode}|{invoiceReference.TemplateNoReference}|{invoiceReference.SerialNoReference}|{invoiceReference.InvoiceNoReference}|{invoiceReference.InvoiceDateReference:yyyy-MM-dd}";
//            }
//        }
//    }
//}
