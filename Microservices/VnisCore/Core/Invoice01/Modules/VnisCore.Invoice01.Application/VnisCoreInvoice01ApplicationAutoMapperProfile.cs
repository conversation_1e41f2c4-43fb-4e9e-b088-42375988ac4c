using AutoMapper;
using Core.SettingManagement;
using Core.Shared.Dto;
using System.Collections.Generic;
using Core.Shared.ElasticSearch.Dto.Invoice01;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.Application.Invoice01.Dto;
using VnisCore.Invoice01.Application.Invoice01.Models;
using VnisCore.Invoice01.Application.Invoice01.Models.Requests.Commands;
using VnisCore.Invoice01.Application.Invoice01.Models.Responses.Queries;
using VnisCore.Invoice01.Application.Invoice01DetailField.Dto;
using VnisCore.Invoice01.Application.Invoice01Document.Dto;
using VnisCore.Invoice01.Application.Invoice01Error.Dto;
using VnisCore.Invoice01.Application.Invoice01HeaderField.Dto;
using VnisCore.Core.Oracle.Domain.Entities.PrintNote;
using Core.Dto.Shared.PrintNote;

namespace VnisCore.Invoice01.Application
{
    public class VnisCoreInvoice01ApplicationAutoMapperProfile : Profile
    {
        public VnisCoreInvoice01ApplicationAutoMapperProfile()
        {
            /* You can configure your AutoMapper mapping configuration here.
             * Alternatively, you can split your mapping configurations
             * into multiple profile classes for a better organization. */
            CreateMap<Invoice01DetailFieldEntity, Invoice01DetailFieldDto>().ReverseMap();
            CreateMap<Invoice01HeaderFieldEntity, Invoice01HeaderFieldDto>().ReverseMap();
            CreateMap<Invoice01DocumentInfoEntity, Invoice01DocumentInfoDto>().ReverseMap();
            CreateMap<Invoice01HeaderEntity, Invoice01HeaderDto>().ReverseMap();
            CreateMap<Invoice01DetailEntity, Invoice01DetailDto>().ReverseMap();
            CreateMap<Invoice01ErrorEntity, Invoice01ErrorDto>().ReverseMap();
            //CreateMap<CreateInvoice01HeaderDto, MongoInvoice01Entity>().ReverseMap();
            CreateMap<MongoInvoice01Entity, CreateInvoice01HeaderDto>().ReverseMap();
            
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01SpecificProductDto, CreateInvoice01HeaderOldDecreeDto.CreateOldDecreeInvoice01SpecificProductExtraModel>().ReverseMap();

            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto, CreateInvoice01HeaderDto.CreateInvoice01DetailModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01SpecificProductDto, CreateInvoice01HeaderDto.CreateInvoice01SpecificProductExtraModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto, CreateInvoice01HeaderDto.CreateInvoice01TaxBreakdownModel>().ReverseMap();

            CreateMap<MongoInvoice01Entity, CreateInvoice01HeaderOldDecreeDto>()
                .ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto, CreateInvoice01HeaderOldDecreeDto.CreateInvoice01DetailModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto, CreateInvoice01HeaderOldDecreeDto.CreateInvoice01TaxBreakdownModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01ReferenceDto, Invoice01ReferenceOldDecreeModel>().ReverseMap();

            CreateMap<MongoInvoice01Entity, CreateReplaceInvoice01Dto>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto, CreateReplaceInvoice01Dto.CreateReplaceInvoice01DetailRequestModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01SpecificProductDto, CreateReplaceInvoice01Dto.CreateReplaceInvoice01SpecificProductExtraModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto, CreateReplaceInvoice01Dto.CreateReplaceInvoice01TaxBreakdownRequestModel>().ReverseMap();

            CreateMap<MongoInvoice01Entity, CreateAdjustmentHeaderInvoice01HeaderDto>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto, CreateAdjustmentHeaderInvoice01HeaderDto.CreateAdjHeaderInvoice01DetailRequestModel>().ReverseMap();

            CreateMap<MongoInvoice01Entity, CreateInvoice01AdjustmentDetailRequest>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto, CreateInvoice01AdjustmentDetailRequest.CreateAdjustmentDetailInvoice01DetailRequestModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto, CreateInvoice01AdjustmentDetailRequest.CreateAdjustmentDetailInvoice01TaxBreakdownRequestModel>().ReverseMap();

            // Dieu chinh nhieu lan
            CreateMap<CreateAdjustInvoice01HeaderDto, MongoInvoice01Entity>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto, CreateAdjustInvoice01HeaderDto.CreateAdjustInvoice01DetailRequestModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01SpecificProductDto, CreateAdjustInvoice01HeaderDto.CreateAdjustInvoice01SpecificProductExtraModel>().ReverseMap();
            CreateMap<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto, CreateAdjustInvoice01HeaderDto.CreateAdjustInvoice01TaxBreakdownRequestModel>().ReverseMap();

            CreateMap<Invoice01HeaderEntity, MongoInvoice01Entity>().ReverseMap();
            CreateMap<Invoice01DetailEntity, global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>().ReverseMap();
            CreateMap<Invoice01TaxBreakdownEntity, global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>().ReverseMap();
            CreateMap<List<Invoice01DetailEntity>, List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01DetailDto>>().ReverseMap();
            CreateMap<List<Invoice01TaxBreakdownEntity>, List<global::Core.Dto.Shared.Invoices.Invoice01.Invoice01TaxBreakdownDto>>().ReverseMap();

            CreateMap<PagingInvoice01Response, Invoice01HeaderEsDto>().ReverseMap();
            CreateMap<PagingInvoice01Response.PagingInvoice01HeaderExtraResponseModel, Invoice01HeaderEsDto.PagingInvoice01HeaderExtraResponseModel>().ReverseMap();

            CreateMap<SettingDto, Setting>()
                .ForMember(x => x.ExtraProperties, opt => opt.MapFrom(src => src.ExtraProperties))
                .ReverseMap();

            CreateMap<Invoice01HeaderEntity, ReadInvoice01Model>().ReverseMap();
            CreateMap<Invoice01DetailEntity, ReadInvoice01Model.GetInvoice01DetailResponseModel>().ForMember(x => x.ExtraPropertieObjects, opt => opt.MapFrom(src => src.ExtraProperties)).ReverseMap();
            CreateMap<Invoice01TaxBreakdownEntity, ReadInvoice01Model.GetInvoice01TaxBreakdownResponseModel>().ReverseMap();
            CreateMap<Invoice01DocumentInfoEntity, ReadInvoice01Model.GetInvoice01DocumentInfoResponseModel>().ReverseMap();
            CreateMap<Invoice01DocumentEntity, ReadInvoice01Model.GetInvoice01DocumentResponseModel>().ReverseMap();
            CreateMap<Invoice01HeaderEntity, ReadInvoice01Model.ReferenceInvoiceResponseModel>().ReverseMap();
            CreateMap<InvoicePrintNoteEntity, InvoicePrintNoteDto>().ReverseMap();
            //CreateMap<CategoryEntity, CategoryDto>().ReverseMap();
            //CreateMap<ProductEntity, ProductDto>().ReverseMap();
        }
    }
}
