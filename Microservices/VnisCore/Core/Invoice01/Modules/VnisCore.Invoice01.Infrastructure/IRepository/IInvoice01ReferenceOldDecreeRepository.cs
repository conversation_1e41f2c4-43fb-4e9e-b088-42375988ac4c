using Core.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Invoice01.Infrastructure.IRepository
{
    public interface IInvoice01ReferenceOldDecreeRepository : IRepository<Invoice01ReferenceOldDecreeEntity, long>
    {
        Task<List<Invoice01ReferenceOldDecreeEntity>> getInvoiceByIds(List<long> ids, Guid tenantId);

        Task<List<Invoice01ReferenceOldDecreeEntity>> getInvoiceReferenceById(long rootId, Guid tenantId);
    }
}
