using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using VnisCore.Invoice01.SyncElasticsearch.Interface;

namespace VnisCore.Invoice01.SyncElasticsearch.BackgroundWorkers
{
    public class Invoice01ReSyncOracleToEsWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;

        public Invoice01ReSyncOracleToEsWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            _configuration = configuration;
            Timer.Period = 1000; //30 s
            //int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            //if (period > 0)
            //    timer.Period = period * 1000;

        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            short.TryParse(_configuration["Settings:IsEnableReSyncOracleToEsWorker"], out var isEnableReSyncOracleToEsWorker);

            if (isEnableReSyncOracleToEsWorker > 0)
            {

                var statusSyncEs = new List<short> {
                    (short)SyncElasticSearchStatus.UnProcess.GetHashCode(),
                    (short)SyncElasticSearchStatus.PendingSyncUpdate.GetHashCode(),
                    (short)SyncElasticSearchStatus.PendingSyncCreateInvoiceError.GetHashCode(),
                    (short)SyncElasticSearchStatus.PendingSyncStatusTvanRequest.GetHashCode(),
                    (short)SyncElasticSearchStatus.PendingSyncStatusTvanResponse.GetHashCode()
                };
                foreach (var esStatus in statusSyncEs)
                {
                    try
                    {
                        await workerContext
                              .ServiceProvider
                              .GetRequiredService<IInvoice01SyncElasticsearchBusiness>()
                              .ReSyncOracleToElasticsearch(esStatus);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, ex.Message);
                    }
                }
            }
        }
    }
}