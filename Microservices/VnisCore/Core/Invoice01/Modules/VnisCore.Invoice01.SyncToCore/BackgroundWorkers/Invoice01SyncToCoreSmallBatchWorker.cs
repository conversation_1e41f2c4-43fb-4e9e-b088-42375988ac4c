using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.Invoice01.SyncToCore.Interface;

namespace VnisCore.Invoice01.SyncToCore.BackgroundWorkers
{
    public class Invoice01SyncToCoreSmallBatchWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public Invoice01SyncToCoreSmallBatchWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            Timer.Period = 1000; //1 s
            int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            if (period > 0)
                timer.Period = period * 1000;

        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            await workerContext
                .ServiceProvider
                .GetRequiredService<IInvoice01SyncToCoreBusiness>()
                .Invoice01SyncSmallBatchToCore();
        }
    }
}