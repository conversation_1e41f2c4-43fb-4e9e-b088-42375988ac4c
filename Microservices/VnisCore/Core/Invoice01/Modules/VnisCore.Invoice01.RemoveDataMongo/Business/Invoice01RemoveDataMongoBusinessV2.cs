using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using Dapper;
using Microsoft.Extensions.Configuration;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01.RemoveDataMongo.Interface;
using VnisCore.Invoice01.RemoveDataMongo.Repository;

namespace VnisCore.Invoice01.RemoveDataMongo.Business
{
    /// <summary>
    /// implement: Refactor lại luồng remove dữ liệu mongo
    /// </summary>
    public class Invoice01RemoveDataMongoBusinessV2 : IInvoice01RemoveDataMongoBusinessV2
    {
        private readonly IVnisCoreMongoInvoice01Repository _mongoInvoice01Repository;
        private readonly IVnisCoreMongoInvoice01ErpIdRepository _vnisCoreMongoInvoice01ErpIdRepository;
        private readonly IInvoice01ErpIdDapperRepository _invoice01ErpIdDapperRepository;
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;

        public Invoice01RemoveDataMongoBusinessV2(
             IVnisCoreMongoInvoice01Repository mongoInvoice01Repository,
             IVnisCoreMongoInvoice01ErpIdRepository vnisCoreMongoInvoice01ErpIdRepository,
             IInvoice01ErpIdDapperRepository invoice01ErpIdDapperRepository,
             IConfiguration configuration,
             IAppFactory appFactory)
        {
            _mongoInvoice01Repository = mongoInvoice01Repository;
            _vnisCoreMongoInvoice01ErpIdRepository = vnisCoreMongoInvoice01ErpIdRepository;
            _invoice01ErpIdDapperRepository = invoice01ErpIdDapperRepository;
            _configuration = configuration;
            _appFactory = appFactory;
        }

        /// <summary>
        /// remove dữ liệu mongo
        /// </summary>
        /// <param name="invoiceHasCode">true: CÓ MÃ, false: KHÔNG MÃ</param>
        /// <returns></returns>
        public async Task RemoveDataMongoAsync(bool invoiceHasCode)
        {
            Log.Information($@"-------- START JOB REMOTE DATA MONGO");

            int.TryParse(_configuration["Settings:NumberInvoicesTake"], out var numberInvoicesTake);
            if (numberInvoicesTake == 0)
                numberInvoicesTake = 1000;

            int.TryParse(_configuration["Settings:NumberInvoicesSkip"], out var numberInvoicesSkip);

            var prefixSerialNo = "K"; // hoá đơn không mã
            if (invoiceHasCode)
            {
                prefixSerialNo = "C"; // hoá đơn có mã
            }

            try
            {
                // lấy hoá đơn theo điều kiện
                var invoicesMongo = await _mongoInvoice01Repository.GetInvoicesAsync(
                    prefixSerialNo: prefixSerialNo,
                    signStatus: (short)SignStatus.DaKy,
                    isSyncToCore: (short)SyncedToCoreStatus.Synced,
                    isSyncToEs: (short)SyncElasticSearchStatus.Synced,
                    invoiceHasCode: invoiceHasCode,
                    tvanStatus: (short)TvanStatus.TCTAccept,
                    isSyncVerificationCodeTocore: (short)SyncVerificationCodeTocore.Success,
                    mailStatus: new List<short> { 1, -2 }, // chỗ enum này chưa tạo enum: 1: đã tạo content mail, -2: ko cấu hình gửi mail tự động
                    syncCatalogtatus: new List<short> { (short)SyncCatalogStatus.IsSyncedToCore, (short)SyncCatalogStatus.WithoutConfigSync },
                    numberInvoicesTake: numberInvoicesTake,
                    numberInvoicesSkip: numberInvoicesSkip);

                if (!invoicesMongo.Any())
                {
                    return;
                }

                var invoiceMongoRemove = new List<MongoInvoice01Entity>();
                var invoiceMongoUpdate = new List<MongoInvoice01Entity>();

                var ids = invoicesMongo.Select(x => x.Id).ToList();
                var invoicesOracle = await QueryOracleAsync(ids);
                if (invoicesOracle.Any())
                {
                    var indexInvoiceOracle = invoicesOracle.ToDictionary(x => x.Id, x => x);
                    foreach (var invoiceMongo in invoicesMongo)
                    {
                        if (indexInvoiceOracle.ContainsKey(invoiceMongo.Id))
                        {
                            var invoiceOracle = indexInvoiceOracle[invoiceMongo.Id];
                            if (invoiceMongo.SignStatus != invoiceOracle.SignStatus)
                            {
                                if (invoiceMongo.VerificationCode != invoiceOracle.VerificationCode)
                                {
                                    if (string.IsNullOrEmpty(invoiceMongo.VerificationCode) && !string.IsNullOrEmpty(invoiceOracle.VerificationCode))
                                    {
                                        invoiceMongo.VerificationCode = invoiceOracle.VerificationCode;
                                    }
                                    else if (invoiceMongo.IsSyncVerificationCodeTocore != 0
                                            && !string.IsNullOrEmpty(invoiceMongo.VerificationCode)
                                            && string.IsNullOrEmpty(invoiceOracle.VerificationCode))
                                    {
                                        invoiceMongo.IsSyncVerificationCodeTocore = 0;
                                    }
                                }

                                invoiceMongo.IsSyncSignTocore = (short)SyncSignedToCoreStatus.UnProcess.GetHashCode();
                                invoiceMongo.IsSyncedToElasticSearch = (short)SyncElasticSearchStatus.PendingSyncStatusTvanRequest.GetHashCode();
                                invoiceMongoUpdate.Add(invoiceMongo);
                                continue;
                            }
                        }
                        else
                        {
                            invoiceMongo.IsSyncedToCore = (short)SyncedToCoreStatus.UnProcess;
                            invoiceMongoUpdate.Add(invoiceMongo);
                            continue;
                        }

                        invoiceMongoRemove.Add(invoiceMongo);
                    }

                    // update
                    await _mongoInvoice01Repository.UpdateTraceRemoveAsync(invoiceMongoUpdate);

                    #region Bổ sung luồng Đồng bộ ErpId Từ Mongo về Oracle
                    // Lấy hóa đơn có ErpId
                    var hasErpIdInvoiceMongoRemove = invoiceMongoRemove
                        .Where(x => !x.ErpId.IsNullOrEmpty())
                        .ToList();
                    if (!hasErpIdInvoiceMongoRemove.IsNullOrEmpty())
                    {
                        // Duyệt từng hóa đơn để đồng bộ ErpId
                        foreach (var invoiceMongo in hasErpIdInvoiceMongoRemove)
                        {
                            var tenantId = invoiceMongo.TenantId;
                            var taxCode = invoiceMongo.SellerTaxCode;
                            var invoicesErpIds = await _vnisCoreMongoInvoice01ErpIdRepository
                                .GetByTenantIdAndErpIdAsync(invoiceMongo.TenantId, invoiceMongo.ErpId);

                            var insertInvoice01ErpIdsMongo = new List<MongoInvoice01Entity>();
                            // Nếu có dữ liệu ErpId bảng  trên mongo
                            // Đồng bị
                            if (!invoicesErpIds.IsNullOrEmpty())
                            {
                                foreach (var item in invoicesErpIds)
                                {
                                    insertInvoice01ErpIdsMongo.Add(new MongoInvoice01Entity
                                    {
                                        TenantId = item.TenantId,
                                        ErpId = item.ErpId,
                                    });
                                }

                                await _invoice01ErpIdDapperRepository.InsertManyErpIdAsync(insertInvoice01ErpIdsMongo, ErpIdStatus.IsSyncedToCore);

                                var erpIds = insertInvoice01ErpIdsMongo.Select(x => x.ErpId).ToList();
                                await _vnisCoreMongoInvoice01ErpIdRepository.DeleteManyByTenantIdAndErpIdsAsync(tenantId, erpIds);
                                Log.Information($@"ErpIds deleted VnisInvoice01ErpId IN MONGO: {string.Join(",", erpIds)} - tenantId: {tenantId} - taxCode: {taxCode}");
                            }
                            else
                            {
                                Log.Information($@"ErpIds not found: ErpId: {invoiceMongo.ErpId} - tenantId: {tenantId} - taxCode: {taxCode}");
                            }
                        }
                    }
                    #endregion

                    // xoá dữ liệu mongo
                    var idsDel = invoiceMongoRemove.Select(x => x.Id).ToList();
                    await _mongoInvoice01Repository.DeleteManyByIdsAsync(idsDel);

                    Log.Information($@"Count Invoices remove: {idsDel.Count()}");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
            }

            Log.Information($@"-------- END JOB REMOTE DATA MONGO");
        }

        /// <summary>
        /// ids hoá đơn
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        private async Task<List<Invoice01HeaderEntity>> QueryOracleAsync(List<long> ids)
        {
            var limit = 1000;
            var query = new StringBuilder();
            
            var count = ids.Count();
            if (count > limit)
            {
                var numberOfTimes = count / limit;
                if (numberOfTimes * limit < count)
                    numberOfTimes += 1;

                query.Append(@$" SELECT ""Id"", ""SignStatus"", ""SerialNo"", ""VerificationCode"", ""StatusTvan"", ""IsDeclared"" FROM ""Invoice01Header"" WHERE ""Id"" in ({string.Join(",", ids.Skip(0).Take(limit).ToList())}) ");
                for (int i = 1; i < numberOfTimes; i++)
                {
                    var idsTake = ids.Skip(i * limit).Take(limit).ToList();
                    query.Append($@" OR ""Id"" IN ({string.Join(",", idsTake)}) ");
                }
            }
            else
            {
                 query.Append(@$" SELECT ""Id"", ""SignStatus"", ""SerialNo"", ""VerificationCode"", ""StatusTvan"", ""IsDeclared"" FROM ""Invoice01Header"" WHERE ""Id"" in ({string.Join(",", ids)}) ");
            }

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderEntity>(query.ToString());

            return result.ToList();
        }
    }
}
