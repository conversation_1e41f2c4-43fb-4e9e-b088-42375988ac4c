using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Threading.Tasks;
using VnisCore.Invoice01.RemoveDataMongo.Interface;
using VnisCore.Invoice01.RemoveDataMongo.Models;

namespace VnisCore.Invoice01.RemoveDataMongo.BackgroundWorkers
{
    public class RemoveDataMongoInvoice01WithoutCodeBackgroundWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public RemoveDataMongoInvoice01WithoutCodeBackgroundWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            Timer.Period = 100; //1 s
            int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            if (period > 0)
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            var timeRunning = workerContext.ServiceProvider.GetRequiredService<IOptions<TimeRunningOption>>().Value;

            if (timeRunning.FromHour > timeRunning.ToHour)
            {
                if ((DateTime.Now.Hour >= timeRunning.FromHour && DateTime.Now.Hour < 24)
                    || (DateTime.Now.Hour >= 0 && DateTime.Now.Hour < timeRunning.ToHour))
                {
                    await workerContext
                        .ServiceProvider
                        .GetRequiredService<IInvoice01RemoveDataMongoBusinessV2>()
                        .RemoveDataMongoAsync(false);
                }
            }
            else
            {
                if (DateTime.Now.Hour >= timeRunning.FromHour && DateTime.Now.Hour < timeRunning.ToHour)
                {
                    await workerContext
                        .ServiceProvider
                        .GetRequiredService<IInvoice01RemoveDataMongoBusinessV2>()
                        .RemoveDataMongoAsync(false);
                }
            }
        }
    }
}
