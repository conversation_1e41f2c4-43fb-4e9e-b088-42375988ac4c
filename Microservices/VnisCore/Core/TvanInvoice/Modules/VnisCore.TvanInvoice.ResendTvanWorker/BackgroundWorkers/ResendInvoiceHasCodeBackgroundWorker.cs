using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Threading;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using VnisCore.TvanInvoice.ResendTvanWorker.Interface;

namespace VnisCore.TvanInvoice.ResendTvanWorker.BackgroundWorkers
{
    public class ResendInvoiceHasCodeBackgroundWorker : AsyncPeriodicBackgroundWorkerBase
    {
        public ResendInvoiceHasCodeBackgroundWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            Timer.Period = 100; //1 s
            int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            if (period > 0)
                timer.Period = period * 1000;

        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            var services = workerContext
                .ServiceProvider.GetServices<IResendInvoiceHasCodeBusiness>();

            foreach (var service in services)
            {
                try
                {
                    await service.ResendTvanAsync();
                }
                catch (System.Exception ex)
                {
                    Log.Error(ex, ex.Message);
                    throw;
                }
            }
        }
    }
}