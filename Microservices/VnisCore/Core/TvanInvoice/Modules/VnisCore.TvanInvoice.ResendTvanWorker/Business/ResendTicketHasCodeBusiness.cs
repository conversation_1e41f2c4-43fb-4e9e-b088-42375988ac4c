using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Core.EventBus.Distributed;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using Dapper;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;
using VnisCore.TvanInvoice.ResendTvanWorker.Abtractions;
using VnisCore.TvanInvoice.ResendTvanWorker.Interface;
using VnisCore.TvanInvoice.ResendTvanWorker.RabbitMqEventBus.MessageEventData.Ticket;

namespace VnisCore.TvanInvoice.ResendTvanWorker.Business
{
    public class ResendTicketHasCodeBusiness : BaseResendInvoiceHasCodeService<TicketHeaderEntity, TicketXmlEntity, TvanInfoTicketHasCodeEntity>, IResendInvoiceHasCodeBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;

        public ResendTicketHasCodeBusiness(IDistributedEventBus distributedEventBus,
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
            _distributedEventBus = distributedEventBus;
        }


        protected override async Task<List<TicketHeaderEntity>> GetInvoiceAsync(int size)
        {
            var query = $@"Select ""Id"", ""TenantId"" from ""TicketHeader"" 
                           WHERE ""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND ""SerialNo"" LIKE 'C%' 
                           AND ""VerificationCode"" IS NULL 
                           AND ""StatusTvan"" in ({TvanStatus.UnSent.GetHashCode()}, {TvanStatus.SendError.GetHashCode()})
                           AND rownum <= {size}";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketHeaderEntity>(query);

            return result.ToList();
        }

        protected override async Task<List<TicketXmlEntity>> GetInvoiceXmlAsync(List<long> invoiceHeaderIds)
        {
            var query = $@"Select ""Id"", ""InvoiceHeaderId"" from ""TicketXml"" 
                           WHERE ""InvoiceHeaderId"" IN ({string.Join(",", invoiceHeaderIds)})";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketXmlEntity>(query);

            return result.ToList();
        }

        protected override async Task<List<TvanInfoTicketHasCodeEntity>> GetTvanInfoHasCodeAsync(List<long> invoiceHeaderIds)
        {
            var query = $@"Select ""Id"", ""InvoiceHeaderId"" from ""TvanInfoTicketHasCode"" 
                           WHERE ""InvoiceHeaderId"" IN ({string.Join(",", invoiceHeaderIds)})";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TvanInfoTicketHasCodeEntity>(query);

            return result.ToList();
        }


        protected override async Task PublishToTvanAsync(long id, Guid tenantId)
        {
            await _distributedEventBus.PublishAsync(new SignTicketEventTvanData
            {
                InvoiceHeaderId = id,
                TenantId = tenantId
            });
        }
    }
}