using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using Core.Tvan.Vnpay.Interfaces;
using Dapper;
using Serilog;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.TvanInvoice.GetResponceError.Interfaces;
using VnisCore.TvanInvoice.GetResponceError.Models;

namespace VnisCore.TvanInvoice.GetResponceError.Services
{
    public class Invoice01HasCodeBussinessService : IInvoiceHasCodeBussinessService
    {
        private readonly IAppFactory _appFactory;
        private readonly ICallBackTvanClient _callBackTvanClient;

        public Invoice01HasCodeBussinessService(IAppFactory appFactory,
            ICallBackTvanClient callBackTvanClient)
        {
            _appFactory = appFactory;
            _callBackTvanClient = callBackTvanClient;
        }


        public async Task Get202MissingAsync()
        {
            //lấy các hóa đơn đã gửi 200 nhưng chưa có 202 hoặc chưa có 204
            var queryMissing = @$"Select header.""Id"", tvaninfo.""MessageCode"", tvaninfo.""CreationTime"", header.""SellerTaxCode"" from ""Invoice01Header"" header, ""TvanInfoInvoice01HasCode"" tvaninfo 
                                  where header.""SerialNo"" like 'C%' and header.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} and header.""VerificationCode"" IS NULL
                                  and header.""StatusTvan"" = {TvanStatus.Sended.GetHashCode()} 
                                  and header.""Id"" = tvaninfo.""InvoiceHeaderId""
                                  and tvaninfo.""MessageTypeCode"" = 200 and tvaninfo.""CreationTime"" > '18/JUN/22 12:00:00.********* AM'
                                  Order by header.""SellerSignedTime"" desc
                                  OFFSET 0 ROWS FETCH NEXT 1000 ROWS ONLY";

            var invoiceMissing202s = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01Model>(queryMissing);

            if (!invoiceMissing202s.Any())
            {
                Log.Fatal("Khong con hoa don 01 nao chua lay ma");
            }

            Log.Fatal($"So luong chua co ma: {invoiceMissing202s.Count()}");
            invoiceMissing202s = invoiceMissing202s.OrderBy(x => x.CreationTime);
            var tvanVnpayClient = _appFactory.GetServiceDependency<ITvanVnpayInvoiceClient>();
            foreach (var item in invoiceMissing202s)
            {
                try
                {
                    var messageHasXml = await tvanVnpayClient.GetXmlResponse200Async(item.MessageCode, item.CreationTime, item.CreationTime.AddDays(5));
                    if (string.IsNullOrEmpty(messageHasXml))
                    {
                        Log.Fatal($"Hóa đơn 01, mstNnt: {item.SellerTaxCode}, id: {item.Id}, mã thông điệp {item.MessageCode} ngày {item.CreationTime} không có dữ liệu xml TCT trả về ở tvan");
                        continue;
                    }

                    var xml = Encoding.UTF8.GetString(Convert.FromBase64String(messageHasXml));

                    await _callBackTvanClient.CallBackVnpayAsync(xml);
                }
                catch (System.Exception ex)
                {
                    Log.Error(ex, ex.Message);
                }

            }
        }

        public async Task OldGet202MissingAsync()
        {
            var userName = _appFactory.Configuration.GetSection("TvanVnpayInvoice:UserName").Value;
            var password = _appFactory.Configuration.GetSection("TvanVnpayInvoice:Password").Value;

            //lấy các hóa đơn đã gửi 200 nhưng chưa có 202 hoặc chưa có 204
            var queryMissing = @$"Select header.""Id"", tvaninfo.""MessageCode"", tvaninfo.""CreationTime"", header.""SellerTaxCode"" from ""Invoice01Header"" header, ""TvanInfoInvoice01HasCode"" tvaninfo 
                                  where header.""SerialNo"" like 'C%' and header.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} and header.""VerificationCode"" IS NULL
                                  and header.""StatusTvan"" = {TvanStatus.Sended.GetHashCode()} 
                                  and header.""Id"" = tvaninfo.""InvoiceHeaderId""
                                  and tvaninfo.""MessageTypeCode"" = 200 and tvaninfo.""CreationTime"" > '28/APR/22 12:00:00.********* AM'
                                  Order by header.""SellerSignedTime"" desc
                                  OFFSET 0 ROWS FETCH NEXT 2000 ROWS ONLY";

            var invoiceMissing202s = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01Model>(queryMissing);

            if (!invoiceMissing202s.Any())
            {
                Log.Fatal("Khong con hoa don 01 nao chua lay ma");
            }

            Log.Fatal($"So luong chua co ma: {invoiceMissing202s.Count()}");
            invoiceMissing202s = invoiceMissing202s.OrderBy(x => x.CreationTime);
            var tvanVnpayClient = _appFactory.GetServiceDependency<ITvanVnpayInvoiceClient>();
            foreach (var item in invoiceMissing202s)
            {
                try
                {
                    var dataPaging = await tvanVnpayClient.SearchAsync(item.MessageCode, item.CreationTime, item.CreationTime.AddDays(5), 10, 0, userName, password);

                    //tìm kiếm paging
                    if (dataPaging.data == null || dataPaging.data.data == null || !dataPaging.data.data.Any())
                    {
                        Log.Fatal($"Hóa đơn 01, mstNnt: {item.SellerTaxCode}, id: {item.Id}, mã thông điệp {item.MessageCode} ngày {item.CreationTime} không có dữ liệu ở tvan");
                        continue;
                    }

                    var messageTvan = dataPaging.data.data.FirstOrDefault();
                    //vào detail
                    var dataDetails = await tvanVnpayClient.GetByCreateAtInMsAsync(messageTvan.id, messageTvan.createdAtInSecond, userName, password);

                    if (dataDetails == null || dataDetails.data?.phases == null || !dataDetails.data.phases.Any())
                    {
                        Log.Fatal($"Hóa đơn 01, mstNnt: {item.SellerTaxCode}, id: {item.Id}, mã thông điệp {item.MessageCode} ngày {item.CreationTime} không có dữ liệu pharse ở tvan");
                        continue;
                    }

                    var messageTCTTvan = dataDetails.data.phases.FirstOrDefault(x => x.phaseNo == "3.1");

                    if (messageTCTTvan == null)
                    {
                        Log.Fatal($"Hóa đơn 01, mstNnt: {item.SellerTaxCode}, id: {item.Id}, mã thông điệp {item.MessageCode} ngày {item.CreationTime} không có dữ liệu từ TCT trả về TVAN");
                        continue;
                    }

                    //lấy thông tin detail có xml
                    var dataHasXml = await tvanVnpayClient.GetInfoXmlByCreateAtInMsAsync(messageTvan.id, messageTvan.createdAtInSecond, messageTCTTvan.messageType.Value, item.MessageCode, userName, password);
                    if (dataHasXml == null || dataHasXml.data == null || !dataHasXml.data.Any())
                    {
                        Log.Fatal($"Hóa đơn 01, mstNnt: {item.SellerTaxCode}, id: {item.Id}, mã thông điệp {item.MessageCode} ngày {item.CreationTime} không có dữ liệu xml ở tvan");
                        continue;
                    }

                    var messageHasXml = dataHasXml.data.FirstOrDefault(x => x.messageType == messageTCTTvan.messageType.Value);
                    if (messageHasXml == null || string.IsNullOrEmpty(messageHasXml.content))
                    {
                        Log.Fatal($"Hóa đơn 01, mstNnt: {item.SellerTaxCode}, id: {item.Id}, mã thông điệp {item.MessageCode} ngày {item.CreationTime} không có dữ liệu xml {messageTCTTvan.messageType.Value} TCT trả về ở tvan");
                        continue;
                    }

                    var xml = messageHasXml.content;

                    await _callBackTvanClient.CallBackVnpayAsync(xml);
                }
                catch (System.Exception ex)
                {
                    Log.Error(ex, ex.Message);
                }

            }
        }

    }
}
