using Core.Tvan.Enums;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Tbss;
using VnisCore.TvanInvoice.Application.ResponseTvan.Models;

namespace VnisCore.TvanInvoice.Application.ResponseTvan.Interfaces
{
    public interface IInvoiceErrorService
    {
        Task<List<BaseInvoiceHeader>> GetInvoiceAsync(Guid tenantId, List<QueryInvoiceInfoModel> invoiceInfo);

        Task SaveTvanInfoAsync(Guid tenantId, long fileId, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel, TransmissionPartnerEnum transmissionPartner, decimal tenantGroup);

        Task<long> UploadAndSaveFileAsync(Guid tenantId, string xml, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel);

        Task<List<BaseInvoiceHeader>> GetInvoiceAsyncV2(Guid tenantId, string messageCode);

        /// <summary>
        /// chỉ dùng cho TBSS hóa đơn ngoài hệ thống
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="MessageCode">mã thông điệp gửi lên 300</param>
        /// <returns></returns>
        Task<TbssHeaderEntity> GetInvoiceNgoaiHeThongAsync(Guid tenantId, string MessageCode);

        /// <summary>
        /// Lưu file cho TBSS hóa đơn ngoài hệ thống
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="tbssHeader"></param>
        /// <param name="xml"></param>
        /// <param name="responseTvanModel"></param>
        /// <returns></returns>
        Task<long> UploadAndSaveFileHĐNgoaiHeThongAsync(Guid tenantId, long tbssHeaderId, string xml, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel);

        /// <summary>
        /// Lưu thông tin TBSS cho hóa đơn ngoài hệ thống
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="fileId"></param>
        /// <param name="tbssHeaderId"></param>
        /// <param name="responseTvanModel"></param>
        /// <param name="transmissionPartner"></param>
        /// <returns></returns>
        Task SaveTvanInfoHĐNgoaiHeThongAsync(Guid tenantId, long fileId, TbssHeaderEntity tbssHeader, TDiepModel<TTChungModel, DLieuInvoiceErrorResponseModel> responseTvanModel, TransmissionPartnerEnum transmissionPartner);
    }
}
