namespace VnisCore.Catalog.Application.AdministrativeDivision.Model
{

    public class CRMHoaDonDienTu
    {
        public string maDongHo { get; set; }
        public string nam { get; set; }
        public string thang { get; set; }
        public string chiSoDau { get; set; }
        public string ngayDoc { get; set; }
        public string chiSoCuoi { get; set; }
        public string soHoaDon { get; set; }
        public string seriHoaDon { get; set; }
        public string ngayLapHoaDon { get; set; }
        public string loaiHoaDon { get; set; }
        public string maDoiTuongGia { get; set; }
        public string phiBVMT { get; set; }
        public string phiThai { get; set; }
        public string phiBVRung { get; set; }
        public string tongTien { get; set; }
        public string ngayDauKy { get; set; }
        public string ngayCuoiKy { get; set; }
        public string ngayHoaDon { get; set; }
        public string nguoiInHoaDon { get; set; }
        public string daThanhToan { get; set; }
        public string nguoiThuTien { get; set; }
        public string phiVAT { get; set; }
        public string maLienKet { get; set; }
        public string maDoiTuong { get; set; }
        public string tenKhachHang { get; set; }
        public string tuyenKhuVuc { get; set; }
        public string soHopDong { get; set; }
        public string diaChi { get; set; }
        public string diaChiSuDungNuoc { get; set; }
        public string mucDichSuDung { get; set; }
        public string thuTuGhi { get; set; }
        public string ghiChu { get; set; }
        public string maKhachHang { get; set; }
        public string canBoDoc { get; set; }
        public string maTuyenDoc { get; set; }
        public string maKhuVuc { get; set; }
        public string truyThu { get; set; }
        public string maDongHoCu { get; set; }
        public string chiSoDauCu { get; set; }
        public string chiSoCuoiCu { get; set; }
        public string ngayThayDongHo { get; set; }
        public string maSoThue { get; set; }
        public string soNhanKhau { get; set; }
        public string ngaySua { get; set; }
        public string tieuThu { get; set; }
        public string tongTienBangChu { get; set; }
        public string tenTuyenDoc { get; set; }
        public string tenKhuVuc { get; set; }
        public string hinhThucThanhToan { get; set; }
        public string seriDongHo { get; set; }
        public string seriDongHoCu { get; set; }
        public string thanhTien { get; set; }
        public string soHoDungChung { get; set; }
        public string mucVAT { get; set; }
        public string mucBVMT { get; set; }
        public string maVung { get; set; }
        public string maDonVi { get; set; }
        public string phiDuyTriDauNoi { get; set; }
        public string soDienThoai { get; set; }
        public string tenNhaMang { get; set; }
        public string taiKhoanNganHang { get; set; }
        public string tenNganHang { get; set; }
        public string tenTaiKhoanNH { get; set; }
        public string tenThuongGoi { get; set; }
        public string maNguoiThu { get; set; }
        public string maTuyenThu { get; set; }
        public string soNuocKhuyenMai { get; set; }
        public string thanhTienGiam { get; set; }
        public string tienPhiVATGiam { get; set; }
        public string tienPhiBVMTGiam { get; set; }
        public string laHoaDonDienTu { get; set; }
        public string maHoaDonCha { get; set; }
        public string trangThaiPhatHanh { get; set; }
        public string maPhamVi { get; set; }
        public string currentMaLienKet { get; set; }
        public string soHoaDonInt { get; set; }
        public string email { get; set; }
        public string inGopHoaDon { get; set; }
    }
}