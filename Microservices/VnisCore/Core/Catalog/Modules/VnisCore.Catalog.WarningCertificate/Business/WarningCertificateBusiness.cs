using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.GenerateContentEmail;
using Core.Shared.Services;

using Microsoft.Extensions.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Catalog.WarningCertificate.Interface;
using VnisCore.Catalog.WarningCertificate.IRepository;
using VnisCore.Core.MongoDB.Entities.Invoices;
using VnisCore.Core.MongoDB.IRepository;

namespace VnisCore.Catalog.WarningCertificate.Business
{
    public class WarningCertificateBusiness : IWarningCertificateBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly ITenantRepository _tenantRepository;
        private readonly ISettingService _settingService;
        private readonly IVnisCoreWarningExpiredHistoryRepository _vnisCoreWarningExpiredHistoryRepository;
        private readonly IConfiguration _configuration;
        private readonly IUsbTokenRepository _usbTokenRepository;
        private readonly ISendMailWarningCertificateBusiness _sendMailWarningCertificateBusiness;


        public WarningCertificateBusiness(
            IAppFactory appFactory,
            ITenantRepository tenantRepository,
            ISettingService settingService,
            IVnisCoreWarningExpiredHistoryRepository vnisCoreWarningExpiredHistoryRepository,
            IUsbTokenRepository usbTokenRepository,
            IConfiguration configuration,
            ISendMailWarningCertificateBusiness sendMailWarningCertificateBusiness)
        {
            _appFactory = appFactory;
            _tenantRepository = tenantRepository;
            _settingService = settingService;
            _vnisCoreWarningExpiredHistoryRepository = vnisCoreWarningExpiredHistoryRepository;
            _usbTokenRepository = usbTokenRepository;
            _configuration = configuration;
            _sendMailWarningCertificateBusiness = sendMailWarningCertificateBusiness;
        }

        public async Task WarningCertificateBusinessAsync()
        {
            //var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
            int.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var isEnableMongoDbLocalTime);
            int.TryParse(_configuration["Settings:ConfigNumberOfTimesWarning"], out var configNumberOfTimesWarning);

            // cấu hình số lần gửi mail lỗi sẽ ko gửi nữa. Tránh spam mail
            // mặc định 5 lần
            if (configNumberOfTimesWarning <= 0)
                configNumberOfTimesWarning = 5;

            var mailSaleVnpay = _configuration.GetSection("Settings:MailSaleVnpay").Value;
            if (string.IsNullOrEmpty(mailSaleVnpay))
            {
                mailSaleVnpay = "<EMAIL>";
            }

            // START => LẤY ALL TENANT - Improvement: CÓ THỂ SET CACHE ĐƯỢC (Vì TenantId ko bao giờ thay đổi)
            var tenants = await _tenantRepository.GetListTenantsAsync();
            if (!tenants.Any())
                return;
            
            foreach (var tenant in tenants)
            {
                var warningCertificates = new List<GenerateContentEmailWarningCertificateSendData>();
                var tenantId = tenant.Id;

                var usbTokensByTenantId = await _usbTokenRepository.GetUsbTokenByTenantIdAsync(tenantId);
                if (!usbTokensByTenantId.Any())
                    continue;

                // Email người nhận thông báo chứng thư số sắp hết hạn
                var settingEmailCustomer = (await _settingService.GetByCodeAsync(tenantId, SettingKey.EmailReceiveNotifyCertificateExpired.ToString()))?.Value;
                if (string.IsNullOrEmpty(settingEmailCustomer) || settingEmailCustomer == "#")
                {
                    settingEmailCustomer = tenant.Emails;
                }

                // Lấy cấu hình chu kỳ quét cảnh báo (theo tuần or theo ngày) theo từng tenant
                var settingWarningPeriod = (await _settingService.GetByCodeAsync(tenantId, SettingKey.CertificateWarningPeriod.ToString()))?.Value;

                // Lấy cấu hình gửi cảnh báo khi số ngày hiệu lực của chứng thư số còn lại
                var settingCertificateExpiredDate = (await _settingService.GetByCodeAsync(tenantId, SettingKey.CertificateExpiredDate.ToString()))?.Value;

                // Lấy lần cảnh báo gần nhất theo cấu hình
                //var lastWarning = await _vnisCoreWarningExpiredHistoryRepository.GetLastWarningExpiredAsync(tenantId, WarningType.WarningCertificate, short.Parse(settingWarningPeriod));

                // Nếu không có lần cảnh báo gần nhất thì
                // => có thể chưa cảnh báo lần nào
                // => có thể là đổi cấu hình từ cảnh báo theo (tuần => ngày) or từ (ngày => tuần) sau khi nhận cảnh báo lần gần nhất theo cấu hình trc đó
                
                foreach (var usbToken in usbTokensByTenantId)
                {
                    // Nếu số lần gửi bị fail quá thời gian config sẽ ko gửi nữa
                    var allTimesFails = await _vnisCoreWarningExpiredHistoryRepository.GetAllTimesFailWarningExpiredBySerialNumberAsync(tenantId, usbToken.SerialNumber, WarningType.WarningCertificate, short.Parse(settingWarningPeriod));
                    if (allTimesFails.Count > configNumberOfTimesWarning)
                        continue;

                    var lastWarningBySerialNumber = await _vnisCoreWarningExpiredHistoryRepository.GetLastWarningExpiredBySerialNumberAsync(tenantId, usbToken.SerialNumber, WarningType.WarningCertificate, short.Parse(settingWarningPeriod));

                    if (lastWarningBySerialNumber == null)
                    {
                        // số ngày hiệu lực còn lại
                        var remainingDaysOfValidity = (usbToken.EndDate.Date - DateTime.Now.Date).Days;
                        
                        // nếu số ngày hiệu lực còn lại bé hơn or bằng Số ngày cảnh báo chứng thư số sẽ hết hạn thì gửi mail cảnh báo
                        if (remainingDaysOfValidity > int.Parse(settingCertificateExpiredDate))
                            continue;

                        // Vì dùng message để publish nên phải đánh dấu cái CTS đấy đang gửi, tránh gửi lặp mail trong TH đã publish mess xử lý gửi mail nhưng worker vẫn chạy
                        var warningHistoryEntity = new MongoExpiredWarningHistoryEntity
                        {
                            Id = Guid.NewGuid(),
                            CreationTime = DateTime.Now,
                            TenantId = tenantId,
                            WarningType = (short)WarningType.WarningCertificate,
                            Status = (short)WarningStatus.Sending,
                            SerialNumber = usbToken.SerialNumber,
                            EndDate = isEnableMongoDbLocalTime > 0 ? (usbToken.EndDate == usbToken.EndDate.ToLocalTime() ? usbToken.EndDate.AddHours(7) : usbToken.EndDate.ToLocalTime()) : usbToken.EndDate,
                            WarningPeriod = short.Parse(settingWarningPeriod)
                        };
                        
                        await _vnisCoreWarningExpiredHistoryRepository.InsertAsync(warningHistoryEntity);
                        warningCertificates.Add(new GenerateContentEmailWarningCertificateSendData
                        {
                            ExpiredWarningHistoryId = warningHistoryEntity.Id,
                            Action = EmailAction.SendWarningCertificateExpried.ToString(),
                            TenantId = tenantId,
                            RemainingDaysOfValidity = remainingDaysOfValidity,
                            EndDate = usbToken.EndDate,
                            SubjectName = usbToken.SubjectName,
                            SerialNumber = usbToken.SerialNumber,
                            CustomerEmails = settingEmailCustomer,
                            SaleEmails = mailSaleVnpay,
                            WarningType = (short)WarningType.WarningCertificate,
                            WarningPeriod = short.Parse(settingWarningPeriod)
                        });
                    }
                    else
                    {
                        var remainingDaysOfValidity = (usbToken.EndDate.Date - DateTime.Now.Date).Days;

                        // nếu số ngày hiệu lực còn lại bé hơn or bằng Số ngày cảnh báo chứng thư số sẽ hết hạn thì gửi mail cảnh báo
                        if (remainingDaysOfValidity > int.Parse(settingCertificateExpiredDate))
                            continue;

                        // theo tuần thì phải check xem thời gian lần nhất gửi đã lớn hơn or bằng 7 ngày (1 tuần) hay chưa
                        var lastTimeWarning = lastWarningBySerialNumber.CreationTime.Date;

                        // số ngày đã cảnh báo hết hạn gần nhất
                        var soNgayDaCanhBaoLanGanNhat = (DateTime.Now.Date - lastTimeWarning).Days;

                        var warningPeriod = lastWarningBySerialNumber.WarningPeriod;
                        if (warningPeriod == (short)WarningPeriod.TheoTuan) // theo tuần
                        {
                            // giá trị 7 tương đương vs 1 tuần
                            // nếu ngày đấy đã cảnh báo rồi 
                            if (soNgayDaCanhBaoLanGanNhat >= 7)
                            {
                                // Vì dùng message để publish nên phải đánh dấu cái CTS đấy đang gửi, tránh gửi lặp mail trong TH đã publish mess xử lý gửi mail nhưng worker vẫn chạy
                                var warningHistoryEntity = new MongoExpiredWarningHistoryEntity
                                {
                                    Id = Guid.NewGuid(),
                                    CreationTime = DateTime.Now,
                                    TenantId = tenantId,
                                    WarningType = (short)WarningType.WarningCertificate,
                                    Status = (short)WarningStatus.Sending,
                                    SerialNumber = usbToken.SerialNumber,
                                    EndDate = isEnableMongoDbLocalTime > 0 ? (usbToken.EndDate == usbToken.EndDate.ToLocalTime() ? usbToken.EndDate.AddHours(7) : usbToken.EndDate.ToLocalTime()) : usbToken.EndDate,
                                    WarningPeriod = short.Parse(settingWarningPeriod)
                                };

                                await _vnisCoreWarningExpiredHistoryRepository.InsertAsync(warningHistoryEntity);

                                warningCertificates.Add(new GenerateContentEmailWarningCertificateSendData
                                {
                                    ExpiredWarningHistoryId = warningHistoryEntity.Id,
                                    Action = EmailAction.SendWarningCertificateExpried.ToString(),
                                    TenantId = tenantId,
                                    RemainingDaysOfValidity = remainingDaysOfValidity,
                                    EndDate = usbToken.EndDate,
                                    SubjectName = usbToken.SubjectName,
                                    SerialNumber = usbToken.SerialNumber,
                                    CustomerEmails = settingEmailCustomer,
                                    SaleEmails = mailSaleVnpay,
                                    WarningType = (short)WarningType.WarningCertificate,
                                    WarningPeriod = short.Parse(settingWarningPeriod)
                                });
                            }
                        }
                        else // theo ngày
                        {
                            if (lastTimeWarning < DateTime.Now.Date)
                            {
                                // Vì dùng message để publish nên phải đánh dấu cái CTS đấy đang gửi, tránh gửi lặp mail trong TH đã publish mess xử lý gửi mail nhưng worker vẫn chạy
                                var warningHistoryEntity = new MongoExpiredWarningHistoryEntity
                                {
                                    Id = Guid.NewGuid(),
                                    CreationTime = DateTime.Now,
                                    TenantId = tenantId,
                                    WarningType = (short)WarningType.WarningCertificate,
                                    Status = (short)WarningStatus.Sending,
                                    SerialNumber = usbToken.SerialNumber,
                                    EndDate = isEnableMongoDbLocalTime > 0 ? (usbToken.EndDate == usbToken.EndDate.ToLocalTime() ? usbToken.EndDate.AddHours(7) : usbToken.EndDate.ToLocalTime()) : usbToken.EndDate,
                                    WarningPeriod = short.Parse(settingWarningPeriod)
                                };

                                await _vnisCoreWarningExpiredHistoryRepository.InsertAsync(warningHistoryEntity);
                                warningCertificates.Add(new GenerateContentEmailWarningCertificateSendData
                                {
                                    ExpiredWarningHistoryId = warningHistoryEntity.Id,
                                    Action = EmailAction.SendWarningCertificateExpried.ToString(),
                                    TenantId = tenantId,
                                    RemainingDaysOfValidity = remainingDaysOfValidity,
                                    EndDate = usbToken.EndDate,
                                    SubjectName = usbToken.SubjectName,
                                    SerialNumber = usbToken.SerialNumber,
                                    CustomerEmails = settingEmailCustomer,
                                    SaleEmails = mailSaleVnpay,
                                    WarningType = (short)WarningType.WarningCertificate,
                                    WarningPeriod = short.Parse(settingWarningPeriod)
                                });
                            }
                        }
                    }
                }
                
                // publish message gửi mail
                if (warningCertificates.Any())
                {
                    foreach (var warningCertificate in warningCertificates)
                    {
                        await _sendMailWarningCertificateBusiness.SendMailBusiness(warningCertificate);
                        //await distributedEventBus.PublishAsync(warningCertificate);
                    }
                }
            }
        }
    }
}
