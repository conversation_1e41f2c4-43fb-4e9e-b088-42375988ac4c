using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Catalog.Infrastructure.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.Catalog.Infrastructure.Repository
{
    public class ProductRepository : EfCoreRepository<VnisCoreOracleDbContext, ProductEntity, long>, IProductRepository
    {
        public ProductRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<ProductEntity> GetByIdAsync(long id, Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();

            var result = dbContext
                        .Set<ProductEntity>()
                        .Where(x => x.Id == id && x.TenantId == tenantId && x.IsDeleted == false)
                        .FirstOrDefault();

            return result;
        }

        public async Task<List<ProductEntity>> GetByIdsAsync(List<long> ids, Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();

            var result = dbContext
                        .Set<ProductEntity>()
                        .Where(x => ids.Contains(x.Id) && x.TenantId == tenantId && x.IsDeleted == false)
                        .ToList();

            return result;
        }
    }
}
