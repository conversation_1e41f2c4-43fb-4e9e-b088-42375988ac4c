<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Version>5.12.12</Version>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="License\**" />
    <EmbeddedResource Remove="License\**" />
    <None Remove="License\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="10.1.1" />
    <PackageReference Include="Dapper" Version="2.0.123" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Application.Contracts\VnisCore.Core.Oracle.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.AutoMapper\Core.AutoMapper.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Shared\Core.Shared.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="VNILicenceClient">
      <HintPath>..\..\..\..\..\..\lib\VNILicenceClient.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Dto\" />
  </ItemGroup>
</Project>
