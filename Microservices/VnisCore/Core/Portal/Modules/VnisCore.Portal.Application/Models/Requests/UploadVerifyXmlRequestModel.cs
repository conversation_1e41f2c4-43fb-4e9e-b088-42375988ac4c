using Core.Shared.Dto;
using MediatR;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Portal.Application.Models.Requests
{
    public class UploadVerifyXmlRequestModel : IRequest<FileDto>
    {
        [Required(ErrorMessage = "Chưa nhập mã xác thực")]
        public string Captcha { get; set; }

        [Required(ErrorMessage = "Chưa nhập mã xác thực")]
        public string Secret { get; set; }

        [Required(ErrorMessage = "File xml không được để trống")]
        public string File { get; set; }

        [Required(ErrorMessage = "<PERSON><PERSON><PERSON> chứng từ không được để trống")]
        public short SearchType { get; set; } = 1;
    }
}
