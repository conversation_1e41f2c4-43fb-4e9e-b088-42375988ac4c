using Core;
using Core.Localization.Resources.AbpLocalization;

using Microsoft.Extensions.Localization;

using System;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Portal.Application.Interfaces;
using VnisCore.Portal.Application.Repositories;

namespace VnisCore.Portal.Application.Services
{
    public class Invoice03UnOfficialService : IInvoiceMediaService
    {
        private readonly IInvoiceHeaderRepository<Invoice03HeaderEntity> _repoInvoice03Header;
        private readonly IInvoiceUnOfficialRepository<Invoice03UnOfficialEntity> _repoInvoiceUnofficial;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        public Invoice03UnOfficialService(
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceHeaderRepository<Invoice03HeaderEntity> repoInvoice03Header,
            IInvoiceUnOfficialRepository<Invoice03UnOfficialEntity> repoInvoiceUnofficial)
        {
            _repoInvoice03Header = repoInvoice03Header;
            _repoInvoiceUnofficial = repoInvoiceUnofficial;
            _localizier = localizier;
        }

        public async Task<BaseInvoiceMedia> GetByInvoiceAsync(Guid tenantId, long id, short templateNo, string serialNo, string invoiceNo)
        {
            var invoiceHeader = await _repoInvoice03Header.GetByIdAsync(id);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            var invoiceUnOfficial = await _repoInvoiceUnofficial.GetLastByInvoiceIdAsync(invoiceHeader.Id);

            if (invoiceUnOfficial == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindUnofficial"]);

            return invoiceUnOfficial;
        }

        public async Task<BaseInvoiceMedia> GetByIdInvoiceAsync(short templatNo, string serialNo, string invoiceNo, string sellerTaxCode, string transactionId)
        {
            var invoiceHeader = await _repoInvoice03Header.GetInvoiceAsync(templatNo, serialNo, invoiceNo, sellerTaxCode, transactionId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            var invoiceUnOfficial = await _repoInvoiceUnofficial.GetLastByInvoiceIdAsync(invoiceHeader.Id);

            if (invoiceUnOfficial == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindUnofficial"]);

            return invoiceUnOfficial;
        }

        public async Task<BaseInvoiceHeader> GetInvoiceAsync(Guid tenantId, long id, short templateNo, string serialNo, string invoiceNo)
        {
            var invoiceHeader = await _repoInvoice03Header.GetByIdAsync(id);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            return invoiceHeader;
        }
    }
}
