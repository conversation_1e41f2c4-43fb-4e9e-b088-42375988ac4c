using Core;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Portal.Application.Interfaces;
using VnisCore.Portal.Application.Repositories;

namespace VnisCore.Portal.Application.Services
{
    public class Invoice01UnOfficialService : IInvoiceMediaService
    {
        private readonly IInvoiceHeaderRepository<Invoice01HeaderEntity> _repoInvoice01Header;
        private readonly IInvoiceUnOfficialRepository<Invoice01UnOfficialEntity> _repoInvoiceUnofficial;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        public Invoice01UnOfficialService(
            IStringLocalizer<CoreLocalizationResource> localizier,
            IInvoiceHeaderRepository<Invoice01HeaderEntity> repoInvoice01Header,
            IInvoiceUnOfficialRepository<Invoice01UnOfficialEntity> repoInvoiceUnofficial)
        {
            _repoInvoice01Header = repoInvoice01Header;
            _repoInvoiceUnofficial = repoInvoiceUnofficial;
            _localizier = localizier;
        }

        public async Task<BaseInvoiceMedia> GetByInvoiceAsync(Guid tenantId, long id, short templateNo, string serialNo, string invoiceNo)
        {
            var invoiceHeader = await _repoInvoice01Header.GetByIdAsync(id);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            var invoiceUnOfficial = await _repoInvoiceUnofficial.GetLastByInvoiceIdAsync(invoiceHeader.Id);

            if (invoiceUnOfficial == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindUnofficial"]);

            return invoiceUnOfficial;
        }

        public async Task<BaseInvoiceMedia> GetByIdInvoiceAsync(short templatNo, string serialNo, string invoiceNo, string sellerTaxCode, string transactionId)
        {
            var invoiceHeader = await _repoInvoice01Header.GetInvoiceAsync(templatNo, serialNo, invoiceNo, sellerTaxCode, transactionId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            var invoiceUnOfficial = await _repoInvoiceUnofficial.GetLastByInvoiceIdAsync(invoiceHeader.Id);

            if (invoiceUnOfficial == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindUnofficial"]);

            return invoiceUnOfficial;
        }

        public async Task<BaseInvoiceHeader> GetInvoiceAsync(Guid tenantId, long id, short templateNo, string serialNo, string invoiceNo)
        {
            var invoiceHeader = await _repoInvoice01Header.GetByIdAsync(id);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

            return invoiceHeader;
        }
    }
}
