using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.SettingManagement;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.TenantManagement;

using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Portal.Application.Business;
using VnisCore.Portal.Application.Interfaces;

namespace VnisCore.Portal.Application.Services
{
    public class PortalService : IPortalService
    {
        private readonly HttpClient _client;
        private readonly IInvoiceMediaFactory _invoiceMediaFactory;
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IExportPdfBusiness _exportPdfBusiness;

        public PortalService(
            IConfiguration configuration,
            IInvoiceMediaFactory invoiceMediaFactory,
            IHttpClientFactory httpClientFactory,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IExportPdfBusiness exportPdfBusiness)
        {
            _client = httpClientFactory.CreateClient("exportpdfs");
            _localizier = localizier;
            _appFactory = appFactory;
            _invoiceMediaFactory = invoiceMediaFactory;
            _configuration = configuration;
            _exportPdfBusiness = exportPdfBusiness;
        }

        public string CheckOldVersionXml(string xml)
        {
            //kiểm tra xem có phải xml ký = signclient bản cũ không thì bỏ qua các thẻ signature không có chữ ký
            // kiểm tra bằng cách xem thẻ invoice có attribute xmlns không, có thì là signclient bản cũ, k có thì là signclient đã sửa 
            if (xml.Contains("http://laphoadon.gdt.gov.vn/2014/09/invoicexml/v1"))
            {
                var remove1 = "<Signature id=\"seller\" />";
                var remove2 = "<Signature id=\"buyer\" />";

                if (xml.Contains(remove1))
                    xml = xml.Replace(remove1, "");

                if (xml.Contains(remove2))
                    xml = xml.Replace(remove2, "");
            }

            return xml;
        }

        public async Task<string> ReadFileAsString(IFormFile formFile)
        {
            var result = new StringBuilder();
            using (var reader = new StreamReader(formFile.OpenReadStream()))
            {
                while (reader.Peek() >= 0)
                    result.AppendLine(await reader.ReadLineAsync());
            }

            return result.ToString();
        }

        public async Task<FileDto> UnOfficialAsync(string xml)
        {
            var tenantRepos = _appFactory.Repository<Tenant, Guid>();

            XmlDocument document = new XmlDocument();
            document.PreserveWhitespace = true;
            document.LoadXml(xml);

            var elementMST = document.SelectSingleNode(@"//NBan/MST");
            if (elementMST == null)
                throw new UserFriendlyException("Xml không chứa thẻ MST người bán");

            var taxCode = elementMST.InnerText;
            var tenantByTaxcode = await tenantRepos.FirstOrDefaultAsync(x => x.TaxCode == taxCode);
            if (tenantByTaxcode == null)
                throw new UserFriendlyException("Không tìm thấy thông tin mã số thuế người bán");

            var elementTemplateNo = document.SelectSingleNode(@"//TTChung/KHMSHDon");
            if (elementTemplateNo == null)
                throw new UserFriendlyException("Xml không chứa thẻ KHMSHDon");
            var templateNo = short.Parse(elementTemplateNo.InnerText);

            var elementSerialNo = document.SelectSingleNode(@"//TTChung/KHHDon");
            if (elementSerialNo == null)
                throw new UserFriendlyException("Xml không chứa thẻ KHHDon");
            var serialNo = elementSerialNo.InnerText;
            var subSerialNo = serialNo.Substring(3, 1);

            var elementInvoiceNo = document.SelectSingleNode(@"//TTChung/SHDon");
            if (elementInvoiceNo == null)
                throw new UserFriendlyException("Xml không chứa thẻ SHDon");
            var number = int.Parse(elementInvoiceNo.InnerText);

            if (templateNo == VnisType._01GTKT.GetHashCode())
            {
                var invoice01Header = _appFactory.Repository<Invoice01HeaderEntity, long>().AsNoTracking();
                var invoice01Entity = await invoice01Header.FirstOrDefaultAsync(x => x.Number == number && x.SerialNo == serialNo && x.TemplateNo == templateNo && x.TenantId == tenantByTaxcode.Id);
                if (invoice01Entity == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

                var tenantId = invoice01Entity.TenantId;
                //lấy service
                var service = _invoiceMediaFactory.GetService(MediaFileType.InvoiceUnOfficial, invoice01Entity.TemplateNo);
                var invoice = await service.GetInvoiceAsync(tenantByTaxcode.Id, invoice01Entity.Id, invoice01Entity.TemplateNo, invoice01Entity.SerialNo, invoice01Entity.InvoiceNo);

                var idInvoices = new List<long>();
                idInvoices.Add(invoice.Id);

                #region
                //var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoices), Encoding.UTF8, "application/json");

                //HttpRequestMessage requestMess = new HttpRequestMessage(HttpMethod.Post, $"api/ExportPdf/InvoiceUnOfficial/1");
                //requestMess.Content = httpContent;

                //var result = await _client.SendAsync(requestMess);

                //if (result.StatusCode != HttpStatusCode.OK)
                //    throw new UserFriendlyException("Có lỗi trong quá trình xử lý");

                //var content = await result.Content.ReadAsStringAsync();

                //return JsonConvert.DeserializeObject<FileDto>(content);
                #endregion

                return await _exportPdfBusiness.ExportPdfAsync(idInvoices, VnisType._01GTKT.ToString());
            }
            if (templateNo == VnisType._02GTTT.GetHashCode())
            {
                var invoice02Header = _appFactory.Repository<Invoice02HeaderEntity, long>().AsNoTracking();
                var invoice02Entity = await invoice02Header.FirstOrDefaultAsync(x => x.Number == number && x.SerialNo == serialNo && x.TemplateNo == templateNo && x.TenantId == tenantByTaxcode.Id);
                if (invoice02Entity == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

                var tenantId = invoice02Entity.TenantId;
                //lấy service
                var service = _invoiceMediaFactory.GetService(MediaFileType.InvoiceUnOfficial, invoice02Entity.TemplateNo);
                var invoice = await service.GetInvoiceAsync(tenantByTaxcode.Id, invoice02Entity.Id, invoice02Entity.TemplateNo, invoice02Entity.SerialNo, invoice02Entity.InvoiceNo);

                var idInvoices = new List<long>();
                idInvoices.Add(invoice.Id);

                #region
                //var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoices), Encoding.UTF8, "application/json");

                //var host = _configuration.GetSection("Microservices:ExportPdf:Endpoint");
                //HttpRequestMessage requestMess = new HttpRequestMessage(HttpMethod.Post, $"api/ExportPdf/InvoiceUnOfficial/2");
                //requestMess.Content = httpContent;

                //var result = await _client.SendAsync(requestMess);

                //if (result.StatusCode != HttpStatusCode.OK)
                //    throw new UserFriendlyException("Có lỗi trong quá trình xử lý");

                //var content = await result.Content.ReadAsStringAsync();

                //return JsonConvert.DeserializeObject<FileDto>(content);
                #endregion

                return await _exportPdfBusiness.ExportPdfAsync(idInvoices, VnisType._02GTTT.ToString());
            }
            if (templateNo == RegistrationInvoiceType.CTu.GetHashCode())
            {
                if (subSerialNo == SerialNoInvoiceType.N.ToString())
                {
                    var invoice03Header = _appFactory.Repository<Invoice03HeaderEntity, long>().AsNoTracking();
                    var invoice03Entity = await invoice03Header.FirstOrDefaultAsync(x => x.Number == number && x.SerialNo == serialNo && x.TemplateNo == templateNo && x.TenantId == tenantByTaxcode.Id);
                    if (invoice03Entity == null)
                        throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

                    var tenantId = invoice03Entity.TenantId;
                    //lấy service
                    var service = _invoiceMediaFactory.GetService(MediaFileType.InvoiceUnOfficial, 03);
                    var invoice = await service.GetInvoiceAsync(tenantByTaxcode.Id, invoice03Entity.Id, invoice03Entity.TemplateNo, invoice03Entity.SerialNo, invoice03Entity.InvoiceNo);

                    var idInvoices = new List<long>();
                    idInvoices.Add(invoice.Id);

                    #region
                    //var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoices), Encoding.UTF8, "application/json");

                    //var host = _configuration.GetSection("Microservices:ExportPdf:Endpoint");
                    //HttpRequestMessage requestMess = new HttpRequestMessage(HttpMethod.Post, $"api/ExportPdf/InvoiceUnOfficial/3");
                    //requestMess.Content = httpContent;

                    //var result = await _client.SendAsync(requestMess);

                    //if (result.StatusCode != HttpStatusCode.OK)
                    //    throw new UserFriendlyException("Có lỗi trong quá trình xử lý");

                    //var content = await result.Content.ReadAsStringAsync();

                    //return JsonConvert.DeserializeObject<FileDto>(content);
                    #endregion

                    return await _exportPdfBusiness.ExportPdfAsync(idInvoices, VnisType._03XKNB.ToString());
                }
                if (subSerialNo == SerialNoInvoiceType.B.ToString())
                {
                    var invoice04Header = _appFactory.Repository<Invoice04HeaderEntity, long>().AsNoTracking();
                    var invoice04Entity = await invoice04Header.FirstOrDefaultAsync(x => x.Number == number && x.SerialNo == serialNo && x.TemplateNo == templateNo && x.TenantId == tenantByTaxcode.Id);
                    if (invoice04Entity == null)
                        throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

                    var tenantId = invoice04Entity.TenantId;
                    //lấy service
                    var service = _invoiceMediaFactory.GetService(MediaFileType.InvoiceUnOfficial, 04);
                    var invoice = await service.GetInvoiceAsync(tenantByTaxcode.Id, invoice04Entity.Id, invoice04Entity.TemplateNo, invoice04Entity.SerialNo, invoice04Entity.InvoiceNo);

                    var idInvoices = new List<long>();
                    idInvoices.Add(invoice.Id);

                    #region
                    //var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoices), Encoding.UTF8, "application/json");

                    //var host = _configuration.GetSection("Microservices:ExportPdf:Endpoint");
                    //HttpRequestMessage requestMess = new HttpRequestMessage(HttpMethod.Post, $"api/ExportPdf/InvoiceUnOfficial/4");
                    //requestMess.Content = httpContent;

                    //var result = await _client.SendAsync(requestMess);

                    //if (result.StatusCode != HttpStatusCode.OK)
                    //    throw new UserFriendlyException("Có lỗi trong quá trình xử lý");

                    //var content = await result.Content.ReadAsStringAsync();

                    //return JsonConvert.DeserializeObject<FileDto>(content);
                    #endregion
                    //return await _invoiceUnofficialBusiness.UnOfficial(VnisType._04HGDL, idInvoices);
                    return await _exportPdfBusiness.ExportPdfAsync(idInvoices, VnisType._04HGDL.ToString());
                }
            }
            if (templateNo == VnisType._05TVDT.GetHashCode())
            {
                var ticketHeader = _appFactory.Repository<TicketHeaderEntity, long>().AsNoTracking();
                var ticketEntity = await ticketHeader.FirstOrDefaultAsync(x => x.Number == number && x.SerialNo == serialNo && x.TemplateNo == templateNo && x.TenantId == tenantByTaxcode.Id);
                if (ticketEntity == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindInvoice"]);

                var tenantId = ticketEntity.TenantId;
                //lấy service
                var service = _invoiceMediaFactory.GetService(MediaFileType.InvoiceUnOfficial, ticketEntity.TemplateNo);
                var invoice = await service.GetInvoiceAsync(tenantByTaxcode.Id, ticketEntity.Id, ticketEntity.TemplateNo, ticketEntity.SerialNo, ticketEntity.InvoiceNo);

                var idInvoices = new List<long>();
                idInvoices.Add(invoice.Id);

                #region
                //var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoices), Encoding.UTF8, "application/json");

                //HttpRequestMessage requestMess = new HttpRequestMessage(HttpMethod.Post, $"api/ExportPdf/InvoiceUnOfficial/5");
                //requestMess.Content = httpContent;

                //var result = await _client.SendAsync(requestMess);

                //if (result.StatusCode != HttpStatusCode.OK)
                //    throw new UserFriendlyException("Có lỗi trong quá trình xử lý");

                //var content = await result.Content.ReadAsStringAsync();

                //return JsonConvert.DeserializeObject<FileDto>(content);
                #endregion

                //return await _invoiceUnofficialBusiness.UnOfficial(VnisType._05TVDT, idInvoices);
                return await _exportPdfBusiness.ExportPdfAsync(idInvoices, VnisType._05TVDT.ToString());
            }
            else
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.CannotFindTemplate"]);
            }
        }

        public async Task<FileDto> UnOfficialPITAsync(long id)
        {
            var idInvoices = new List<long>();
            idInvoices.Add(id);

            #region
            //var httpContent = new StringContent(JsonConvert.SerializeObject(idInvoices), Encoding.UTF8, "application/json");

            //HttpRequestMessage requestMess = new HttpRequestMessage(HttpMethod.Post, $"api/ExportPdf/InvoiceUnOfficial/pit");
            //requestMess.Content = httpContent;

            //var result = await _client.SendAsync(requestMess);

            //if (result.StatusCode != HttpStatusCode.OK)
            //    throw new UserFriendlyException("Có lỗi trong quá trình in chứng từ");

            //var content = await result.Content.ReadAsStringAsync();

            //return JsonConvert.DeserializeObject<FileDto>(content);
            #endregion

            //return await _invoiceUnofficialBusiness.PITUnOfficial(idInvoices);
            return await _exportPdfBusiness.ExportPdfAsync(idInvoices, "pit");
        }

        public async Task<bool> VerifyXml(string xml)
        {
            //var message = new HttpRequestMessage(HttpMethod.Post, "api/verifier/xml/invoice");
            var message = new HttpRequestMessage(HttpMethod.Post, "api/verifier/xml/multisiginvoice");
            if (xml.Contains("ds:Signature")) //biết có phải file bc26 không
                message = new HttpRequestMessage(HttpMethod.Post, "api/verifier/xml/bc26");

            message.Content = new StringContent(xml, Encoding.UTF8, "application/xml");

            // lấy cấu hình url server ký theo tenant
            var setting = await GetSettingAsync(Guid.Empty);
            var urlApi = setting.Where(x => x.Code == "UrlApi").FirstOrDefault().Value;
            var endPoint = setting.Where(x => x.Code == "EndPointApi").FirstOrDefault().Value;
            var result = await VerifyXmlAsync(endPoint, urlApi, xml);
            return result;

        }

        private async Task<List<Setting>> GetSettingAsync(Guid tenantId)
        {
            var repoSetting = _appFactory.Repository<Setting, Guid>().AsNoTracking();
            var setting = await repoSetting.Where(x => x.GroupCode == SettingKey.PortalVerify.ToString()).ToListAsync();

            if (setting == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Portal.DontHaveSignConfig"]);

            return setting;

        }

        public string GetContentVerify(string xml)
        {
            var from = xml.IndexOf("<invoice>");
            if (from == -1) //Nếu có thẻ <invoice></invoice> thì lấy nội dung bên trong thẻ
            {
                xml = Unespace(xml);
            }
            else //Nếu ok có thẻ <invoice></invoice> thì loại bỏ ký tự thừa
            {
                var to = xml.IndexOf("</invoice>");
                xml = xml.Substring(from, to - from + 10);
            }
            xml = CheckOldVersionXml(xml);
            return xml;
        }

        public async Task<bool> GetSettingCheckByHostAsync(Guid tenantId)
        {
            //var repoSetting = _coreUoW.GetRepository<ISettingRepository>();
            //var setting = await repoSetting.GetByKeyAsync(tenantCode, SettingKey.PortalPublicUncheckByTenant.ToString());
            var settingService = _appFactory.GetServiceDependency<ISettingService>();
            var setting = await settingService.GetByCodeAsync(tenantId,  SettingKey.PortalPublicUncheckByTenant.ToString());

            if (setting != null && setting.Value == "1")
                return true;
            return false;
        }

        private string Unespace(string xml)
        {
            //bỏ ký tự \ trong \" bị duplicate
            xml = xml.Replace("\\\"", "\"");

            //Đổi \\ thành \
            xml = xml.Replace("\\\\", "\\");

            //Đổi n thành \n
            xml = xml.Replace(">\n<", $"><");

            //bỏ ký tự đặc biệt ở đầu
            //Kiểm tra nếu ký tự đầu tiên ko phải < thì SubString(1, data.Length - 1). TAM THỜI ĐỂ THẾ NÀY
            var first = xml[0];
            if (first != '<')
            {
                xml = xml.Substring(1, xml.Length - 1);
            }
            return xml;
        }

        //private async Task<Setting> GetSettingAsync(Guid tenantCode)
        //{
        //    //var repoSetting = _coreUoW.GetRepository<ISettingRepository>();
        //    var setting = await repoSetting.GetByKeyAsync(tenantCode, SettingKey.UrlSignServer.ToString());
        //    if (setting == null)
        //        throw new Exception("Không có cấu hình server ký");
        //    return setting;
        //}

        private async Task<bool> VerifyXmlAsync(string host, string urlApi, string xml)
        {
            var client = new HttpClient();
            var content = new StringContent(xml, Encoding.UTF8, "application/xml");
            var result = await client.PostAsync(host + urlApi, content);
            var response = await result.Content.ReadAsStringAsync();
            if (result.StatusCode != HttpStatusCode.OK)
                return false;

            return true;
            //var message = new HttpRequestMessage(HttpMethod.Post, urlApi);
            //if (xml.Contains("ds:Signature")) //biết có phải file bc26 không
            //    message = new HttpRequestMessage(HttpMethod.Post, "api/verifier/xml/bc26");

            //message.Content = new StringContent(xml, Encoding.UTF8, "application/xml");

            //var client = new HttpClient
            //{
            //    BaseAddress = new Uri(host)
            //};
            //var response = await client.SendAsync(message);
            //await response.Content.ReadAsStringAsync();
            //if (response.StatusCode != HttpStatusCode.OK)
            //    return false;

            //return true;
        }


        //private async Task<bool> VerifyXmlAsync(string host, string xml)
        //{
        //    var message = new HttpRequestMessage(HttpMethod.Post, "/api/verifier/xml/invoice");
        //    if (xml.Contains("ds:Signature")) //biết có phải file bc26 không
        //        message = new HttpRequestMessage(HttpMethod.Post, "/api/verifier/xml/bc26");

        //    message.Content = new StringContent(xml, Encoding.UTF8, "text/plain");

        //    var client = new HttpClient
        //    {
        //        BaseAddress = new Uri(host)
        //    };
        //    var response = await client.SendAsync(message);
        //    await response.Content.ReadAsStringAsync();
        //    if (response.StatusCode != HttpStatusCode.OK)
        //        return false;

        //    return true;
        //}
    }
}
