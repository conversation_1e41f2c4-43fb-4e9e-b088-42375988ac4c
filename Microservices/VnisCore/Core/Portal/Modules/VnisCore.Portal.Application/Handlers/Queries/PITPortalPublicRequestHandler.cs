using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.TenantManagement;

using MediatR;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.PITDeductionDocument.Infrastructure.IRepository;
using VnisCore.PITDeductionDocument.Infrastructure.Models.Reponse;
using VnisCore.Portal.Application.Models.Requests;
using VnisCore.Portal.Application.Models.Responses;
using VnisCore.Portal.Application.Repositories;

namespace VnisCore.Portal.Application.Handlers.Queries
{
    public class PITPortalPublicRequestHandler : IRequestHandler<PITPortalPublicRequestModel, PortalPITDeductionDocumentResponse>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IPITDeductionDocumentRepository _pITDeductionDocumentRepository;

        public PITPortalPublicRequestHandler(IAppFactory appFactory,
                                         IStringLocalizer<CoreLocalizationResource> localizer,
                                         IPITDeductionDocumentRepository pITDeductionDocumentRepository)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _pITDeductionDocumentRepository = pITDeductionDocumentRepository;
        }

        public async Task<PortalPITDeductionDocumentResponse> Handle(PITPortalPublicRequestModel request, CancellationToken cancellationToken)
        {
            var response = await _pITDeductionDocumentRepository.GetByTransactionIdAsync(request.TransactionId);
            return response;

        }
    }
}
