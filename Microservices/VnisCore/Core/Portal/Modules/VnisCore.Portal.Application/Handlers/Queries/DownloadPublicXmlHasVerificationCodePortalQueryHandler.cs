using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.FileManager.Interfaces;
using MediatR;
using Microsoft.Extensions.Localization;
using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Portal.Application.Interfaces;
using VnisCore.Portal.Application.Models.Events;
using VnisCore.Portal.Application.Models.Requests;
using VnisCore.Portal.Application.Models.Responses;

namespace VnisCore.Portal.Application.Handlers.Queries
{
    public class DownloadPublicXmlHasVerificationCodePortalQueryHandler : IRequestHandler<DownloadPublicXmlHasVerificationCodePortalRequestModel, DownloadXmlPortalResponseModel>
    {
        private readonly IInvoiceMediaFactory _invoiceMediaFactory;
        private readonly IFileService _fileService;
        private readonly IMediator _mediator;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public DownloadPublicXmlHasVerificationCodePortalQueryHandler(
            IInvoiceMediaFactory invoiceMediaFactory,
            IFileService fileService,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IMediator mediator)
        {
            _invoiceMediaFactory = invoiceMediaFactory;
            _fileService = fileService;
            _mediator = mediator;
            _localizer = localizer;
        }

        public async Task<DownloadXmlPortalResponseModel> Handle(DownloadPublicXmlHasVerificationCodePortalRequestModel request, CancellationToken cancellationToken)
        {
            //lấy service
            var mediaType = GetMediaType(request.TemplateNo);

            var service = _invoiceMediaFactory.GetService(mediaType, request.TemplateNo);
            var file = await service.GetByIdInvoiceAsync(request.TemplateNo, request.SerialNo, request.InvoiceNo, request.SellerTaxCode, request.TransactionId);

            //download file từ minio về
            var pathFileMinio = $"{mediaType}/{file.TenantId}/{file.CreationTime.Year}/{file.CreationTime.Month:00}/{file.CreationTime.Day:00}/{file.CreationTime.Hour:00}/{file.PhysicalFileName}";

            var bytes = await _fileService.DownloadAsync(pathFileMinio);

            //check xml chỉ lấy thông tin thẻ HDon thôi
            var xml = Encoding.UTF8.GetString(bytes);
            var hdonStartIndex = xml.IndexOf("<HDon>");
            var hdonEndIndex = xml.IndexOf("</HDon>");

            xml = xml.Substring(hdonStartIndex, hdonEndIndex + 7 - hdonStartIndex);
            bytes = Encoding.UTF8.GetBytes(xml);

            var result = new DownloadXmlPortalResponseModel
            {
                ContentType = file.ContentType,
                Datas = bytes,
                FileName = file.FileName
            };

            await _mediator.Publish(new AfterDownloadXmlEventModel
            {
                Id = file.InvoiceHeaderId,
                Type = EnumExtension.ToEnum<VnisType>(request.TemplateNo),
                ViewAt = DateTime.Now,
                ViewAtUtc = DateTime.UtcNow
            }, cancellationToken);

            return result;
        }

        private MediaFileType GetMediaType(short templateNo)
        {
            switch (templateNo)
            {
                case 1:
                    return MediaFileType.Invoice01HasCodeTvanXml;
                case 2:
                    return MediaFileType.Invoice02HasCodeTvanXml;
                case 3:
                    return MediaFileType.Invoice03HasCodeTvanXml;
                case 4:
                    return MediaFileType.Invoice04HasCodeTvanXml; 
                case 5:
                    return MediaFileType.TicketHasCodeTvanXml;
                default:
                    //throw new UserFriendlyException($"Chưa phát triển tính năng download xml cho loại hóa đơn {templateNo}");
                    throw new UserFriendlyException(_localizer["Vnis.BE.Portal.CannotDowloadXml", new string[] { templateNo.ToString()}]);//TODO: multilanguage
            }
        }
    }
}
