using MediatR;
using VnisCore.Portal.Application.Models.Events;

namespace VnisCore.Portal.Application.Handlers.Events
{
    public class AfterPublicQueryEventHandler : NotificationHandler<AfterPublicQueryEventModel>
    {
        //private readonly IRabbitService _rabbitService;

        public AfterPublicQueryEventHandler()
        {
          //  _rabbitService = rabbitService;
        }

        protected override void Handle(AfterPublicQueryEventModel notification)
        {
            //_rabbitService.Publish(new RabbitResponseModel<InvoiceCommandResponseModel>
            //{
            //    Data = new InvoiceCommandResponseModel
            //    {
            //        Code = notification.InvoiceCode,
            //        Type = notification.Type,
            //        ActionAt = notification.ViewAt,
            //        ActionAtUtc = notification.ViewAtUtc
            //    }
            //}, RabbitKey.Exchanges.Events, RabbitMqKey.Routings.ViewInvoice);
        }
    }
}
