using Core.Application.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Sign.Application.SignServer.Invoice03.Interfaces;

namespace VnisCore.Sign.Application.SignServer.Invoice03
{
    [Authorize]
    public class SignInvoice03Service : ApplicationService
    {
        private readonly ISignServerInvoice03Business _signServerInvoice03Business;

        public SignInvoice03Service(ISignServerInvoice03Business signServerInvoice03Business)
        {
            _signServerInvoice03Business = signServerInvoice03Business;
        }

        /// <summary>
        /// Ký hóa đơn 01GTKT
        /// </summary>
        /// <param name="requests"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase)]
        public async Task SignInvoice03([FromBody] List<long> ids)
        {
            await _signServerInvoice03Business.SignAsync(ids);
        }
    }
}
