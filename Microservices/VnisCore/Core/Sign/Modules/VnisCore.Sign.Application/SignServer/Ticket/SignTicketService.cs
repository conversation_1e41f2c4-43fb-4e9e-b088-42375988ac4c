using Core.Application.Services;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Sign.Application.SignServer.Ticket.Interfaces;

namespace VnisCore.Sign.Application.SignServer.Ticket
{
    [Authorize]
    public class SignTicketService : ApplicationService
    {
        private readonly ISignServerTicketBusiness _signServerTicketBusiness;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IRepository<TicketErrorEntity, long> _repoError;

        public SignTicketService(
            ISignServerTicketBusiness signServerTicketBusiness,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IRepository<TicketErrorEntity, long> repoError)
        {
            _signServerTicketBusiness = signServerTicketBusiness;
            _appFactory = appFactory;
            _localizer = localizer;
            _repoError = repoError;
        }

        /// <summary>
        /// Ký tem/vé điện tử
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase)]
        public async Task SignTicket([FromBody] List<long> ids)
        {
            await _signServerTicketBusiness.SignAsync(ids);
        }
    }
}
