using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Tvan.Constants;

using Dapper;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Sign.Application.Dto;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.SignServer;
using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.TaxReport01;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Dtos;
using VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Interfaces;

namespace VnisCore.Sign.Application.SignServer.TaxReport.TaxReport01.Business
{
    public class SignServerTaxReport01Business : ISignServerTaxReport01Business
    {
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ISign123TaxReport01Business _sign123TaxReport01Business;
        private readonly ISignTaxReport01BusinessFactory _signTaxReport01BusinessFactory;
        private readonly IVnisCoreMongoInvoice01Repository _vnisCoreMongoInvoice01Repository;
        private readonly IVnisCoreMongoInvoice02Repository _vnisCoreMongoInvoice02Repository;

        public SignServerTaxReport01Business(
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ISignTaxReport01BusinessFactory signTaxReport01BusinessFactory,
            ISign123TaxReport01Business sign123TaxReport01Business,
            IVnisCoreMongoInvoice01Repository vnisCoreMongoInvoice01Repository,
            IVnisCoreMongoInvoice02Repository vnisCoreMongoInvoice02Repository
            )
        {
            _appFactory = appFactory;
            _distributedEventBus = distributedEventBus;
            _localizer = localizer;
            _sign123TaxReport01Business = sign123TaxReport01Business;
            _signTaxReport01BusinessFactory = signTaxReport01BusinessFactory;
            _vnisCoreMongoInvoice01Repository = vnisCoreMongoInvoice01Repository;
            _vnisCoreMongoInvoice02Repository = vnisCoreMongoInvoice02Repository;
        }

        public async Task SignAsync(long id)
        {
            var repoTaxReportHeader = _appFactory.Repository<TaxReport01HeaderEntity, long>();

            var taxHeader = await repoTaxReportHeader.Where(x => x.Id == id).Include(x => x.TaxReport01DetailMappings).FirstOrDefaultAsync();
            if (taxHeader == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.Sign.NotFound"]);

            if (taxHeader.SignStatus != (short)SignStatus.ChoKy
                && taxHeader.SignStatus != (short)SignStatus.KyLoi)
                throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.Sign.CannotSign"]);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxHeader.TenantId);
            if (!taxHeader.TaxReport01DetailMappings.Any())
                return;

            var type = ReportType.Month;
            var index = taxHeader.ReportMonth;
            if (taxHeader.ReportQuarter > 0)
            {
                type = ReportType.Quarter;
                index = taxHeader.ReportQuarter;
            }

            var (key, value) = GetCurrentRange(taxHeader);
            //var (key, value) = GetCurrentRange(type, index, taxHeader.ReportYear, taxHeader.DataPeriod);

            //var fromDate = long.Parse(key.ToString("yyyyMMdd0000"));
            //var toDate = long.Parse(value.AddDays(1).ToString("yyyyMMdd0000"));
            string fromDate = key.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));
            string toDate = value.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));

            var skipCount = 0;
            var maxResultCount = 1000;

            var service = await _signTaxReport01BusinessFactory.GetService(taxHeader.TenantId);

            foreach (var item in taxHeader.TaxReport01DetailMappings.OrderBy(x => x.Index))
            {
                try
                {
                    if (item.SignStatus == (short)SignStatus.DaKy)
                    {
                        skipCount += skipCount;
                        continue;
                    }

                    IEnumerable<TaxReport01DetailDto> resultDetails = await service.GetTaxReport01DetailAsync(taxHeader, fromDate, toDate, skipCount, maxResultCount);

                    var taxReport01DetailDtos = resultDetails as List<TaxReport01DetailDto> ?? resultDetails.ToList();
                    if (taxReport01DetailDtos.Any())
                    {
                        var xml = await _sign123TaxReport01Business.Sign123TaxReport01Async(taxHeader, item, taxReport01DetailDtos);
                        var firstItem = taxReport01DetailDtos.FirstOrDefault();

                        var sqlUpdateDetailMapping = $@"
                                                        UPDATE ""TaxReport01DetailMapping"" 
                                                        SET ""SignedTime"" = SYSDATE,
                                                        ""SignerId""='{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.Id.Value)}',
                                                        ""SignStatus""= {(short)SignStatus.DaKy.GetHashCode()},
                                                        ""FullNameSigner"" = N'{_appFactory.CurrentUser.FullName}' 
                                                        WHERE ""Id""= {item.Id}
                                                     ";

                        //update trạng thái thành đã ký
                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateDetailMapping);

                        var invoice01Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._01GTKT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                        var invoice02Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._02GTTT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                        var invoice03Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._03XKNB).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                        var invoice04Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._04HGDL).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                        var invoice05Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._05TVDT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();

                        await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice01Ids);

                        //update hóa đơn đã kê khai
                        var updateIsDeclare = new StringBuilder(" BEGIN ");

                        if (invoice01Ids.Any())
                            updateIsDeclare.Append(ToRawUpdate(invoice01Ids, "Invoice01Header"));

                        if (invoice02Ids.Any())
                            updateIsDeclare.Append(ToRawUpdate(invoice02Ids, "Invoice02Header"));

                        if (invoice03Ids.Any())
                            updateIsDeclare.Append(ToRawUpdate(invoice03Ids, "Invoice03Header"));

                        if (invoice04Ids.Any())
                            updateIsDeclare.Append(ToRawUpdate(invoice04Ids, "Invoice04Header"));

                        if (invoice05Ids.Any())
                            updateIsDeclare.Append(ToRawUpdate(invoice05Ids, "TicketHeader"));

                        updateIsDeclare.Append(" END; ");

                        var queryUpdateIsDeclared = updateIsDeclare.ToString();
                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateIsDeclared);

                        // update trạng thái đã kê khai ở mongo
                        if (invoice01Ids.Any())
                        {
                            var i = 0;
                            const int pageSize = 1000;
                            var indx = 0;
                            while (true)
                            {
                                if (i >= invoice01Ids.Count)
                                    break;

                                var page = invoice01Ids.Skip(i).Take(pageSize);
                                var ids01 = page.Select(x => x.InvoiceHeaderId).ToList();
                                await _vnisCoreMongoInvoice01Repository.UpdateDeclaresStatusTvanV2(ids01, (short)TvanDeclaredStatus.IsDeclared, (short)TvanStatus.Sended, (short)SyncElasticSearchStatus.PendingSyncIsDeclared);
                                i += pageSize;
                                indx++;
                            }
                        }

                        if (invoice02Ids.Any())
                        {
                            var i = 0;
                            const int pageSize = 1000;
                            var indx = 0;
                            while (true)
                            {
                                if (i >= invoice02Ids.Count)
                                    break;

                                var page = invoice02Ids.Skip(i).Take(pageSize);
                                var ids02 = page.Select(x => x.InvoiceHeaderId).ToList();
                                await _vnisCoreMongoInvoice02Repository.UpdateDeclaresStatusTvanV2(ids02, (short)TvanDeclaredStatus.IsDeclared, (short)TvanStatus.Sended, (short)SyncElasticSearchStatus.PendingSyncIsDeclared);
                                i += pageSize;
                                indx++;
                            }
                        }

                    }

                    skipCount += skipCount;
                }
                catch (Exception ex)
                {
                    var sqlUpdateTaxHeaderError = $@"
                                                UPDATE ""TaxReport01Header"" 
                                                SET ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()}
                                                WHERE ""Id""= {taxHeader.Id} ";

                    //update trạng thái thành đã ký
                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateTaxHeaderError);

                    throw ex;
                }
            }


            var sqlUpdateTaxHeader = $@"
                                                        UPDATE ""TaxReport01Header"" 
                                                        SET ""SignedTime"" = SYSDATE,
                                                        ""SignerId""='{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.Id.Value)}',
                                                        ""SignStatus""= {(short)SignStatus.DaKy.GetHashCode()},
                                                        ""FullNameSigner"" = N'{_appFactory.CurrentUser.FullName}',
                                                        ""StatusTvan"" = {TvanStatus.Sended.GetHashCode()} 
                                                        WHERE ""Id""= {taxHeader.Id}
                                                     ";

            //update trạng thái thành đã ký
            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateTaxHeader);
        }

        public async Task SignRefactorAsync(long id, Guid tenantId)
        {
            var repoTaxReportHeader = _appFactory.Repository<TaxReport01HeaderEntity, long>();

            var taxHeader = await repoTaxReportHeader.Where(x => x.Id == id && x.TenantId == tenantId).Include(x => x.TaxReport01DetailMappings).FirstOrDefaultAsync();
            if (taxHeader == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.Sign.NotFound"]);

            if (taxHeader.SignStatus != (short)SignStatus.ChoKy
                && taxHeader.SignStatus != (short)SignStatus.KyLoi)
                throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.Sign.CannotSign"]);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxHeader.TenantId);
            if (!taxHeader.TaxReport01DetailMappings.Any())
                return;

            if (taxHeader.TaxReport01DetailMappings.Any(x => string.IsNullOrEmpty(x.InvoiceIds)))
            {
                throw new UserFriendlyException("Ký số không thành công do bảng tổng hợp không có dữ liệu hóa đơn");
            }

            // Ký bảng tổng hợp cho XĂNG DẦU
            if (taxHeader.TypeMerchandise == (short)TypeMerchandise.XangDau)
            {
                //var (key, value) = GetCurrentRange(type, index, taxHeader.ReportYear, taxHeader.DataPeriod.Value);
                var (key, value) = GetCurrentRange(taxHeader);

                string fromDate = key.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));
                string toDate = value.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));

                var service = await _signTaxReport01BusinessFactory.GetService(taxHeader.TenantId);

                foreach (var item in taxHeader.TaxReport01DetailMappings.OrderBy(x => x.Index))
                {
                    try
                    {
                        if (item.SignStatus == (short)SignStatus.DaKy)
                        {
                            continue;
                        }

                        QueryInvoiceModel modelQuery = new QueryInvoiceModel();
                        modelQuery.TenantId = _appFactory.CurrentTenant.Id.Value;
                        modelQuery.ReportHeaderId = taxHeader.Id;
                        modelQuery.SkipCount = 0;
                        modelQuery.MaxResultCount = 1000;
                        if (item.InvoiceType == (short)VnisType._01GTKT)
                        {
                            modelQuery.IdsInvoice01 = item.InvoiceIds;
                        }

                        if (item.InvoiceType == (short)VnisType._02GTTT)
                        {
                            modelQuery.IdsInvoice02 = item.InvoiceIds;
                        }

                        if (item.InvoiceType == (short)VnisType._03XKNB)
                        {
                            modelQuery.IdsInvoice03 = item.InvoiceIds;
                        }

                        if (item.InvoiceType == (short)VnisType._04HGDL)
                        {
                            modelQuery.IdsInvoice04 = item.InvoiceIds;
                        }

                        if (item.InvoiceType == (short)VnisType._05TVDT)
                        {
                            modelQuery.IdsInvoice05 = item.InvoiceIds;
                        }
                        // Lay thong tin cua 1 DetailMapping tien hanh tao file XML
                        IEnumerable<TaxReport01DetailDto> resultDetails = await service.GetTaxReport01DetailDieuChinhNhieuLanXangDauAsync(taxHeader, fromDate, toDate, modelQuery);

                        var taxReport01DetailDtos = resultDetails as List<TaxReport01DetailDto> ?? resultDetails.ToList();
                        if (taxReport01DetailDtos.Any())
                        {
                            var xml = await _sign123TaxReport01Business.Sign123TaxReport01XangDauAsync(taxHeader, item, taxReport01DetailDtos);

                            var firstItem = taxReport01DetailDtos.FirstOrDefault();

                            var sqlUpdateDetailMapping = $@"
                                                        UPDATE ""TaxReport01DetailMapping"" 
                                                        SET ""SignedTime"" = SYSDATE,
                                                        ""SignerId""='{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.Id.Value)}',
                                                        ""SignStatus""= {(short)SignStatus.DaKy.GetHashCode()},
                                                        ""FullNameSigner"" = N'{_appFactory.CurrentUser.FullName}' 
                                                        WHERE ""Id""= {item.Id}
                                                     ";

                            //update trạng thái thành đã ký
                            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateDetailMapping);

                            var invoice01Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._01GTKT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice02Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._02GTTT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice03Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._03XKNB).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice04Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._04HGDL).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice05Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._05TVDT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();

                            if (item.InvoiceType == VnisType._01GTKT.GetHashCode())
                            {
                                await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice01Ids);
                            }
                            else if (item.InvoiceType == VnisType._02GTTT.GetHashCode())
                            {
                                await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice02Ids);
                            }
                            else if (item.InvoiceType == VnisType._03XKNB.GetHashCode())
                            {
                                await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice03Ids);
                            }
                            else if (item.InvoiceType == VnisType._04HGDL.GetHashCode())
                            {
                                await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice04Ids);
                            }
                            else if (item.InvoiceType == VnisType._05TVDT.GetHashCode())
                            {
                                await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice05Ids);
                            }

                            //update hóa đơn đã kê khai
                            var updateIsDeclare = new StringBuilder(" BEGIN ");

                            if (invoice01Ids.Any())
                                updateIsDeclare.Append(ToRawUpdate(invoice01Ids, "Invoice01Header"));

                            if (invoice02Ids.Any())
                                updateIsDeclare.Append(ToRawUpdate(invoice02Ids, "Invoice02Header"));

                            if (invoice03Ids.Any())
                                updateIsDeclare.Append(ToRawUpdate(invoice03Ids, "Invoice03Header"));

                            if (invoice04Ids.Any())
                                updateIsDeclare.Append(ToRawUpdate(invoice04Ids, "Invoice04Header"));

                            if (invoice05Ids.Any())
                                updateIsDeclare.Append(ToRawUpdate(invoice05Ids, "TicketHeader"));

                            updateIsDeclare.Append(" END; ");

                            var queryUpdateIsDeclared = updateIsDeclare.ToString();
                            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateIsDeclared);

                            // update trạng thái đã kê khai ở mongo
                            if (invoice01Ids.Any())
                            {
                                var i = 0;
                                const int pageSize = 1000;
                                var indx = 0;
                                while (true)
                                {
                                    if (i >= invoice01Ids.Count)
                                        break;

                                    var page = invoice01Ids.Skip(i).Take(pageSize);
                                    var ids01 = page.Select(x => x.InvoiceHeaderId).ToList();
                                    await _vnisCoreMongoInvoice01Repository.UpdateDeclaresStatusTvan(ids01, (short)TvanDeclaredStatus.IsDeclared, (short)TvanStatus.Sended);
                                    i += pageSize;
                                    indx++;
                                }
                            }

                            if (invoice02Ids.Any())
                            {
                                var i = 0;
                                const int pageSize = 1000;
                                var indx = 0;
                                while (true)
                                {
                                    if (i >= invoice02Ids.Count)
                                        break;

                                    var page = invoice02Ids.Skip(i).Take(pageSize);
                                    var ids02 = page.Select(x => x.InvoiceHeaderId).ToList();
                                    await _vnisCoreMongoInvoice02Repository.UpdateDeclaresStatusTvan(ids02, (short)TvanDeclaredStatus.IsDeclared, (short)TvanStatus.Sended);
                                    i += pageSize;
                                    indx++;
                                }
                            }

                        }
                    }
                    catch (Exception ex)
                    {
                        var sqlUpdateTaxHeaderError = $@"
                                                UPDATE ""TaxReport01Header"" 
                                                SET ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()}
                                                WHERE ""Id""= {taxHeader.Id} ";

                        //update trạng thái thành đã ký
                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateTaxHeaderError);

                        throw;
                    }
                }

                var sqlUpdateTaxHeader = $@"
                                        UPDATE ""TaxReport01Header"" 
                                        SET ""SignedTime"" = SYSDATE,
                                        ""SignerId""='{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.Id.Value)}',
                                        ""SignStatus""= {(short)SignStatus.DaKy.GetHashCode()},
                                        ""FullNameSigner"" = N'{_appFactory.CurrentUser.FullName}',
                                        ""StatusTvan"" = {TvanStatus.Sended.GetHashCode()} 
                                        WHERE ""Id""= {taxHeader.Id}
                                        ";

                //update trạng thái thành đã ký
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateTaxHeader);
            }
            else
            {
                var type = ReportType.Month;
                var index = taxHeader.ReportMonth;
                if (taxHeader.ReportQuarter > 0)
                {
                    type = ReportType.Quarter;
                    index = taxHeader.ReportQuarter;
                }

                //var (key, value) = GetCurrentRange(type, index, taxHeader.ReportYear, null);
                var (key, value) = GetCurrentRange(taxHeader);

                string fromDate = key.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));
                string toDate = value.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));

                var service = await _signTaxReport01BusinessFactory.GetService(taxHeader.TenantId);

            // Lấy thông tin CTS và Token
            SignServerInfomationModel signServerInfomation = new SignServerInfomationModel();
            var signService = _appFactory.GetServiceDependency<ISignService>();
            var certificate = await signService.GetCertificateAsync(_appFactory.CurrentUser.Id.Value, _appFactory.CurrentTenant.Id.Value);
            var setting = await signService.GetSettingRawAsync(tenantId);
            var token = await signService.GetTokenAsync(setting);
            var isSignHsm = await signService.IsSignHsmAsync(tenantId);

            signServerInfomation.UsbToken = certificate;
            signServerInfomation.SignServerSetting = setting;
            signServerInfomation.Token = token;
            signServerInfomation.IsSignHsm = isSignHsm;

            //Tạo batch
            await signService.CreateBatch123Async(setting.Host, token, certificate, isSignHsm);

            try
            {
                foreach (var item in taxHeader.TaxReport01DetailMappings.OrderBy(x => x.Index))
                {
                    try
                    {
                        if (item.SignStatus == (short)SignStatus.DaKy)
                        {
                            continue;
                        }
                        QueryInvoiceModel modelQuery = new QueryInvoiceModel();
                        modelQuery.TenantId = _appFactory.CurrentTenant.Id.Value;
                        modelQuery.ReportHeaderId = taxHeader.Id;
                        modelQuery.SkipCount = 0;
                        modelQuery.MaxResultCount = 1000;
                        if (item.InvoiceType == 1)
                        {
                            modelQuery.IdsInvoice01 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 2)
                        {
                            modelQuery.IdsInvoice02 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 3)
                        {
                            modelQuery.IdsInvoice03 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 4)
                        {
                            modelQuery.IdsInvoice04 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 5)
                        {
                            modelQuery.IdsInvoice05 = item.InvoiceIds;
                        }
                        // Lay thong tin cua 1 DetailMapping tien hanh tao file XML
                        IEnumerable<TaxReport01DetailDto> resultDetails = await service.GetTaxReport01DetailDieuChinhNhieuLanAsync(taxHeader, fromDate, toDate, modelQuery);

                        var taxReport01DetailDtos = resultDetails as List<TaxReport01DetailDto> ?? resultDetails.ToList();
                        if (taxReport01DetailDtos.Any())
                        {
                            var xml = await _sign123TaxReport01Business.Sign123TaxReport01Async(taxHeader, item, taxReport01DetailDtos, signServerInfomation);

                            var firstItem = taxReport01DetailDtos.FirstOrDefault();

                            var sqlUpdateDetailMapping = $@"
                                                        UPDATE ""TaxReport01DetailMapping"" 
                                                        SET ""SignedTime"" = SYSDATE,
                                                        ""SignerId""='{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.Id.Value)}',
                                                        ""SignStatus""= {(short)SignStatus.DaKy.GetHashCode()},
                                                        ""FullNameSigner"" = N'{_appFactory.CurrentUser.FullName}' 
                                                        WHERE ""Id""= {item.Id}
                                                     ";

                            //update trạng thái thành đã ký
                            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateDetailMapping);

                            var invoice01Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._01GTKT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice02Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._02GTTT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice03Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._03XKNB).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice04Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._04HGDL).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();
                            var invoice05Ids = taxReport01DetailDtos.Where(x => x.InvoiceType == (short)VnisType._05TVDT).Select(dto => new ReportInvoiceHeaderIdDto { InvoiceHeaderId = dto.InvoiceHeaderId }).Distinct().ToList();

                        if(item.InvoiceType == VnisType._01GTKT.GetHashCode()){
                            await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice01Ids);
                        } 
                        else if (item.InvoiceType == VnisType._02GTTT.GetHashCode())
                        {
                            await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice02Ids);
                        } 
                        else if (item.InvoiceType == VnisType._03XKNB.GetHashCode())
                        {
                            await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice03Ids);
                        } 
                        else if (item.InvoiceType == VnisType._04HGDL.GetHashCode())
                        {
                            await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice04Ids);
                        }
                        else if (item.InvoiceType == VnisType._05TVDT.GetHashCode())
                        {
                            await SendToTvanAsync(_appFactory.CurrentTenant.TaxCode, taxHeader, item.Index, xml, invoice05Ids);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var sqlUpdateTaxHeaderError = $@"
                                                UPDATE ""TaxReport01Header"" 
                                                SET ""SignStatus"" = {SignStatus.KyLoi.GetHashCode()}
                                                WHERE ""Id""= {taxHeader.Id} ";

                        //update trạng thái thành đã ký
                        await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateTaxHeaderError);

                        throw;
                    }
                }

                var sqlUpdateTaxHeader = $@"
                                        UPDATE ""TaxReport01Header"" 
                                        SET ""SignedTime"" = SYSDATE,
                                        ""SignerId""='{OracleExtension.ConvertGuidToRaw(_appFactory.CurrentUser.Id.Value)}',
                                        ""SignStatus""= {(short)SignStatus.DaKy.GetHashCode()},
                                        ""FullNameSigner"" = N'{_appFactory.CurrentUser.FullName}',
                                        ""StatusTvan"" = {TvanStatus.Sended.GetHashCode()} 
                                        WHERE ""Id""= {taxHeader.Id}
                                        ";

                //update trạng thái thành đã ký
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdateTaxHeader);
            }
            catch (Exception)
            {

                throw;
            }
            finally
            {
                //remove batch
                await signService.RemoveBatch123Async(setting.Host, token);
            }

        }

            
        }

        public async Task<string> SignXmlAsync(string tag, string xml)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            xml = await _sign123TaxReport01Business.Sign123TaxReport01ByXmlAsync(tenantId, tag, xml);

            return xml;
        }

        #region helpers
        private async Task SendToTvanAsync(string sellerTaxcode, TaxReport01HeaderEntity taxReport, int index, string xml, List<ReportInvoiceHeaderIdDto> invoiceIds)
        {
            //publish mess để gửi lên tvan
            await _distributedEventBus.PublishAsync(new SignReport01EventResultData
            {
                Id = taxReport.Id,
                TenantId = taxReport.TenantId,
                SellerTaxCode = _appFactory.CurrentTenant.TaxCode,
                InvoiceHeaderIds = invoiceIds.Select(x => x.InvoiceHeaderId).ToList(),
                Index = index,
                TimeRetry = 0,
                XmlDatas = new List<SignReport01EventResultData.SignXmlReportPartialEventResultModel>{ new SignReport01EventResultData.SignXmlReportPartialEventResultModel
                {
                    AdditionalTimes = taxReport.AdditionalTimes,
                    IsFirstTimeInPeriod = taxReport.IsFirstTimeInPeriod,
                    FromNumber = 0,
                    ToNumber = 0,
                    TemplateId = 0,
                    SerialNo = null,
                    TemplateNo = 0,
                    Xml = xml
                } }
            });
        }

        private static string ToRawUpdate(IReadOnlyCollection<ReportInvoiceHeaderIdDto> ids, string tableName)
        {
            var i = 0;
            const int pageSize = 1000;
            var index = 0;
            var subSql = new StringBuilder();
            while (true)
            {
                if (i >= ids.Count)
                    break;

                var page = ids.Skip(i).Take(pageSize);
                subSql.Append(@$" Update ""{tableName}"" SET ""IsDeclared"" = 1, ""StatusTvan"" = {(short)TvanStatus.Sended} WHERE ""Id"" in ({string.Join(",", page.Select(x => x.InvoiceHeaderId).Distinct())}) AND  ""IsDeclared"" = 0;  ");
                i += pageSize;
                index++;
            }

            return subSql.ToString();
        }

        private KeyValuePair<DateTime, DateTime> GetCurrentRange(TaxReport01HeaderEntity taxHeader)
        {
            return GetRangeOfTime(taxHeader);
        }

        private KeyValuePair<DateTime, DateTime> GetRangeOfTime(TaxReport01HeaderEntity taxHeader)
        {
            var from = DateTime.Now;
            var to = DateTime.Now;

            if (taxHeader.TypeReport == (short)ReportType.Month)
            {
                from = new DateTime(taxHeader.ReportYear, taxHeader.ReportMonth, 1);
                to = from.AddMonths(1).AddDays(-1);
            }

            if (taxHeader.TypeReport == (short)ReportType.Quarter)
            {
                var range = DateTimeExtension.GetRangeOfQuater(taxHeader.ReportQuarter, taxHeader.ReportYear);
                from = range.Key;
                to = range.Value;
            }

            if (taxHeader.TypeReport == (short)ReportType.Day)
            {
                from = taxHeader.DataPeriod.Value.Date;
                to = taxHeader.DataPeriod.Value.Date;
            }

            return new KeyValuePair<DateTime, DateTime>(from, to);
        }

        //private KeyValuePair<DateTime, DateTime> GetCurrentRange(ReportType type, int index, int year, DateTime? dataPeriod)
        //{
        //    return GetRangeOfTime(type, index, year, dataPeriod);
        //}

        //private KeyValuePair<DateTime, DateTime> GetRangeOfTime(ReportType type, int index, int year, DateTime? dataPeriod)
        //{
        //    var from = DateTime.Now;
        //    var to = DateTime.Now;

        //    if (type == ReportType.Month)
        //    {
        //        from = new DateTime(year, index, 1);
        //        to = from.AddMonths(1).AddDays(-1);
        //    }

        //    if (type == ReportType.Quarter)
        //    {
        //        var range = DateTimeExtension.GetRangeOfQuater(index, year);
        //        from = range.Key;
        //        to = range.Value;
        //    }

        //    if (type == ReportType.Day)
        //    {
        //        from = dataPeriod.Value.Date;
        //        to = dataPeriod.Value.Date;
        //    }    

        //    return new KeyValuePair<DateTime, DateTime>(from, to);
        //}

        /// <summary>
        /// Generate xml bảng tổng hợp bổ sung
        /// <param name="DLBTHopId">Id xml dữ liệu bảng tổng hợp</param>
        /// <param name="BSLThu">Bổ sung lần thứ</param>
        /// <param name="SBTHDLieu">Số bảng tổng hợp dữ liệu</param>
        /// <param name="invoiceIds">Danh sách Id hóa đơn</param>
        /// <param name="InvoiceType">Loại hóa đơn</param>
        /// </summary>
        public async Task<string> GenerateBTHBoSung(GenerateBoSungBTHRequest request)
        {
            var xml = " ";
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy chi nhánh");
            }

            try
            {
                #region Tạo Fake Bảo tổng hợp bổ sung
                TaxReport01HeaderEntity taxHeader = new TaxReport01HeaderEntity()
                {
                    AdditionalTimes = request.BSLThu,
                    IsFirstTimeInPeriod = false,
                    ReportDate = DateTime.Now,
                    CurrencyUnit = "VND", // Xét tạm để không lỗi gen xm

                    ReportMonth = (short)(request.Type == ReportType.Month ? request.ReportMonth : 0),
                    ReportQuarter = (short)(request.Type == ReportType.Quarter ? request.ReportMonth : 0),
                    ReportYear = request.ReportYear
                };
                TaxReport01DetailMappingEntity taxReport01DetailMappingEntity = new TaxReport01DetailMappingEntity();
                taxReport01DetailMappingEntity.Id = request.DLBTHopId;
                taxReport01DetailMappingEntity.Index = request.SBTHDLieu;
                taxReport01DetailMappingEntity.InvoiceIds = request.invoiceIds;
                taxReport01DetailMappingEntity.InvoiceType = (short)request.invoiceType;
                taxReport01DetailMappingEntity.SignStatus = (short)SignStatus.DaKy;

                taxHeader.TaxReport01DetailMappings = new List<TaxReport01DetailMappingEntity>()
            {
                taxReport01DetailMappingEntity
            };
                #endregion

                #region Tạo xml báo cáo bổ sung
                var service = await _signTaxReport01BusinessFactory.GetService(taxHeader.TenantId);

                foreach (var item in taxHeader.TaxReport01DetailMappings.OrderBy(x => x.Index))
                {
                    try
                    {
                        QueryInvoiceModel modelQuery = new QueryInvoiceModel();
                        modelQuery.TenantId = _appFactory.CurrentTenant.Id.Value;
                        modelQuery.ReportHeaderId = taxHeader.Id;
                        modelQuery.SkipCount = 0;
                        modelQuery.MaxResultCount = 1000;
                        if (item.InvoiceType == 1)
                        {
                            //modelQuery.FromIdInvoice01 = item.FromId;
                            //modelQuery.ToIdInvoice01 = item.ToId;
                            modelQuery.IdsInvoice01 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 2)
                        {
                            //modelQuery.FromIdInvoice02 = item.FromId;
                            //modelQuery.ToIdInvoice02 = item.ToId;
                            modelQuery.IdsInvoice02 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 3)
                        {
                            //modelQuery.FromIdInvoice03 = item.FromId;
                            //modelQuery.ToIdInvoice03 = item.ToId;
                            modelQuery.IdsInvoice03 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 4)
                        {
                            //modelQuery.FromIdInvoice04 = item.FromId;
                            //modelQuery.ToIdInvoice04 = item.ToId;
                            modelQuery.IdsInvoice04 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 5)
                        {
                            //modelQuery.FromIdInvoice05 = item.FromId;
                            //modelQuery.ToIdInvoice05 = item.ToId;
                            modelQuery.IdsInvoice05 = item.InvoiceIds;
                        }

                        // Cập nhật trạng thái hóa đơn trước khi gen xml
                        //var updateSql = UpdateStatusInvocie(invoiceType, item.InvoiceIds, InvoiceStatus);
                        //await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(updateSql);

                        // Lay thong tin cua 1 DetailMapping tien hanh tao file XML
                        IEnumerable<TaxReport01DetailDto> resultDetails = await service.GetTaxReport01DetailRefactorBoSungAsync(taxHeader, "", "", modelQuery);

                        var taxReport01DetailDtos = resultDetails as List<TaxReport01DetailDto> ?? resultDetails.ToList();

                        #region Sửa thông tin hóa đơn
                        foreach (var invoice in taxReport01DetailDtos)
                        {
                            invoice.InvoiceStatus = (short)request.InvoiceStatus;

                            if(invoice.InvoiceStatus == 0)
                            {
                                invoice.Status = "Mới";
                            } 
                            else if(invoice.InvoiceStatus== 1)
                            {
                                invoice.Status = "Hủy";
                            }
                            else if (invoice.InvoiceStatus == 2)
                            {
                                invoice.Status = "Điều chỉnh";
                            }
                            else if (invoice.InvoiceStatus == 3)
                            {
                                invoice.Status = "Thay thế";
                            }
                            else if (invoice.InvoiceStatus == 4)
                            {
                                invoice.Status = "Giải trình";
                            }
                            else if (invoice.InvoiceStatus == 5)
                            {
                                invoice.Status = "Sai sót do tổng hợp";
                            }

                            invoice.TotalAmount = -invoice.TotalAmount;
                            invoice.TotalPaymentAmount = -invoice.TotalPaymentAmount;
                            invoice.TotalVatAmount = -invoice.TotalVatAmount;

                            invoice.Amount = -invoice.Amount;
                            invoice.PaymentAmount = -invoice.PaymentAmount;

                            if (invoice.TTPhi.HasValue)
                            {
                                invoice.TTPhi = -invoice.TTPhi.Value;
                            }
                        }
                        #endregion

                        if (taxReport01DetailDtos.Any())
                        {
                            xml = await _sign123TaxReport01Business.Sign123TaxReport01BoSungAsync(taxHeader, item, taxReport01DetailDtos);

                        } else
                        {
                            throw new UserFriendlyException("Không tìm thấy hóa đơn nào");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                }
                #endregion

                return xml;

            }
            catch (Exception exx)
            {

                throw;
            }
        }

        /// <summary>
        /// Generate xml bảng tổng hợp bổ sung
        /// <param name="DLBTHopId">Id xml dữ liệu bảng tổng hợp</param>
        /// <param name="BSLThu">Bổ sung lần thứ</param>
        /// <param name="SBTHDLieu">Số bảng tổng hợp dữ liệu</param>
        /// <param name="invoiceIds">Danh sách Id hóa đơn</param>
        /// <param name="InvoiceType">Loại hóa đơn</param>
        /// </summary>
        public async Task<string> GenerateBTHBoSungVINMASS845(GenerateBoSungBTHRequest request)
        {
            var xml = " ";
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy chi nhánh");
            }

            try
            {
                #region Tạo Fake Bảo tổng hợp bổ sung
                TaxReport01HeaderEntity taxHeader = new TaxReport01HeaderEntity()
                {
                    AdditionalTimes = request.BSLThu,
                    IsFirstTimeInPeriod = false,
                    ReportDate = DateTime.Now,
                    CurrencyUnit = "VND", // Xét tạm để không lỗi gen xm

                    ReportMonth = (short)(request.Type == ReportType.Month ? request.ReportMonth : 0),
                    ReportQuarter = (short)(request.Type == ReportType.Quarter ? request.ReportMonth : 0),
                    ReportYear = request.ReportYear
                };
                TaxReport01DetailMappingEntity taxReport01DetailMappingEntity = new TaxReport01DetailMappingEntity();
                taxReport01DetailMappingEntity.Id = request.DLBTHopId;
                taxReport01DetailMappingEntity.Index = request.SBTHDLieu;
                taxReport01DetailMappingEntity.InvoiceIds = request.invoiceIds;
                taxReport01DetailMappingEntity.InvoiceType = (short)request.invoiceType;
                taxReport01DetailMappingEntity.SignStatus = (short)SignStatus.DaKy;

                taxHeader.TaxReport01DetailMappings = new List<TaxReport01DetailMappingEntity>()
            {
                taxReport01DetailMappingEntity
            };
                #endregion

                #region Tạo xml báo cáo bổ sung
                var service = await _signTaxReport01BusinessFactory.GetService(taxHeader.TenantId);

                foreach (var item in taxHeader.TaxReport01DetailMappings.OrderBy(x => x.Index))
                {
                    try
                    {
                        QueryInvoiceModel modelQuery = new QueryInvoiceModel();
                        modelQuery.TenantId = _appFactory.CurrentTenant.Id.Value;
                        modelQuery.ReportHeaderId = taxHeader.Id;
                        modelQuery.SkipCount = 0;
                        modelQuery.MaxResultCount = 1000;
                        if (item.InvoiceType == 1)
                        {
                            //modelQuery.FromIdInvoice01 = item.FromId;
                            //modelQuery.ToIdInvoice01 = item.ToId;
                            modelQuery.IdsInvoice01 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 2)
                        {
                            //modelQuery.FromIdInvoice02 = item.FromId;
                            //modelQuery.ToIdInvoice02 = item.ToId;
                            modelQuery.IdsInvoice02 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 3)
                        {
                            //modelQuery.FromIdInvoice03 = item.FromId;
                            //modelQuery.ToIdInvoice03 = item.ToId;
                            modelQuery.IdsInvoice03 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 4)
                        {
                            //modelQuery.FromIdInvoice04 = item.FromId;
                            //modelQuery.ToIdInvoice04 = item.ToId;
                            modelQuery.IdsInvoice04 = item.InvoiceIds;
                        }
                        else if (item.InvoiceType == 5)
                        {
                            //modelQuery.FromIdInvoice05 = item.FromId;
                            //modelQuery.ToIdInvoice05 = item.ToId;
                            modelQuery.IdsInvoice05 = item.InvoiceIds;
                        }

                        // Cập nhật trạng thái hóa đơn trước khi gen xml
                        //var updateSql = UpdateStatusInvocie(invoiceType, item.InvoiceIds, InvoiceStatus);
                        //await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(updateSql);

                        // Lay thong tin cua 1 DetailMapping tien hanh tao file XML
                        IEnumerable<TaxReport01DetailDto> resultDetails = await service.GetTaxReport01DetailRefactorBoSungVINMASS845Async(taxHeader, "", "", modelQuery);

                        var taxReport01DetailDtos = resultDetails as List<TaxReport01DetailDto> ?? resultDetails.ToList();

                        #region Sửa thông tin hóa đơn
                        foreach (var invoice in taxReport01DetailDtos)
                        {
                            invoice.InvoiceStatus = (short)request.InvoiceStatus;

                            if (invoice.InvoiceStatus == 0)
                            {
                                invoice.Status = "Mới";
                            }
                            else if (invoice.InvoiceStatus == 1)
                            {
                                invoice.Status = "Hủy";
                            }
                            else if (invoice.InvoiceStatus == 2)
                            {
                                invoice.Status = "Điều chỉnh";
                            }
                            else if (invoice.InvoiceStatus == 3)
                            {
                                invoice.Status = "Thay thế";
                            }
                            else if (invoice.InvoiceStatus == 4)
                            {
                                invoice.Status = "Giải trình";
                            }
                            else if (invoice.InvoiceStatus == 5)
                            {
                                invoice.Status = "Sai sót do tổng hợp";
                            }
                            invoice.Note = @$"Sai sót do tổng hợp. Nội dung đúng: Tổng tiền chưa thuế: {invoice.TotalAmount}, tổng tiền thuế: {invoice.TotalVatAmount} , tổng tiền thanh toán:{invoice.TotalPaymentAmount}";
                        }
                        #endregion

                        if (taxReport01DetailDtos.Any())
                        {
                            xml = await _sign123TaxReport01Business.Sign123TaxReport01BoSungAsync(taxHeader, item, taxReport01DetailDtos);

                        }
                        else
                        {
                            throw new UserFriendlyException("Không tìm thấy hóa đơn nào");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw;
                    }
                }
                #endregion

                return xml;

            }
            catch (Exception exx)
            {

                throw;
            }
        }

        /// <summary>
        /// Sửa trạng thái hóa đơn
        /// </summary>
        public string UpdateStatusInvocie(int invoiceType, string invoiceIds, int invoiceStatus)
        {
            string tableName = "";
            switch (invoiceType)
            {
                case 1:
                    tableName = "Invoice01Header";
                    break;
                case 2:
                    tableName = "Invoice02Header";
                    break;
                case 3:
                    tableName = "Invoice03Header";
                    break;
                case 4:
                    tableName = "Invoice04Header";
                    break;
                case 5:
                    tableName = "TicketHeader";
                    break;
                default:
                    tableName = "Invoice01Header";
                    break;
            }
            var sql = @$" Update ""{tableName}"" SET ""InvoiceStatus"" = {invoiceStatus} WHERE ""Id"" in ({invoiceIds}) ";

            return sql;
        }

        #endregion
    }
}
