using Core;
using Core.Application.Services;
using Core.Shared.Cached;
using Core.Shared.Factory;
using Core.Shared.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Sign.Application.SignServer.Invoice01.Interfaces;

namespace VnisCore.Sign.Application.SignServer.Invoice01
{
    [Authorize]
    public class SignInvoice01Service : ApplicationService
    {
        private readonly IAppFactory _appFactory;
        private readonly ISignServerInvoice01Business _signServerInvoice01Business;
        private readonly ILockInvoiceService _lockInvoiceService;

        public SignInvoice01Service(
            IAppFactory appFactory,
            ISignServerInvoice01Business signServerInvoice01Business,
            ILockInvoiceService lockInvoiceService)
        {
            _appFactory = appFactory;
            _signServerInvoice01Business = signServerInvoice01Business;
            _lockInvoiceService = lockInvoiceService;
        }

        /// <summary>
        /// Ký hóa đơn 01GTKT
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase)]
        public async Task SignInvoice01([FromBody] List<long> ids)
        {
            // Lock hóa đơn đang được xử lý
            if (!ids.IsNullOrEmpty())
            {
                foreach (var id in ids)
                {
                    _lockInvoiceService.LockInvoiceProcessing(id, _appFactory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                }
            }
            try
            {
                await _signServerInvoice01Business.SignAsync(ids);
            }
            catch (UserFriendlyException)
            {

                throw;
            }
            finally
            {
                // Xóa cache Processing
                if (!ids.IsNullOrEmpty())
                {
                    foreach (var id in ids)
                    {
                        _lockInvoiceService.UnLockInvoiceProcessed(id, _appFactory.CurrentTenant.TaxCode, CacheKeyPath.Invoice01);
                    }
                }
            }
        }
    }
}
