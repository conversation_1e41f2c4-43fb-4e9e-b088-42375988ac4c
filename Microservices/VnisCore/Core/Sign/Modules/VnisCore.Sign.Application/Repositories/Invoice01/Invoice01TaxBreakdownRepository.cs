using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using Core.Domain.Repositories;
using Core.Shared.Extensions;
using Dapper;
using Core.Shared.DapperSqlTypeHandler;
using Core.Shared.Factory;

namespace VnisCore.Sign.Application.Repositories.Invoice01
{
    public interface IInvoice01TaxBreakdownRepository
    {
        Task<List<Invoice01TaxBreakdownEntity>> QueryByInvoiceHeaderIdAsync(long idInvoiceHeader);

        Task<List<Invoice01TaxBreakdownEntity>> GetInvoiceTaxBreakDownRawAsync(Guid tenantId, long IdRootInvoice);
        //void Delete(Invoice01TaxBreakdownEntity item);
    }

    public class Invoice01TaxBreakdownRepository : IInvoice01TaxBreakdownRepository
    {
        private readonly IRepository<Invoice01TaxBreakdownEntity, long> _repoInvoiceTaxBreakdown;
        private readonly IAppFactory _appFactory;

        public Invoice01TaxBreakdownRepository(IRepository<Invoice01TaxBreakdownEntity, long> repoInvoiceDetail,
            IAppFactory appFactory)
        {
            _repoInvoiceTaxBreakdown = repoInvoiceDetail;
            _appFactory = appFactory;
        }

        public async Task<List<Invoice01TaxBreakdownEntity>> QueryByInvoiceHeaderIdAsync(long idInvoiceHeader)
        {
            return await _repoInvoiceTaxBreakdown.Where(x => x.InvoiceHeaderId == idInvoiceHeader).AsQueryable().ToListAsync();
        }

        public async Task<List<Invoice01TaxBreakdownEntity>> GetInvoiceTaxBreakDownRawAsync(Guid tenantId, long IdRootInvoice)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var query = $"Select * from \"Invoice01TaxBreakdown\" where \"TenantId\" = '{rawTenantId}' and \"InvoiceHeaderId\" = {IdRootInvoice}";
            var details = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01TaxBreakdownEntity>(query);

            return details.ToList();
        }
    }
}
