using Core.EventBus;

using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Common;

namespace VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Ticket
{
    [EventName("ticket.signticketerror")]
    public class SignTicketErrorEventSendData : SignInvoiceErrorRequestModel
    {
        public SignTicketErrorEventSendData()
        {
        }

        public SignTicketErrorEventSendData(SignInvoiceErrorRequestModel item)
        {
            TenantId = item.TenantId;
            UserId = item.UserId;
            UserFullName = item.UserFullName;
            GroupCode = item.GroupCode;
        }
    }
}
