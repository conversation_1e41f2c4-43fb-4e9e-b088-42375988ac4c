//using Core.EventBus;
//using System;

//namespace VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Invoice03
//{
//    [EventName("invoice03.signinvoice03result")]
//    public class SignInvoice03EventResultData : Invoice03SignResult
//    {
//        public SignInvoice03EventResultData()
//        {

//        }

//        public SignInvoice03EventResultData(Invoice03SignResult item)
//        {
//            TenantId = item.TenantId;
//            InvoiceHeaderId = item.InvoiceHeaderId;
//            SerialNo = item.SerialNo;
//        }
//    }

//    public class Invoice03SignResult
//    {
//        public Guid TenantId { get; set; }
//        public long InvoiceHeaderId { get; set; }
//        public string SerialNo { get; set; }

//    }
//}
