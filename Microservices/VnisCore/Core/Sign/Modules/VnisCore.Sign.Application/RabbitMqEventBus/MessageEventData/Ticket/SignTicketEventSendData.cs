using Core.EventBus;

using VnisCore.Sign.Application.Models.Requests.Commands;

namespace VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Ticket
{
    [EventName("ticket.signticket")]
    public class SignTicketEventSendData : SignInvoiceRequestModel
    {
        public SignTicketEventSendData()
        {

        }

        public SignTicketEventSendData(SignInvoiceRequestModel item)
        {
            TenantId = item.TenantId;
            UserId = item.UserId;
            UserFullName = item.UserFullName;
            InvoiceHeaderId = item.InvoiceHeaderId;
            TemplateNo = item.TemplateNo;
            SerialNo = item.SerialNo;
            InvoiceNo = item.InvoiceNo;
        }
    }
}
