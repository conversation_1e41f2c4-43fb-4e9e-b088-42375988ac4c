using Core.EventBus;

using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Common;

namespace VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Invoice03
{
    [EventName("invoice03.signinvoice03error")]
    public class SignInvoice03ErrorEventSendData : SignInvoiceErrorRequestModel
    {
        public SignInvoice03ErrorEventSendData()
        {
        }

        public SignInvoice03ErrorEventSendData(SignInvoiceErrorRequestModel item)
        {
            TenantId = item.TenantId;
            UserId = item.UserId;
            UserFullName = item.UserFullName;
            GroupCode = item.GroupCode;
        }
    }
}
