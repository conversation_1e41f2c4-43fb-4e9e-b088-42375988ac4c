//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Shared.Constants;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using Core.Shared.FileManager.Interfaces;
//using Core.Tvan.Constants;
//using Core.Tvan.Vnpay.Interfaces;

//using Dapper;

//using Serilog;

//using System;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//using VnisCore.Core.MongoDB.Entities.Invoices.Invoice03;
//using VnisCore.Core.MongoDB.IRepository.Invoice03;
//using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
//using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Invoice03;

//namespace VnisCore.Sign.Application.RabbitMqEventBus.ReceivedEventHandler.Invoice03
//{
//    public class SignInvoice03ReceivedTvanEventHandler : IDistributedEventHandler<SignInvoice03EventTvanData>, ITransientDependency
//    {
//        private readonly IAppFactory _appFactory;
//        private readonly IVnisCoreMongoXmlInvoice03SignedRepository _mongoXmlInvoice03SignedRepository;
//        private readonly IVnisCoreMongoInvoice03Repository _mongoInvoice03Repository;

//        public SignInvoice03ReceivedTvanEventHandler(
//            IAppFactory appFactory,
//            IVnisCoreMongoXmlInvoice03SignedRepository mongoXmlInvoice03SignedRepository,
//            IVnisCoreMongoInvoice03Repository mongoInvoice03Repository)
//        {
//            _appFactory = appFactory;
//            _mongoInvoice03Repository = mongoInvoice03Repository;
//            _mongoXmlInvoice03SignedRepository = mongoXmlInvoice03SignedRepository;
//        }


//        public async Task HandleEventAsync(SignInvoice03EventTvanData eventData)
//        {
//            // nếu là có mã thì gửi lên TVAN
//            var query = $@"SELECT ""Id"", ""TenantId"", ""InvoiceNo"", ""SerialNo"", ""TemplateNo"", ""StatusTvan"", ""SellerTaxCode"" FROM ""Invoice03Header"" WHERE ""IsDeleted"" = 0 AND ""Id"" = {eventData.InvoiceHeaderId}";
//            var invoice = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice03HeaderEntity>(query);
//            if (invoice == null)
//            {
//                var invoiceMongo = await _mongoInvoice03Repository.GetById(eventData.InvoiceHeaderId);
//                if (invoiceMongo == null)
//                    return;
//                else
//                {
//                    invoice = _appFactory.ObjectMapper.Map<MongoInvoice03Entity, Invoice03HeaderEntity>(invoiceMongo);
//                }
//            }

//            var registrationInvoiceType = invoice.SerialNo.First();
//            if (registrationInvoiceType != 'C')
//                return;

//            var xml = string.Empty;

//            var queryXml = $@"SELECT * FROM ""Invoice03Xml"" WHERE ""InvoiceHeaderId"" = {eventData.InvoiceHeaderId} AND ""IsDeleted"" = 0 ";
//            var invoiceXml = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice03XmlEntity>(queryXml);
//            if (invoiceXml == null)
//            {
//                var invoiceXmlMongo = await _mongoXmlInvoice03SignedRepository.GetByInvoiceHeaderId(eventData.InvoiceHeaderId);
//                if (invoiceXmlMongo == null)
//                    return;
//                else
//                    xml = Encoding.UTF8.GetString(Convert.FromBase64String(invoiceXmlMongo.Xml));
//            }
//            else
//            {
//                var fileService = _appFactory.GetServiceDependency<IFileService>();
//                var pathFileMinio = $"{MediaFileType.Invoice03Xml}/{invoiceXml.TenantId}/{invoiceXml.CreationTime.Year}/{invoiceXml.CreationTime.Month:00}/{invoiceXml.CreationTime.Day:00}/{invoiceXml.CreationTime.Hour:00}/{invoiceXml.PhysicalFileName}";
//                var bytes = await fileService.DownloadAsync(pathFileMinio);

//                xml = Encoding.UTF8.GetString(bytes);
//            }

//            if (string.IsNullOrEmpty(xml))
//                return;


//            var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();

//            if (registrationInvoiceType == 'C')
//            {

//                try
//                {
//                    var responseTvan = await tvanVnpayInvoice.SendInvoice03HasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
//                    if (responseTvan != null && responseTvan.Code == "00")
//                    {
//                        invoice.StatusTvan = (short)TvanStatus.Sended;
//                    }
//                    else
//                    {
//                        invoice.StatusTvan = (short)TvanStatus.SendError;
//                    }
//                }
//                catch (Exception ex)
//                {
//                    invoice.StatusTvan = (short)TvanStatus.SendError;
//                    Log.Error(ex, ex.Message);
//                }
//                finally
//                {
//                    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {eventData.InvoiceHeaderId}";
//                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
//                }
//            }
//        }

//        #region
//        //public async Task HandleEventAsync(SignInvoice03EventTvanData eventData)
//        //{
//        //    // nếu là có mã thì gửi lên TVAN
//        //    var query = $@"SELECT ""Id"", ""TenantId"", ""InvoiceNo"", ""SerialNo"", ""TemplateNo"", ""StatusTvan"", ""SellerTaxCode"" FROM ""Invoice03Header"" WHERE ""IsDeleted"" = 0 AND ""Id"" = {eventData.InvoiceHeaderId}";
//        //    var invoice = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice03HeaderEntity>(query);
//        //    if (invoice == null)
//        //        return;

//        //    var registrationInvoiceType = invoice.SerialNo.First();
//        //    if (registrationInvoiceType != 'C')
//        //        return;

//        //    var queryXml = $@"SELECT * FROM ""Invoice03Xml"" WHERE ""InvoiceHeaderId"" = {eventData.InvoiceHeaderId} AND ""IsDeleted"" = 0 ";
//        //    var invoiceXml = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice03XmlEntity>(queryXml);
//        //    if (invoiceXml == null)
//        //        return;


//        //    var fileService = _appFactory.GetServiceDependency<IFileService>();
//        //    var pathFileMinio = $"{MediaFileType.Invoice03Xml}/{invoiceXml.TenantId}/{invoiceXml.CreationTime.Year}/{invoiceXml.CreationTime.Month:00}/{invoiceXml.CreationTime.Day:00}/{invoiceXml.CreationTime.Hour:00}/{invoiceXml.PhysicalFileName}";
//        //    var bytes = await fileService.DownloadAsync(pathFileMinio);

//        //    var xml = Encoding.UTF8.GetString(bytes);

//        //    // check độ ưu tiên của các nhà VAN
//        //    var tvanMInvoice = _appFactory.GetServiceDependency<ITvanMInvoiceService>();
//        //    var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanInvoiceService>();

//        //    var maxTimesOfTvanDisconnected = short.Parse(_configuration.GetSection("MaxTimesOfTvanDisconnected").Value);

//        //    var tvanSupplierModel = _config.Value;
//        //    string tvanSupplierJson = JsonConvert.SerializeObject(tvanSupplierModel);
//        //    var dict = JsonConvert.DeserializeObject<Dictionary<string, short>>(tvanSupplierJson);
//        //    var tvans = new List<TvanModel>();

//        //    foreach (var item in dict)
//        //    {
//        //        tvans.Add(new TvanModel
//        //        {
//        //            Stt = item.Value,
//        //            TvanName = item.Key,
//        //        });
//        //    }

//        //    var tvansOrderByStt = tvans.OrderBy(x => x.Stt);

//        //    if (registrationInvoiceType == 'C')
//        //    {
//        //        var tvanFirst = new TvanModel();
//        //        foreach (var tvan in tvansOrderByStt)
//        //        {
//        //            var getCached = await _cachedService.GetTimesDisconnectedTvanFromCached(tvan.TvanName);
//        //            if (getCached != null && getCached.TimesDisconnected <= maxTimesOfTvanDisconnected)
//        //            {
//        //                tvanFirst.Stt = tvan.Stt;
//        //                tvanFirst.TvanName = tvan.TvanName;

//        //                break;
//        //            }
//        //            else
//        //            {
//        //                tvanFirst = tvansOrderByStt.First();
//        //            }
//        //        }

//        //        switch (tvanFirst.TvanName)
//        //        {
//        //            case nameof(tvanSupplierModel.VnPayInvoice):
//        //                try
//        //                {
//        //                    Log.Error($"************* invoice-templateNo: {invoice.TemplateNo} *************");
//        //                    var responseTvan = await tvanVnpayInvoice.SendInvoice03HasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
//        //                    if (responseTvan != null && responseTvan.Success == false)
//        //                    {
//        //                        var getCachedTvanVnpay = await _cachedService.GetTimesDisconnectedTvanFromCached(nameof(tvanSupplierModel.VnPayInvoice));
//        //                        if (getCachedTvanVnpay != null)
//        //                        {
//        //                            var timesDisconnected = getCachedTvanVnpay.TimesDisconnected + 1;
//        //                            await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.VnPayInvoice), timesDisconnected);
//        //                        }
//        //                        else
//        //                        {
//        //                            await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.VnPayInvoice), 1);
//        //                        }

//        //                        try
//        //                        {
//        //                            var responseTvanMInvoice = await tvanMInvoice.SendInvoice03HasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
//        //                            if (responseTvanMInvoice != null && responseTvanMInvoice.Code == "00")
//        //                            {
//        //                                invoice.StatusTvan = (short)TvanStatus.Sended;
//        //                            }
//        //                            else
//        //                            {
//        //                                invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                            }
//        //                        }
//        //                        catch (Exception ex)
//        //                        {
//        //                            invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                            Log.Error(ex, ex.Message);
//        //                        }
//        //                        finally
//        //                        {
//        //                            var queryUpdateInvoice = $@"UPDATE ""{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} WHERE ""Id"" = {eventData.InvoiceHeaderId} ";
//        //                            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
//        //                        }
//        //                    }
//        //                    else
//        //                    {
//        //                        if (responseTvan != null && responseTvan.Success == true)
//        //                        {
//        //                            invoice.StatusTvan = (short)TvanStatus.Sended;
//        //                        }
//        //                        else
//        //                        {
//        //                            invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                        }
//        //                    }
//        //                }
//        //                catch (Exception ex)
//        //                {
//        //                    invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                    Log.Error(ex, ex.Message);
//        //                }
//        //                finally
//        //                {
//        //                    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {eventData.InvoiceHeaderId}";
//        //                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
//        //                }

//        //                break;

//        //            case nameof(tvanSupplierModel.MInvoice):
//        //                try
//        //                {
//        //                    Log.Error($"************* invoice-templateNo: {invoice.TemplateNo}*************");
//        //                    var responseTvan = await tvanMInvoice.SendInvoice03HasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
//        //                    if (responseTvan == null)
//        //                    {
//        //                        var getCachedTvanVnpay = await _cachedService.GetTimesDisconnectedTvanFromCached(nameof(tvanSupplierModel.MInvoice));
//        //                        if (getCachedTvanVnpay != null)
//        //                        {
//        //                            var timesDisconnected = getCachedTvanVnpay.TimesDisconnected + 1;
//        //                            await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.MInvoice), timesDisconnected);
//        //                        }
//        //                        else
//        //                        {
//        //                            await _cachedService.SetTimesDisconnectedTvanToCached(nameof(tvanSupplierModel.MInvoice), 1);
//        //                        }


//        //                        try
//        //                        {
//        //                            await tvanVnpayInvoice.SendInvoice03HasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
//        //                            invoice.StatusTvan = (short)TvanStatus.Sended;
//        //                        }
//        //                        catch (Exception ex)
//        //                        {
//        //                            invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                            Log.Error(ex, ex.Message);
//        //                        }
//        //                        finally
//        //                        {
//        //                            var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {eventData.InvoiceHeaderId}";
//        //                            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
//        //                        }
//        //                    }
//        //                    else
//        //                    {
//        //                        if (responseTvan != null && responseTvan.Code == "00")
//        //                        {
//        //                            invoice.StatusTvan = (short)TvanStatus.Sended;
//        //                        }
//        //                        else
//        //                        {
//        //                            invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                        }
//        //                    }
//        //                }
//        //                catch (Exception ex)
//        //                {
//        //                    invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                    Log.Error(ex, ex.Message);
//        //                }
//        //                finally
//        //                {
//        //                    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {eventData.InvoiceHeaderId}";
//        //                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
//        //                }

//        //                break;
//        //            default:
//        //                try
//        //                {
//        //                    var responseTvan = await tvanVnpayInvoice.SendInvoice03HasCodeAsync(invoice.TenantId, invoice.Id, invoice.SellerTaxCode, invoice.TemplateNo, invoice.SerialNo, invoice.InvoiceNo, xml);
//        //                    if (responseTvan != null && responseTvan.Success == false)
//        //                    {
//        //                        invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                    }
//        //                    else
//        //                    {
//        //                        invoice.StatusTvan = (short)TvanStatus.Sended;
//        //                    }
//        //                }
//        //                catch (Exception ex)
//        //                {
//        //                    invoice.StatusTvan = (short)TvanStatus.SendError;
//        //                    Log.Error(ex, ex.Message);
//        //                }
//        //                finally
//        //                {
//        //                    var queryUpdateInvoice = @$"UPDATE ""{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}"" SET ""StatusTvan"" = {invoice.StatusTvan} where ""Id"" = {eventData.InvoiceHeaderId}";
//        //                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateInvoice);
//        //                }
//        //                break;
//        //        }
//        //    }
//        //}
//        #endregion
//    }
//}
