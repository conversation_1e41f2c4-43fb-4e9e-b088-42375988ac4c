//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Shared.Constants;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using Core.Shared.FileManager.Interfaces;
//using Core.Tvan.Constants;
//using Core.Tvan.Vnpay.Interfaces;

//using Dapper;

//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;

//using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
//using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
//using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
//using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Invoice02;

//namespace VnisCore.Sign.Application.RabbitMqEventBus.ReceivedEventHandler.InvoicesError
//{
//    public class Invoice02ErrorReceivedTvanEventHandler : IDistributedEventHandler<SignInvoice02ErrorEventTvanData>, ITransientDependency
//    {
//        private readonly IAppFactory _appFactory;
//        private readonly IVnisCoreMongoInvoice02ReSyncRepository _mongoInvoice02ReSyncRepository;


//        public Invoice02ErrorReceivedTvanEventHandler(
//            IAppFactory appFactory,
//            IVnisCoreMongoInvoice02ReSyncRepository mongoInvoice02ReSyncRepository)
//        {
//            _appFactory = appFactory;
//            _mongoInvoice02ReSyncRepository = mongoInvoice02ReSyncRepository;
//        }

//        public async Task HandleEventAsync(SignInvoice02ErrorEventTvanData eventData)
//        {
//            var rawTenantId = OracleExtension.ConvertGuidToRaw(eventData.TenantId);

//            var query = $@" SELECT ""TenantId"", ""CreationTime"", ""PhysicalFileName"" FROM ""Invoice02ErrorXml"" WHERE ""GroupCode"" = {eventData.GroupCode} AND ""TenantId"" = '{rawTenantId}' ";
//            var invoiceXml = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice02ErrorXmlEntity>(query);
//            if (invoiceXml == null)
//                return;

//            var fileService = _appFactory.GetServiceDependency<IFileService>();
//            var pathFileMinio = $"{MediaFileType.Invoice02ErrorXml}/{invoiceXml.TenantId}/{invoiceXml.CreationTime.Year}/{invoiceXml.CreationTime.Month:00}/{invoiceXml.CreationTime.Day:00}/{invoiceXml.CreationTime.Hour:00}/{invoiceXml.PhysicalFileName}";
//            var bytes = await fileService.DownloadAsync(pathFileMinio);

//            var xml = Encoding.UTF8.GetString(bytes);

//            var tvanVnpayInvoice = _appFactory.GetServiceDependency<ITvanVnpayService>();

//            var queryInvoiceError = $@" SELECT * FROM ""Invoice02Error"" WHERE ""GroupCode"" = {eventData.GroupCode} AND ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 ";
//            var invoiceErrors = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice02ErrorEntity>(queryInvoiceError)).ToList();

//            var invoiceIds = invoiceErrors.Select(x => x.InvoiceHeaderId).ToList();

//            try
//            {
//                var responseTvan = await tvanVnpayInvoice.SendInvoice02ErrorAsync(eventData.TaxCode, xml, eventData.TenantId, invoiceErrors);
//                if (responseTvan != null && responseTvan.Code == "00")
//                {
//                    var sql = @$"UPDATE ""Invoice02Error"" SET ""TvanStatus"" = {(short)TvanStatus.Sended} WHERE ""GroupCode"" = {eventData.GroupCode}";
//                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
//                }
//                else
//                {
//                    var sql = @$"UPDATE ""Invoice02Error"" SET ""TvanStatus"" = {(short)TvanStatus.SendError} WHERE ""GroupCode"" = {eventData.GroupCode}";
//                    await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
//                }
//            }
//            catch (Exception)
//            {
//                var sql = @$"UPDATE ""Invoice02Error"" SET ""TvanStatus"" = {(short)TvanStatus.SendError} WHERE ""GroupCode"" = {eventData.GroupCode}";
//                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
//            }
//            finally
//            {
//                await UpdateResyncAsync(invoiceIds, SyncElasticSearchStatus.PendingSyncStatusTvanRequest);
//            }
//        }

//        private async Task UpdateResyncAsync(List<long> ids, SyncElasticSearchStatus syncElasticSearchStatus)
//        {
//            //var ids = invoiceHeaders.Select(x => x.Id).ToList();
//            if (ids.Any())
//            {
//                var invoice02ReSyncs = await _mongoInvoice02ReSyncRepository.GetByIdsAsync(ids);

//                var invoice02ReSyncInsert = new List<MongoInvoice02ReSyncEntity>();

//                if (invoice02ReSyncs.Any())
//                {
//                    invoice02ReSyncs.ForEach(item =>
//                    {
//                        item.IsSyncedToElasticSearch = (short)syncElasticSearchStatus;
//                    });

//                    foreach (var item in ids)
//                    {
//                        var invoice02ReSync = invoice02ReSyncs.FirstOrDefault(x => x.Id == item);
//                        if (invoice02ReSync == null)
//                        {
//                            invoice02ReSync = new MongoInvoice02ReSyncEntity
//                            {
//                                Id = item,
//                                IsSyncedToElasticSearch = (short)syncElasticSearchStatus
//                            };
//                            invoice02ReSyncInsert.Add(invoice02ReSync);
//                        }
//                    }

//                    await _mongoInvoice02ReSyncRepository.UpdateManyAsync(invoice02ReSyncs);

//                    if (invoice02ReSyncInsert.Any())
//                        await _mongoInvoice02ReSyncRepository.InsertManyAsync(invoice02ReSyncInsert);
//                }
//                else
//                {
//                    foreach (var item in ids)
//                    {
//                        var invoice02ReSync = new MongoInvoice02ReSyncEntity
//                        {
//                            Id = item,
//                            IsSyncedToElasticSearch = (short)syncElasticSearchStatus
//                        };
//                        invoice02ReSyncInsert.Add(invoice02ReSync);
//                    }

//                    if (invoice02ReSyncInsert.Any())
//                    {
//                        await _mongoInvoice02ReSyncRepository.InsertManyAsync(invoice02ReSyncInsert);
//                    }
//                }
//            }
//        }
//    }
//}
