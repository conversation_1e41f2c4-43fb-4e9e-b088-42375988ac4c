//using Core.DependencyInjection;
//using Core.EventBus.Distributed;

//using System.Linq;
//using System.Threading.Tasks;

//using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Invoice01;

//namespace VnisCore.Sign.Application.RabbitMqEventBus.ReceivedEventHandler.Invoice01
//{
//    public class SignInvoice01ReceivedEventHandler : IDistributedEventHandler<SignInvoice01EventResultData>, ITransientDependency
//    {
//        private readonly IDistributedEventBus _distributedEventBus;

//        public SignInvoice01ReceivedEventHandler(
//            IDistributedEventBus distributedEventBus)
//        {
//            _distributedEventBus = distributedEventBus;

//        }

//        public async Task HandleEventAsync(SignInvoice01EventResultData eventData)
//        {
//            //nếu là có mã mới gửi lên tvan
//            var registrationInvoiceType = eventData.SerialNo.First();
//            if (registrationInvoiceType == 'C')
//            {
//                await _distributedEventBus.PublishAsync(new SignInvoice01EventTvanData
//                {
//                    InvoiceHeaderId = eventData.InvoiceHeaderId,
//                    TenantId = eventData.TenantId
//                });
//            }
//        }
//    }
//}
