//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Shared.Constants;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using Dapper;
//using System;
//using System.Collections.Generic;
//using System.Globalization;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
//using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Invoice01;

//namespace VnisCore.Sign.Application.RabbitMqEventBus.SubscribeEventHandler.Invoice01
//{
//    public class Invoice01LogPublishEventHandler : IDistributedEventHandler<Invoice01LogEventSendData>, ITransientDependency
//    {
//        private readonly IAppFactory _appFactory;

//        public Invoice01LogPublishEventHandler(
//            IAppFactory appFactory)
//        {
//            _appFactory = appFactory;
//        }

//        public async Task HandleEventAsync(Invoice01LogEventSendData eventData)
//        {
//            var tableName = DatabaseExtension<Invoice01LogEntity>.GetTableName();
//            var seqName = SequenceName.Invoice01Log;
//            var id = (await GetSEQsNextVal(1, seqName)).First();
//            StringBuilder raw = new StringBuilder();
//            raw.Append($@"BEGIN
//                             INSERT INTO  ""{tableName}""(
//                                    ""Id"",                                 
//                                    ""InvoiceHeaderId"",                              
//                                    ""TenantId"",                              
//                                    ""UserId"",                           
//                                    ""UserName"",                           
//                                    ""Action"",                    
//                                    ""InvoiceType"",                              
//                                    ""CreationTime"",                              
//                                    ""Metadata"",                              
//                                    ""Partition""
//                            )
//                            VALUES(
//                            {id},
//                            {eventData.InvoiceHeaderId},
//                            '{OracleExtension.ConvertGuidToRaw(eventData.TenantId)}',
//                            '{OracleExtension.ConvertGuidToRaw(eventData.UserId)}',
//                            '{eventData.UserName}',
//                            {eventData.Action},
//                            {eventData.InvoiceType},
//                            '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',
//                            '{eventData.Metadata}',
//                            {eventData.Partition}
//                            )");

//            raw.Append($"; ");
//            var queryResult = raw.Append($" END; ").ToString();
//            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);
//        }

//        private async Task<List<long>> GetSEQsNextVal(int level, string SequenceName)
//        {
//            var sql = $@"   SELECT ""{SequenceName}"".NEXTVAL
//                            FROM(
//                               SELECT level
//                               FROM dual
//                               CONNECT BY level <= {level}
//                            ) ";

//            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<long>(sql);

//            return result.ToList();
//        }
//    } 
//}
