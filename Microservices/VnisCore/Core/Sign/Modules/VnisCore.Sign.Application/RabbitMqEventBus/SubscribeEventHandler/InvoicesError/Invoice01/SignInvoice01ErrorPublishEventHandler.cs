//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Localization.Resources.AbpLocalization;
//using Core.Shared.Factory;

//using Microsoft.Extensions.Localization;

//using System;
//using System.Threading.Tasks;

//using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.InvoicesError.Invoice01;
//using VnisCore.Sign.Application.SignServer.InvoicesError.Invoice01.Interfaces;

//namespace VnisCore.Sign.Application.RabbitMqEventBus.SubscribeEventHandler.InvoicesError.Invoice01
//{
//    public class SignInvoice01ErrorPublishEventHandler : IDistributedEventHandler<SignInvoice01ErrorEventSendData>, ITransientDependency
//    {
//        private readonly IAppFactory _appFactory;
//        private readonly IServiceProvider _serviceProvider;
//        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
//        private readonly ISign123Invoice01ErrorBusiness _sign123Invoice01ErrorBusiness;

//        public SignInvoice01ErrorPublishEventHandler(
//            IServiceProvider serviceProvider,
//            IAppFactory appFactory,
//            IStringLocalizer<CoreLocalizationResource> localizer,
//            ISign123Invoice01ErrorBusiness sign123Invoice01ErrorBusiness)
//        {
//            _appFactory = appFactory;
//            _serviceProvider = serviceProvider;
//            _localizer = localizer;
//            _sign123Invoice01ErrorBusiness = sign123Invoice01ErrorBusiness;
//        }

//        public async Task HandleEventAsync(SignInvoice01ErrorEventSendData eventData)
//        {
//            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();

//            var invoiceError = await _sign123Invoice01ErrorBusiness.SignInvoiceError123Async(eventData.TenantId, eventData.UserId, eventData.UserFullName, eventData.GroupCode);

//            await distributedEventBus.PublishAsync(new SignInvoice01ErrorEventTvanData
//            {
//                TenantId = invoiceError.TenantId,
//                GroupCode = invoiceError.GroupCode,
//                TaxCode = invoiceError.TaxCode
//            });
//        }
//    }
//}
