using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;

using Microsoft.Extensions.Localization;

using System.Collections.Generic;
using System.Linq;

using VnisCore.Sign.Application.Interfaces;

namespace VnisCore.Sign.Application.Services.Factories
{
    public interface ISignClientFactory
    {
        ISignClientService GetService(long? invoiceType);
    }

    public class SignClientFactory : ISignClientFactory
    {
        public IEnumerable<ISignClientService> _services;
        public IStringLocalizer<CoreLocalizationResource> _localizer;

        public SignClientFactory(IEnumerable<ISignClientService> services, IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _services = services;
            _localizer = localizer;
        }

        public ISignClientService GetService(long? invoiceType)
        {
            var name = "";
            if (invoiceType.HasValue)
            {
                name = $"Invoice{invoiceType.GetHashCode():00}SignClientService";

                if (invoiceType == VnisType._05TVDT.GetHashCode())
                    name = $"TicketSignClientService";

                if (invoiceType == VnisType._52DKSDCT.GetHashCode())
                    name = $"PITDeductionDocumentDeclarationSignClientService";
            }
            else
                name = $"RegistrationSignClientService";

            var implement = _services.FirstOrDefault(x => x.GetType().Name == name);
            if (implement == null)
                throw new UserFriendlyException(_localizer[$"Vnis.BE.Sign.ServiceNotYet,{name}"]);

            return implement;
        }
    }
}
