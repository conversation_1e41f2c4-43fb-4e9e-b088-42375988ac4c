using Core.Shared.Constants;
using MediatR;
using System;

namespace VnisCore.Sign.Application.Models.Events
{
    public class AfterSaveInvoice01XmlEventModel : INotification
    {
        public Guid Code { get; set; }
        public Guid TenantCode { get; set; }

        public string TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public string InvoiceNo { get; set; }
        public SignStatus SignStatus { get; set; }
        public InvoiceStatus InvoiceStatus { get; set; }

        public VnisType Type { get; set; }
        public string UserName { get; set; }
        public Guid UserCode { get; set; }
    }
}
