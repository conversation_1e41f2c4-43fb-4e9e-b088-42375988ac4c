using Core.Shared.Constants;
using Core.Shared.Models;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Sign.Application.Models.Invoices.Invoice01
{
    [XmlType("invoice")]
    public class SignInvoice01Model
    {
        [XmlElement("invoiceData")]
        public Invoice01HeaderXmlModel InvoiceData { get; set; }

        public static SignInvoice01Model Parse(Invoice01HeaderEntity invoice, List<Invoice01DetailEntity> invoiceDetails, List<Invoice01TaxBreakdownEntity> invoiceTaxbreakDowns, Invoice01ReferenceEntity invoiceReference, Invoice01DocumentInfoEntity invoiceFile, bool isSignBackDate)
        {
            var model = new Invoice01HeaderXmlModel();
            model.Data = "data";
            model.Id = invoice.Id.ToString();

            #region 1 Thông tin chung của hóa đơn
            model.PublicCode = invoice.ErpId;
            model.InvoiceType = StaticData._01GTKT;
            model.InvoiceName = StaticData.InvoiceTypeDefaults[StaticData._01GTKT];
            model.TemplateNo = invoice.TemplateNo;
            model.SerialNo = invoice.SerialNo;
            model.InvoiceNo = invoice.InvoiceNo;
            model.InvoiceDate = invoice.InvoiceDate.ToString("yyyy-MM-dd");
            model.SignedDate = isSignBackDate == true ? invoice.InvoiceDate.ToString("yyyy-MM-dd") : DateTime.Now.Date.ToString("yyyy-MM-dd");
            model.Note = invoice.Note;

            if (invoiceReference != null)
            {
                model.IdInvoiceReference = invoiceReference.InvoiceNoReference;

                if (invoiceFile != null)
                {
                    model.AdjustmentReason = invoiceFile.DocumentReason;
                    model.AdjustmentDate = invoiceFile.DocumentDate.HasValue ? invoiceFile.DocumentDate.Value.ToString("yyyy-MM-dd") : null;
                }

                if (!string.IsNullOrEmpty(invoiceReference.Note))
                    model.AdjustmentContent = invoiceReference.Note;
            }

            model.AdjustmentType = invoice.InvoiceStatus switch
            {
                (int)InvoiceStatus.Goc => 1,
                (int)InvoiceStatus.ThayThe => 3,
                (int)InvoiceStatus.DieuChinhDinhDanh => 5,
                (int)InvoiceStatus.DieuChinhTangGiam => 9,
                (int)InvoiceStatus.XoaBo => 7,
                _ => 1,
            };
            #endregion

            #region 2 Thông tin người bán
            model.SellerLegalName = invoice.SellerLegalName;
            model.SellerTaxCode = invoice.SellerTaxCode;
            model.SellerAddressLine = invoice.SellerAddressLine;
            model.SellerDistrictName = invoice.SellerDistrictName;
            model.SellerCityName = invoice.SellerCityName;
            model.SellerCountryCode = invoice.SellerCountryCode;
            model.SellerPhoneNumber = invoice.SellerPhoneNumber;
            model.SellerFaxNumber = invoice.SellerFaxNumber;
            model.SellerEmail = invoice.SellerEmail;
            model.SellerBankName = invoice.SellerBankName;
            model.SellerBankAccount = invoice.SellerBankAccount;
            model.SellerFullName = invoice.SellerFullName;
            model.SellerSignedPersonName = invoice.SellerFullNameSigned;

            #endregion

            #region 3 Thông tin người mua
            model.BuyerCode = invoice.BuyerCode;
            model.BuyerFullName = invoice.BuyerFullName;
            model.BuyerLegalName = string.IsNullOrWhiteSpace(invoice.BuyerLegalName) ? invoice.BuyerFullName : invoice.BuyerLegalName;
            model.BuyerTaxCode = invoice.BuyerTaxCode;
            model.BuyerAddressLine = invoice.BuyerAddressLine;
            model.BuyerDistrictName = invoice.BuyerDistrictName;
            model.BuyerCityName = invoice.BuyerCityName;
            model.BuyerCountryCode = invoice.BuyerCountryCode;
            model.BuyerPhoneNumber = invoice.BuyerPhoneNumber;
            model.BuyerFaxNumber = invoice.BuyerFaxNumber;
            model.BuyerEmail = invoice.BuyerEmail;
            model.BuyerBankName = invoice.BuyerBankName;
            model.BuyerBankAccount = invoice.BuyerBankAccount;
            #endregion

            #region 4 Thông tin thanh toán
            model.CurrencyCode = invoice.ToCurrency;
            model.ExchangeRate = invoice.ExchangeRate;
            model.PaymentMethod = invoice.PaymentMethod;
            model.PaymentAmount = invoice.TotalPaymentAmount;
            model.PaymentDate = invoice.PaymentDate.HasValue ? invoice.PaymentDate.Value.ToString("yyyy-MM-dd") : invoice.InvoiceDate.ToString("yyyy-MM-dd");
            #endregion

            #region 6 Thông tin chi tiết hóa đơn
            if (invoiceDetails != null && invoiceDetails.Count > 0)
            {
                model.Items = new List<Invoice01DetailXmlModel>();
                foreach (var item in invoiceDetails)
                {
                    var detail = new Invoice01DetailXmlModel();
                    detail.Id = item.Id.ToString();
                    detail.Line = item.Index;
                    detail.ProductCode = item.ProductCode;
                    detail.ProductName = item.ProductName;
                    detail.UnitName = item.UnitName;
                    detail.UnitPrice = Math.Abs(item.UnitPrice);
                    detail.IsIncreaseUnitPrice = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (item.UnitPrice > 0 ? true : false) : (bool?)null;
                    detail.Quantity = Math.Abs(item.Quantity);
                    detail.IsIncreaseQuantity = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (item.Quantity > 0 ? true : false) : (bool?)null;
                    detail.ItemTotalAmountWithoutVat = Math.Abs(item.Amount);
                    detail.IsIncreaseAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (item.Amount > 0 ? true : false) : (bool?)null;
                    detail.VatPercent = item.VatPercent;
                    detail.VatAmount = Math.Abs(item.VatAmount);
                    detail.IsIncreaseVatAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (item.VatAmount > 0 ? true : false) : (bool?)null;
                    detail.DiscountAmountBeforeTax = item.DiscountAmountBeforeTax != 0 ? Math.Abs(item.DiscountAmountBeforeTax) : (decimal?)null;
                    detail.IsIncreaseDiscountAmountBeforeTax = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (item.DiscountAmountBeforeTax > 0 ? true : false) : (bool?)null;
                    detail.DiscountPercentBeforeTax = item.DiscountPercentBeforeTax != 0 ? item.DiscountPercentBeforeTax : (decimal?)null;
                    detail.Note = item.Note;


                    //if (detailExtras != null && detailFields != null)
                    //{
                    //    var lstDetailExtras = new List<Invoice01ExtraXmlModel>();
                    //    var index = detailFields.ToDictionary(x => x.Id, x => x.FieldName);
                    //    foreach (var extra in detailExtras)
                    //    {
                    //        if (item.Id != extra.InvoiceDetailId)
                    //            continue;

                    //        if (!index.ContainsKey(extra.InvoiceDetailFieldId))
                    //            continue;

                    //        lstDetailExtras.Add(new Invoice01ExtraXmlModel
                    //        {
                    //            Name = index[extra.InvoiceDetailFieldId],
                    //            Value = extra.FieldValue
                    //        });
                    //    }
                    //    detail.Extras = lstDetailExtras;
                    //}

                    if (item.ExtraProperties != null && item.ExtraProperties.Any())
                    {
                        var extraProperties = new List<InvoiceDetailExtraModel>();
                        if (item.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceDetailExtras"].ToString()))
                        {
                            extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(item.ExtraProperties["invoiceDetailExtras"].ToString());
                        }
                        if (extraProperties.Any())
                        {
                            var detailExtras = extraProperties.GroupBy(x => x.InvoiceDetailId).ToDictionary(x => x.Key, x => x.ToList());
                            detail.Extras = detailExtras[item.Id].Select(x => new Invoice01ExtraXmlModel
                            {
                                Name = x.FieldName,
                                Value = x.FieldValue,
                            }).ToList();
                        }
                    }

                    model.Items.Add(detail);

                }
            }

            //model.Items = invoiceDetails?.Select(x => new Invoice01DetailXmlModel
            //{
            //    Id = x.Id.ToString(),
            //    Line = x.Index,
            //    ProductCode = x.ProductId,
            //    ProductName = x.ProductName,
            //    UnitName = x.UnitName,
            //    UnitPrice = Math.Abs(x.UnitPrice),
            //    IsIncreaseUnitPrice = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (x.UnitPrice > 0 ? true : false) : (bool?)null,
            //    Quantity = Math.Abs(x.Quantity),
            //    IsIncreaseQuantity = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (x.Quantity > 0 ? true : false) : (bool?)null,
            //    ItemTotalAmountWithoutVat = Math.Abs(x.Amount),
            //    IsIncreaseAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (x.Amount > 0 ? true : false) : (bool?)null,
            //    VatPercent = x.VatPercent,
            //    VatAmount = Math.Abs(x.VatAmount),
            //    IsIncreaseVatAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (x.VatAmount > 0 ? true : false) : (bool?)null,
            //    DiscountAmountBeforeTax = x.DiscountAmountBeforeTax != 0 ? Math.Abs(x.DiscountAmountBeforeTax) : (decimal?)null,
            //    IsIncreaseDiscountAmountBeforeTax = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (x.DiscountAmountBeforeTax > 0 ? true : false) : (bool?)null,
            //    DiscountPercentBeforeTax = x.DiscountPercentBeforeTax != 0 ? x.DiscountPercentBeforeTax : (double?)null,
            //    Note = x.Note,
            //}).ToList();

            #endregion

            #region 7 Thông tin các mức thuế và thành tiền thuế
            //if (invoiceTaxbreakDowns != null && invoiceTaxbreakDowns.Any())
            //{
            //    foreach (var breakdown in invoiceTaxbreakDowns)
            //    {
            //        var invoiceTaxBreakdown = new InvoiceTaxBreakdownsXmlModel
            //        {
            //            VatName = breakdown.Name,
            //            VatPercentage = breakdown.VatPercent,
            //            VatTaxableAmount = breakdown.VatAmount,
            //            VatTaxAmount = breakdown.VatAmount
            //        };

            //        model.InvoiceTaxBreakdowns = new List<InvoiceTaxBreakdownsXmlModel>();
            //        if (invoiceReference == null)
            //        {
            //            model.InvoiceTaxBreakdowns.Add(invoiceTaxBreakdown);
            //            continue;
            //        }

            //        invoiceTaxBreakdown.IsVatTaxableAmountPos = invoiceTaxBreakdown.VatTaxAmount > 0;
            //        invoiceTaxBreakdown.IsVatTaxAmountPos = invoiceTaxBreakdown.VatTaxAmount > 0;
            //        invoiceTaxBreakdown.VatExemptionReason = null;

            //        model.InvoiceTaxBreakdowns.Add(invoiceTaxBreakdown);
            //    }
            //}

            model.InvoiceTaxBreakdowns = invoiceTaxbreakDowns?.Select(x => new Invoice01TaxBreakdownsXmlModel
            {
                VatName = x.VatPercentDisplay,
                VatAmount = x.VatAmount,
                VatPercent = x.VatPercent,
                IsIncrease = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (x.VatAmount > 0 ? true : false) : (bool?)null
            }).ToList();
            #endregion

            #region 8 Thông tin tóm tắt hóa đơn
            //model.SumOfTotalLineAmountWithoutVat = invoice.TotalAmount;
            //model.TotalAmountWithoutVat = invoice.TotalAmount;
            //model.TotalVatAmount = invoice.TotalVatAmount;
            //model.TotalAmountWithVat = invoice.TotalPaymentAmount;
            //model.TotalAmountWithVatFrn = invoice.TotalPaymentAmount;
            //model.TotalAmountWithVatInWords = invoice.PaymentAmountWords;
            //model.DiscountAmount = invoice.TotalDiscountAmountBeforeTax;

            model.TotalAmount = Math.Abs(invoice.TotalAmount);
            model.IsIncreaseTotalAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (invoice.TotalAmount > 0 ? true : false) : (bool?)null;
            model.TotalVatAmount = Math.Abs(invoice.TotalVatAmount);
            model.IsIncreaseTotalVatAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (invoice.TotalVatAmount > 0 ? true : false) : (bool?)null;
            model.TotalPaymentAmount = Math.Abs(invoice.TotalPaymentAmount);
            model.IsIncreaseTotalPaymentAmount = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (invoice.TotalPaymentAmount > 0 ? true : false) : (bool?)null;
            model.PaymentAmountWords = invoice.PaymentAmountWords;
            model.TotalDiscountAmountBeforeTax = invoice.TotalDiscountAmountBeforeTax != 0 ? invoice.TotalDiscountAmountBeforeTax : (decimal?)null;
            model.TotalDiscountAmountAfterTax = invoice.TotalDiscountAmountAfterTax != 0 ? Math.Abs(invoice.TotalDiscountAmountAfterTax) : (decimal?)null;
            model.IsIncreaseTotalDiscountAmountAfterTax = invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode() ? (invoice.TotalDiscountAmountAfterTax > 0 ? true : false) : (bool?)null;
            model.TotalDiscountPercentAfterTax = invoice.TotalDiscountPercentAfterTax != 0 ? invoice.TotalDiscountPercentAfterTax : (double?)null;


            #endregion

            //Header extras
            //if (headerExtras != null && headerFields != null)
            //{
            //    var index = headerFields.ToDictionary(x => x.Id, x => x.FieldName);
            //    var lstExtras = new List<Invoice01ExtraXmlModel>();
            //    foreach (var extra in headerExtras)
            //    {
            //        if (!index.ContainsKey(extra.InvoiceHeaderFieldId))
            //            continue;

            //        lstExtras.Add(new Invoice01ExtraXmlModel
            //        {
            //            Name = index[extra.InvoiceHeaderFieldId],
            //            Value = extra.FieldValue
            //        });
            //    }
            //    model.Extras = lstExtras;
            //}
            if (invoice.ExtraProperties != null && invoice.ExtraProperties.Any())
            {
                var extraProperties = new List<InvoiceHeaderExtraModel>();
                if (invoice.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(invoice.ExtraProperties["invoiceHeaderExtras"].ToString()))
                {
                    extraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(invoice.ExtraProperties["invoiceHeaderExtras"].ToString());
                }

                if (extraProperties.Any())
                {
                    model.Extras = extraProperties.Select(x => new Invoice01ExtraXmlModel
                    {
                        Value = x.FieldValue,
                        Name = x.FieldName,
                    }).ToList();
                }
            }


            return new SignInvoice01Model
            {
                InvoiceData = model
            };
        }
    }

    [XmlType("invoiceData")]
    public class Invoice01HeaderXmlModel
    {
        [XmlAttribute("id")]
        public string Data { get; set; }

        [XmlElement("id")]
        public string Id { get; set; }

        #region 1 Thông tin chung
        /// <summary>
        ///Id phát sinh của phần mềm kế toán nếu người nộp thuế phát sinh hóa đơn từ phần mềm kế toán và nhập vào hệ thống lập hóa đơn
        /// </summary>
        [XmlElement("publicCode")]
        public string PublicCode { get; set; }

        /// <summary>
        ///Mã loại hóa đơn chỉ nhận các giá trị sau: 01GTKT, 02GTTT, 07KPTQ, 03XKNB, 04HGDL.tuân thủ theo quy định ký hiệu loại
        ///hóa đơn của Thông tư hướng dẫn thi hành nghị định số 51/2010/NĐ-CP
        ///</summary>
        [XmlElement("invoiceType")]
        public string InvoiceType { get; set; }

        /// <summary>
        ///Tên hóa đơn tuân thủ theo quy tắc đặt tên của Thông tư hướng dẫn thi hành nghị định số 51/2010/NĐ-CP
        ///</summary>
        [XmlElement("invoiceName")]
        public string InvoiceName { get; set; }

        /// <summary>
        ///Mã mẫu hóa đơn, tuân thủ theo quy định ký hiệu mẫu hóa đơn của Thông tư hướng dẫn thi hành nghị định số 51/2010/NĐ-CP
        ///</summary>
        [XmlElement("templateCode")]
        public short TemplateNo { get; set; }

        /// <summary>
        ///Là “Ký hiệu hóa đơn” tuân thủ theo quy tắc tạo ký hiệu hóa đơn của Thông tư hướng dẫn thi hành nghị định số 51/2010/NĐ-CP
        ///</summary>
        [XmlElement("invoiceSeries")]
        public string SerialNo { get; set; }

        /// <summary>
        ///Là Số hóa đơn phát sinh từ phần mềm phát sinh hóa đơn (phần mềm kế toán, ICA, T-VAN). Tuân thủ theo quy định về “Số
        /// thứ tự hóa đơn” của Thông tư hướng dẫn thi hành nghị định số 51/2010/NĐ-CP
        ///</summary>
        [XmlElement("invoiceNumber")]
        public string InvoiceNo { get; set; }

        /// <summary>
        ///Ngày lập hóa đơn được thiết lập theo Nghị định 51/2010/NĐ-CP. / Ngày phiếu xuất
        ///</summary>
        [XmlElement("invoiceDate")]
        public string InvoiceDate { get; set; }

        /// <summary>
        ///Thời điểm ký duyệt hóa đơn bằng chữ ký điện tử
        ///</summary>
        [XmlElement("signedDate")]
        public string SignedDate { get; set; }

        /// <summary>
        ///Ghi chú cho hóa đơn / Nội dung điều động
        ///</summary>
        [XmlElement("note")]
        public string Note { get; set; }

        /// <summary>
        /// Nội dung điều chỉnh
        /// </summary>
        [XmlElement("adjustmentContent")]
        public string AdjustmentContent { get; set; }
        /// <summary>
        /// Trạng thái điều chỉnh hóa đơn:
        ///1: Hóa đơn gốc
        ///3: Hóa đơn thay thế
        ///5: Hóa đơn điều chỉnh
        ///7: Hóa đơn xóa bỏ
        ///9: Hóa đơn điều chỉnh chiết khấu Xem định nghĩa tại Phụ lục 02
        ///</summary>
        [XmlElement("adjustmentType")]
        public int AdjustmentType { get; set; }

        /// <summary>
        /// Id của hóa đơn đã xác thực gốc trong trường hợp hóa đơn là
        /// - Hóa đơn thay thế
        /// - Hóa đơn điều chỉnh
        /// - Hóa đơn xóa bỏ
        ///</summary>
        [XmlElement("idInvoiceReference")]
        public string IdInvoiceReference { get; set; }

        /// <summary>
        /// Id của hóa đơn đã xác thực gốc trong trường hợp hóa đơn là
        /// - Hóa đơn thay thế
        /// - Hóa đơn điều chỉnh
        /// - Hóa đơn xóa bỏ
        ///</summary>
        [XmlElement("adjustmentReason")]
        public string AdjustmentReason { get; set; }

        /// <summary>
        ///Ngày phát sinh văn bản thỏa thuận giữa bên mua và bên bán, dùng cho hóa đơn thay thế, điều chỉnh.
        ///</summary>
        [XmlElement("additionalReferenceDate")]
        public string AdjustmentDate { get; set; }
        #endregion

        #region 2 Thông tin người bán
        /// <summary>
        ///Tên (đăng ký kinh doanh trong trường hợp là doanh nghiệp) của người bán
        /// </summary>
        [XmlElement("sellerLegalName")]
        public string SellerLegalName { get; set; }

        /// <summary>
        ///Mã số thuế người bán được cấp bởi TCT Việt Nam
        /// Mẫu 1: 0312770607
        /// Mẫu 2: 0312770607-00
        /// </summary>
        [XmlElement("sellerTaxCode")]
        public string SellerTaxCode { get; set; }

        /// <summary>
        ///Địa chỉ bưu điện người bán
        /// </summary>
        [XmlElement("sellerAddressLine")]
        public string SellerAddressLine { get; set; }

        /// <summary>
        ///Tên Quận Huyện
        /// </summary>
        [XmlElement("sellerDistrictName")]
        public string SellerDistrictName { get; set; }

        /// <summary>
        ///Tên Tỉnh/Thành phố
        /// </summary>
        [XmlElement("sellerCityName")]
        public string SellerCityName { get; set; }

        /// <summary>
        ///Mã quốc gia
        /// </summary>
        [XmlElement("sellerCountryCode")]
        public string SellerCountryCode { get; set; }

        /// <summary>
        ///Số điện thoại người bán
        /// </summary>
        [XmlElement("sellerPhoneNumber")]
        public string SellerPhoneNumber { get; set; }

        /// <summary>
        ///Số fax người bán
        /// </summary>
        [XmlElement("sellerFaxNumber")]
        public string SellerFaxNumber { get; set; }

        /// <summary>
        ///Địa chỉ thư điện tử người bán
        /// </summary>
        [XmlElement("sellerEmail")]
        public string SellerEmail { get; set; }

        /// <summary>
        ///Tên trụ sở chính ngân hàng nơi người bán mở tài khoản giao dịch
        /// </summary>
        [XmlElement("sellerBankName")]
        public string SellerBankName { get; set; }

        /// <summary>
        ///Tài khoản ngân hàng của người bán
        /// </summary>
        [XmlElement("sellerBankAccount")]
        public string SellerBankAccount { get; set; }

        /// <summary>
        ///Tên người đại diện người bán
        /// </summary>
        [XmlElement("sellerContactPersonName")]
        public string SellerContactPersonName { get; set; }

        /// <summary>
        ///Tên người ký duyệt hóa đơn bằng chữ ký điện tử
        /// </summary>
        [XmlElement("sellerSignedPersonName")]
        public string SellerSignedPersonName { get; set; }

        /// <summary>
        ///Tên người gửi hóa đơn
        /// </summary>
        [XmlElement("sellerSubmittedPersonName")]
        public string SellerSubmittedPersonName { get; set; }

        /// <summary>
        ///Tên người đại diện người bán
        /// </summary>
        [XmlElement("sellerFullName")]
        public string SellerFullName { get; set; }
        #endregion

        #region 3 Thông tin người mua
        /// <summary>
        ///Mã bưu điện 04 (Hà Nội) 08 (HCM)
        /// </summary>
        [XmlElement("buyerCode")]
        public string BuyerCode { get; set; }

        /// <summary>
        ///Tên người mua
        /// </summary>
        [XmlElement("buyerFullName")]
        public string BuyerFullName { get; set; }

        /// <summary>
        ///Tên người mua
        /// </summary>
        [XmlElement("buyerDisplayName")]
        public string BuyerDisplayName { get; set; }

        /// <summary>
        ///Tên (đăng ký kinh doanh trong trường hợp là doanh nghiệp) của người mua
        /// </summary>
        [XmlElement("buyerLegalName")]
        public string BuyerLegalName { get; set; }

        /// <summary>
        ///Mã số thuế người mua được cấp bởi TCT Việt Nam
        ///Mẫu 1: 0312770607
        ///Mẫu 2: 0312770607-001
        /// </summary>
        [XmlElement("buyerTaxCode")]
        public string BuyerTaxCode { get; set; }

        /// <summary>
        ///Địa chỉ bưu điện người mua
        /// </summary>
        [XmlElement("buyerAddressLine")]
        public string BuyerAddressLine { get; set; }

        /// <summary>
        ///Tên Quận Huyện
        /// </summary>
        [XmlElement("buyerDistrictName")]
        public string BuyerDistrictName { get; set; }

        /// <summary>
        ///Tên Tỉnh/Thành phố
        /// </summary>
        [XmlElement("buyerCityName")]
        public string BuyerCityName { get; set; }

        /// <summary>
        ///Mã quốc gia VN (Việt Nam)
        /// </summary>
        [XmlElement("buyerCountryCode")]
        public string BuyerCountryCode { get; set; }

        /// <summary>
        ///Số điện thoại người mua
        /// </summary>
        [XmlElement("buyerPhoneNumber")]
        public string BuyerPhoneNumber { get; set; }

        /// <summary>
        ///Số fax người mua
        /// </summary>
        [XmlElement("buyerFaxNumber")]
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        ///Địa chỉ thư điện tử người mua
        /// </summary>
        [XmlElement("buyerEmail")]
        public string BuyerEmail { get; set; }

        /// <summary>
        ///Tên trụ sở chính ngân hàng nơi người mua mở tài khoản giao dịch
        /// </summary>
        [XmlElement("buyerBankName")]
        public string BuyerBankName { get; set; }

        /// <summary>
        ///Tài khoản ngân hàng của người mua
        /// </summary>
        [XmlElement("buyerBankAccount")]
        public string BuyerBankAccount { get; set; }
        #endregion

        #region 4 Thông tin thanh toán
        /// <summary>
        ///Mã tiền tệ dùng cho hóa đơn có chiều dài 3 ký tự theo quy định của NHNN Việt Nam. Ví dụ: USD, VND, EUR…
        ///</summary>
        [XmlElement("currencyCode")]
        public string CurrencyCode { get; set; }

        /// <summary>
        ///Tỷ giá ngoại tệ tại thời điểm lập hóa đơn quy đổi ra VNĐ
        ///</summary>
        [XmlElement("exchangeRate")]
        public decimal? ExchangeRate { get; set; }

        /// <summary>
        ///Phương thức thanh toán
        ///</summary>
        [XmlElement("paymentMethod")]
        public string PaymentMethod { get; set; }

        /// <summary>
        ///Tổng tiền thanh toán
        ///</summary>
        [XmlElement("paymentAmount")]
        public decimal PaymentAmount { get; set; }

        /// <summary>
        ///Ngày tới hạn thanh toán
        ///</summary>
        [XmlElement("paymentDueDate")]
        public string PaymentDate { get; set; }
        #endregion

        #region 5 Thông tin vận chuyển hàng hóa
        #endregion

        #region 6 Thông tin chi tiết hóa đơn
        /// <summary>
        ///Thông tin chi tiết hóa đơn
        /// </summary>
        [XmlArray("items")]
        [XmlArrayItem("item")]
        public List<Invoice01DetailXmlModel> Items { get; set; }
        #endregion

        #region 7 Thông tin các mức thuế và thành tiền thuế
        /// <summary>
        ///Thông tin các mức thuế và thành tiền thuế
        /// </summary>
        [XmlElement("invoiceTaxBreakdowns")]
        public List<Invoice01TaxBreakdownsXmlModel> InvoiceTaxBreakdowns { get; set; }
        #endregion

        #region 8 Thông tin tóm tắt hóa đơn
        /// <summary>
        ///Tổng tiền hóa đơn chưa bao gồm VAT
        ///Hóa đơn thường: Tổng tiền HHDV trên các dòng HĐ và các khoản tăng/giảm khác trên toàn HĐ
        ///Hóa đơn điều chỉnh: Tổng tiền điều chỉnh của các dòng HĐ và các khoản tăng/giảm khác trên toàn HĐ.
        ///</summary>
        [XmlElement("totalAmount")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        ///Tổng tiền thuế trên toàn hóa đơn
        /// Hóa đơn thường: Tổng tiền VAT trên các dòng HĐ và các khoản thuế khác trên toàn HĐ
        ///Hóa đơn điều chỉnh: Tổng tiền VAT điều chỉnh của các dòng HĐ và các khoản tăng/giảm VAT khác trên toàn HĐ
        ///</summary>
        [XmlElement("totalVatAmount")]
        public decimal? TotalVatAmount { get; set; }

        /// <summary>
        ///Tổng tiền trên hóa đơn đã bao gồm VAT
        ///Hóa đơn thường: Tổng tiền HHDV trên các dòng HĐ và các khoản tăng/giảm khác trên toàn HĐ đã bao gồm cả VAT
        ///Hóa đơn điều chỉnh: Tổng tiền điều chỉnh của các dòng HĐ và các khoản tăng/giảm khác trên toàn HĐ đã bao gồm cả VAT
        ///</summary>
        [XmlElement("totalPaymentAmount")]
        public decimal? TotalPaymentAmount { get; set; }

        /// <summary>
        ///Số tiền hóa đơn bao gồm VAT viết bằng chữ
        ///</summary>
        [XmlElement("totalPaymentAmountInWords")]
        public string PaymentAmountWords { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế
        /// </summary>
        /// <value></value>
        [XmlElement("totalDiscountAmountBeforeTax")]
        public decimal? TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu sau thuế
        /// </summary>
        /// <value></value>
        [XmlElement("totalDiscountPercentAfterTax")]
        public double? TotalDiscountPercentAfterTax { get; set; }

        /// <summary>
        ///Tổng tiền chiết khấu trên toàn hóa đơn trước khi tính thuế.
        ///Chú ý:Khi tính chiết khấu, toàn hóa đơn chỉ sử dụng một mức thuế.
        ///</summary>
        [XmlElement("totalDiscountAmountAfterTax")]
        public decimal? TotalDiscountAmountAfterTax { get; set; }

        /// <summary>
        ///Trường nhận biết tổng tiền hóa đơn bao gồm VAT tăng giảm(Hóa đơn điều chỉnh):
        ///- Hóa đơn thường: null
        ///- True: tăng
        ///- False: Giảm
        ///</summary>
        [XmlElement("isIncreaseTotalAmount")]
        public bool? IsIncreaseTotalAmount { get; set; }

        /// <summary>
        ///Trường nhận biết tổng tiền thuế hóa đơn tăng giảm(Hóa đơn điều chỉnh):
        ///- Hóa đơn thường: null
        ///- True: tăng
        ///- False: Giảm
        ///</summary>
        [XmlElement("isIncreaseTotalVatAmount")]
        public bool? IsIncreaseTotalVatAmount { get; set; }

        /// <summary>
        ///Trường nhận biết tổng tiền hóa đơn chưa bao gồm VAT tăng giảm (Hóa đơn điều chỉnh):
        ///- Hóa đơn thường: null
        ///- True: tăng
        ///- False: Giảm
        ///</summary>
        [XmlElement("isIncreaseTotalPaymentAmount")]
        public bool? IsIncreaseTotalPaymentAmount { get; set; }

        /// <summary>
        ///Trường nhận biết tổng tiền chiết khấu tăng (dương) hay giảm(âm)
        ///False: giảm
        ///True: tăng
        ///Hóa đơn thường: giá trị này luôn là False.
        ///Hóa đơn điều chỉnh: giá trị này có thể là
        /// True/False tuy vào <inv:discountAmount/> là tăng hoặc giảm.
        ///</summary>
        [XmlElement("isIncreaseTotalDiscountAmountAfterTax")]
        public bool? IsIncreaseTotalDiscountAmountAfterTax { get; set; }
        #endregion

        [XmlArray("extras")]
        [XmlArrayItem("extra")]
        public List<Invoice01ExtraXmlModel> Extras { get; set; }
    }

    [XmlType("extra")]
    public class Invoice01ExtraXmlModel
    {
        [XmlElement("name")]
        public string Name { get; set; }

        [XmlElement("value")]
        public string Value { get; set; }
    }

    /// <summary>
    /// Thông tin các mức thuế và thành tiền thuế
    /// </summary>
    [XmlType("invoiceTaxBreakdowns")]
    public class Invoice01TaxBreakdownsXmlModel
    {
        /// <summary>
        /// Tên thuế suất
        /// </summary>
        /// <value></value>
        [XmlElement("vatName")]
        public string VatName { get; set; }

        /// <summary>
        ///Mức thuế: khai báo giá trị như sau
        /// 0%: 0
        /// 5%: 5
        /// 10%: 10
        /// Không chịu thuế: -1
        /// Không kê khai nộp thuế:
        /// -2
        /// </summary>
        [XmlElement("vatPercent")]
        public decimal VatPercent { get; set; }

        /// <summary>
        ///Tổng tiền chịu thuế của mức thuế tương ứng
        /// </summary>
        [XmlElement("vatAmount")]
        public decimal VatAmount { get; set; }

        [XmlElement("isIncrease")]
        public bool? IsIncrease { get; set; }
    }

    ///// <summary>
    ///// Thông tin chi tiết hóa đơn
    ///// </summary>
    //[XmlType("item")]
    //public class ItemsXmlModel
    //{
    //    /// <summary>
    //    ///Thứ tự dòng hóa đơn, bắt đầu từ 1
    //    /// </summary>
    //    [XmlElement("lineNumber")]
    //    public int LineNumber { get; set; }

    //    /// <summary>
    //    ///Mã hàng hóa, dịch vụ
    //    /// </summary>
    //    [XmlElement("itemCode")]
    //    public string ItemCode { get; set; }

    //    /// <summary>
    //    ///Tên hàng hóa, dịch vụ
    //    /// </summary>
    //    [XmlElement("itemName")]
    //    public string ItemName { get; set; }

    //    /// <summary>
    //    ///Mã đơn vị tính
    //    /// </summary>
    //    [XmlElement("unitCode")]
    //    public string UnitCode { get; set; }

    //    /// <summary>
    //    ///Tên đơn vị tính hàng hóa, dịch vụ
    //    /// </summary>
    //    [XmlElement("unitName")]
    //    public string UnitName { get; set; }

    //    /// <summary>
    //    ///Đơn giá
    //    /// </summary>
    //    [XmlElement("unitPrice")]
    //    public decimal UnitPrice { get; set; }

    //    /// <summary>
    //    ///Số lượng
    //    /// </summary>
    //    [XmlElement("quantity")]
    //    public decimal? Quantity { get; set; }

    //    /// <summary>
    //    ///Số lượng thực xuất
    //    /// </summary>
    //    [XmlElement("quantityActualExport")]
    //    public double? QuantityActualExport { get; set; }

    //    /// <summary>
    //    ///Số lượng thực nhập
    //    /// </summary>
    //    [XmlElement("quantityActualImported")]
    //    public double? QuantityActualImported { get; set; }

    //    /// <summary>
    //    ///Hóa đơn thường: Là tổng tiền hàng hóa dịch vụ chưa có VAT Hóa đơn điều chỉnh: 
    //    /// Là tổng tiền phần điều chỉnh của hàng hóa dịch vụ chưa có VAT
    //    /// </summary>
    //    [XmlElement("itemTotalAmountWithoutVat")]
    //    public decimal ItemTotalAmountWithoutVat { get; set; }

    //    /// <summary>
    //    ///Thuế suất của hàng hóa, dịch vụ
    //    /// </summary>
    //    [XmlElement("vatPercentage")]
    //    public decimal? VatPercentage { get; set; }

    //    /// <summary>
    //    ///Tổng tiền thuế
    //    /// </summary>
    //    [XmlElement("vatAmount")]
    //    public decimal VatAmount { get; set; }

    //    /// <summary>
    //    ///Cho biết loại hàng hóa dịch vụ là khuyến mãi hay không:
    //    /// True: hàng khuyến mãi
    //    /// False: hàng hóa thường
    //    /// </summary>
    //    [XmlElement("promotion")]
    //    public bool? Promotion { get; set; }

    //    /// <summary>
    //    ///Hóa đơn thường: có giá trị là Null.
    //    /// Hóa đơn điều chỉnh: Tổng giá trị tiền thuế bị điều chỉnh
    //    /// </summary>
    //    [XmlElement("adjustmentVatAmount")]
    //    public decimal? AdjustmentVatAmount { get; set; }

    //    /// <summary>
    //    ///Hóa bình thường: có giá trị là Null
    //    ///Hóa đơn điều chỉnh:
    //    /// - False: dòng hàng hóa dịch vụ bị điều chình giảm
    //    ///- True: dòng hàng hóa dịch vụ bị điều chỉnh tăng
    //    /// </summary>
    //    [XmlElement("isIncreaseItem")]
    //    public bool? IsIncreaseItem { get; set; }

    //    /// <summary>
    //    /// Ghi chú
    //    /// </summary>
    //    [XmlElement("note")]
    //    public string Note { get; set; }


    //    /// <summary>
    //    /// Mở rộng chi tiết hoa đơn
    //    /// </summary>
    //    [XmlArray("extras")]
    //    [XmlArrayItem("extra")]
    //    public List<Invoice01ExtraXmlModel> Extras { get; set; }

    //    public static ItemsXmlModel Detail01ToXml(int invoiceStatus, Invoice01Detail entity)
    //    {
    //        if (entity == null)
    //            return null;

    //        var item = new ItemsXmlModel();
    //        {
    //            item.LineNumber = entity.Id;
    //            item.ItemCode = entity.ProductId;
    //            item.ItemName = entity.ProductName;
    //            item.UnitCode = entity.UnitName;
    //            item.UnitName = entity.UnitName;
    //            item.UnitPrice = entity.UnitPrice;
    //            item.Quantity = entity.Quantity;
    //            item.ItemTotalAmountWithoutVat = entity.PaymentAmount;
    //            item.VatPercentage = entity.VatPercent;
    //            item.VatAmount = entity.VatAmount;
    //            item.Promotion = null;
    //            item.Note = entity.Note;
    //            if (invoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
    //            {
    //                if (entity.VatAmount > 0)
    //                    item.AdjustmentVatAmount = entity.VatAmount;

    //                if (entity.PaymentAmount > 0)
    //                    item.IsIncreaseItem = entity.PaymentAmount > 0;
    //            }

    //        };
    //        return item;
    //    }

    //}

    /// <summary>
    /// Thông tin chi tiết hóa đơn
    /// </summary>
    [XmlType("item")]
    public class Invoice01DetailXmlModel
    {
        [XmlElement("itemId")]
        public string Id { get; set; }

        /// <summary>
        ///Thứ tự dòng hóa đơn, bắt đầu từ 1
        /// </summary>
        [XmlElement("line")]
        public int Line { get; set; }

        /// <summary>
        ///Mã hàng hóa, dịch vụ
        /// </summary>
        [XmlElement("productCode")]
        public string ProductCode { get; set; }

        /// <summary>
        ///Tên hàng hóa, dịch vụ
        /// </summary>
        [XmlElement("productName")]
        public string ProductName { get; set; }

        /// <summary>
        ///Tên đơn vị tính hàng hóa, dịch vụ
        /// </summary>
        [XmlElement("unitName")]
        public string UnitName { get; set; }

        /// <summary>
        ///Đơn giá
        /// </summary>
        [XmlElement("unitPrice")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        ///Hóa bình thường: có giá trị là Null
        ///Hóa đơn điều chỉnh:
        ///- False: dòng hàng hóa dịch vụ bị điều chình giảm
        ///- True: dòng hàng hóa dịch vụ bị điều chỉnh tăng
        /// </summary>
        [XmlElement("isIncreaseUnitPrice")]
        public bool? IsIncreaseUnitPrice { get; set; }

        /// <summary>
        ///Số lượng
        /// </summary>
        [XmlElement("quantity")]
        public decimal? Quantity { get; set; }

        /// <summary>
        ///Hóa bình thường: có giá trị là Null
        ///Hóa đơn điều chỉnh:
        ///- False: dòng hàng hóa dịch vụ bị điều chình giảm
        ///- True: dòng hàng hóa dịch vụ bị điều chỉnh tăng
        /// </summary>
        [XmlElement("isIncreaseQuantity")]
        public bool? IsIncreaseQuantity { get; set; }

        /// <summary>
        ///Hóa đơn thường: Là tổng tiền hàng hóa dịch vụ chưa có VAT Hóa đơn điều chỉnh: 
        /// Là tổng tiền phần điều chỉnh của hàng hóa dịch vụ chưa có VAT
        /// </summary>
        [XmlElement("itemTotalAmountWithoutVat")]
        public decimal ItemTotalAmountWithoutVat { get; set; }

        /// <summary>
        ///Hóa bình thường: có giá trị là Null
        ///Hóa đơn điều chỉnh:
        ///- False: dòng hàng hóa dịch vụ bị điều chình giảm
        ///- True: dòng hàng hóa dịch vụ bị điều chỉnh tăng
        /// </summary>
        [XmlElement("isIncreaseAmount")]
        public bool? IsIncreaseAmount { get; set; }

        /// <summary>
        ///Thuế suất của hàng hóa, dịch vụ
        /// </summary>
        [XmlElement("vatPercent")]
        public decimal VatPercent { get; set; }

        /// <summary>
        ///Tổng tiền thuế
        /// </summary>
        [XmlElement("vatAmount")]
        public decimal VatAmount { get; set; }

        /// <summary>
        ///Hóa bình thường: có giá trị là Null
        ///Hóa đơn điều chỉnh:
        ///- False: dòng hàng hóa dịch vụ bị điều chình giảm
        ///- True: dòng hàng hóa dịch vụ bị điều chỉnh tăng
        /// </summary>
        [XmlElement("isIncreaseVatAmount")]
        public bool? IsIncreaseVatAmount { get; set; }

        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        [XmlElement("discountPercentBeforeTax")]
        public decimal? DiscountPercentBeforeTax { get; set; }

        /// <summary>
        /// Tiền chiết khấu
        /// </summary>
        [XmlElement("discountAmountBeforeTax")]
        public decimal? DiscountAmountBeforeTax { get; set; }

        /// <summary>
        ///Hóa bình thường: có giá trị là Null
        ///Hóa đơn điều chỉnh:
        ///- False: dòng hàng hóa dịch vụ bị điều chình giảm
        ///- True: dòng hàng hóa dịch vụ bị điều chỉnh tăng
        /// </summary>
        [XmlElement("isIncreaseDiscountAmountBeforeTax")]
        public bool? IsIncreaseDiscountAmountBeforeTax { get; set; }

        /// <summary>
        ///Ghi chú cho chi tiết hóa đơn
        ///</summary>
        [XmlElement("note")]
        public string Note { get; set; }

        [XmlArray("extras")]
        [XmlArrayItem("extra")]
        public List<Invoice01ExtraXmlModel> Extras { get; set; }
    }

}
