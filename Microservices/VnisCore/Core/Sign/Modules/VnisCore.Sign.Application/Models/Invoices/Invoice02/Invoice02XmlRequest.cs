using System;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;

namespace VnisCore.Sign.Application.Models.Invoices.Invoice02
{
    public class Invoice02XmlRequest
    {
        public long Id { get; set; }

        public long InvoiceHeaderId { get; set; }

        public string PhysicalFileName { get; set; }

        public string ContentType { get; set; }

        public string FileName { get; set; }

        public long Length { get; set; }

        public long Partition { get; set; }

        public DateTime CreationTime { get; set; }

        public static explicit operator Invoice02XmlRequest(Invoice02XmlEntity entity)
        {
            if (entity == null)
                return null;

            return new Invoice02XmlRequest
            {
                Id = entity.Id,
                InvoiceHeaderId = entity.InvoiceHeaderId,
                PhysicalFileName = entity.PhysicalFileName,
                ContentType = entity.ContentType,
                FileName = entity.FileName,
                Length = entity.Length,
                Partition = entity.Partition,
                CreationTime = entity.CreationTime,
            };
        }
    }
}