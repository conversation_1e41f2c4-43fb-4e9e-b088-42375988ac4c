using System.ComponentModel.DataAnnotations;

namespace VnisCore.Sign.Application.Models.SignClient
{
    public class SignClientXmlModel
    {
        [Required(ErrorMessage = "Id hóa đơn không được để trống")]
        public long Id { get; set; }

        public short TemplateNo { get; set; }

        public string TemplateNoPitDocument { get; set; }

        public string SerialNo { get; set; }

        public string InvoiceNo { get; set; }

        [Required(ErrorMessage = "XML đã ký không được để trống")]
        public string Xml { get; set; }

        public long? InvoiceType { get; set; }

        //[Required(ErrorMessage = "IdData hóa đơn không được để trống")]
        public string IdData { get; set; }

        //[Required(ErrorMessage = "IdObject hóa đơn không được để trống")]
        public string IdObject { get; set; }

        public string IdSignature { get; set; }
    }
}
