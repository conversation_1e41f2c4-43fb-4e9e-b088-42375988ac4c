using MediatR;
using System;

namespace VnisCore.Sign.Application.Models.Requests.Commands
{
    public class SignInvoiceRequestModel : IRequest
    {
        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }
        public string UserFullName { get; set; }
        public long InvoiceHeaderId { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public string InvoiceNo { get; set; }
    }
}
