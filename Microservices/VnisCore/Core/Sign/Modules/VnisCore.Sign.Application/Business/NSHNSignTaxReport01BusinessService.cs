using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using System;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Sign.Application.Abtractions;
using VnisCore.Sign.Application.Dto;
using VnisCore.Sign.Application.Interfaces;

namespace VnisCore.Sign.Application.Business
{
    public class NSHNSignTaxReport01BusinessService : BaseSignTaxReport01BusinessService, ISignTaxReport01BusinessService
    {
        public NSHNSignTaxReport01BusinessService(IAppFactory appFactory)
            : base(appFactory)
        {
        }

        public override string GetQueryTaxReportDetail(TaxReport01HeaderEntity taxHeader, string fromDate, string toDate, int skipCount, int maxResultCount)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxHeader.TenantId);
            return $@"
                                WITH Invoice01 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        a.""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""ExchangeRate"",
                                        a.""ReferenceInvoiceType"",
                                        a.""InvoiceDate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalVatAmount"" END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        1 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice01Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            CAST(SUBSTR(LISTAGG(""ProductName"",';' ON OVERFLOW TRUNCATE) within group( order by ""ProductName"" ),1,500) as NVARCHAR2(500)) as ""ProductName"",
                                            ""VatPercent"",
                                            CAST(SUBSTR(LISTAGG(""Note"",';' ON OVERFLOW TRUNCATE) within group( order by ""Note"" ),1,255) as NVARCHAR2(255)) as ""Note""
                                        FROM ""Invoice01Detail"" WHERE ""TenantId"" = '{rawTenantId}'
                                        GROUP BY ""InvoiceHeaderId"",""VatPercent""
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 1 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    ORDER BY a.""InvoiceDate"", a.""Number""
                                    ),
                                    Invoice02 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""Invoice02Detail""  WHERE ""TenantId"" = '{rawTenantId}' 
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    ORDER BY a.""InvoiceDate"",a.""Number""
                                    ),
                                    Invoice03 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        NULL AS ""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""Invoice03Detail""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                   AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    ORDER BY a.""InvoiceDate"", a.""Number""
                                    ),
                                    Invoice04 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        NULL AS ""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""Invoice04Detail""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    ORDER BY a.""InvoiceDate"", a.""Number""
                                    ),
                                    Ticket AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""TicketDetail""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                            
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    ORDER BY a.""InvoiceDate"", a.""Number""
                                    )
                                SELECT * FROM (
                                    SELECT 
                                        rownum as ""RowNumber"", 
                                        a.""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        a.""ReceiverName"",
                                        a.""BuyerFullName"",
                                        a.""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""ExchangeRate"",
                                        a.""ReferenceInvoiceType"",
                                        a.""InvoiceDate"",
                                        a.""TotalAmount"",
                                        a.""TotalVatAmount"",
                                        a.""TotalPaymentAmount"",
                                        CAST(SUBSTR(a.""Status"",1,500) AS NVARCHAR2(50)) AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        a.""InvoiceReferenceData"",
                                        a.""InvoiceReferenceDataSplit"",
                                        a.""InvoiceType"",
                                        a.""ProductName"",
                                        a.""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CAST(SUBSTR(a.""Note"",1,255) AS NVARCHAR2(255)) AS ""Note""
                                    FROM (
                                        SELECT * FROM Invoice01
                                        UNION
                                        SELECT * FROM Invoice02
                                        UNION
                                        SELECT * FROM Invoice03
                                        UNION
                                        SELECT * FROM Invoice04
                                        UNION
                                        SELECT * FROM Ticket
                                    ) a 
                                ) a
                                WHERE ""RowNumber"" BETWEEN  {skipCount + 1} AND {skipCount + maxResultCount}
                            ";
        }
        public override string GetQueryTaxReportDetail(TaxReport01HeaderEntity taxHeader, QueryInvoiceModel model)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(model.TenantId);

            if (!model.IdsInvoice01.IsNullOrEmpty())
            {
                var conditionIdInvoice01 = GetConditionId(model.IdsInvoice01);
                var sql = $@"
                                    SELECT 
                                        rownum as ""RowNumber"",
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        a.""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""ReferenceInvoiceType"",
                                        a.""InvoiceDate"",
                                        a.""ExchangeRate"" AS ""ExchangeRate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmountBeforeTax"" END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalVatAmount"" END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        1 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice01Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            CAST(SUBSTR(LISTAGG(""ProductName"",';' ON OVERFLOW TRUNCATE) within group( order by ""ProductName"" ),1,500) as NVARCHAR2(500)) as ""ProductName"",
                                            ""VatPercent"",
                                            CAST(SUBSTR(LISTAGG(""Note"",';' ON OVERFLOW TRUNCATE) within group( order by ""Note"" ),1,255) as NVARCHAR2(255)) as ""Note""
                                        FROM ""Invoice01Detail"" WHERE ""TenantId"" = '{rawTenantId}'
                                        GROUP BY ""InvoiceHeaderId"",""VatPercent""
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 1 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    -- AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    {conditionIdInvoice01}
                                    ORDER BY a.""InvoiceDate"", a.""Id""";
                return sql;
            }
            else if (!model.IdsInvoice02.IsNullOrEmpty())
            {
                var conditionIdInvoice02 = GetConditionId(model.IdsInvoice02);
                var sql = $@"SELECT 
                                        rownum as ""RowNumber"",
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""Invoice02Detail""  WHERE ""TenantId"" = '{rawTenantId}' 
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    -- AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    {conditionIdInvoice02}
                                    ORDER BY a.""InvoiceDate"", a.""Id""";
                return sql;
            }
            else if (!model.IdsInvoice03.IsNullOrEmpty())
            {
                var conditionIdInvoice03 = GetConditionId(model.IdsInvoice03);
                var sql = $@"SELECT 
                                        rownum as ""RowNumber"",
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        NULL AS ""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""Invoice03Detail""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    -- AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    {conditionIdInvoice03}
                                    ORDER BY a.""InvoiceDate"", a.""Id""";
                return sql;
            }
            else if (!model.IdsInvoice04.IsNullOrEmpty())
            {
                var conditionIdInvoice04 = GetConditionId(model.IdsInvoice04);
                var sql = $@"SELECT 
                                        rownum as ""RowNumber"",
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        NULL AS ""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""Invoice04Detail""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                         
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    -- AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    {conditionIdInvoice04}
                                    ORDER BY a.""InvoiceDate"", a.""Id""";
                return sql;
            }
            else if (!model.IdsInvoice05.IsNullOrEmpty())
            {
                var conditionIdInvoice05 = GetConditionId(model.IdsInvoice05);
                var sql = $@"SELECT 
                                        rownum as ""RowNumber"",
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ReferenceInvoiceType"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmountBeforeTax"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalPaymentAmount"" END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN d.""Status"" IS NOT NULL THEN d.""Status""
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                            ELSE null
                                        END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        a.""BudgetUnitCode"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""ProductName"",
                                            ""Note""
                                        FROM ""TicketDetail""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference""  WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",                                            
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'YYYY/MM/DD') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 AND ""TaxReportHeaderId"" = {taxHeader.Id} 
                                    ) d
                                    ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    -- AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    {conditionIdInvoice05}
                                    ORDER BY a.""InvoiceDate"", a.""Id""";
                return sql;
            }
            return " ";
            #region dataReport
            //return $@"
            //                    WITH Invoice01 AS (
                                    
            //                        ),
            //                        Invoice02 AS (
                                    
            //                        ),
            //                        Invoice03 AS (
                                    
            //                        ),
            //                        Invoice04 AS (
                                    
            //                        ),
            //                        Ticket AS (
                                    
            //                        )
            //                    SELECT * FROM (
            //                        SELECT 
            //                            rownum as ""RowNumber"", 
            //                            a.""InvoiceHeaderId"",
            //                            a.""TemplateNo"",
            //                            a.""SerialNo"",
            //                            a.""ReceiverName"",
            //                            a.""BuyerFullName"",
            //                            a.""BuyerName"",
            //                            a.""BuyerTaxCode"",
            //                            a.""InvoiceNo"",
            //                            a.""Number"",
            //                            a.""ReferenceInvoiceType"",
            //                            a.""InvoiceDate"",
            //                            a.""TotalAmount"",
            //                            a.""TotalVatAmount"",
            //                            a.""TotalPaymentAmount"",
            //                            CAST(SUBSTR(a.""Status"",1,500) AS NVARCHAR2(50)) AS ""Status"",
            //                            a.""InvoiceStatus"",
            //                            a.""InvoiceTemplateId"",
            //                            a.""InvoiceReferenceData"",
            //                            a.""InvoiceReferenceDataSplit"",
            //                            a.""InvoiceType"",
            //                            a.""ProductName"",
            //                            a.""VatPercent"",
            //                            CAST(SUBSTR(a.""Note"",1,255) AS NVARCHAR2(255)) AS ""Note""
            //                        FROM (
            //                            SELECT * FROM Invoice01
            //                            UNION
            //                            SELECT * FROM Invoice02
            //                            UNION
            //                            SELECT * FROM Invoice03
            //                            UNION
            //                            SELECT * FROM Invoice04
            //                            UNION
            //                            SELECT * FROM Ticket
            //                        ) a 
            //                    ) a
            //                    WHERE ""RowNumber"" BETWEEN  {model.SkipCount + 1} AND {model.SkipCount + model.MaxResultCount}
            //                ";
            #endregion
        }
    }
}
