using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Sign.Application.SignClient.InvoicesError.Common;

namespace VnisCore.Sign.Application.SignClient.InvoicesError.Invoice01.Interfaces
{
    public interface ISignClientInvoice01ErrorBusiness
    {
        Task<List<SignClientInvoiceErrorXmlModel>> GetInvoice01ErrorXmlAsync(string serialNumber, string subjectName);

        Task SaveInvoice01ErrorAsync(SignClientInvoiceErrorXmlModel command);
    }
}
