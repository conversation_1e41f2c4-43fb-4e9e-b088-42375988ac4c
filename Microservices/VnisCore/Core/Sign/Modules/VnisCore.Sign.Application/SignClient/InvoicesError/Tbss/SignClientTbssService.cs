using Core.Application.Services;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Sign.Application.SignClient.InvoicesError.Common;
using VnisCore.Sign.Application.SignClient.InvoicesError.Tbss.Interfaces;

namespace VnisCore.Sign.Application.SignClient.InvoicesError.Tbss
{
    /// <summary>
    /// SignClient TBSS hóa đơn ngoài hệ thống
    /// </summary>
    public class SignClientTbssService : ApplicationService
    {
        private readonly ISignClientTbssBusiness _signClientTbssBusiness;

        public SignClientTbssService(
            ISignClientTbssBusiness signClientTbssBusiness)
        {
            _signClientTbssBusiness = signClientTbssBusiness;
        }

        /// <summary>
        /// Lấy 1 TBSS đang chờ ký để bắt đầu ký
        /// </summary>
        /// <returns></returns>
        /// <param name="serialNumber"></param>
        /// <param name="subjectName"></param>
        [HttpGet(Utilities.ApiUrlBase + "GetInvoiceXml")]
        public async Task<List<SignClientInvoiceErrorXmlModel>> GetInvoiceXml([FromQuery] string serialNumber, [FromQuery] string subjectName)
        {
            return await _signClientTbssBusiness.GetTbssXmlAsync(serialNumber, subjectName);
        }


        /// <summary>
        /// Post 1 TBSS sau khi ký
        /// </summary>
        /// <param name="request"></param>
        [HttpPost(Utilities.ApiUrlBase + "SaveInvoiceXml")]
        public async Task SaveInvoiceXml([FromBody] SignClientInvoiceErrorXmlModel request)
        {
            await _signClientTbssBusiness.SaveTbssAsync(request);
        }
    }
}
