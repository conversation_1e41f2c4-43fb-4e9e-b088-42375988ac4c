using Core;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Tvan.Models.Xmls.Invoices.PITDeductionDocument;

using Microsoft.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Core.Shared.Helper;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITDeductionDocument;
using VnisCore.Sign.Application.Interfaces;
using VnisCore.Sign.Application.Models.Invoices.PitDocumentError;
using VnisCore.Sign.Application.SignClient.InvoicesError.Common;
using VnisCore.Sign.Application.SignClient.InvoicesError.PITDeductionDocument.Interfaces;

namespace VnisCore.Sign.Application.SignClient.InvoicesError.PITDeductionDocument.Business
{
    public class SignClientPITDeductionDocumentErrorBusiness : ISignClientPITDeductionDocumentErrorBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly ISignService _signService;
        private readonly ISignClientPITDeductionDocumentErrorSaveXmlBusiness _signClientPITDeductionDocumentErrorSaveXmlBusiness;

        public SignClientPITDeductionDocumentErrorBusiness(IAppFactory appFactory,
            ISignService signService,
            ISignClientPITDeductionDocumentErrorSaveXmlBusiness signClientPITDeductionDocumentErrorSaveXmlBusiness)
        {
            _appFactory = appFactory;
            _signService = signService;
            _signClientPITDeductionDocumentErrorSaveXmlBusiness = signClientPITDeductionDocumentErrorSaveXmlBusiness;
        }

        public async Task<List<SignClientInvoiceErrorXmlModel>> GetPITDeductionDocumentErrorXmlAsync(string serialNumber, string subjectName)
        {
            var tenant = _appFactory.CurrentTenant;
            if (!tenant.IsAvailable)
                throw new UserFriendlyException("Không tìm thấy chi nhánh");

            var user = _appFactory.CurrentUser;
            if (!user.IsAuthenticated)
                throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");

            // lấy ra usb token
            var repoCertificate = _appFactory.Repository<UsbTokenEntity, long>();
            var certificates = await repoCertificate.Where(x => x.TenantId == tenant.Id && new List<string> { serialNumber }.Contains(x.SerialNumber) && new List<string> { subjectName }.Contains(x.SubjectName)).ToListAsync();

            if (certificates == null || certificates.Count == 0)
                throw new UserFriendlyException("Không tìm thấy chứng thư số");

            var cetificate = certificates.FirstOrDefault();
            if (cetificate == null)
                throw new UserFriendlyException("Không tìm thấy chứng thư số");

            if (cetificate.EndDate < DateTime.UtcNow)
                throw new UserFriendlyException("Chứng thư số đã hết hạn");

            var certTaxCode = StringHelper.ExtractTaxCodeNew(subjectName);
            if (certTaxCode != _appFactory.CurrentTenant.TaxCode)
                throw new UserFriendlyException("Chứng thư số không hợp lệ với mã số thuế");

            var model = await GetInvoiceErrorXmlAsync(tenant.Id ?? Guid.Empty);
            return model;
        }

        public async Task SavePITDeductionDocumentErrorAsync(SignClientInvoiceErrorXmlModel command)
        {
            var tenant = _appFactory.CurrentTenant;
            if (!tenant.IsAvailable)
                throw new UserFriendlyException("Không tìm thấy chi nhánh");

            var user = _appFactory.CurrentUser;
            if (!user.IsAuthenticated)
                throw new UserFriendlyException("Không tìm thấy thông tin tài khoản");

            var repo = _appFactory.Repository<PITDeductionDocumentErrorEntity, long>();
            if (!(await repo.AnyAsync(x => x.TenantId == tenant.Id && x.GroupCode == command.GroupCode)))
                throw new UserFriendlyException("Không tìm thấy thông báo sai sót");

            await _signClientPITDeductionDocumentErrorSaveXmlBusiness.SavePITDeductionDocumentErrorXmlAsync(tenant.Id.Value, user.Id.Value, tenant.TaxCode, _appFactory.CurrentUser.FullName, command);
        }

        #region helpers
        private async Task<List<SignClientInvoiceErrorXmlModel>> GetInvoiceErrorXmlAsync(Guid tenantId)
        {
            var repoInvoiceError = _appFactory.Repository<PITDeductionDocumentErrorEntity, long>();
            var invoiceErrors = await repoInvoiceError.Where(x => x.TenantId == tenantId
                                                                  && (x.SignStatus == (short)PITSignStatus.ChuaKy.GetHashCode() || x.SignStatus == (short)PITSignStatus.KySoLoi.GetHashCode()))
                                                      .OrderBy(x => x.GroupCode)
                                                      .ToListAsync();

            if (invoiceErrors == null)
                return null;

            //group lai
            var groupByGroupCode = invoiceErrors.GroupBy(x => x.GroupCode).OrderBy(x => x.Key);
            var result = new List<SignClientInvoiceErrorXmlModel>();

            foreach (var group in groupByGroupCode)
            {
                var xml = GetInvoiceErrorXmlModel(group.ToList());

                result.Add(new SignClientInvoiceErrorXmlModel
                {
                    GroupCode = group.Key,
                    IdData = xml.DLTBao.Data,
                    IdObject = xml.DSCKS.NNT.Signature.Object.Id,
                    IdSignature = xml.DSCKS.NNT.Signature.Id,
                    InvoiceType = VnisType._6TNCN.GetHashCode(),
                    Xml = _signService.NewObjToXml(xml)
                });
            }

            return result;
        }

        private TBaoPITDeductionDocumentErrorModel GetInvoiceErrorXmlModel(List<PITDeductionDocumentErrorEntity> invoiceErrors)
        {
            return PITDocumentErrorXmlModel.GetXmlModel(invoiceErrors, _appFactory.CurrentTenant.FullNameVi, _appFactory.CurrentTenant.TaxCode);
        }
        #endregion
    }
}
