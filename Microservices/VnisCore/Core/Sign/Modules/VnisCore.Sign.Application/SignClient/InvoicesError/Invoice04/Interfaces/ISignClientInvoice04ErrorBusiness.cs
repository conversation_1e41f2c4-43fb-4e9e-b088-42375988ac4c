using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Sign.Application.SignClient.InvoicesError.Common;

namespace VnisCore.Sign.Application.SignClient.InvoicesError.Invoice04.Interfaces
{
    public interface ISignClientInvoice04ErrorBusiness
    {
        Task<List<SignClientInvoiceErrorXmlModel>> GetInvoice04ErrorXmlAsync(string serialNumber, string subjectName);

        Task SaveInvoice04ErrorAsync(SignClientInvoiceErrorXmlModel command);
    }
}
