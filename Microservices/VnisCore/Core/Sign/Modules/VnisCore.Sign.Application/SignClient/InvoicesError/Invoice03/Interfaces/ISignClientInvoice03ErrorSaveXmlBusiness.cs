using System;
using System.Threading.Tasks;

using VnisCore.Sign.Application.SignClient.InvoicesError.Common;

namespace VnisCore.Sign.Application.SignClient.InvoicesError.Invoice03.Interfaces
{
    public interface ISignClientInvoice03ErrorSaveXmlBusiness
    {
        Task SaveInvoice03ErrorXmlAsync(Guid tenantId, Guid userId, string sellerTaxCode, string userFullName, SignClientInvoiceErrorXmlModel command);
    }
}
