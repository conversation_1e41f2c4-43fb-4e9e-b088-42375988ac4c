using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Sign.Application.SignClient.InvoicesError.Common;

namespace VnisCore.Sign.Application.SignClient.InvoicesError.Invoice02.Interfaces
{
    public interface ISignClientInvoice02ErrorBusiness
    {
        Task<List<SignClientInvoiceErrorXmlModel>> GetInvoice02ErrorXmlAsync(string serialNumber, string subjectName);

        Task SaveInvoice02ErrorAsync(SignClientInvoiceErrorXmlModel command);
    }
}
