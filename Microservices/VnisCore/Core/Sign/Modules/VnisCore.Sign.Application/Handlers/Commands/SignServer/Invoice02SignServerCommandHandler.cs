//using Core;
//using Core.EventBus.Distributed;
//using Core.Localization.Resources.AbpLocalization;
//using Core.Shared.Constants;
//using Core.Shared.Extensions;
//using Core.Shared.Factory;
//using MediatR;
//using Microsoft.Extensions.Localization;
//using System;
//using System.Linq;
//using System.Threading;
//using System.Threading.Tasks;
//using VnisCore.Sign.Application.Models;
//using VnisCore.Sign.Application.Models.Requests.Commands;
//using VnisCore.Sign.Application.RabbitMqEventBus.MessageEventData.Invoice02;
//using VnisCore.Sign.Application.Repositories.Invoice02;

//namespace Einvoice.Module.Invoice02SignServer.Handlers.Commands.SignServer
//{
//    public class Invoice02SignServerCommandHandler : AsyncRequestHandler<Invoice02SignServerRequestModel>
//    {
//        private readonly IServiceProvider _serviceProvider;
//        private readonly IAppFactory _appFactory;
//        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

//        public Invoice02SignServerCommandHandler(
//            IServiceProvider serviceProvider,
//            IStringLocalizer<CoreLocalizationResource> localizer,
//            IAppFactory appFactory)
//        {
//            _serviceProvider = serviceProvider;
//            _appFactory = appFactory;
//            _localizer = localizer;
//        }

//        protected override async Task Handle(Invoice02SignServerRequestModel request, CancellationToken cancellationToken)
//        {
//            var tenantId = _appFactory.CurrentTenant.Id.Value;
//            var userId = _appFactory.CurrentUser.Id.Value;
//            var userFullName = _appFactory.CurrentUser.Name;
//            var userName = _appFactory.CurrentUser.UserName;
//            if (request.InvoiceIds == null && !request.InvoiceIds.Any())
//                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

//            var repoInvoiceHeader = _appFactory.GetServiceDependency<IInvoice02HeaderRepository>();
//            var invoices = await repoInvoiceHeader.GetInvoiceHeadersRawAsync(tenantId, request.InvoiceIds);

//            if (!invoices.Any())
//                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmpty"]);

//            var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
//            foreach (var invoice in invoices)
//            {
//                //ToDo: Thêm check cấu hình tự động ký
//                if (invoice.SignStatus > SignStatus.ChoKy.GetHashCode()
//                    || invoice.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()
//                    || invoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode())
//                    continue;

//                await distributedEventBus.PublishAsync(new SignInvoice02EventSendData(new SignInvoiceRequestModel
//                {
//                    TenantId = tenantId,
//                    UserId = userId,
//                    UserFullName = userFullName,
//                    InvoiceHeaderId = invoice.Id,
//                }));

//                //Insert Log
//                await distributedEventBus.PublishAsync(new Invoice02LogEventSendData(new Invoice02LogModel
//                {
//                    InvoiceHeaderId = invoice.Id,
//                    TenantId = invoice.TenantId,
//                    UserId = userId,
//                    UserName = userName,
//                    InvoiceType = EnumExtension.ToEnum<VnisType>(invoice.TemplateNo).GetHashCode(),
//                    Action = ActionLogInvoice.Sign.GetHashCode(),
//                    Partition = long.Parse(invoice.InvoiceDate.ToString("yyyyMMddHHmm")),
//                    ActionTime = DateTime.Now
//                }));
//            }
//        }
//    }
//}
