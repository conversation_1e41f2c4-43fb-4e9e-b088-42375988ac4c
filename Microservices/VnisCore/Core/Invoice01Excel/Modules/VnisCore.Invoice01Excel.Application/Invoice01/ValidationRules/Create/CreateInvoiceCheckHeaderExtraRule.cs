using Core.Shared.Validations;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice01Excel.Application.Invoice01.Dto;
using Core.Shared.Factory;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;
using Core.Dto.Shared;
using System.Collections.Generic;
using Core.Shared.Constants;

namespace VnisCore.Invoice01Excel.Application.Invoice01.ValidationRule.Create
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class CreateInvoiceCheckHeaderExtraRule : IValidationRuleAsync<CreateInvoice01HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        //private readonly IRepository<Invoice01HeaderFieldEntity, long> _repoInvoice01DetailField;

        public CreateInvoiceCheckHeaderExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext)
            //IRepository<Invoice01HeaderFieldEntity, long> repoInvoice01DetailField)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            //_repoInvoice01DetailField = repoInvoice01DetailField;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice01HeaderDto input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice01GetDataValidDto>>("DataValidate");
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            //var headerFields = _repoInvoice01DetailField.Where(x => x.TenantId == tenantId)
            //                                            .Select(x => new { x.FieldName, x.DisplayName })
            //                                            .ToDictionary(x => x.FieldName, x => x.DisplayName);

            //var headerFieldNames = headerFields.Select(x => x.Key).ToList();

            var headerFieldNames = dataValidate.Where(x => x.Type == ValidateDataType.HeaderField.GetHashCode()).Select(x => x.FieldName).ToList();

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.HeaderExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
