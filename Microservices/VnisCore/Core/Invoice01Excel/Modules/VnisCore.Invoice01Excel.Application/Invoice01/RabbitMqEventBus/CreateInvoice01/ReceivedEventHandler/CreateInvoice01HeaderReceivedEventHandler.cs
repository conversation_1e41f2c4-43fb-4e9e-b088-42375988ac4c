//using System.Threading.Tasks;
//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using VnisCore.Invoice01Excel.Application.Factories.Services;
//using VnisCore.Invoice01Excel.Application.Invoice01.RabbitMqEventBus.CreateInvoice01.MessageEventData;

//namespace VnisCore.Invoice01Excel.Application.Invoice01.RabbitMqEventBus.CreateInvoice01.ReceivedEventHandler
//{
//    public class CreateInvoice01HeaderReceivedEventHandler : IDistributedEventHandler<CreateInvoice01HeaderEventResultData>, ITransientDependency
//    {
//        public CreateInvoice01HeaderReceivedEventHandler()
//        {
//        }

//        public async Task HandleEventAsync(CreateInvoice01HeaderEventResultData eventData)
//        {

//            //var s = eventData.ErrorMessages;
//            //var b = 1;
//            // logic get result
//            // returl signalr
//        }
//    }
//}