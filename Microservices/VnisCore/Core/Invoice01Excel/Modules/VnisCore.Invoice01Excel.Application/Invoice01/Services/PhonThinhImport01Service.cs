using Core;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Invoice01Excel.Application.Factories.Services;
using VnisCore.Invoice01Excel.Application.Invoice01.Dto;
using VnisCore.Invoice01Excel.Application.Invoice01.Models;
using static VnisCore.Invoice01Excel.Application.Invoice01.Dto.CreateInvoice01HeaderDto;

namespace VnisCore.Invoice01Excel.Application.Invoice01.Services
{
    public class PhonThinhImport01Service : BaseImport01Service, IImport01Service
    {
        private readonly ILogger<PhonThinhImport01Service> _logger;
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> _invoiceService;

        public PhonThinhImport01Service(
            ILogger<PhonThinhImport01Service> logger,
            IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IDistributedEventBus distributedEventBus,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IInvoiceService<Invoice01HeaderEntity, Invoice01HeaderFieldEntity, Invoice01DetailFieldEntity> invoiceService,
            IVnisCoreMongoInvoice01BatchIdRepository mongoInvoice01BatchIdRepository,
            IConfiguration configuration,
            IInvoice01Service invoice01Service,
            IVnisCoreMongoInvoice01ErpIdRepository mongoInvoice01ErpIdRepository)
            : base(logger, appFactory, serviceProvider, distributedEventBus, localizer, invoiceService, mongoInvoice01BatchIdRepository, configuration, invoice01Service, mongoInvoice01ErpIdRepository)
        {
            _invoiceService = invoiceService;
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _localizer = localizer;
        }

        public override async Task<List<ImportInvoice01Model>> ReadExcelAsync(Guid tenantId, ExcelPackage package, Dictionary<string, string> parameters)
        {
            try
            {
                var worksheet = package.Workbook.Worksheets[0];
                if (worksheet?.Dimension == null || worksheet.Dimension.End.Row < 4)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Customer.Import.EmptyDataImport"]);

                var paymentMethods = new List<string>();
                var repoSetting = _serviceProvider.GetService<ISettingService>();
                var setting = await repoSetting.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());
                if (setting == null || string.IsNullOrEmpty(setting.Value))
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PaymentMethodNotFound"]);

                paymentMethods = setting.Value.Split(";").ToList();
                var repoInvoice01 = _appFactory.Repository<Invoice01HeaderEntity, long>();
                var repoInvoice01ErpId = _appFactory.Repository<Invoice01ErpIdEntity, long>();
                var repoProduct = _appFactory.Repository<ProductEntity, long>();
                var duplicateErpIds = new List<string>();
                var templateNo = "TemplateNo";
                var serialNo = "SerialNo";
                var exchangeRate = 1;
                var currency = "VND";
                var productCode = "EXCEL";
                var paymentMethod = "TM/CK";

                if (parameters == null || !parameters.ContainsKey(templateNo) || !parameters.ContainsKey(serialNo))
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.ConfigurationIncorrect"]);


                var repoCustomer = _appFactory.Repository<CustomerEntity, long>();
                var customers = await repoCustomer.Where(x => x.TenantId == tenantId).ToListAsync();
                if (customers.Count == 0)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.CustomerNotFound"]);
                var templateNO = parameters.Where(x => x.Key == templateNo).Select(x => x.Value).FirstOrDefault();
                var serialNO = parameters.Where(x => x.Key == serialNo).Select(x => x.Value).FirstOrDefault();
                if (string.IsNullOrEmpty(templateNO))
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.TemplateNoIsNull"]);

                if (string.IsNullOrEmpty(serialNO))
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.SerialNoIsNull"]);

                var invoices = new List<ImportInvoice01Model>();
                var idErps = new List<string>();
                // Regex Cu
                //var regexCode = new Regex("^[a-zA-Z0-9][a-zA-Z0-9-_/.@]{0,48}[a-zA-Z0-9]{1,49}$|^[a-zA-Z0-9]{1}$");
                // Regex moi
                var regexCode = new Regex("^[a-zA-Z0-9-/_][a-zA-Z0-9-_/.@]{0,48}[a-zA-Z0-9-/_]{1,49}$|^[a-zA-Z0-9-/_]{1}$");
                var customerIdExcels = new Dictionary<string, string>();
                var fromCurrency = await _appFactory.Repository<CurrencyEntity, long>().FirstOrDefaultAsync(x => x.TenantId == tenantId && x.IsDefault);

                _logger.LogDebug($"Số dòng excel: {worksheet.Dimension.End.Row}");
                for (var i = 4; i <= worksheet.Dimension.End.Row; i++)
                {
                    var idErp = worksheet.Cells[i, 1].GetValue<string>()?.Trim();
                    var invoiceDate = worksheet.Cells[i, 2].GetValue<string>()?.Trim();
                    var detailNote = worksheet.Cells[i, 3].GetValue<string>()?.Trim();
                    var detailUnitName = worksheet.Cells[i, 4].GetValue<string>()?.Trim();
                    var detailVatPercent = worksheet.Cells[i, 5].GetValue<string>()?.Trim();
                    var detailQuantity = worksheet.Cells[i, 6].GetValue<string>()?.Trim();
                    var detailUnitPrice = worksheet.Cells[i, 7].GetValue<string>()?.Trim();
                    var detailAmount = worksheet.Cells[i, 8].GetValue<string>()?.Trim();
                    var buyerCode = worksheet.Cells[i, 9].GetValue<string>()?.Trim();
                    var buyerFullName = worksheet.Cells[i, 10].GetValue<string>()?.Trim();
                    var buyerLegalName = worksheet.Cells[i, 11].GetValue<string>()?.Trim();

                    //validate
                    var customer = customers.FirstOrDefault(x => x.CustomerCode == buyerCode);

                    if (string.IsNullOrEmpty(idErp))
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.InvoiceNoIsNull", new string[] { $"A{i}" }]);

                    if (idErp.Length > 20)
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.InvoiceNoMaxLength", new string[] { $"A{i}" }]);

                    //if (await repoInvoice01ErpId.AnyAsync(x => x.TenantId == tenantId && x.ErpId == idErp))
                    //    //throw new UserFriendlyException($"Số chứng từ tại C{i} đã tồn tại trong hệ thống");
                    //    throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice01.PhonThinhImportService.ErpIdIsExist", new string[] { $"A{i}" }]);

                    if (string.IsNullOrEmpty(invoiceDate))
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.InvoiceDateIsNull", new string[] { $"A{i}" }]);

                    if (invoices.Count() > 0 && invoices.Last().ErpId != idErp && idErps.Any(x => x == idErp))
                        throw new UserFriendlyException("Vnis.BE.Invoice01.PhonThinhImportService.DuplicateErpId");
                    //throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice01.ImportService.DuplicateErpId", new string[] { i.ToString() }]);

                    idErps.Add(idErp);

                    var dateInvoice = _invoiceService.GetDateTimeExcel(invoiceDate);
                    if (!dateInvoice.HasValue)
                        //    throw new UserFriendlyException($"Ngày hóa đơn tại B{i} không đúng kiểu dữ liệu DateTime như định dạng ô B3");
                        throw new UserFriendlyException(_localizer[$"Vnis.BE.Invoice01.PhonThinhImportService.InvoiceDateFormatIncorrect", new string[] { $"B{i}" }]);

                    if (dateInvoice.Value.Date > DateTime.Now.Date)
                        //throw new UserFriendlyException($"Ngày hóa đơn tại B{i} không được lớn hơn ngày hiện tại");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.InvoiceDateIncorrect", new string[] { $"B{i}" }]);

                    // nội dung hàng hóa 
                    if (!string.IsNullOrEmpty(detailNote))
                        if (detailNote.Length > 250)
                            //throw new UserFriendlyException($"Nội dung hàng hóa tại C{i} không được dài quá 250 kí tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.DetailNoteMaxLength", new string[] { $"C{i}" }]);

                    // đơn vị tính - not null
                    if (string.IsNullOrEmpty(detailUnitName))
                        //throw new UserFriendlyException($"Đơn vị tính tại D{i} không được để trống");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.UnitNameIsNull", new string[] { $"D{i}" }]);

                    if (detailUnitName.Length > 50)
                        //throw new UserFriendlyException($"Đơn vị tính tại D{i} không được dài quá 50 kí tự");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.UnitNameMaxLength", new string[] { $"D{i}" }]);

                    //% thuế - not null
                    if (string.IsNullOrEmpty(detailVatPercent))
                        //throw new UserFriendlyException($"Thuế suất tại E{i} không được để trống");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.VatPercentIsNull", new string[] { $"E{i}" }]);

                    if (!decimal.TryParse(detailVatPercent, out decimal numberVatPercent))
                        //throw new UserFriendlyException($"Thuế suất tại E{i} không đúng định dạng số nguyên Int");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.VatPercentFormatIntIncorrect", new string[] { $"E{i}" }]);

                    if (!numberVatPercent.IsInRange(2, 2, true))
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.VatPercentInvalid", $"E{i}", $"-{StaticData.MaxVatPercent}", StaticData.MaxVatPercent]);

                    decimal quantityOut = 0;
                    if (!string.IsNullOrEmpty(detailQuantity))
                    {
                        if (!decimal.TryParse(detailQuantity, out quantityOut))
                            //throw new UserFriendlyException($"Số lượng tại F{i} không đúng định dạng số");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.QuantityNumberFormatIncorrect", new string[] { $"F{i}" }]);

                        if (!quantityOut.IsInRange(15, 6, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.QuantityInvalid", $"F{i}", $"-{StaticData.MaxQuantity}", StaticData.MaxQuantity]);
                    }

                    // đơn giá
                    decimal unitPriceOut = 0;
                    if (!string.IsNullOrEmpty(detailUnitPrice))
                    {
                        if (!decimal.TryParse(detailUnitPrice, out unitPriceOut))
                            //throw new UserFriendlyException($"Đơn giá tại G{i} không đúng định dạng số");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.UnitPriceNumberFormatIncorrect", new string[] { $"G{i}" }]);

                        if (!unitPriceOut.IsInRange(15, 6, true))
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.UnitPriceInvalid", $"G{i}", $"-{StaticData.MaxUnitPrice}", StaticData.MaxUnitPrice] + $". Giá trị hiên tại là: {unitPriceOut}");
                    }

                    // thành tiền trước thuế - not null
                    if (string.IsNullOrEmpty(detailAmount))
                        //throw new UserFriendlyException($"Thành tiền trước thuế tại H{i} không được để trống");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.AmountBeforeTaxIsNull", new string[] { $"H{i}" }]);

                    if (!decimal.TryParse(detailAmount, out decimal amountOut))
                        //throw new UserFriendlyException($"Thành tiền trước thuế tại H{i} không đúng định dạng số");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.AmountBeforeTaxNumberFormatIncorrect", new string[] { $"H{i}" }] + $". Giá trị hiên tại là: {amountOut}");

                    if (!amountOut.IsInRange(15, 6, true))
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.AmountBeforeTaxInvalid", $"H{i}", $"-{StaticData.MaxAmount}", StaticData.MaxAmount] + $". Giá trị hiên tại là: {amountOut}");

                    if (string.IsNullOrEmpty(buyerCode))
                        //throw new UserFriendlyException($"Mã số người mua tại I{i} không được đê trống");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.BuyerCodeIsNull", new string[] { $"I{i}" }]);

                    if (customer == null)
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.BuyerCodeNotFound", new string[] { $"I{i}" }]);

                    if (buyerCode.Length > 50)
                        //throw new UserFriendlyException($"Mã số người mua tại I{i} không được quá 50 kí tự");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.BuyerCodeMaxLength", new string[] { $"I{i}" }]);


                    // tên đơn vị mua hàng - not null
                    if (string.IsNullOrEmpty(buyerFullName))
                        //throw new UserFriendlyException($"Tên đơn vị mua hàng tại J{i} không được để trống");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.BuyerNameIsNull", new string[] { $"J{i}" }]);

                    if (buyerFullName.Length > 250)
                        //throw new UserFriendlyException($"Tên đơn vị mua hàng tại J{i} dài tối đa 250 ký tự");
                        throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.BuyerNameMaxLength", new string[] { $"J{i}" }]);

                    if (customer.FullName != buyerFullName)
                        customer.FullName = buyerFullName;

                    // tên người mua
                    if (!string.IsNullOrEmpty(buyerLegalName))
                    {
                        if (buyerLegalName.Length > 100)
                            //throw new UserFriendlyException($"Tên người mua tại K{i} dài tối đa 250 ký tự");
                            throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01.PhonThinhImportService.BuyerLegalNameMaxLength", new string[] { i.ToString() }]);
                    }

                    if (customer.LegalName != buyerLegalName)
                        customer.LegalName = buyerLegalName;

                    var invoice = new ImportInvoice01Model
                    {
                        ErpId = idErp,
                        InvoiceDate = dateInvoice.Value,
                        UnitName = detailUnitName,
                        VatPercent = numberVatPercent,
                        Quantity = quantityOut,
                        UnitPrice = unitPriceOut,
                        Amount = amountOut,
                        BuyerCode = buyerCode,
                        BuyerLegalName = buyerLegalName,
                        BuyerFullName = buyerFullName,
                        ExchangeRate = exchangeRate,
                        Currency = currency,
                        SerialNo = serialNO,
                        TemplateNo = short.Parse(templateNO),
                        ProductCode = productCode,
                        BuyerAddressLine = customer.Address,
                        ProductName = detailNote,
                        PaymentMethod = paymentMethod,
                    };
                    invoices.Add(invoice);
                }
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
                return invoices;
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }
        }


        public override async Task<List<CreateInvoice01HeaderDto>> ToEntitiesAsync(Guid tenantId, Guid createBy, string userFullName, string userName, string cashierCode, List<ImportInvoice01Model> excelModels)
        {

            //thuế
            var taxService = _serviceProvider.GetService<ITaxService>();
            var existedTaxes = await taxService.GetTaxesAsync(tenantId);
            var taxes = excelModels.GroupBy(x => x.VatPercent).Select(x => x.FirstOrDefault());
            foreach (var item in taxes)
            {
                if (!existedTaxes.ContainsKey(item.VatPercent))
                    throw new UserFriendlyException($"Không tồn tại thuế suất {item.VatPercent}");
            }

            //tim cac mau/loai hoa don trong csdl mà được tạo
            var commonInvoice01Service = _serviceProvider.GetService<ICommonInvoice01Service>();
            var invoiceTemplates = (await commonInvoice01Service.GetRegisterAvailabilities(tenantId, createBy, 1))
                                    .ToDictionary(x => $"{x.TemplateNo}|{x.SerialNo}", x => x);


            var invoiceHeaders = new List<CreateInvoice01HeaderDto>();
            var tempInvoiceHeaders = excelModels.GroupBy(x => x.ErpId);

            foreach (var item in tempInvoiceHeaders)
            {
                //loc theo tung mau hoa don
                var invoiceHeader = new CreateInvoice01HeaderDto();
                var groupInvoice = item.ToList();
                var firstKey = groupInvoice.FirstOrDefault();

                if (firstKey == null)
                    continue;

                if (!invoiceTemplates.ContainsKey($"{firstKey.TemplateNo}|{firstKey.SerialNo}"))
                    throw new UserFriendlyException("Không tìm thấy mẫu hóa đơn để TẠO. Vui lòng kiểm tra thông tin mẫu hóa đơn hoặc phân quyền tạo hóa đơn");


                var invoiceTemplate = invoiceTemplates[$"{firstKey.TemplateNo}|{firstKey.SerialNo}"];

                AddInvoiceHeader(tenantId, createBy, userFullName, userName, cashierCode, firstKey, invoiceHeader);

                //add header extra
                var tenantInfo = _appFactory.CurrentTenant;
                var invoiceHeaderExtras = new List<CreateInvoice01HeaderExtraModel>();
                if (firstKey.MetadataHeaderExtra != null)
                {
                    foreach (var field in firstKey.MetadataHeaderExtra)
                    {
                        invoiceHeaderExtras.Add(new CreateInvoice01HeaderExtraModel
                        {
                            FieldValue = field.Value,
                            FieldName = field.Key,
                        });
                    }
                }

                var invoiceDetails = new List<CreateInvoice01DetailModel>();
                var invoiceTaxBreakDown = new List<CreateInvoice01TaxBreakdownModel>();

                decimal totalAmount = 0;
                decimal totalPaymentAmount = 0;
                decimal totalVatAmount = 0;

                var index = 1;
                foreach (var detailExcel in groupInvoice)
                {
                    var detail = ConvertToInvoiceDetail(detailExcel);
                    detail.Index = index;

                    //tự tính tiền thuế, paymentAmount
                    if (detail.VatPercent > 0)
                        detail.VatAmount = detail.Amount * detail.VatPercent / 100;
                    detail.PaymentAmount = detail.Amount + detail.VatAmount;

                    invoiceDetails.Add(detail);
                    index++;
                    if (!invoiceTaxBreakDown.Any(x => x.VatPercent == detail.VatPercent))
                    {
                        invoiceTaxBreakDown.Add(new CreateInvoice01TaxBreakdownModel
                        {
                            VatAmount = detail.VatAmount,
                            VatPercent = detail.VatPercent,
                            VatAmountBackUp = 0,
                        });
                    }
                    else
                    {
                        var taxBreakDown = invoiceTaxBreakDown.First(x => x.VatPercent == detail.VatPercent);
                        taxBreakDown.VatAmount += detail.VatAmount;
                    }
                    totalAmount += detail.Amount;
                    totalPaymentAmount += detail.PaymentAmount;
                    totalVatAmount += detail.VatAmount;
                }

                invoiceHeader.TotalPaymentAmount = totalPaymentAmount;
                invoiceHeader.TotalAmount = totalAmount;
                invoiceHeader.TotalVatAmount = totalVatAmount;

                invoiceHeader.InvoiceDetails = invoiceDetails;

                invoiceHeader.InvoiceTaxBreakdowns = GetTaxBreakdowns(invoiceHeader, groupInvoice);
                invoiceHeader.DiscountType = (short)GetAutoDiscountType(invoiceHeader).GetHashCode(); 
                ValidateTotalMoneyAsync(invoiceHeader);

                invoiceHeaders.Add(invoiceHeader);
            }

            return invoiceHeaders;
        }

    }
}
