using Core.Dto.Shared.Invoices.Invoice01;
using Core.Shared.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;

namespace VnisCore.Invoice01Excel.Application.Invoice01.Utils
{
    public class ReCaculateInvoiceUtil
    {
        public static List<Invoice01TaxBreakdownDto> GetTaxBreakDown(MongoInvoice01Entity input)
        {
            if(!input.InvoiceTaxBreakdowns.IsNullOrEmpty())
            {
                List<Invoice01TaxBreakdownDto> taxBreakdowns = input.InvoiceTaxBreakdowns;
                if (input.InvoiceTaxBreakdowns.Where(x => x.Amount == 0).Count() == input.InvoiceTaxBreakdowns.Count())
                {
                    var details = input.InvoiceDetails;
                    foreach (var detailByVatPercent in details.GroupBy(x => x.VatPercent))
                    {
                        if (input.TotalDiscountAmountBeforeTax != 0
                            && details.Where(detail => detail.DiscountAmountBeforeTax == 0).Count() == details.Count
                            && !details.Where(detail => detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any())
                        {
                            // Case: Loại chiết khấu = Chiết khấu tổng
                            //var vatAmount = detailByVatPercent.Where(x => x.ProductType != ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.VatAmount) - detailByVatPercent.Where(x => x.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.VatAmount) - input.TotalDiscountAmountBeforeTax * detailByVatPercent.Key / 100;
                            var amount = detailByVatPercent.Where(x => x.ProductType != ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.Amount) - detailByVatPercent.Where(x => x.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.Amount) - input.TotalDiscountAmountBeforeTax;

                            var currentTaxBreakDown = taxBreakdowns.Where(x => x.VatPercent == detailByVatPercent.Key).FirstOrDefault();
                            currentTaxBreakDown.Amount = amount;
                        }
                        else
                        {
                            // Case: Loại chiết khấu = Chiết khấu hàng hóa
                            // Case: Loại chiết khấu = Không chiết khấu
                            var vatAmount = detailByVatPercent.Where(x => x.ProductType != ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.VatAmount) - detailByVatPercent.Where(x => x.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.VatAmount);
                            var amount = detailByVatPercent.Where(x => x.ProductType != ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.Amount) - detailByVatPercent.Where(x => x.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Sum(x => x.Amount);

                            var currentTaxBreakDown = taxBreakdowns.Where(x => x.VatPercent == detailByVatPercent.Key).FirstOrDefault();
                            currentTaxBreakDown.Amount = amount;
                        }
                    }
                }
                else
                {
                    return input.InvoiceTaxBreakdowns;
                }
                return taxBreakdowns;
            }
            return new List<Invoice01TaxBreakdownDto>();
        }

        /// <summary>
        /// Xác định tự động loại chiết khấu
        /// </summary>
        public static DiscountType GetAutoDiscountType(MongoInvoice01Entity input)
        {
            if (input.TotalDiscountAmountBeforeTax != 0
                    && input.InvoiceDetails.Where(detail => detail.DiscountAmountBeforeTax == 0).Count() == input.InvoiceDetails.Count
                    && !input.InvoiceDetails.Where(detail => detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any())
            {
                return DiscountType.TongChietKhau;
            }
            if ((input.TotalDiscountAmountBeforeTax != 0
                && input.InvoiceDetails.Where(detail => detail.DiscountAmountBeforeTax != 0).Any() || input.InvoiceDetails.Where(detail => detail.ProductType == ProductType.ChietKhauThuongMai.GetHashCode()).Any()))
            {
                return DiscountType.ChietKhauHangHoa;
            }
            if (input.TotalDiscountAmountBeforeTax == 0
                && !input.InvoiceDetails.Where(detail => detail.DiscountAmountBeforeTax != 0).Any())
            {
                return DiscountType.KhongChietKhau;
            }
            return DiscountType.KhongChietKhau;
        }

    }
}
