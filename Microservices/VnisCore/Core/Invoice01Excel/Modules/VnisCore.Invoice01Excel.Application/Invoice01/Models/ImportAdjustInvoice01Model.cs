using System;

namespace VnisCore.Invoice01Excel.Application.Invoice01.Models
{
    public class ImportAdjustInvoice01Model
    {
        /// <summary>
        /// <PERSON><PERSON> thứ tự trong excel
        /// </summary>
        public int STT { get; set; }

        /// <summary>
        /// Loại chiết khấu
        /// </summary>
        public short DiscountType { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn Bị ĐC
        /// </summary>
        public string ReferenceSerialNo { get; set; }

        /// <summary>
        /// Mẫu số của hóa đơn Bị ĐC
        /// </summary>
        public int ReferenceInvoiceNo { get; set; }

        /// <summary>
        /// Ngay của hóa đơn Bị ĐC
        /// </summary>
        public DateTime ReferenceInvoiceDate { get; set; }

        /// <summary>
        /// So Bị ĐC
        /// </summary>
        public string ReferenceInvoiceNoValue { get; set; }

        /// <summary>
        /// So Bị ĐC
        /// </summary>
        public long ReferenceId { get; set; }

        /// <summary>
        /// <PERSON><PERSON> chu Bị ĐC
        /// </summary>
        public string ReferenceNote { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Loại tiền tệ
        /// </summary>
        public string Currency { get; set; }

        /// <summary>
        /// Tỷ giá
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Mã người mua
        /// </summary>
        public string BuyerCode { get; set; }

        /// <summary>
        /// Tên người mua
        /// </summary>
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Tên đơn vị mua hàng
        /// </summary>
        public string BuyerFullName { get; set; }

        /// <summary>
        /// MST người mua
        /// </summary>
        public string BuyerTaxCode { get; set; }
        
        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        public string BuyerAddressLine { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người mua
        /// </summary>
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Chi nhánh ngân hàng người mua
        /// </summary>
        public string BuyerBankName { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        public string BuyerPhone { get; set; }

        /// <summary>
        /// Số Fax người mua
        /// </summary>
        public string BuyerFax { get; set; }

        /// <summary>
        /// Mã số đơn vị có quan hệ với ngân sách
        /// </summary>
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Số CC/CCCD/Định danh
        /// </summary>
        public string BuyerIDNumber { get; set; }

        /// <summary>
        /// Số hộ chiếu/giấy tờ xuất/nhập cảnh
        /// </summary>
        public string BuyerPassportNumber { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế
        /// </summary>
        public decimal TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal TotalVatAmount { get; set; } // tổng tiền thuế

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; } // tổng thành tiền sau thuế

        #region Chi tiết hóa đơn
        /// <summary>
        /// STT
        /// </summary>
        public int? Index { get; set; }

        /// <summary>
        /// Tính chất hàng hóa
        /// </summary>
        public short? ProductType { get; set; }

        /// <summary>
        /// Mã sản phẩm
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// Tên hàng
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Nội dung hàng hóa
        /// </summary>
        public string DetailNote { get; set; }

        /// <summary>
        /// Đơn vị tính
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// Đơn giá
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        public decimal? DiscountPercent { get; set; }

        /// <summary>
        /// Tiền chiết khấu
        /// </summary>
        public decimal? DiscountAmount { get; set; } // tiền chiết khấu từng mặt hàng

        /// <summary>
        /// Thành tiền trước thuế
        /// </summary>
        public decimal? Amount { get; set; } // tiền hàng trước thuế từng mặt hàng

        /// <summary>
        /// Phần trăm thuế
        /// </summary>
        public decimal? VatPercent { get; set; }

        /// <summary>
        /// Tiền thuế
        /// </summary>
        public decimal? VatAmount { get; set; } // tiền thuế từng mặt hàng

        /// <summary>
        /// Tiền thanh toán
        /// </summary>
        public decimal? PaymentAmount { get; set; } // thành tiền từng mặt hàng

        /// <summary>
        /// Loại thuế suất
        /// </summary>
        public decimal? TaxBreakDownVatPerent { get; set; }

        /// <summary>
        /// Số tiền thuế theo loại thuế
        /// </summary>
        public decimal? TaxBreakDownAmount{ get; set; }

        /// <summary>
        /// Thành tiền trước thuế theo loại thuế suất
        /// </summary>
        public decimal? TaxBreakDownVatAmount { get; set; }

        #endregion

        #region Ghi chú in thể hiện
        /// <summary>
        /// Ghi chú điều chỉnh hóa đơn
        /// </summary>
        public string PrintNote { get; set; }

        /// <summary>
        /// Đơn vị tính
        /// </summary>
        public int? IsShow { get; set; }
        #endregion

    }
}
