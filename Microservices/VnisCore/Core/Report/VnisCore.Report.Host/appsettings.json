{"App": {"SelfUrl": "https://localhost:6011", "CorsOrigins": "http://localhost:6789", "RedirectAllowedUrls": "http://localhost:4200"}, "AppSelfUrl": "https://localhost:6011/", "ConnectionStrings": {"Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoiceauth50staging;Password=Vnis@12A", "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoice50staging;Password=Vnis@12A", "VnisCoreMongoDbAuditLogging": "***************************************************************************************"}, "Service": {"Name": "VnisCore.Report.Host", "Title": "VnisCore.Report.Host", "BaseUrl": "report", "AuthApiName": "VnisCore.Report.Host"}, "Redis": {"IsUsing": "true", "Configuration": "**************,allowAdmin=true"}, "Elasticsearch": {"Index": "dev.core50.invoice01", "Url": "http://**************:9200/"}, "AuthServer": {"Authority": "http://localhost:6868", "RequireHttpsMetadata": "false", "ApiName": "einvoice", "SwaggerClientId": "einvoice_Swagger", "SwaggerClientSecret": "Vnis@12A"}, "TvanConfiguration": {"ApiLogin": "https://tvan-api.vnpaytest.vn/v2/invoice-crawler/auth/token"}}