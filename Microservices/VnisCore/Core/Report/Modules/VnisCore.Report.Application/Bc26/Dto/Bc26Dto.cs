using Core.Shared.Attributes;
using Core.Shared.Constants;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using Core.Application.Dtos;
using static VnisCore.Report.Application.Bc26.Bc26Service;
using System.Text.Json.Serialization;

namespace VnisCore.Report.Application.Bc26.Dto
{
    public class Bc26Dto : EntityDto<long>
    {
        public new long Id { get; set; }

        [Required(ErrorMessage = "Chưa chọn loại báo cáo")]
        [EnumDataType(typeof(ReportType), ErrorMessage = "Loại báo cáo không đúng")]
        public ReportType Type { get; set; }

        [Required(ErrorMessage = "Chưa chọn kỳ báo cáo")]
        public int Index { get; set; } //Kỳ báo cáo
        [Required(ErrorMessage = "Chưa chọn năm báo cáo")]
        public int Year { get; set; } // Năm báo cáo

        public DateTime ReportDate { get; set; } // Ngày lập báo cáo, người dùng tự chọn
        public DateTime FromDate { get; set; } // Ngày đầu tiên của kỳ báo cáo
        public DateTime ToDate { get; set; } // Ngày cuối cùng của kỳ báo cáo
        public short ReportYear { get; set; } // Năm báo cáo
        public short ReportMonth { get; set; } // Tháng báo cáo
        public short ReportQuarter { get; set; } // Quý báo cáo
        public int TotalOfRegister { get; set; } // Tổng số tồn đầu kỳ, mua/phát hành trong kỳ
        public int TotalOfUsed { get; set; } // Tổng số sử dụng trong kỳ
        public int TotalOfCancel { get; set; } // Tổng số hủy bỏ trong kỳ
        public int TotalOfDelete { get; set; } // Tổng số hóa bỏ trong kỳ
        public int TotalOfUnused { get; set; } // Tổng số tồn cuối kỳ
        [MaxLength(250, ErrorMessage = "Tối đa 250 ký tự")]
        public string FullNameCreator { get; set; }

        [MaxLength(250, ErrorMessage = "Tối đa 250 ký tự")]
        public string Representative { get; set; } // Người đại diện pháp luật

        [JsonIgnore]
        public Guid TenantId { get; set; }

        public virtual ICollection<Bc26DetailDto> Bc26Details { get; set; }
    }

    public class Bc26DetailDto : EntityDto<long>
    {
        public new long Id { get; set; }

        //Hóa đơn
        [MaxLength(50, ErrorMessage = "Mã loại hóa đơn tối đa 50 ký tự")]
        public string InvoiceTypeCode { get; set; }

        public string InvoiceTypeName { get; set; }

        public long TemplateId { get; set; }

        [TemplateNo(ErrorMessage = "Định dạng ký hiệu mẫu hóa đơn không đúng")]
        public short TemplateNo { get; set; }

        [MaxLength(50, ErrorMessage = "Ký hiệu tối đa 50 ký tự")]
        [SerialNo(ErrorMessage = "Định dạng ký hiệu hóa đơn không đúng")]
        public string SerialNo { get; set; }

        //----------------------------Số tồn đầu kỳ, mua / phát hành trong kỳ----------------------------
        /// <summary>
        /// Tổng số tồn đầu kỳ và phát hành trong kỳ
        /// </summary>
        public int QuantityOfRegister { get; set; }

        /// <summary>
        /// Tồn đầu kỳ từ số
        /// </summary>
        public int StartNumberOfBeginBacklog { get; set; }

        /// <summary>
        /// Tồn đầu kỳ đến số
        /// </summary>
        public int EndNumberOfBeginBacklog { get; set; }

        /// <summary>
        /// Phát hành trong kỳ từ số
        /// </summary>
        public int StartNumberOfRegister { get; set; }

        /// <summary>
        /// Phát hành trong kỳ đến số
        /// </summary>
        public int EndNumberOfRegister { get; set; }

        //----------------------------Số sử dụng, xóa bỏ, hủy trong kỳ----------------------------
        /// <summary>
        /// Hủy bỏ, xóa bỏ, hủy bỏ trong kỳ từ số
        /// </summary>
        public int StartNumberUsedCancelDelete { get; set; }

        /// <summary>
        /// Hủy bỏ, xóa bỏ, hủy bỏ trong kỳ đến số
        /// </summary>
        public int EndNumberUsedCancelDelete { get; set; }

        /// <summary>
        /// Tổng số sử dụng, xóa bỏ, hủy bỏ trong kỳ
        /// </summary>
        public int TotalOfUsedCancelDelete { get; set; }

        /// <summary>
        /// Số lượng sử dụng trong kỳ
        /// </summary>
        public int QuantityOfUsed { get; set; }

        /// <summary>
        /// Số lượng xóa bỏ trong kỳ
        /// </summary>
        public int QuantityOfDelete { get; set; }

        /// <summary>
        /// Các số bị xóa bỏ trong kỳ vd: 7;10-20
        /// </summary>
        public string DeleteInvoices { get; set; }

        /// <summary>
        /// Số lượng hủy bỏ trong kỳ
        /// </summary>
        public int QuantityOfCancel { get; set; }

        /// <summary>
        /// Các số bị hủy bỏ trong kỳ vd: 21;2-3
        /// </summary>
        public string CancelInvoices { get; set; }

        //----------------------------Tồn cuối kỳ----------------------------
        /// <summary>
        /// Tồn cuối kỳ từ số
        /// </summary>
        public int StartNumberOfEndBacklog { get; set; }

        /// <summary>
        /// Tồn cuối kỳ đến số
        /// </summary>
        public int EndNumberOfEndBacklog { get; set; }

        /// <summary>
        /// Số lượng tồn cuối kỳ
        /// </summary>
        public int QuantityOfEndBacklog { get; set; }

        //----------------------------Other----------------------------
        /// <summary>
        /// Phân biệt đâu là dữ liệu khách hàng tự nhập thêm và đâu là dữ liệu lấy từ DB về
        /// </summary>
        public bool IsManually { get; set; }

        //FK
        public long Bc26Id { get; set; }
    }

    public class InvoiceBc26Model
    {
        public long Id { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public string InvoiceNo { get; set; }
        public int Number { get; set; }
        public long InvoiceTemplateId { get; set; }
        public DateTime InvoiceDate { get; set; }
        public InvoiceStatus InvoiceStatus { get; set; }

        public Guid TenantId { get; set; }
        public DateTime CreatedTime { get; set; }
    }
}
