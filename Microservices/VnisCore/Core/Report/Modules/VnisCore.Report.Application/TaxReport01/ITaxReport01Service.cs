using Core.Shared.Services;
using VnisCore.Report.Application.TaxReport01.Dto;

namespace VnisCore.Report.Application.TaxReport01
{
    public interface ITaxReport01Service :
         ICrudAppService< //Defines CRUD methods
             TaxReport01Dto, //Used to show books
             long, //Primary key of the Store entity
             TaxReport01PagedRequestDto, //Used for paging/sorting on getting a list of books
             TaxReport01Dto, //Used to create a new Store
             TaxReport01Dto> //Used to update a Store
    {

    }
}
