using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Shared.Services;
using Core.Tvan.Constants;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Report.Application.TaxReport01.Dto;
using VnisCore.Report.Application.TaxReport01.Models;

namespace VnisCore.Report.Application.TaxReport01.Abstrations
{
    public abstract class BaseTaxReport01BusinessService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<TaxReport01HeaderEntity, long> _repoTaxReport01;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ISettingService _settingService;

        public BaseTaxReport01BusinessService(IRepository<TaxReport01HeaderEntity, long> repoTaxReport01,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            ISettingService settingService)
        {
            _appFactory = appFactory;
            _repoTaxReport01 = repoTaxReport01;
            _localizer = localizer;
            _settingService = settingService;
        }

        #region old
        //public async Task<TaxReport01Dto> GetByIdAsync(Guid tenantId, TaxReport01InfoPagedRequestDto input)
        //{
        //    var taxReportHeader = await _repoTaxReport01.Where(x => x.Id == input.Id && x.TenantId == tenantId).Include(x => x.TaxReport01DetailMappings).FirstOrDefaultAsync();
        //    if (taxReportHeader == null)
        //        throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.NotFound"]);

        //    var type = ReportType.Month;
        //    var index = taxReportHeader.ReportMonth;
        //    if (taxReportHeader.ReportQuarter > 0)
        //    {
        //        type = ReportType.Quarter;
        //        index = taxReportHeader.ReportQuarter;
        //    }

        //    var (key, value) = GetCurrentRange(type, index, taxReportHeader.ReportYear);

        //    //var fromDate = long.Parse(key.ToString("yyyyMMdd0000"));
        //    //var toDate = long.Parse(value.AddDays(1).ToString("yyyyMMdd0000"));

        //    string fromDate = key.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));
        //    string toDate = value.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"));

        //    //lấy dữ liệu báo cáo

        //    var resultData = await GetTaxReport01Async(taxReportHeader.Id, tenantId, input, fromDate, toDate);

        //    var itemStart = taxReportHeader.TaxReport01DetailMappings.OrderBy(x => x.Index).FirstOrDefault();

        //    TaxReport01Dto response = _appFactory.ObjectMapper.Map<TaxReport01HeaderEntity, TaxReport01Dto>(taxReportHeader);
        //    response.Year = taxReportHeader.ReportYear;
        //    response.Index = index;
        //    response.StartIndex = itemStart != null ? itemStart.Index - 1 : 0;

        //    response.TaxReportDetails = resultData.ToList();

        //    return response;
        //}
        #endregion

        public async Task<TaxReport01Dto> GetByIdRefactorAsync(Guid tenantId, TaxReport01InfoPagedRequestDto input)
        {
            try
            {
                var taxReportHeader = await _repoTaxReport01.Where(x => x.Id == input.Id && x.TenantId == tenantId).Include(x => x.TaxReport01DetailMappings).FirstOrDefaultAsync();
                if (taxReportHeader == null)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.NotFound"]);

                var detailMappingList = taxReportHeader.TaxReport01DetailMappings;
                var groupDetailMapping = detailMappingList.GroupBy(x => x.InvoiceType).OrderBy(x => x.Key);
                QueryInvoiceModel modelQuery = new QueryInvoiceModel();
                modelQuery.SkipCount = input.SkipCount;
                modelQuery.MaxResultCount = input.MaxResultCount;
                modelQuery.ReportHeaderId = taxReportHeader.Id;
                modelQuery.TenantId = tenantId;

                var detailData = new List<TaxReport01DetailDto>();
                foreach (var group in groupDetailMapping)
                {
                    var list = group.OrderBy(x => x.Index).ToList();
                    var firstMapping = list.FirstOrDefault();
                    var lastMapping = list.LastOrDefault();
                    //var firstId = firstMapping.FromId;
                    //var lastId = lastMapping.ToId;
                    foreach (var item in list)
                    {
                        if (group.Key == 1)
                        {
                            //modelQuery.FromIdInvoice01 = firstId;
                            //modelQuery.ToIdInvoice01 = lastId;
                            if (modelQuery.IdsInvoice01.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice01 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice01 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == 2)
                        {
                            if (modelQuery.IdsInvoice02.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice02 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice02 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == 3)
                        {
                            if (modelQuery.IdsInvoice03.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice03 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice03 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == 4)
                        {
                            if (modelQuery.IdsInvoice04.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice04 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice04 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == 5)
                        {
                            if (modelQuery.IdsInvoice05.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice05 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice05 += "," + item.InvoiceIds;
                            }
                        }
                    }

                }

                #region Cập nhật lại cách lấy dữ liệu hd từ ds các Id
                List<TaxReport01DetailDto> resultData = new List<TaxReport01DetailDto>();
                var detailMapping = taxReportHeader.TaxReport01DetailMappings;
                if (!modelQuery.IdsInvoice01.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == 1);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice01(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice02.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == 2);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice02(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice03.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == 3);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice03(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice04.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == 4);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice04(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice05.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == 5);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice05(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }

                // Nhóm dữ liệu báo cáo 
                var groupInvoice = resultData.GroupBy(invoice => new
                {
                    invoice.InvoiceHeaderId,
                    invoice.TemplateNo,
                    invoice.SerialNo,
                    invoice.ReceiverName,
                    invoice.BuyerFullName,
                    invoice.BuyerName,
                    invoice.BuyerTaxCode,
                    invoice.InvoiceNo,
                    invoice.Number,
                    invoice.ExchangeRate,
                    invoice.InvoiceDate,
                    invoice.TotalAmount,
                    invoice.TotalVatAmount,
                    invoice.TotalPaymentAmount,
                    invoice.Status,
                    invoice.InvoiceStatus,
                    invoice.InvoiceTemplateId,

                    invoice.InvoiceType,
                    invoice.ProductName,
                    invoice.VatPercent,
                    invoice.Note,
                    invoice.ExtraProperties,
                    invoice.BudgetUnitCode
                }).Select(invoice => new TaxReport01DetailGroupDto()
                {
                    InvoiceHeaderId = invoice.Key.InvoiceHeaderId,
                    TemplateNo = invoice.Key.TemplateNo,
                    SerialNo = invoice.Key.SerialNo,
                    ReceiverName = invoice.Key.ReceiverName,
                    BuyerFullName = invoice.Key.BuyerFullName,
                    BuyerName = invoice.Key.BuyerName,
                    BuyerTaxCode = invoice.Key.BuyerTaxCode,
                    InvoiceNo = invoice.Key.InvoiceNo,
                    Number = invoice.Key.Number,
                    ExchangeRate = invoice.Key.ExchangeRate,
                    InvoiceDate = invoice.Key.InvoiceDate,
                    TotalAmount = invoice.Key.TotalAmount,
                    TotalVatAmount = invoice.Key.TotalVatAmount,
                    TotalPaymentAmount = invoice.Key.TotalPaymentAmount,
                    Status = invoice.Key.Status,
                    InvoiceStatus = invoice.Key.InvoiceStatus,
                    InvoiceTemplateId = invoice.Key.InvoiceTemplateId,

                    InvoiceType = invoice.Key.InvoiceType,
                    ProductName = invoice.Key.ProductName,
                    VatPercent = invoice.Key.VatPercent,
                    Note = invoice.Key.Note,
                    ExtraProperties = invoice.Key.ExtraProperties,
                    BudgetUnitCode = invoice.Key.BudgetUnitCode,

                    ReferenceInvoices = invoice.ToList(),
                }).ToList();

                var TotalItems = groupInvoice.Count;
                groupInvoice = groupInvoice.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

                int dem = input.SkipCount + 1;
                foreach (var item in groupInvoice)
                {
                    item.RowNumber = dem;
                    item.TotalItems = TotalItems;

                    dem++;
                }
                #endregion

                #region Tinh TTPhi
                var groupByInvoiceType = groupInvoice.GroupBy(x => x.TemplateNo);
                foreach (var invoices in groupByInvoiceType)
                {
                    // Lấy cấu hình thể TPhi
                    TenantSetting settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice01.ToString());
                    if (invoices.Key == 2)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
                    }
                    else if (invoices.Key == 3)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice03.ToString());
                    }
                    else if (invoices.Key == 4)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice04.ToString());
                    }
                    else if (invoices.Key == 5)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice05.ToString());
                    }

                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = invoices.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var indexOf = groupInvoice.IndexOf(maxVatPercent);
                                            groupInvoice[indexOf].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                var dataHasReferences = groupInvoice.Where(x => !x.ReferenceInvoices.IsNullOrEmpty() && !x.ReferenceInvoices.First().InvoiceReferenceDataSplit.IsNullOrEmpty()).ToList();

                foreach (var item in dataHasReferences)
                {
                    var invoiceStatus = item.InvoiceStatus;

                    var splits = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit?.Split("_");

                    if (item.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || item.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode())
                    {
                        invoiceStatus = short.Parse(splits.LastOrDefault());
                    }

                    #region Hóa đơn Bị thay thế
                    if (invoiceStatus == InvoiceStatus.BiThayThe.GetHashCode())
                    {
                        // Kiểm tra có phải hd gốc không
                        if (item.ReferenceInvoices.Count > 1)
                        {
                            // Lấy hóa đơn thay thế
                            //var hdThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId > item.InvoiceHeaderId).ToList();
                            // Lấy hóa đơn bị thay thế
                            var hdBiThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                            // Không phải => HD thay thế lần thứ 2 trở đi

                            StringBuilder note = new StringBuilder();

                            note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.ThayThe.GetHashCode()).ToDisplayName()} cho ");
                            foreach (var referenceInvoice in hdBiThayThe)
                            {
                                splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                                note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                                if (referenceInvoice != hdBiThayThe.Last())
                                {
                                    note.Append("; ");
                                }
                            }
                            item.Note = note.ToString();
                            item.Status = "Thay thế";
                            item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                            item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;
                        }
                        else
                        {
                            // Phải => HD thay thế lần thứ 2 trở đi
                            //splits = item.ReferenceInvoices.First().InvoiceReferenceDataSplit.Split("_");
                            //item.Note = $"{EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName()} bởi hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                            item.InvoiceReferenceDataSplit = null;
                            item.InvoiceReferenceData = null;
                        }

                        // Ko hiển thị ra
                        item.ReferenceInvoices = null;
                    }
                    #endregion

                    #region Hóa đơn Điều chỉnh, Điều chỉnh định danh, điều chỉnh tăng giảm
                    if (invoiceStatus == InvoiceStatus.DieuChinh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                    {
                        // Lấy hóa đơn bị thay thế
                        var hdDieuChinh = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                        // Không phải => HD thay thế lần thứ 2 trở đi

                        StringBuilder note = new StringBuilder();

                        note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.DieuChinh.GetHashCode()).ToDisplayName()} cho ");
                        foreach (var referenceInvoice in hdDieuChinh)
                        {
                            splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                            note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                            if (referenceInvoice != hdDieuChinh.Last())
                            {
                                note.Append("; ");
                            }
                        }
                        item.Note = note.ToString();
                        item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                        item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;

                        // Ko hiển thị ra
                        item.ReferenceInvoices = null;
                    }
                    #endregion

                    #region Hóa đơn khác
                    if (invoiceStatus == InvoiceStatus.Goc.GetHashCode()
                        || invoiceStatus == InvoiceStatus.BiDieuChinh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode())
                    {
                        item.InvoiceReferenceDataSplit = null;
                        item.InvoiceReferenceData = null;
                        item.ReferenceInvoices = null;
                    }
                    else if (invoiceStatus != InvoiceStatus.DieuChinh.GetHashCode()
                      && invoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                      && invoiceStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                      && invoiceStatus != InvoiceStatus.BiThayThe.GetHashCode())
                    {
                        item.Note = $"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName().ToLower()} cho hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                        item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                        item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;
                        item.ReferenceInvoices = null;
                    }
                    #endregion
                }

                var type = ReportType.Month;
                var index = taxReportHeader.ReportMonth;
                if (taxReportHeader.ReportQuarter > 0)
                {
                    type = ReportType.Quarter;
                    index = taxReportHeader.ReportQuarter;
                }

                TaxReport01Dto response = _appFactory.ObjectMapper.Map<TaxReport01HeaderEntity, TaxReport01Dto>(taxReportHeader);
                response.Year = taxReportHeader.ReportYear;
                response.Index = index;
                response.TotalPage = TotalItems % input.MaxResultCount == 0 ? TotalItems / input.MaxResultCount : TotalItems / input.MaxResultCount + 1;
                response.PageSize = input.MaxResultCount;
                //response.Type = type;

                //response.TaxReportDetails = resultData;
                response.TaxReportDetails = groupInvoice;

                return response;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<TaxReport01Dto> GetByIdBTHXangDauAsync(Guid tenantId, TaxReport01InfoPagedRequestDto input)
        {
            try
            {
                var taxReportHeader = await _repoTaxReport01.Where(x => x.Id == input.Id && x.TenantId == tenantId).Include(x => x.TaxReport01DetailMappings).FirstOrDefaultAsync();
                if (taxReportHeader == null)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport01.NotFound"]);

                var detailMappingList = taxReportHeader.TaxReport01DetailMappings;
                var groupDetailMapping = detailMappingList.GroupBy(x => x.InvoiceType).OrderBy(x=>x.Key);
                QueryInvoiceModel modelQuery = new QueryInvoiceModel();
                modelQuery.SkipCount = input.SkipCount;
                modelQuery.MaxResultCount = input.MaxResultCount;
                modelQuery.ReportHeaderId = taxReportHeader.Id;
                modelQuery.TenantId = tenantId;

                var detailData = new List<TaxReport01DetailDto>();
                foreach (var group in groupDetailMapping)
                {
                    var list = group.OrderBy(x => x.Index).ToList();
                    var firstMapping = list.FirstOrDefault();
                    var lastMapping = list.LastOrDefault();
                    //var firstId = firstMapping.FromId;
                    //var lastId = lastMapping.ToId;
                    foreach (var item in list)
                    {
                        if (group.Key == (short)VnisType._01GTKT)
                        {
                            //modelQuery.FromIdInvoice01 = firstId;
                            //modelQuery.ToIdInvoice01 = lastId;
                            if (modelQuery.IdsInvoice01.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice01 += item.InvoiceIds;
                            } else
                            {
                                modelQuery.IdsInvoice01 += ","+item.InvoiceIds;
                            }
                        }
                        else if (group.Key == (short)VnisType._02GTTT)
                        {
                            if (modelQuery.IdsInvoice02.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice02 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice02 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == (short)VnisType._03XKNB)
                        {
                            if (modelQuery.IdsInvoice03.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice03 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice03 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == (short)VnisType._04HGDL)
                        {
                            if (modelQuery.IdsInvoice04.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice04 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice04 += "," + item.InvoiceIds;
                            }
                        }
                        else if (group.Key == (short)VnisType._05TVDT)
                        {
                            if (modelQuery.IdsInvoice05.IsNullOrEmpty())
                            {
                                modelQuery.IdsInvoice05 += item.InvoiceIds;
                            }
                            else
                            {
                                modelQuery.IdsInvoice05 += "," + item.InvoiceIds;
                            }
                        }
                    }
                    
                }

                #region Cập nhật lại cách lấy dữ liệu hd từ ds các Id
                List<TaxReport01DetailDto> resultData = new List<TaxReport01DetailDto>();
                var detailMapping = taxReportHeader.TaxReport01DetailMappings;
                if (!modelQuery.IdsInvoice01.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == (short)VnisType._01GTKT);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice01XangDau(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice02.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == (short)VnisType._02GTTT);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice02XangDau(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice03.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == (short)VnisType._03XKNB);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice03XangDau(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice04.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == (short)VnisType._04HGDL);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice04XangDau(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }
                if (!modelQuery.IdsInvoice05.IsNullOrEmpty())
                {
                    // Ds cac DetailMapping theo InvoiceType
                    var listDetail = detailMapping.Where(x => x.InvoiceType == (short)VnisType._05TVDT);
                    foreach (var detail in listDetail)
                    {
                        var sql = QueryInvoice05XangDau(taxReportHeader, detail);
                        var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sql);
                        resultData.AddRange(invoices);
                    }
                }

                // Nhóm dữ liệu báo cáo 
                var groupInvoice = resultData
                    .Select(invoice => new TaxReport01DetailGroupDto()
                    {
                        InvoiceHeaderId = invoice.InvoiceHeaderId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        ReceiverName = invoice.ReceiverName,
                        BuyerFullName = invoice.BuyerFullName,
                        BuyerName = invoice.BuyerName,
                        BuyerTaxCode = invoice.BuyerTaxCode,
                        InvoiceNo = invoice.InvoiceNo,
                        Number = invoice.Number,
                        ExchangeRate = invoice.ExchangeRate,
                        InvoiceDate = invoice.InvoiceDate,
                        TotalAmount = invoice.TotalAmount,
                        TotalVatAmount = invoice.TotalVatAmount,
                        TotalPaymentAmount = invoice.TotalPaymentAmount,
                        Status = invoice.Status,
                        InvoiceStatus = invoice.InvoiceStatus,
                        InvoiceTemplateId = invoice.InvoiceTemplateId,

                        InvoiceType = invoice.InvoiceType,
                        ProductName = invoice.ProductName,
                        ProductCode = invoice.ProductCode,
                        UnitName = invoice.UnitName,
                        Quantity = invoice.Quantity,
                        VatPercent = invoice.VatPercent,
                        Note = invoice.Note,
                        ExtraProperties = invoice.ExtraProperties,

                        ReferenceInvoices = new List<TaxReport01DetailDto> { invoice },
                    }).ToList();

                var TotalItems = groupInvoice.Count;
                groupInvoice = groupInvoice.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

                int dem = input.SkipCount+1;
                foreach (var item in groupInvoice)
                {
                    item.RowNumber = dem;
                    item.TotalItems = TotalItems;

                    dem++;
                }
                #endregion

                #region Tinh TTPhi
                var groupByInvoiceType = groupInvoice.GroupBy(x => x.TemplateNo);
                foreach (var invoices in groupByInvoiceType)
                {
                    // Lấy cấu hình thể TPhi
                    TenantSetting settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice01.ToString());
                    if (invoices.Key == 2)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
                    }
                    else if (invoices.Key == 3)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice03.ToString());
                    }
                    else if (invoices.Key == 4)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice04.ToString());
                    }
                    else if (invoices.Key == 5)
                    {
                        settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice05.ToString());
                    }

                    var tPhiConfig = settingConfigTPhi.Value;
                    if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                    {
                        // Khong xu ly
                    }
                    else
                    {
                        // TH DetailMapping loai HD 01 => Co VAT PERCENT
                        var groupByIds = invoices.GroupBy(x => x.InvoiceHeaderId);
                        foreach (var item in groupByIds)
                        {
                            if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                            {
                                var lines = item.ToList();
                                // Tim dong hd co VAT PERCENT MAX
                                var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                                if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                                {
                                    // Neu ton tai Extra
                                    var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                    if (a != null && a.InvoiceHeaderExtras != null)
                                    {
                                        var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                        if (!extraHeaders.IsNullOrEmpty())
                                        {
                                            decimal TongTien = 0;
                                            var tPhiArray = tPhiConfig.Split(';');
                                            foreach (var tPhi in tPhiArray)
                                            {
                                                if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                                {
                                                    var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                    TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                                }
                                            }

                                            var indexOf = groupInvoice.IndexOf(maxVatPercent);
                                            groupInvoice[indexOf].TTPhi = TongTien;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                #endregion

                var dataHasReferences = groupInvoice.Where(x => !x.ReferenceInvoices.IsNullOrEmpty() && !x.ReferenceInvoices.First().InvoiceReferenceDataSplit.IsNullOrEmpty()).ToList();

                foreach (var item in dataHasReferences)
                {
                    var invoiceStatus = item.InvoiceStatus;

                    var splits = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit?.Split("_");

                    if (item.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || item.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode())
                    {
                        invoiceStatus = short.Parse(splits.LastOrDefault());
                    }

                    #region Hóa đơn Bị thay thế
                    if (invoiceStatus == InvoiceStatus.BiThayThe.GetHashCode())
                    {
                        // Kiểm tra có phải hd gốc không
                        if (item.ReferenceInvoices.Count > 1)
                        {
                            // Lấy hóa đơn thay thế
                            //var hdThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId > item.InvoiceHeaderId).ToList();
                            // Lấy hóa đơn bị thay thế
                            var hdBiThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                            // Không phải => HD thay thế lần thứ 2 trở đi

                            StringBuilder note = new StringBuilder();

                            note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.ThayThe.GetHashCode()).ToDisplayName()} cho ");
                            foreach (var referenceInvoice in hdBiThayThe)
                            {
                                splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                                note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                                if (referenceInvoice != hdBiThayThe.Last())
                                {
                                    note.Append("; ");
                                }
                            }
                            item.Note = note.ToString();
                            item.Status = "Thay thế";
                            item.InvoiceReferenceData = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).FirstOrDefault().InvoiceReferenceData;
                            item.InvoiceReferenceDataSplit = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).FirstOrDefault().InvoiceReferenceDataSplit;
                        }
                        else
                        {
                            // Phải => HD thay thế lần thứ 2 trở đi
                            //splits = item.ReferenceInvoices.First().InvoiceReferenceDataSplit.Split("_");
                            //item.Note = $"{EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName()} bởi hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                            item.InvoiceReferenceDataSplit = null;
                            item.InvoiceReferenceData = null;
                        }

                        // Ko hiển thị ra
                        item.ReferenceInvoices = null;
                    }
                    #endregion

                    #region Hóa đơn Điều chỉnh, Điều chỉnh định danh, điều chỉnh tăng giảm
                    if (invoiceStatus == InvoiceStatus.DieuChinh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                    {
                        // Lấy hóa đơn bị thay thế
                        var hdDieuChinh = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                        // Không phải => HD thay thế lần thứ 2 trở đi

                        StringBuilder note = new StringBuilder();

                        note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.DieuChinh.GetHashCode()).ToDisplayName()} cho ");
                        foreach (var referenceInvoice in hdDieuChinh)
                        {
                            splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                            note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                            if (referenceInvoice != hdDieuChinh.Last())
                            {
                                note.Append("; ");
                            }
                        }
                        item.Note = note.ToString();
                        item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                        item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;

                        // Ko hiển thị ra
                        item.ReferenceInvoices = null;
                    }
                    #endregion

                    #region Hóa đơn khác
                    if (invoiceStatus == InvoiceStatus.Goc.GetHashCode()
                        || invoiceStatus == InvoiceStatus.BiDieuChinh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
                        || invoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode())
                    {
                        item.InvoiceReferenceDataSplit = null;
                        item.InvoiceReferenceData = null;
                        item.ReferenceInvoices = null;
                    }
                    else if (invoiceStatus != InvoiceStatus.DieuChinh.GetHashCode()
                        && invoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                        && invoiceStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                        && invoiceStatus != InvoiceStatus.BiThayThe.GetHashCode())
                    {
                        item.Note = $"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName().ToLower()} cho hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                        item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                        item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;
                        item.ReferenceInvoices = null;
                    }
                    #endregion
                }

                var type = ReportType.Month;
                var index = taxReportHeader.ReportMonth;
                if (taxReportHeader.ReportQuarter > 0)
                {
                    type = ReportType.Quarter;
                    index = taxReportHeader.ReportQuarter;
                }

                TaxReport01Dto response = _appFactory.ObjectMapper.Map<TaxReport01HeaderEntity, TaxReport01Dto>(taxReportHeader);
                response.Year = taxReportHeader.ReportYear;
                response.Index = index;
                response.TotalPage = TotalItems % input.MaxResultCount == 0 ? TotalItems / input.MaxResultCount : TotalItems / input.MaxResultCount + 1;
                response.PageSize = input.MaxResultCount;
                //response.Type = type;

                //response.TaxReportDetails = resultData;
                response.TaxReportDetails = groupInvoice;

                return response;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public virtual async Task<List<TaxReport01DetailDto>> GetTaxReport01Async(long? reportHeaderId, Guid tenantId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            //string sqlReport = GetQueryTaxReport(tenantId, reportHeaderId, input, fromDate, toDate);
            //var resultData = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlReport)).ToList();

            string sqlInvoice01 = GetQueryTaxReportInfoInvoice01(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice02 = GetQueryTaxReportInfoInvoice02(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice03 = GetQueryTaxReportInfoInvoice03(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice04 = GetQueryTaxReportInfoInvoice04(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice05 = GetQueryTaxReportInfoInvoice05(tenantId, reportHeaderId, input, fromDate, toDate);
            var resultDataInvoice01 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice01)).ToList();
            var resultDataInvoice02 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice02)).ToList();
            var resultDataInvoice03 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice03)).ToList();
            var resultDataInvoice04 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice04)).ToList();
            var resultDataInvoice05 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice05)).ToList();

            var resultData = new List<TaxReport01DetailDto>();
            if (!resultDataInvoice01.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice01);
            }
            if (!resultDataInvoice02.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice02);
            }
            if (!resultDataInvoice03.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice03);
            }
            if (!resultDataInvoice04.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice04);
            }
            if (!resultDataInvoice05.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice05);
            }
            if (resultData.Any())
            {
                resultData[0].TotalItems = resultData.Count;
            }

            resultData = resultData.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

            #region Tinh TTPhi
            var groupByInvoiceType = resultData.GroupBy(x => x.TemplateNo);
            foreach (var invoices in groupByInvoiceType)
            {
                // Lấy cấu hình thể TPhi
                TenantSetting settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice01.ToString());
                if (invoices.Key == 2)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
                }
                else if (invoices.Key == 3)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice03.ToString());
                }
                else if (invoices.Key == 4)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice04.ToString());
                }
                else if (invoices.Key == 5)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice05.ToString());
                }

                var tPhiConfig = settingConfigTPhi.Value;
                if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                {
                    // Khong xu ly
                }
                else
                {
                    // TH DetailMapping loai HD 01 => Co VAT PERCENT
                    var groupByIds = invoices.GroupBy(x => x.InvoiceHeaderId);
                    foreach (var item in groupByIds)
                    {
                        if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                        {
                            var lines = item.ToList();
                            // Tim dong hd co VAT PERCENT MAX
                            var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                            if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                            {
                                // Neu ton tai Extra
                                var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                if (a != null && a.InvoiceHeaderExtras != null)
                                {
                                    var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                    if (!extraHeaders.IsNullOrEmpty())
                                    {
                                        decimal TongTien = 0;
                                        var tPhiArray = tPhiConfig.Split(';');
                                        foreach (var tPhi in tPhiArray)
                                        {
                                            if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                            {
                                                var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                            }
                                        }

                                        var indexOf = resultData.IndexOf(maxVatPercent);
                                        resultData[indexOf].TTPhi = TongTien;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion

            int dem = input.SkipCount + 1;
            foreach (var item in resultData)
            {
                item.RowNumber = dem;

                dem++;
            }

            var dataHasReferences = resultData.Where(x => !string.IsNullOrEmpty(x.InvoiceReferenceDataSplit)).ToList();
            foreach (var item in dataHasReferences)
            {
                var splits = item.InvoiceReferenceDataSplit.Split("_");
                var invoiceStatus = item.InvoiceStatus;

                if (item.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || item.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode())
                    invoiceStatus = short.Parse(splits.LastOrDefault());

                if (invoiceStatus == InvoiceStatus.BiThayThe.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinh.GetHashCode())
                {
                    item.Note = $"{EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName()} bởi hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                }
                else
                {
                    item.Note = $"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName().ToLower()} cho hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                }
            }

            return resultData;
        }

        public virtual string GetQueryTaxReport(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport

            var sqlReport = $@"
                                WITH Invoice01 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        a.""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""ExchangeRate"",
                                        a.""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE b.TotalAmount END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE b.TotalVatAmount END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(b.TotalPaymentAmount, a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        1 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice01Header"" a 
                                    LEFT JOIN (
                                        SELECT
	                                        ""InvoiceHeaderId"",
	                                        ""VatPercent"",
	                                        CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP( ORDER BY ""ProductName"" ), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
	                                        CAST(SUBSTR(LISTAGG(""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP( ORDER BY ""Note"" ), 1, 255) AS NVARCHAR2(255)) AS ""Note"",
	                                        Sum(""TotalAmount"") AS TotalAmount,
		                                        Sum(""TotalPaymentAmount"") AS TotalPaymentAmount,
		                                        Sum(""TotalVatAmount"") AS TotalVatAmount
                                        FROM
	                                        (
	                                        SELECT
			                                    id.""InvoiceHeaderId"",

                                                ih.""InvoiceStatus"",
			                                    CASE
                                                    WHEN ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN NULL

                                                    ELSE id.""VatPercent""

                                                END AS ""VatPercent"",
			                                    CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
			                                    CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note"",
			                                    Sum(""Amount"") AS ""TotalAmount"",
			                                    Sum(""PaymentAmount"") AS ""TotalPaymentAmount"",
			                                    Sum(""VatAmount"") AS ""TotalVatAmount""
                                            FROM
                                                ""Invoice01Detail"" id
                                            INNER JOIN ""Invoice01Header"" ih ON
                                                id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'

                                            GROUP BY

                                                ""InvoiceHeaderId"",
			                                    ""VatPercent"",
			                                    ""InvoiceStatus"")
                                        GROUP BY
	                                        ""InvoiceHeaderId"",
	                                        ""VatPercent""
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 1 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Number""
                                ),
                                Invoice02 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                       SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice02Detail"" id INNER JOIN ""Invoice02Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Number""
                                ),
                                Invoice03 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                       SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice03Detail"" id INNER JOIN ""Invoice03Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Number""
                                ),
                                Invoice04 AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice04Detail"" id INNER JOIN ""Invoice04Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Number""
                                ),
                                Ticket AS (
                                    SELECT 
                                        b.""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalVatAmount"" END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}) THEN N'Gốc'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""TicketDetail"" id INNER JOIN ""TicketHeader"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Number""
                                )
                                SELECT * FROM (
                                    SELECT 
                                        rownum as ""RowNumber"", 
                                        COUNT(*) OVER () ""TotalItems"", 
                                        a.""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        a.""ReceiverName"",
                                        a.""BuyerFullName"",
                                        a.""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""ExchangeRate"",
                                        a.""InvoiceDate"",
                                        a.""TotalAmount"",
                                        a.""TotalVatAmount"",
                                        a.""TotalPaymentAmount"",
                                        CAST(SUBSTR(a.""Status"",1,500) AS NVARCHAR2(50)) AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        a.""InvoiceReferenceData"",   
                                        a.""InvoiceType"",
                                        a.""ProductName"",
                                        a.""VatPercent"",
                                        CAST(SUBSTR(a.""Note"",1,500) AS NVARCHAR2(500)) AS ""Note""
                                    FROM (
                                        SELECT * FROM Invoice01
                                        UNION
                                        SELECT * FROM Invoice02
                                        UNION
                                        SELECT * FROM Invoice03
                                        UNION
                                        SELECT * FROM Invoice04
                                        UNION
                                        SELECT * FROM Ticket
                                    ) a 
                                ) a
                                WHERE ""RowNumber"" BETWEEN  {input.SkipCount + 1} AND {input.SkipCount + input.MaxResultCount}
                            ";

            #endregion
            return sqlReport;
        }

        #region Turning cau lenh get Info bao cao
        public virtual string GetQueryTaxReportInfoInvoice01(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                            a.""Id"" AS ""InvoiceHeaderId"",
                            a.""TemplateNo"",
                            a.""SerialNo"",
                            NULL AS ""ReceiverName"",
                            a.""BuyerFullName"",
                            NULL AS ""BuyerName"",
                            a.""BuyerTaxCode"",
                            a.""InvoiceNo"",
                            a.""Number"",
                            a.""ExchangeRate"",
                            a.""InvoiceDate"",
                            TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                            CASE 
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN a.""TotalAmount"" 
                            ELSE TAX.""Amount"" END AS ""TotalAmount"",
                            CASE 
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN a.""TotalVatAmount"" 
                            ELSE TAX.""VatAmount"" END AS ""TotalVatAmount"",
                            CASE 
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN a.""TotalPaymentAmount"" 
                            ELSE ROUND(TAX.""Amount"" + TAX.""VatAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                            CASE 
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                            ELSE null END AS ""Status"",
                            a.""InvoiceStatus"",
                            a.""InvoiceTemplateId"",
                            CASE 
                                WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                            ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                            CASE 
                                WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                            ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                            1 AS ""InvoiceType"",
                            b.""ProductName"",
                            b.""VatPercent"",
                            CASE 
                                WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                            ELSE b.""Note"" END AS ""Note"",
                            c.""InvoiceReferenceId"",
                            a.""ExtraProperties"",
                            a.""BudgetUnitCode""
                        FROM ""Invoice01Header"" a 
                        LEFT JOIN (
                            SELECT
	                            ""InvoiceHeaderId"",
	                            ""VatPercent"",
	                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP( ORDER BY ""ProductName"" ), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
	                            CAST(SUBSTR(LISTAGG(""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP( ORDER BY ""Note"" ), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                            FROM
	                            (
	                            SELECT
			                        id.""InvoiceHeaderId"",

                                    ih.""InvoiceStatus"",
			                        CASE
                                        WHEN ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN NULL

                                        ELSE id.""VatPercent""

                                    END AS ""VatPercent"",
			                        CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
			                        CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                FROM
                                    ""Invoice01Detail"" id
                                RIGHT JOIN ""Invoice01Header"" ih ON
                                    id.""InvoiceHeaderId"" = ih.""Id""
                                WHERE
                                    id.""TenantId"" = '{rawTenantId}'

                                GROUP BY

                                    ""InvoiceHeaderId"",
			                        ""VatPercent"",
			                        ""InvoiceStatus"")
                            GROUP BY
	                            ""InvoiceHeaderId"",
	                            ""VatPercent""
                        ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceHeaderId"",
                                ""InvoiceReferenceId"",
                                ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                            FROM ""Invoice01Reference"" WHERE ""TenantId"" = '{rawTenantId}' 
                        ) c ON a.""Id"" = c.""InvoiceHeaderId"" 
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceHeaderId"",
                                ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                            FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                        ) e ON a.""Id"" = e.""InvoiceHeaderId""
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceType"",
                                ""InvoiceHeaderId"",
                                ""Note"",
                                ""Status""
                            FROM ""TaxReport01Detail"" 
                            WHERE ""InvoiceType"" = 1 {condition} 
                        ) d ON a.""Id"" = d.""InvoiceHeaderId""
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceHeaderId"",
                                ""Amount"",
                                ""VatAmount"",
                                ""VatPercent""
                            FROM ""Invoice01TaxBreakdown"" 
                        ) TAX ON a.""Id"" = TAX.""InvoiceHeaderId"" AND b.""VatPercent"" = TAX.""VatPercent""
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceHeaderId"",
                                ""Amount"",
                                ""VatAmount"",
                                ""VatPercent""
                            FROM ""Invoice01TaxBreakdown"" 
                        ) TAX ON a.""Id"" = TAX.""InvoiceHeaderId"" AND b.""VatPercent"" = TAX.""VatPercent""
                        WHERE a.""TenantId"" = '{rawTenantId}'
                        AND a.""IsDeclared"" = 0
                        AND a.""Number"" IS NOT NULL
                        AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()} ";
                sql += $@" AND a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaBo} ";

            sql += $@" AND a.""InvoiceNo"" IS NOT NULL
                        AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                        AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                        ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }

        public virtual async Task<List<TaxReport01DetailGroupDto>> GetTaxReport01RefactorAsync(long? reportHeaderId, Guid tenantId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            string sqlInvoice01 = GetQueryTaxReportInfoInvoice01(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice02 = GetQueryTaxReportInfoInvoice02(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice03 = GetQueryTaxReportInfoInvoice03(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice04 = GetQueryTaxReportInfoInvoice04(tenantId, reportHeaderId, input, fromDate, toDate);
            string sqlInvoice05 = GetQueryTaxReportInfoInvoice05(tenantId, reportHeaderId, input, fromDate, toDate);
            var resultDataInvoice01 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice01)).ToList();
            var resultDataInvoice02 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice02)).ToList();
            var resultDataInvoice03 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice03)).ToList();
            var resultDataInvoice04 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice04)).ToList();
            var resultDataInvoice05 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice05)).ToList();

            var resultData = new List<TaxReport01DetailDto>();
            if (!resultDataInvoice01.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice01);
            }
            if (!resultDataInvoice02.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice02);
            }
            if (!resultDataInvoice03.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice03);
            }
            if (!resultDataInvoice04.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice04);
            }
            if (!resultDataInvoice05.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice05);
            }
            if (resultData.Any())
            {
                resultData[0].TotalItems = resultData.Count;
            }

            // Nhóm dữ liệu báo cáo 
            var groupInvoice = resultData.GroupBy(invoice => new
            {
                invoice.InvoiceHeaderId,
                invoice.TemplateNo,
                invoice.SerialNo,
                invoice.ReceiverName,
                invoice.BuyerFullName,
                invoice.BuyerName,
                invoice.BuyerTaxCode,
                invoice.InvoiceNo,
                invoice.Number,
                invoice.ExchangeRate,
                invoice.InvoiceDate,
                invoice.TotalAmount,
                invoice.TotalVatAmount,
                invoice.TotalPaymentAmount,
                invoice.Status,
                invoice.InvoiceStatus,
                invoice.InvoiceTemplateId,

                invoice.InvoiceType,
                invoice.ProductName,
                invoice.VatPercent,
                invoice.Note,
                invoice.BudgetUnitCode,
                invoice.ExtraProperties
            }).Select(invoice => new TaxReport01DetailGroupDto()
            {
                InvoiceHeaderId = invoice.Key.InvoiceHeaderId,
                TemplateNo = invoice.Key.TemplateNo,
                SerialNo = invoice.Key.SerialNo,
                ReceiverName = invoice.Key.ReceiverName,
                BuyerFullName = invoice.Key.BuyerFullName,
                BuyerName = invoice.Key.BuyerName,
                BuyerTaxCode = invoice.Key.BuyerTaxCode,
                InvoiceNo = invoice.Key.InvoiceNo,
                Number = invoice.Key.Number,
                ExchangeRate = invoice.Key.ExchangeRate,
                InvoiceDate = invoice.Key.InvoiceDate,
                TotalAmount = invoice.Key.TotalAmount,
                TotalVatAmount = invoice.Key.TotalVatAmount,
                TotalPaymentAmount = invoice.Key.TotalPaymentAmount,
                Status = invoice.Key.Status,
                InvoiceStatus = invoice.Key.InvoiceStatus,
                InvoiceTemplateId = invoice.Key.InvoiceTemplateId,

                InvoiceType = invoice.Key.InvoiceType,
                ProductName = invoice.Key.ProductName,
                VatPercent = invoice.Key.VatPercent,
                Note = invoice.Key.Note,
                ExtraProperties = invoice.Key.ExtraProperties,

                ReferenceInvoices = invoice.ToList(),
                BudgetUnitCode = invoice.Key.BudgetUnitCode,
            }).ToList();

            var TotalItems = groupInvoice.Count;
            groupInvoice = groupInvoice.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

            #region Tinh TTPhi
            var groupByInvoiceType = groupInvoice.GroupBy(x => x.TemplateNo);
            foreach (var invoices in groupByInvoiceType)
            {
                // Lấy cấu hình thể TPhi
                TenantSetting settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice01.ToString());
                if (invoices.Key == 2)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
                }
                else if (invoices.Key == 3)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice03.ToString());
                }
                else if (invoices.Key == 4)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice04.ToString());
                }
                else if (invoices.Key == 5)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice05.ToString());
                }

                var tPhiConfig = settingConfigTPhi.Value;
                if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                {
                    // Khong xu ly
                }
                else
                {
                    // TH DetailMapping loai HD 01 => Co VAT PERCENT
                    var groupByIds = invoices.GroupBy(x => x.InvoiceHeaderId);
                    foreach (var item in groupByIds)
                    {
                        if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                        {
                            var lines = item.ToList();
                            // Tim dong hd co VAT PERCENT MAX
                            var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                            if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                            {
                                // Neu ton tai Extra
                                var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                if (a != null && a.InvoiceHeaderExtras != null)
                                {
                                    var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                    if (!extraHeaders.IsNullOrEmpty())
                                    {
                                        decimal TongTien = 0;
                                        var tPhiArray = tPhiConfig.Split(';');
                                        foreach (var tPhi in tPhiArray)
                                        {
                                            if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                            {
                                                var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                            }
                                        }

                                        var indexOf = groupInvoice.IndexOf(maxVatPercent);
                                        groupInvoice[indexOf].TTPhi = TongTien;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion
            int dem = input.SkipCount + 1;

            foreach (var item in groupInvoice)
            {
                item.RowNumber = dem;
                item.TotalItems = TotalItems;
                dem++;
            }

            var dataHasReferences = groupInvoice.Where(x => !x.ReferenceInvoices.IsNullOrEmpty() && !x.ReferenceInvoices.First().InvoiceReferenceDataSplit.IsNullOrEmpty()).ToList();

            foreach (var item in dataHasReferences)
            {
                var invoiceStatus = item.InvoiceStatus;

                var splits = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit?.Split("_");

                if (item.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || item.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode())
                {
                    invoiceStatus = short.Parse(splits.LastOrDefault());
                }

                #region Hóa đơn Bị thay thế
                if (invoiceStatus == InvoiceStatus.BiThayThe.GetHashCode())
                {
                    // Kiểm tra có phải hd gốc không
                    if (item.ReferenceInvoices.Count > 1)
                    {
                        // Lấy hóa đơn thay thế
                        //var hdThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId > item.InvoiceHeaderId).ToList();
                        // Lấy hóa đơn bị thay thế
                        var hdBiThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                        // Không phải => HD thay thế lần thứ 2 trở đi

                        StringBuilder note = new StringBuilder();

                        note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.ThayThe.GetHashCode()).ToDisplayName()} cho ");
                        foreach (var referenceInvoice in hdBiThayThe)
                        {
                            splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                            note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                            if (referenceInvoice != hdBiThayThe.Last())
                            {
                                note.Append("; ");
                            }
                        }
                        item.Note = note.ToString();
                        item.Status = "Thay thế";
                        item.InvoiceReferenceData = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).FirstOrDefault().InvoiceReferenceData;
                        item.InvoiceReferenceDataSplit = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).FirstOrDefault().InvoiceReferenceDataSplit;
                    }
                    else
                    {
                        // Phải => HD thay thế lần thứ 2 trở đi
                        //splits = item.ReferenceInvoices.First().InvoiceReferenceDataSplit.Split("_");
                        //item.Note = $"{EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName()} bởi hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                        item.InvoiceReferenceDataSplit = null;
                        item.InvoiceReferenceData = null;
                    }

                    // Ko hiển thị ra
                    item.ReferenceInvoices = null;
                }
                #endregion

                #region Hóa đơn Điều chỉnh, Điều chỉnh định danh, điều chỉnh tăng giảm
                if (invoiceStatus == InvoiceStatus.DieuChinh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                {
                    // Lấy hóa đơn bị thay thế
                    var hdDieuChinh = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                    // Không phải => HD thay thế lần thứ 2 trở đi

                    StringBuilder note = new StringBuilder();

                    note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.DieuChinh.GetHashCode()).ToDisplayName()} cho ");
                    foreach (var referenceInvoice in hdDieuChinh)
                    {
                        splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                        note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                        if (referenceInvoice != hdDieuChinh.Last())
                        {
                            note.Append("; ");
                        }
                    }
                    item.Note = note.ToString();
                    item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                    item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;

                    // Ko hiển thị ra
                    item.ReferenceInvoices = null;
                }
                #endregion

                #region Hóa đơn khác
                if (invoiceStatus == InvoiceStatus.Goc.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode())
                {
                    item.InvoiceReferenceDataSplit = null;
                    item.InvoiceReferenceData = null;
                    item.ReferenceInvoices = null;
                }
                else if (invoiceStatus != InvoiceStatus.DieuChinh.GetHashCode()
                    && invoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                    && invoiceStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                    && invoiceStatus != InvoiceStatus.BiThayThe.GetHashCode())
                {
                    item.Note = $"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName().ToLower()} cho hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                    item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                    item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;
                    item.ReferenceInvoices = null;
                }
                #endregion
            }

            return groupInvoice;
        }

        public virtual async Task<List<TaxReport01DetailGroupDto>> GetTaxReport01XangDauAsync(long? reportHeaderId, Guid tenantId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            string sqlInvoice01 = GetQueryTaxReportInfoInvoice01XangDau(tenantId, reportHeaderId, input, fromDate, toDate);
            Log.Information($@"sqlInvoice01: {sqlInvoice01}");

            string sqlInvoice02 = GetQueryTaxReportInfoInvoice02XangDau(tenantId, reportHeaderId, input, fromDate, toDate);
            Log.Information($@"sqlInvoice02: {sqlInvoice02}");

            string sqlInvoice03 = GetQueryTaxReportInfoInvoice03XangDau(tenantId, reportHeaderId, input, fromDate, toDate);
            Log.Information($@"sqlInvoice03: {sqlInvoice03}");

            string sqlInvoice04 = GetQueryTaxReportInfoInvoice04XangDau(tenantId, reportHeaderId, input, fromDate, toDate);
            Log.Information($@"sqlInvoice04: {sqlInvoice04}");

            string sqlInvoice05 = GetQueryTaxReportInfoInvoice05XangDau(tenantId, reportHeaderId, input, fromDate, toDate);
            Log.Information($@"sqlInvoice05: {sqlInvoice05}");

            var resultDataInvoice01 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice01)).ToList();
            var resultDataInvoice02 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice02)).ToList();
            var resultDataInvoice03 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice03)).ToList();
            var resultDataInvoice04 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice04)).ToList();
            var resultDataInvoice05 = (await _appFactory.VnisCoreOracle.Connection.QueryAsync<TaxReport01DetailDto>(sqlInvoice05)).ToList();

            var resultData = new List<TaxReport01DetailDto>();
            if (!resultDataInvoice01.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice01);
            }
            if (!resultDataInvoice02.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice02);
            }
            if (!resultDataInvoice03.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice03);
            }
            if (!resultDataInvoice04.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice04);
            }
            if (!resultDataInvoice05.IsNullOrEmpty())
            {
                resultData.AddRange(resultDataInvoice05);
            }
            if (resultData.Any())
            {
                resultData[0].TotalItems = resultData.Count;
            }

            // Nhóm dữ liệu báo cáo
            var groupInvoice = resultData
                .Select(invoice => new TaxReport01DetailGroupDto()
                    {
                        InvoiceHeaderId = invoice.InvoiceHeaderId,
                        TemplateNo = invoice.TemplateNo,
                        SerialNo = invoice.SerialNo,
                        ReceiverName = invoice.ReceiverName,
                        BuyerFullName = invoice.BuyerFullName,
                        BuyerName = invoice.BuyerName,
                        BuyerTaxCode = invoice.BuyerTaxCode,
                        InvoiceNo = invoice.InvoiceNo,
                        Number = invoice.Number,
                        ExchangeRate = invoice.ExchangeRate,
                        InvoiceDate = invoice.InvoiceDate,
                        TotalAmount = invoice.TotalAmount,
                        TotalVatAmount = invoice.TotalVatAmount,
                        TotalPaymentAmount = invoice.TotalPaymentAmount,
                        Status = invoice.Status,
                        InvoiceStatus = invoice.InvoiceStatus,
                        InvoiceTemplateId = invoice.InvoiceTemplateId,

                        InvoiceType = invoice.InvoiceType,
                        ProductName = invoice.ProductName,
                        ProductCode = invoice.ProductCode,
                        UnitName = invoice.UnitName,
                        Quantity = invoice.Quantity,

                        VatPercent = invoice.VatPercent,
                        Note = invoice.Note,
                        ExtraProperties = invoice.ExtraProperties,

                        ReferenceInvoices = new List<TaxReport01DetailDto> { invoice } 
                    }).ToList();

            var TotalItems = groupInvoice.Count;
            groupInvoice = groupInvoice.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

            #region Tinh TTPhi
            var groupByInvoiceType = groupInvoice.GroupBy(x => x.TemplateNo);
            foreach (var invoices in groupByInvoiceType)
            {
                // Lấy cấu hình thể TPhi
                TenantSetting settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice01.ToString());
                if (invoices.Key == 2)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice02.ToString());
                }
                else if (invoices.Key == 3)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice03.ToString());
                }
                else if (invoices.Key == 4)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice04.ToString());
                }
                else if (invoices.Key == 5)
                {
                    settingConfigTPhi = await _settingService.GetByCodeAsync(tenantId, SettingKey.ConfigLPhiForInvoice05.ToString());
                }

                var tPhiConfig = settingConfigTPhi.Value;
                if (tPhiConfig.IsNullOrEmpty() || tPhiConfig == "#")
                {
                    // Khong xu ly
                }
                else
                {
                    // TH DetailMapping loai HD 01 => Co VAT PERCENT
                    var groupByIds = invoices.GroupBy(x => x.InvoiceHeaderId);
                    foreach (var item in groupByIds)
                    {
                        if (item.FirstOrDefault().InvoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                        {
                            var lines = item.ToList();
                            // Tim dong hd co VAT PERCENT MAX
                            var maxVatPercent = lines.OrderByDescending(x => x.VatPercent).FirstOrDefault();
                            if (!maxVatPercent.ExtraProperties.IsNullOrEmpty())
                            {
                                // Neu ton tai Extra
                                var a = maxVatPercent.ExtraProperties.JsonDeserialize<ExtraDto>();

                                if (a != null && a.InvoiceHeaderExtras != null)
                                {
                                    var extraHeaders = maxVatPercent.ExtraProperties == null ? new List<InvoiceHeaderExtraModel>() : JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(a.InvoiceHeaderExtras);

                                    if (!extraHeaders.IsNullOrEmpty())
                                    {
                                        decimal TongTien = 0;
                                        var tPhiArray = tPhiConfig.Split(';');
                                        foreach (var tPhi in tPhiArray)
                                        {
                                            if (extraHeaders.Select(x => x.FieldName).Contains(tPhi))
                                            {
                                                var extraHeader = extraHeaders.Where(x => x.FieldName == tPhi).First();

                                                TongTien += decimal.Parse(extraHeader.FieldValue.Replace('.', ','));
                                            }
                                        }

                                        var indexOf = groupInvoice.IndexOf(maxVatPercent);
                                        groupInvoice[indexOf].TTPhi = TongTien;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            #endregion
            int dem = input.SkipCount + 1;

            foreach (var item in groupInvoice)
            {
                item.RowNumber = dem;
                item.TotalItems = TotalItems;
                dem++;
            }

            var dataHasReferences = groupInvoice.Where(x => !x.ReferenceInvoices.IsNullOrEmpty() && !x.ReferenceInvoices.First().InvoiceReferenceDataSplit.IsNullOrEmpty()).ToList();

            foreach (var item in dataHasReferences)
            {
                var invoiceStatus = item.InvoiceStatus;

                var splits = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit?.Split("_");

                if (item.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || item.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode())
                {
                    invoiceStatus = short.Parse(splits.LastOrDefault());
                }

                #region Hóa đơn Bị thay thế
                if (invoiceStatus == InvoiceStatus.BiThayThe.GetHashCode())
                {
                    // Kiểm tra có phải hd gốc không
                    if (item.ReferenceInvoices.Count > 1)
                    {
                        // Lấy hóa đơn thay thế
                        //var hdThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId > item.InvoiceHeaderId).ToList();
                        // Lấy hóa đơn bị thay thế
                        var hdBiThayThe = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                        // Không phải => HD thay thế lần thứ 2 trở đi

                        StringBuilder note = new StringBuilder();

                        note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.ThayThe.GetHashCode()).ToDisplayName()} cho ");
                        foreach (var referenceInvoice in hdBiThayThe)
                        {
                            splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                            note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                            if (referenceInvoice != hdBiThayThe.Last())
                            {
                                note.Append("; ");
                            }
                        }
                        item.Note = note.ToString();
                        item.Status = "Thay thế";
                        item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                        item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;
                    }
                    else
                    {
                        // Phải => HD thay thế lần thứ 2 trở đi
                        //splits = item.ReferenceInvoices.First().InvoiceReferenceDataSplit.Split("_");
                        //item.Note = $"{EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName()} bởi hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                        item.InvoiceReferenceDataSplit = null;
                        item.InvoiceReferenceData = null;
                    }

                    // Ko hiển thị ra
                    item.ReferenceInvoices = null;
                }
                #endregion

                #region Hóa đơn Điều chỉnh, Điều chỉnh định danh, điều chỉnh tăng giảm
                if (invoiceStatus == InvoiceStatus.DieuChinh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.DieuChinhTangGiam.GetHashCode())
                {
                    // Lấy hóa đơn bị thay thế
                    var hdDieuChinh = item.ReferenceInvoices.Where(x => x.InvoiceReferenceId < item.InvoiceHeaderId).ToList();
                    // Không phải => HD thay thế lần thứ 2 trở đi

                    StringBuilder note = new StringBuilder();

                    note.Append($"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(InvoiceStatus.DieuChinh.GetHashCode()).ToDisplayName()} cho ");
                    foreach (var referenceInvoice in hdDieuChinh)
                    {
                        splits = referenceInvoice.InvoiceReferenceDataSplit.Split("_");
                        note.Append($"hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}");
                        if (referenceInvoice != hdDieuChinh.Last())
                        {
                            note.Append("; ");
                        }
                    }
                    item.Note = note.ToString();
                    item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                    item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;

                    // Ko hiển thị ra
                    item.ReferenceInvoices = null;
                }
                #endregion

                #region Hóa đơn khác
                if (invoiceStatus == InvoiceStatus.Goc.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()
                    || invoiceStatus == InvoiceStatus.BiDieuChinhTangGiam.GetHashCode())
                {
                    item.InvoiceReferenceDataSplit = null;
                    item.InvoiceReferenceData = null;
                    item.ReferenceInvoices = null;
                }
                else if (invoiceStatus != InvoiceStatus.DieuChinh.GetHashCode()
                    && invoiceStatus != InvoiceStatus.DieuChinhDinhDanh.GetHashCode()
                    && invoiceStatus != InvoiceStatus.DieuChinhTangGiam.GetHashCode()
                    && invoiceStatus != InvoiceStatus.BiThayThe.GetHashCode())
                {
                    item.Note = $"Hóa đơn {EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus).ToDisplayName().ToLower()} cho hóa đơn số {splits[2]}, ký hiệu {splits[1]}, ngày {splits[3]}";
                    item.InvoiceReferenceData = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceData;
                    item.InvoiceReferenceDataSplit = item.ReferenceInvoices.FirstOrDefault().InvoiceReferenceDataSplit;
                    item.ReferenceInvoices = null;
                }
                #endregion
            }

            return groupInvoice;
        }

        public virtual string GetQueryTaxReportInfoInvoice02(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties"",
                                        a.""BudgetUnitCode""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                       SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice02Detail"" id INNER JOIN ""Invoice02Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()} ";
                            sql += $@" AND a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaBo} ";

                        sql += $@"  AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }
        public virtual string GetQueryTaxReportInfoInvoice03(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                       SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice03Detail"" id INNER JOIN ""Invoice03Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()} ";
                            sql += $@" AND a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaBo} ";

                        sql += $@"  AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }
        public virtual string GetQueryTaxReportInfoInvoice04(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice04Detail"" id INNER JOIN ""Invoice04Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()} ";
                       
                            sql += $@" AND a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaBo} ";

                        sql += $@"  AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }
        public virtual string GetQueryTaxReportInfoInvoice05(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        b.""TotalAmount"" AS ""TotalAmount"",
                                        b.""TotalVatAmount"" AS ""TotalVatAmount"",
                                        b.""TotalPaymentAmount"" AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""VatPercent"" AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        NULL AS ""Note"",
                                        a.""ExtraProperties"",
                                        a.""BudgetUnitCode""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
                                                    id.""VatPercent"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note"",
                                                    Sum(""Amount"") AS ""TotalAmount"",
		                                            Sum(""PaymentAmount"") AS ""TotalPaymentAmount"",
		                                            Sum(""VatAmount"") AS ""TotalVatAmount""

                                            FROM
                                                ""TicketDetail"" id INNER JOIN ""TicketHeader"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(
                                                    case
                                                        when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                                    else id.""InvoiceHeaderId""
                                                    end,
                                                    id.""VatPercent""
                                                )
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()} ";
                       
                            sql += $@" AND a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaBo} ";

                        sql += $@"  
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }
        #endregion

        #region lấy thông tin báo cáo XĂNG DẦU
        /// <summary>
        /// lấy thông tin báo cáo 01 - XĂNG DẦU
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="reportHeaderId"></param>
        /// <param name="input"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public virtual string GetQueryTaxReportInfoInvoice01XangDau(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                            a.""Id"" AS ""InvoiceHeaderId"",
                            a.""TemplateNo"",
                            a.""SerialNo"",
                            NULL AS ""ReceiverName"",
                            a.""BuyerFullName"",
                            NULL AS ""BuyerName"",
                            a.""BuyerTaxCode"",
                            a.""InvoiceNo"",
                            a.""Number"",
                            a.""ExchangeRate"",
                            a.""InvoiceDate"",
                            TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                            CASE 
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                WHEN (b.""TotalDiscountAmountBeforeTaxDetail"" = 0 AND a.""TotalDiscountAmountBeforeTax"" != 0) THEN (b.TotalAmount - a.""TotalDiscountAmountBeforeTax"")
                            ELSE b.TotalAmount END AS ""TotalAmount"",
                            CASE 
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                WHEN (b.""TotalDiscountAmountBeforeTaxDetail"" = 0 AND a.""TotalDiscountAmountBeforeTax"" != 0) THEN a.""TotalVatAmount""
                            ELSE b.TotalVatAmount END AS ""TotalVatAmount"",
                            CASE 
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                WHEN (b.""TotalDiscountAmountBeforeTaxDetail"" = 0 AND a.""TotalDiscountAmountBeforeTax"" != 0) THEN ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"")
                            ELSE ROUND(b.TotalPaymentAmount, a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                            CASE 
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.Moi.ToDisplayName()}'
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'{TaxReport01Status.Huy.ToDisplayName()}'
                                WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'{TaxReport01Status.ThayThe.ToDisplayName()}'
                                WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.DieuChinh.ToDisplayName()}'
                            ELSE null END AS ""Status"",
                            a.""InvoiceStatus"",
                            a.""InvoiceTemplateId"",
                            CASE 
                                WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                            ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                            CASE 
                                WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                            ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                            1 AS ""InvoiceType"",
                            b.""ProductName"",
                            b.""ProductCode"",
                            b.""UnitName"",
	                        b.""Quantity"",
                            b.""VatPercent"",
                            CASE 
                                WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                            ELSE b.""Note"" END AS ""Note"",
                            c.""InvoiceReferenceId"",
                            a.""ExtraProperties""
                        FROM ""Invoice01Header"" a 
                        LEFT JOIN (
                            SELECT
	                            ""InvoiceHeaderId"",
	                            ""VatPercent"",
	                            ""ProductCode"",
                                ""ProductName"",
		                        ""Quantity"",
		                        ""UnitName"",
		                        ""Note"",
		                        ""TotalAmount""  AS TotalAmount,
                                ""TotalDiscountAmountBeforeTaxDetail"",
                                ""TotalPaymentAmount"" AS TotalPaymentAmount,
                                ""TotalVatAmount"" AS TotalVatAmount
                            FROM
	                            (
	                            SELECT
			                        id.""InvoiceHeaderId"",

                                    ih.""InvoiceStatus"",
			                        CASE
                                        WHEN ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN NULL

                                        ELSE id.""VatPercent""

                                    END AS ""VatPercent"",
			                        ""Quantity"",
                                    ""ProductCode"",
			                        ""UnitName"",
			                        id.""ProductName"",
			                        id.""Note"",
			                        (id.""Amount"" - id.""DiscountAmountBeforeTax"") AS ""TotalAmount"",
			                        ""DiscountAmountBeforeTax"" AS ""TotalDiscountAmountBeforeTaxDetail"",
			                        ""PaymentAmount"" AS ""TotalPaymentAmount"",
			                        ""VatAmount"" AS ""TotalVatAmount""
                                FROM
                                    ""Invoice01Detail"" id
                                RIGHT JOIN ""Invoice01Header"" ih ON
                                    id.""InvoiceHeaderId"" = ih.""Id""
                                WHERE
                                    id.""TenantId"" = '{rawTenantId}'
                                )
                        ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceHeaderId"",
                                ""InvoiceReferenceId"",
                                ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                            FROM ""Invoice01Reference"" WHERE ""TenantId"" = '{rawTenantId}' 
                        ) c ON a.""Id"" = c.""InvoiceHeaderId"" 
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceHeaderId"",
                                ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                            FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                        ) e ON a.""Id"" = e.""InvoiceHeaderId""
                        LEFT JOIN (
                            SELECT 
                                ""InvoiceType"",
                                ""InvoiceHeaderId"",
                                ""Note"",
                                ""Status""
                            FROM ""TaxReport01Detail"" 
                            WHERE ""InvoiceType"" = 1 {condition} 
                        ) d ON a.""Id"" = d.""InvoiceHeaderId""
                        WHERE a.""TenantId"" = '{rawTenantId}'
                        AND a.""IsDeclared"" = 0
                        AND a.""Number"" IS NOT NULL
                        AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                        AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                        AND a.""InvoiceNo"" IS NOT NULL
                        AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                        AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                        ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }

        /// <summary>
        /// lấy thông tin báo cáo 02 - XĂNG DẦU
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="reportHeaderId"></param>
        /// <param name="input"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public virtual string GetQueryTaxReportInfoInvoice02XangDau(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                             WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.Moi.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'{TaxReport01Status.Huy.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'{TaxReport01Status.ThayThe.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.DieuChinh.ToDisplayName()}'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
                                        b.""UnitName"",
                                        b.""Quantity"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                       SELECT
                                                id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
                                                id.""ProductName"",
                                                id.""ProductCode"",
                                                id.""UnitName"",
                                                id.""Quantity"",
		                                        id.""Note""
                                            FROM
                                                ""Invoice02Detail"" id 
                                            INNER JOIN ""Invoice02Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }

        /// <summary>
        /// lấy thông tin báo cáo 03 - XĂNG DẦU
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="reportHeaderId"></param>
        /// <param name="input"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public virtual string GetQueryTaxReportInfoInvoice03XangDau(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.Moi.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'{TaxReport01Status.Huy.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'{TaxReport01Status.ThayThe.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.DieuChinh.ToDisplayName()}'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
                                        b.""UnitName"",
                                        b.""Quantity"",
                                        NULL AS ""VatPercent"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        c.""InvoiceReferenceId"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                       SELECT 
                                                id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
                                                id.""ProductName"",
                                                id.""ProductCode"",
                                                id.""UnitName"",
                                                id.""Quantity"",
		                                        id.""Note""
                                            FROM
                                                ""Invoice03Detail"" id INNER JOIN ""Invoice03Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                    AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }

        /// <summary>
        /// lấy thông tin báo cáo 04 - XĂNG DẦU
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="reportHeaderId"></param>
        /// <param name="input"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public virtual string GetQueryTaxReportInfoInvoice04XangDau(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.Moi.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'{TaxReport01Status.Huy.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'{TaxReport01Status.ThayThe.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.DieuChinh.ToDisplayName()}'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
                                        b.""UnitName"",
                                        b.""Quantity"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                                    id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
                                                    id.""ProductName"",
                                                    id.""ProductCode"",
                                                    id.""UnitName"",
                                                    id.""Quantity"",
		                                            id.""Note""
                                            FROM
                                                ""Invoice04Detail"" id INNER JOIN ""Invoice04Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()}
                                     AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy})
                                    AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }

        /// <summary>
        /// lấy thông tin báo cáo ticket - XĂNG DẦU
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="reportHeaderId"></param>
        /// <param name="input"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public virtual string GetQueryTaxReportInfoInvoice05XangDau(Guid tenantId, long? reportHeaderId, TaxReport01InfoPagedRequestDto input, string fromDate, string toDate)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var condition = "";
            if (reportHeaderId.HasValue)
                condition = $" AND \"TaxReportHeaderId\" = {reportHeaderId}";

            #region dataReport
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""ExchangeRate"",
                                        ""InvoiceDate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmountBeforeTax"" END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalVatAmount"" END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.Moi.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'{TaxReport01Status.Huy.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'{TaxReport01Status.ThayThe.ToDisplayName()}'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'{TaxReport01Status.DieuChinh.ToDisplayName()}'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
                                        b.""UnitName"",
                                        b.""Quantity"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT 
                                                    id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
                                                    id.""ProductName"",
                                                    id.""ProductCode"",
                                                    id.""UnitName"",
                                                    id.""Quantity"",
		                                            id.""Note""
                                            FROM
                                                ""TicketDetail"" id INNER JOIN ""TicketHeader"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND a.""IsDeclared"" = 0
                                    AND a.""Number"" IS NOT NULL
                                    AND a.""StatusTvan"" = {TvanStatus.UnSent.GetHashCode()} ";
                        
                            sql += $@" AND ((a.""SignStatus"" = {SignStatus.DaKy.GetHashCode()} AND a.""InvoiceStatus"" != {(short)InvoiceStatus.XoaHuy}) OR a.""InvoiceStatus"" = {(short)InvoiceStatus.XoaHuy}) ";
                        
                        sql += $@"  AND a.""InvoiceNo"" IS NOT NULL
                                    AND a.""InvoiceDate"" >= '{fromDate}' AND a.""InvoiceDate"" < '{toDate}'
                                    AND a.""ToCurrency"" = '{input.CurrencyUnit}'
                                    ORDER BY a.""InvoiceDate"",a.""Id""";

            #endregion
            return sql;
        }
        #endregion

        private KeyValuePair<DateTime, DateTime> GetCurrentRange(ReportType type, int index, int year)
        {
            return GetRangeOfTime(type, index, year);
        }

        private KeyValuePair<DateTime, DateTime> GetRangeOfTime(ReportType type, int index, int year)
        {
            var from = DateTime.Now;
            var to = DateTime.Now;

            if (type == ReportType.Month)
            {
                from = new DateTime(year, index, 1);
                to = from.AddMonths(1).AddDays(-1);
            }

            if (type == ReportType.Quarter)
            {
                var range = DateTimeExtension.GetRangeOfQuater(index, year);
                from = range.Key;
                to = range.Value;
            }

            return new KeyValuePair<DateTime, DateTime>(from, to);
        }
        #region Lenh Sql lay du lieu hd
        public virtual string QueryInvoice01(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";

            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        a.""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""InvoiceDate"",
                                        a.""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN a.""TotalAmount"" 
                                        ELSE TAX.""Amount"" END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN a.""TotalVatAmount"" 
                                        ELSE TAX.""VatAmount"" END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN a.""TotalPaymentAmount"" 
                                        ELSE ROUND(TAX.""Amount"" + TAX.""VatAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        1 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties"",
                                        a.""BudgetUnitCode""
                                    FROM ""Invoice01Header"" a 
                                    LEFT JOIN (
                                        SELECT
	                                        ""InvoiceHeaderId"",
	                                        ""VatPercent"",
	                                        CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP( ORDER BY ""ProductName"" ), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
	                                        CAST(SUBSTR(LISTAGG(""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP( ORDER BY ""Note"" ), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                        FROM
	                                        (
	                                        SELECT
			                                    id.""InvoiceHeaderId"",

                                                ih.""InvoiceStatus"",
			                                    CASE
                                                    WHEN ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN NULL

                                                    ELSE id.""VatPercent""

                                                END AS ""VatPercent"",
			                                    CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
			                                    CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice01Detail"" id
                                            RIGHT JOIN ""Invoice01Header"" ih ON
                                                id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'

                                            GROUP BY

                                                ""InvoiceHeaderId"",
			                                    ""VatPercent"",
			                                    ""InvoiceStatus"")
                                        GROUP BY
	                                        ""InvoiceHeaderId"",
	                                        ""VatPercent""
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 1 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""Amount"",
                                            ""VatAmount"",
                                            ""VatPercent""
                                        FROM ""Invoice01TaxBreakdown"" 
                                    ) TAX ON a.""Id"" = TAX.""InvoiceHeaderId"" AND b.""VatPercent"" = TAX.""VatPercent""
                                WHERE a.""TenantId"" = '{rawTenantId}'
                                AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }
        public virtual string QueryInvoice02(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        a.""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmount""  END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties"",
                                        a.""BudgetUnitCode""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                       SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice02Detail"" id RIGHT JOIN ""Invoice02Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }
        public virtual string QueryInvoice03(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                       SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice03Detail"" id RIGHT JOIN ""Invoice03Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }
        public virtual string QueryInvoice04(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT (case
                                            when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                            else id.""InvoiceHeaderId""
                                            end) ""InvoiceHeaderId"",
		                                            CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                            CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note""
                                            FROM
                                                ""Invoice04Detail"" id RIGHT JOIN ""Invoice04Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                            else id.""InvoiceHeaderId""
                                            end)
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }
        public virtual string QueryInvoice05(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        b.""TotalAmount"" AS ""TotalAmount"",
                                        b.""TotalVatAmount"" AS ""TotalVatAmount"",
                                        b.""TotalPaymentAmount"" AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""VatPercent"" AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        NULL AS ""Note"",
                                        a.""ExtraProperties"",
                                        a.""BudgetUnitCode""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT (
                                                case
                                                    when ih.""InvoiceStatus"" IN ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId"" 
                                                else id.""InvoiceHeaderId""
                                                end) ""InvoiceHeaderId"",
                                                id.""VatPercent"",
		                                        CAST(SUBSTR(LISTAGG(""ProductName"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY ""ProductName""), 1, 500) AS NVARCHAR2(500)) AS ""ProductName"",
		                                        CAST(SUBSTR(LISTAGG(id.""Note"", ';' ON OVERFLOW TRUNCATE) WITHIN GROUP(ORDER BY id.""Note""), 1, 255) AS NVARCHAR2(255)) AS ""Note"",
                                                Sum(""Amount"") AS ""TotalAmount"",
		                                        Sum(""PaymentAmount"") AS ""TotalPaymentAmount"",
		                                        Sum(""VatAmount"") AS ""TotalVatAmount""
                                            FROM
                                                ""TicketDetail"" id RIGHT JOIN ""TicketHeader"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                                group by(case
                                                            when ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) then id.""InvoiceHeaderId""
                                                        else id.""InvoiceHeaderId""
                                                        end,
                                                        id.""VatPercent"")
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }
        #endregion


        #region QUERY HĐ XĂNG DẦU
        public virtual string QueryInvoice01XangDau(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";

            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        a.""TemplateNo"",
                                        a.""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        a.""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        a.""BuyerTaxCode"",
                                        a.""InvoiceNo"",
                                        a.""Number"",
                                        a.""InvoiceDate"",
                                        a.""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                            WHEN (b.""TotalDiscountAmountBeforeTaxDetail"" = 0 AND a.""TotalDiscountAmountBeforeTax"" != 0) THEN (b.TotalAmount - a.""TotalDiscountAmountBeforeTax"")
                                        ELSE b.TotalAmount END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                            WHEN (b.""TotalDiscountAmountBeforeTaxDetail"" = 0 AND a.""TotalDiscountAmountBeforeTax"" != 0) THEN a.""TotalVatAmount""
                                        ELSE b.TotalVatAmount END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                            WHEN (b.""TotalDiscountAmountBeforeTaxDetail"" = 0 AND a.""TotalDiscountAmountBeforeTax"" != 0) THEN ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"")
                                        ELSE ROUND(b.TotalPaymentAmount, a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        a.""InvoiceStatus"",
                                        a.""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        1 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
                                        b.""Quantity"",
                                        b.""UnitName"",
                                        b.""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice01Header"" a 
                                    LEFT JOIN (
                                        SELECT
	                                        ""InvoiceHeaderId"",
	                                        ""VatPercent"",
	                                        ""ProductName"",
                                            ""ProductCode"",
                                            ""Quantity"",
                                            ""UnitName"",
	                                        ""Note"",
	                                        ""TotalAmount"" AS TotalAmount,
                                            ""TotalDiscountAmountBeforeTaxDetail"" AS ""TotalDiscountAmountBeforeTaxDetail"",
		                                    ""TotalPaymentAmount"" AS TotalPaymentAmount,
		                                    ""TotalVatAmount"" AS TotalVatAmount
                                        FROM
	                                        (
	                                        SELECT
			                                    id.""InvoiceHeaderId"",
                                                ih.""InvoiceStatus"",
			                                    CASE
                                                    WHEN ih.""InvoiceStatus"" IN({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN NULL

                                                    ELSE id.""VatPercent""

                                                END AS ""VatPercent"",
                                                id.""Quantity"",
                                                id.""ProductCode"",
                                                id.""UnitName"",
			                                    id.""ProductName"",
			                                    id.""Note"",
			                                    (id.""Amount"" - id.""DiscountAmountBeforeTax"") AS ""TotalAmount"",
                                                ""DiscountAmountBeforeTax"" AS ""TotalDiscountAmountBeforeTaxDetail"",
			                                    ""PaymentAmount"" AS ""TotalPaymentAmount"",
			                                    ""VatAmount"" AS ""TotalVatAmount""
                                            FROM
                                                ""Invoice01Detail"" id
                                            RIGHT JOIN ""Invoice01Header"" ih ON
                                                id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                        )
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice01ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 1 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                WHERE a.""TenantId"" = '{rawTenantId}'
                                AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }

        public virtual string QueryInvoice02XangDau(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        a.""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmount""  END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        2 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
                                        b.""UnitName"",
                                        b.""Quantity"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice02Header"" a 
                                    LEFT JOIN (
                                       SELECT
                                                id.""InvoiceHeaderId"" as ""InvoiceHeaderId"",
		                                        id.""ProductName"",
                                                id.""ProductCode"",
                                                id.""UnitName"",
                                                id.""Quantity"",
		                                        id.""Note""
                                            FROM
                                                ""Invoice02Detail"" id RIGHT JOIN ""Invoice02Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'

                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice02ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 2 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }

        public virtual string QueryInvoice03XangDau(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        ""ReceiverName"",
                                        ""ReceiverFullName"" AS ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""ReceiverTaxCode"" AS ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        3 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
		                                b.""Quantity"",
		                                b.""UnitName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice03Header"" a 
                                    LEFT JOIN (
                                       SELECT
                                                id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
		                                        id.""ProductName"",
		                                        id.""ProductCode"",
		                                        id.""Quantity"",
		                                        id.""UnitName"",
		                                        id.""Note""
                                            FROM
                                                ""Invoice03Detail"" id RIGHT JOIN ""Invoice03Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'
                                               
                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice03ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 3 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }

        public virtual string QueryInvoice04XangDau(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" END AS ""TotalAmount"",
                                        0 AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        4 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
		                                b.""Quantity"",
		                                b.""UnitName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""Invoice04Header"" a 
                                    LEFT JOIN (
                                        SELECT 
                                                id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
		                                        id.""ProductName"",
		                                        id.""ProductCode"",
		                                        id.""Quantity"",
		                                        id.""UnitName"",
		                                        id.""Note""
                                            FROM
                                                ""Invoice04Detail"" id RIGHT JOIN ""Invoice04Header"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'

                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04Reference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""Invoice04ReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 4 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }

        public virtual string QueryInvoice05XangDau(TaxReport01HeaderEntity taxReportHeader, TaxReport01DetailMappingEntity taxReportDetail)
        {
            var condition = $" AND \"TaxReportHeaderId\" = {taxReportHeader.Id}";
            var rawTenantId = OracleExtension.ConvertGuidToRaw(taxReportHeader.TenantId);
            var sql = $@"SELECT 
                                        a.""Id"" AS ""InvoiceHeaderId"",
                                        ""TemplateNo"",
                                        ""SerialNo"",
                                        NULL AS ""ReceiverName"",
                                        ""BuyerFullName"",
                                        NULL AS ""BuyerName"",
                                        ""BuyerTaxCode"",
                                        ""InvoiceNo"",
                                        ""Number"",
                                        ""InvoiceDate"",
                                        ""ExchangeRate"" AS ""ExchangeRate"",
                                        TO_CHAR(""InvoiceDate"", 'DD/MM/YYYY') AS ""InvoiceDateDisplay"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalAmount"" - a.""TotalDiscountAmountBeforeTax"" END AS ""TotalAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE a.""TotalVatAmount"" END AS ""TotalVatAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()} THEN 0
                                        ELSE ROUND(a.""TotalPaymentAmount"", a.""RoundingCurrency"") END AS ""TotalPaymentAmount"",
                                        CASE 
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.Goc.GetHashCode()}, {InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.BiDieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.BiThayThe.GetHashCode()}, {InvoiceStatus.BiDieuChinh.GetHashCode()}) THEN N'Mới'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.XoaBo.GetHashCode()}, {InvoiceStatus.XoaHuy.GetHashCode()}) THEN N'Hủy'
                                            WHEN ""InvoiceStatus"" = {InvoiceStatus.ThayThe.GetHashCode()} THEN N'Thay thế'
                                            WHEN ""InvoiceStatus"" in ({InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}, {InvoiceStatus.DieuChinhTangGiam.GetHashCode()}, {InvoiceStatus.DieuChinh.GetHashCode()}) THEN N'Điều chỉnh'
                                        ELSE null END AS ""Status"",
                                        ""InvoiceStatus"",
                                        ""InvoiceTemplateId"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceData"" IS NOT NULL THEN c.""InvoiceReferenceData""
                                        ELSE e.""InvoiceReferenceData"" END AS ""InvoiceReferenceData"",
                                        CASE 
                                            WHEN c.""InvoiceReferenceDataSplit"" IS NOT NULL THEN c.""InvoiceReferenceDataSplit""
                                        ELSE e.""InvoiceReferenceDataSplit"" END AS ""InvoiceReferenceDataSplit"",
                                        5 AS ""InvoiceType"",
                                        b.""ProductName"",
                                        b.""ProductCode"",
		                                b.""Quantity"",
		                                b.""UnitName"",
                                        NULL AS ""VatPercent"",
                                        c.""InvoiceReferenceId"",
                                        CASE 
                                            WHEN d.""Note"" IS NOT NULL THEN d.""Note""
                                        ELSE b.""Note"" END AS ""Note"",
                                        a.""ExtraProperties""
                                    FROM ""TicketHeader"" a 
                                    LEFT JOIN (
                                        SELECT
                                                    id.""InvoiceHeaderId"" AS ""InvoiceHeaderId"",
		                                            id.""ProductName"",
		                                            id.""ProductCode"",
		                                            id.""Quantity"",
		                                            id.""UnitName"",
		                                            id.""Note""
                                            FROM
                                                ""TicketDetail"" id RIGHT JOIN ""TicketHeader"" ih
                                                ON id.""InvoiceHeaderId"" = ih.""Id""
                                            WHERE
                                                id.""TenantId"" = '{rawTenantId}'

                                    ) b ON a.""Id"" = b.""InvoiceHeaderId"" and a.""InvoiceStatus"" != {InvoiceStatus.DieuChinhDinhDanh.GetHashCode()}
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""InvoiceReferenceId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReference"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) c ON a.""Id"" = c.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceHeaderId"",
                                            ""TemplateNoReference"" || '_' || ""SerialNoReference""  || '_' || ""InvoiceNoReference"" || '_'  || to_char( ""InvoiceDateReference"",'DD-MM-YYYY') ||'_' || ""InvoiceStatus"" AS ""InvoiceReferenceDataSplit"",
                                            ""TemplateNoReference""  || ""SerialNoReference""  || ""InvoiceNoReference"" AS ""InvoiceReferenceData""
                                        FROM ""TicketReferenceOldDecree"" WHERE ""TenantId"" = '{rawTenantId}'
                                    ) e ON a.""Id"" = e.""InvoiceHeaderId""
                                    LEFT JOIN (
                                        SELECT 
                                            ""InvoiceType"",
                                            ""InvoiceHeaderId"",
                                            ""Note"",
                                            ""Status""
                                        FROM ""TaxReport01Detail"" 
                                        WHERE ""InvoiceType"" = 5 {condition} 
                                    ) d ON a.""Id"" = d.""InvoiceHeaderId""
                                    WHERE a.""TenantId"" = '{rawTenantId}'
                                    AND ""Id"" IN ({taxReportDetail.InvoiceIds})
                                    ORDER BY a.""InvoiceDate"",a.""Id""";
            return sql;
        }
        #endregion
    }


}
