using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared.Factory;
using Core.Shared.Services;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using Nest;
using System;
using System.Reflection;
using VnisCore.Dashboard.Application.Interface;
using VnisCore.Dashboard.Application.Services;

namespace VnisCore.Dashboard.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(AbpAutoMapperModule)
    )]
    public class VnisCoreDashboardApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddProfile<VnisCoreDashboardApplicationAutoMapperProfile>(validate: false);
            });
            //context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreDashboardApplicationModule).GetTypeInfo().Assembly);
            //IFactory
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));

            //var configuration = context.Services.GetConfiguration();

            context.Services.AddAutoMapperObjectMapper<VnisCoreDashboardApplicationModule>();
            //Configure<AbpAutoMapperOptions>(options =>
            //{
            //    options.AddMaps<VnisCoreExportApplicationAutoMapperProfile>(validate: false);
            //});
            //context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            //context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            //context.Services.AddMediatR(typeof(VnisCoreExportApplicationModule).GetTypeInfo().Assembly);


            //Statistic service
            context.Services.AddScoped<ISummaryServices, SummaryServices>();
            context.Services.AddScoped<ISettingService, SettingService>();

            // elastic
            var configuration = context.Services.GetConfiguration();
            var url = configuration["Elasticsearch:Url"];
            var defaultIndex = configuration["Elasticsearch:Index"];

            var settings = new ConnectionSettings(new Uri(url))
                .DefaultIndex(defaultIndex)
                .EnableDebugMode();

            //AddDefaultMappings(settings);

            var client = new ElasticClient(settings);
            context.Services.AddSingleton<IElasticClient>(client);
        }
    }
}