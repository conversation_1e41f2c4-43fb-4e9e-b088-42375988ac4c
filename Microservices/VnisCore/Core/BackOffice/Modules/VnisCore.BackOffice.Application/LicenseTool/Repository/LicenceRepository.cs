using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Licenses;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.BackOffice.Application.LicenseTool.Repository
{
    public class LicenceRepository : EfCoreRepository<VnisCoreOracleDbContext, LicenseEntity, long>, ILicenceRepository
    {
        public LicenceRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<List<LicenseEntity>> GetList(List<Guid> tenants)
        {
            var dbContext = await GetDbContextAsync();
            var result = dbContext
                        .Set<LicenseEntity>()
                        .Where(x => tenants.Contains(x.TenantId))
                        .Select(x => new LicenseEntity()
                        {
                            LicenseKey = x.LicenseKey
                        }).ToList();

            return result;
        }

        public async Task<List<LicenseEntity>> GetListByLicenseKey(List<Guid> licenseKey)
        {
            var dbContext = await GetDbContextAsync();
            var result = dbContext
                        .Set<LicenseEntity>()
                        .Where(x => licenseKey.Contains(x.LicenseKey) && x.IsDeleted != true)
                        .Select(x => x).ToList();

            return result;
        }
    }
}
