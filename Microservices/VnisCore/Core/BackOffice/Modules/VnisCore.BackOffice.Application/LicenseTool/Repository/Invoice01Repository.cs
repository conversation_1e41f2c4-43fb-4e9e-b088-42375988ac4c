using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;

using Microsoft.EntityFrameworkCore;

using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.BackOffice.Application.LicenseTool.Dto;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.BackOffice.Application.LicenseTool.Repository
{
    public class Invoice01Repository : EfCoreRepository<VnisCoreOracleDbContext, Invoice01HeaderEntity, long>, IInvoice01Repository
    {
        public Invoice01Repository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider)
        : base(dbContextProvider)
        {

        }
        public async Task<long> CountBySellerTaxCode(string sellerTaxCode)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Set<Invoice01HeaderEntity>()
                .Where(x=>x.SellerTaxCode == sellerTaxCode && x.IsDeleted == false)
                .CountAsync();
        }
    }
}
