using Core.Shared.Constants;
using MediatR;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.MediaFile.Application.Models.Responses;

namespace VnisCore.MediaFile.Application.Models.Requests
{
    public class UploadInvoice01DocumentRequestModel : IRequest<UploadInvoice01DocumentResponseModel>
    {
        public IFormFile File { get; set; }
        public DocumentTemplateType Type { get; set; }
        public Guid TenantId { get; set; }
        public string SecretKey { get; set; }
        public string TaxCode { get; set; }
    }
    public class UploadInvoice01DocumentModel
    {
        public string SecretKey { get; set; }
        public DocumentTemplateType Type { get; set; }
        public string TaxCode { get; set; }
    }
}