{
  "App": {
    "SelfUrl": "https://*********:6063",
    "CorsOrigins": "https://invoice-mass.vnpaytest.vn,https://invoice-mass-gateway.vnpaytest.vn,https://invoice-mass-auth.vnpaytest.vn,https://invoice-mass-api.vnpaytest.vn",
    "RedirectAllowedUrls": "https://invoice-mass.vnpaytest.vn"
  },
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "Service": {
    "Name": "VnisCore.PITDeductionDocument.Host",
    "Title": "VnisCore.PITDeductionDocument.Host",
    "BaseUrl": "PITDeductionDocument",
    "AuthApiName": "VnisCore.PITDeductionDocument.Host"
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "************:6380,************:6381,************:6382,allowAdmin=true,password=qIcY4mSSZVmhaOC,serviceName=redismaster,defaultDatabase=14"
    // "Configuration": "************,allowAdmin=true,password=vnpayredis@123,defaultDatabase=14"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "************",
        "Port": 5672,
        "UserName": "admin",
        "Password": "admin@123",
        "VirtualHost": "/massv5-invoice"
      }
    },
    "EventBus": {
      "ClientName": "einvoice.PITDeductionDocument",
      "ExchangeName": "einvoice"
    }
  },
  "AuthServer": {
    "Authority": "https://invoice-mass-auth.vnpaytest.vn/",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_Swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "Logging": {
    "RootFolder": {
      "Folder": "/var/logs/invoice"
    }
  },
  "Serilog": {
    "Using": [ "Serilog.Sinks.PersistentFile" ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Information",
        "Microsoft.EntityFrameworkCore": "Warning"
      }
    },
    "Enrich": [
      "FromLogContext",
      "WithCorrelationId"
    ],
    "WriteTo": [
      {
        "Name": "PersistentFile",
        "Args": {
          "path": "/var/logs/invoice/PITDeductionDocument/log.txt", // "D:\\Logs\\backoffice\\log.txt", // Ðu?ng d?n file log
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{CorrelationId}] {Message:lj}{NewLine}{Exception:lj}", // M?u template
          "rollingInterval": "Day", // Xác d?nh kho?ng th?i gian file log m?i s? du?c t?o (ví d?: "Day" - hàng ngày, "Hour" - hàng gi?)
          "fileSizeLimitBytes": 52428800, // Gi?i h?n kích thu?c c?a file log tru?c khi nó du?c roll sang file m?i. Ví d?: 10485760 bytes (10 MB).
          "retainedFileCountLimit": null, // S? lu?ng file log cu s? du?c gi? l?i tru?c khi xóa
          "rollOnFileSizeLimit": true, // N?u true, khi kích thu?c file log vu?t quá fileSizeLimitBytes, m?t file log m?i s? du?c t?o
          "preserveLogFilename": true, // N?u true, tên file log ban d?u s? du?c gi? nguyên, và các file roll s? du?c thêm ch? s? (ví d?: log-001.txt)
          "shared": true, // N?u true, nhi?u ti?n trình có th? ghi vào cùng m?t file log
          "flushToDiskInterval": "00:00:10" // Kho?ng th?i gian gi?a các l?n ghi log ra dia. Ví d?: "00:00:10" là 10 giây.
        }
      },
      {
        "Name": "PersistentFile",
        "Args": {
          "path": "/var/logs/invoice/PITDeductionDocument/log.json",
          "formatter": "Serilog.Formatting.Json.JsonFormatter, Serilog",
          "persistentFileRollingInterval": "Day",
          "rollingInterval": "Day",
          "fileSizeLimitBytes": 52428800,
          "retainedFileCountLimit": null,
          "preserveLogFilename": true,
          "rollOnFileSizeLimit": true,
          "shared": true
        }
      }
    ]
  }
}