using Core.Caching;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Invoices;
using Dapper;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace VnisCore.PITDeductionDocument.Application.Business
{
    public interface ICachedInvoiceBusiness
    {
        Task<List<ReadTemplateModel>> GetOrSetCachedReadTemplateAsync(Guid tenantId, Guid userId, short templateNo, VnisType vnisType);
    }
    public class CachedInvoiceBusiness : ICachedInvoiceBusiness
    {
        private readonly IConfiguration _configuration;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public CachedInvoiceBusiness(
            IConfiguration configuration,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _configuration = configuration;
            _appFactory = appFactory;
            _localizer = localizer;
        }
        public async Task<List<ReadTemplateModel>> GetOrSetCachedReadTemplateAsync(Guid tenantId, Guid userId, short templateNo, VnisType vnisType)
        {
            var cacheKey = tenantId.ToString() + "-" + userId.ToString() + "-" + templateNo.ToString();
            var cache = _appFactory.GetServiceDependency<IDistributedCache<List<ReadTemplateModel>>>();
            var isTimeCaching = int.TryParse(_configuration.GetSection("Settings:MaxTimeCaching").Value, out int timeCaching);
            if (!isTimeCaching)
                timeCaching = 30;

            return await cache.GetOrAddAsync(
                        cacheKey, // cacheKey
                        async () => await GetInvoiceTemplateAsync(tenantId, userId, vnisType),
                        () => new DistributedCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(timeCaching)
                        });
        }
        private async Task<List<ReadTemplateModel>> GetInvoiceTemplateAsync(Guid tenantId, Guid userId, VnisType invoiceType)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var sqlReadTemplates = $@"  SELECT a.""Id"", a.""Name"", a.""SerialNo"", a.""TemplateNo"" 
                                        FROM ""InvoiceTemplate"" a
                                        INNER JOIN ""UserReadTemplate"" b on a.""Id"" = b.""InvoiceTemplateId""
                                        WHERE a.""TenantId"" = '{rawTenantId}' 
                                            AND b.""UserId"" = '{rawUserId}' 
                                            AND a.""Type"" = {invoiceType.GetHashCode()} 
                                            AND a.""IsDeleted"" = 0
                                    ";

            return (await _appFactory.VnisCoreOracle.Connection.QueryAsync<ReadTemplateModel>(sqlReadTemplates.ToString())).ToList();
        }
    }
}
