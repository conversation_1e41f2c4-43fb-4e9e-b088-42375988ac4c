using Core.Shared.Attributes;
using Core.Shared.Attributes.PITDeductionDocument;
using System;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.PITDeductionDocument.Application.Model.Requests
{
    public class CreatePITDeductionDocumentRequest : BasePITDeductionDocumentRequest
    {
        #region Thông tin tờ khai
        /// <summary>
        /// Id Template
        /// </summary>
        public long TemplateId { get; set; }
        /// <summary>
        /// Mẫu số
        /// </summary>
        public string TemplateNo { get; set; }

        /// <summary>
        /// Ng<PERSON>y lập chứng từ
        /// </summary>
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Trạng thái chứng từ
        /// </summary>
        public short? DocumentStatus { get; set; }

        /// <summary>
        /// Trạng thái ký
        /// </summary>
        public short SignStatus { get; set; }

        /// <summary>
        /// <PERSON><PERSON> chứng từ dạng số 
        /// </summary>
        public long Number { get; set; }

        /// <summary>
        /// S<PERSON> chứng từ dạng 7 ký tự
        /// </summary>
        public string DocumentNumber { get; set; }

        /// <summary>
        /// Tháng nộp thuế
        /// </summary>
        public short PITDeductionDocumentMonth { get; set; }

        #endregion
        #region Thông tin tổ chức chi trả thu nhập
        /// <summary>
        /// Mã số thuế tổ chức chi trả thu nhập
        /// </summary>
        [MaxLength(14, ErrorMessage = "Mã số thuế tổ chức chi trả tối đa 14 ký tự")]
        //[Taxcode(ErrorMessage = "Mã số thuế tổ chức chi trả không đúng định dạng")]
        public string OrganizationTaxCode { get; set; }

        /// <summary>
        /// Tên tổ chức chi trả thu nhập
        /// </summary>
        [MaxLength(400, ErrorMessage = "Tên tổ chức trả thu nhập trả tối đa 400 ký tự")]
        public string OrganizationLegalName { get; set; }

        /// <summary>
        /// Địa chỉ tổ chức chi trả thu nhập
        /// </summary>
        [MaxLength(400, ErrorMessage = "Địa chỉ tổ chức trả thu nhập tối đa 400 ký tự")]
        public string OrganizationAddress { get; set; }

        /// <summary>
        /// Số điện thoại tổ chức chi trả thu nhập
        /// </summary>
        [MaxLength(20, ErrorMessage = "Số điện thoại tổ chức trả thu nhập tối đa 20 ký tự")]
        //[MinLength(10, ErrorMessage = "Số điện thoại tổ chức chi trả tối thiểu 10 ký tự")]
        public string OrganizationPhone { get; set; }

        /// <summary>
        /// Email tổ chức chi trả thu nhập
        /// </summary>
        [MaxLength(250, ErrorMessage = "Email tổ chức trả thu nhập tối đa 250 ký tự")]
        [PITEmail(ErrorMessage = "Email tổ chức chi trả không đúng định dạng")]
        public string OrganizationEmail { get; set; }

        #endregion
        #region Thông tin người nộp thuế        
        /// <summary>
        /// Mã số thuế người nộp thuế
        /// </summary>
        [StringLength(14, ErrorMessage = "Mã số thuế người nộp thuế chỉ được nhập tối đa 14 ký tự")]
        [TaxCode(ErrorMessage = "Mã số thuế thông tin người nộp thuế không đúng định dạng")]
        public string TaxPayerTaxCode { get; set; }

        /// <summary>
        /// Số CMND/CCCD/Hộ chiếu người nộp thuế
        /// </summary>
        public string TaxPayerIDNumber { get; set; }

        /// <summary>
        /// Loại cư trú người nộp thuế
        /// </summary>
        [Required(ErrorMessage = "Loại cư trú không được để trống")]
        public short TaxPayerResidenceType { get; set; }

        /// <summary>
        /// Email người nộp thuế
        /// </summary>
        [MaxLength(50, ErrorMessage = "Email người nộp thuế chỉ được nhập tối đa 50 ký tự")]
        [PITEmail(ErrorMessage = "Email người nhận không đúng định dạng")]
        public string TaxPayerEmail { get; set; }

        #endregion
        #region Thong tin thuế thu nhập cá nhân khấu trừ
        /// <summary>
        /// Tên khoản thu nhập
        /// </summary>
        [MaxLength(250, ErrorMessage = "Thông tin loại thu nhập khai báo khấu trừ thuế chỉ được nhập tối đa 250 ký tự")]
        [Required(ErrorMessage = "Vui lòng nhập tên khoản thu nhập")]
        public string IncomeName { get; set; }

        /// <summary>
        /// Từ tháng thu nhập
        /// </summary>
        [Required(ErrorMessage = "Tháng thu nhập đầu không được để trống")]
        public short FromIncomeMonth { get; set; }

        /// <summary>
        /// Đến tháng thu nhập
        /// </summary>
        [Required(ErrorMessage = "Tháng thu nhập cuối không được để trống")]
        public short ToIncomeMonth { get; set; }

        /// <summary>
        /// Tổng thu nhập chịu thuế phải khấu trừ
        /// </summary>
        [Required(ErrorMessage = "Vui lòng nhập Tổng thu nhập chịu thuế phải khấu trừ")]
        [NewRangeNumber(21, 6, ErrorMessage = "Tổng thu nhập chịu thuế phải khấu trừ nằm ngoài phạm vi cho phép")]
        public decimal TotalIncomeAmount { get; set; }

        /// <summary>
        /// Năm trả thu nhập
        /// </summary>
        [Required(ErrorMessage = "Năm trả thu nhập không được để trống")]
        public int IncomeYear { get; set; }

        /// <summary>
        /// Tổng thu nhập tính thuế
        /// </summary>
        [Required(ErrorMessage = "Vui lòng nhập Tổng thu nhập tính thuế")]
        [NewRangeNumber(21, 6, ErrorMessage = "Tổng thu nhập tính thuế nhập nằm ngoài phạm vi cho phép")]
        public decimal TotalTaxableIncome { get; set; }

        /// <summary>
        /// Khoản đóng bảo hiểm bắt buộc
        /// </summary>
        [Required(ErrorMessage = "Vui lòng nhập Khoản đóng bảo hiểm bắt buộc")]
        [NewRangeNumber(21, 6, ErrorMessage = "Khoản đóng bảo hiểm bắt buộc nằm ngoài phạm vi cho phép")]
        public decimal InsuranceFee { get; set; }

        /// <summary>
        /// Số thuế thu nhập cá nhân đã khấu trừ
        /// </summary>
        [Required(ErrorMessage = "Vui lòng nhập Số thuế thu nhập cá nhân đã khấu trừ")]
        [NewRangeNumber(21, 6, ErrorMessage = "Số thuế thu nhập cá nhân đã khấu trừ nằm ngoài phạm vi cho phép")]
        public decimal TotalPersonalIncomeDeducted { get; set; }

        /// <summary>
        /// Tổng thu nhập còn được nhân
        /// </summary>
        [NewRangeNumber(21, 6, ErrorMessage = "Tổng thu nhập còn được nhân nằm ngoài phạm vi cho phép")]
        public decimal TotalIncomeAfterTax { get; set; }
        #endregion

        public long ReferenceId { get; set; }

        public string ReferenceSerialNo { get; set; }

        public string ReferenceNumber { get; set; }

        public string AmendmentReason { get; set; }
    }
}
