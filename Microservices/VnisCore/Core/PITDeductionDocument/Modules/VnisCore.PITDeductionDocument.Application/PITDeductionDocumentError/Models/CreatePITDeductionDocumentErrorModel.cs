using Core.Shared.Attributes;
using Core.Shared.Constants;
using Core.Tvan.Constants;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.PITDeductionDocument.Application.PITDeductionDocumentError.Models
{
    public class CreatePITDeductionDocumentErrorModel
    {
        /// <summary>
        /// loại chứng từ
        /// dùng để xác định loại chứng từ khấu trừ thuế TNCN
        /// </summary>
        [Required(ErrorMessage = "Loại chứng từ không được để trống")]
        public VnisType InvoiceType { get; set; }

        /// <summary>
        /// Mã cơ quan thuế quản lý
        /// </summary>
        [Required(ErrorMessage = "Mã cơ quan thuế quản lý không được để trống")]
        [MaxLength(5, ErrorMessage = "Mã cơ quan thuế quản lý dài tối đa 5 ký tự")]
        public string CodeTaxDepartment { get; set; }

        /// <summary>
        /// Tên cơ quan thuế quản lý
        /// </summary>
        [Required(ErrorMessage = "Tên cơ quan thuế quản lý không được để trống")]
        [MaxLength(100, ErrorMessage = "Tên cơ quan thuế quản lý dài tối đa 100 ký tự")]
        public string TaxDepartment { get; set; }

        /// <summary>
        /// Địa danh
        /// </summary>
        [Required(ErrorMessage = "Địa danh không được để trống")]
        [MaxLength(50, ErrorMessage = "Địa danh dài tối đa 50 ký tự")]
        public string PlaceName { get; set; }

        /// <summary>
        /// Mã đơn vị dự toán
        /// </summary>
        [MaxLength(7, ErrorMessage = "Mã đơn vị dự toán dài tối đa 7 ký tự")]
        public string BudgetUnitCode { get; set; }

        /// <summary>
        /// Ngày thông báo
        /// </summary>
        [Required(ErrorMessage = "Ngày thông báo không được để trống")]
        public DateTime AnnouncementDate { get; set; }

        /// <summary>
        /// Email
        /// </summary>
        [MaxLength(400, ErrorMessage = "Email dài tối đa 400 ký tự")]
        public string Emails { get; set; }

        /// <summary>
        /// Danh sách chứng từ sai sót
        /// </summary>
        [Required(ErrorMessage = "Danh sách chứng từ sai sót không được để trống")]
        public List<PITDeductionDocumentTvanErrorRequestDataModel> PITDeductionDocumentErrors { get; set; }
    }

    public class PITDeductionDocumentTvanErrorRequestDataModel
    {
        /// <summary>
        /// Id chứng từ
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// thứ tự
        /// </summary>
        public int Index { get; set; }

        public string TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu mẫu chứng từ
        /// </summary>
        [SerialNo(ErrorMessage = "Định dạng ký hiệu mẫu số chứng từ không đúng")]
        public string SerialNo { get; set; }

        public long Number { get; set; }

        /// <summary>
        /// Số chứng từ
        /// </summary>
        public string DocumentNumber { get; set; }

        /// <summary>
        /// Mã CQT cấp
        /// </summary>
        public string VerificationCode { get; set; }

        /// <summary>
        /// Ngày lập chứng từ
        /// </summary>
        [Required(ErrorMessage = "Ngày lập chứng từ không được để trống")]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Lý do sai sót
        /// </summary>
        // [Required(ErrorMessage = "Lý do sai sót không được để trống")]
        [MaxLength(255, ErrorMessage = "Lý do sai sót dài tối đa 255 ký tự")]
        public string Reason { get; set; }

        /// <summary>
        /// Loại chứng từ
        /// </summary>
        [Required(ErrorMessage = "Loại chứng từ không được để trống")]
        public VnisType InvoiceType { get; set; }

        public long PITDeductionDocumentId { get; set; }
    }

    public class PITDeductionDocumentTvanErrorRequestModel
    {
        /// <summary>
        /// Ngày thông báo
        /// </summary>
        [Required(ErrorMessage = "Ngày thông báo không được để trống")]
        public DateTime AnnouncementDate { get; set; }

        /// <summary>
        /// Danh sách chứng từ sai sót
        /// </summary>
        [Required(ErrorMessage = "Danh sách chứng từ sai sót không được để trống")]
        public List<PITDeductionDocumentTvanErrorRequestDataModel> PITDeductionDocumentErrors { get; set; }
    }
}
