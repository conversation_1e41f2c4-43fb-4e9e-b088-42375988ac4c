using System;

namespace VnisCore.PITDeductionDocument.Infrastructure.Models.Reponse
{
    /// <summary>
    /// Thông tin tra cứu chứng từ
    /// </summary>
    public class PortalPITDeductionDocumentResponse
    {
        public long Id { get; set; }

        /// <summary>
        /// Id Template
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
        /// Mẫu số
        /// </summary>
        public string TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu đầy đủ
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Ng<PERSON>y lập
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// Ng<PERSON>y chứng từ
        /// </summary>
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// Trạng thái chứng từ
        /// </summary>
        public short DocumentStatus { get; set; }

        /// <summary>
        /// Trạng thái ký
        /// </summary>
        public short SignStatus { get; set; }

        /// <summary>
        /// Số chứng từ dạng số 
        /// </summary>
        public long Number { get; set; }

        /// <summary>
        /// Số chứng từ dạng 7 ký tự
        /// </summary>
        public string DocumentNumber { get; set; }

        /// <summary>
        /// Năm trả thu nhập
        /// </summary>
        public int IncomeYear { get; set; }

        /// <summary>
        /// Họ và tên người nộp thuế
        /// </summary>
        public string PayerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người nộp thuế
        /// </summary>
        public string PayerTaxCode { get; set; }
        /// <summary>
        /// Email người nộp thuế
        /// </summary>
        public string TaxPayerEmail { get; set; }
        /// <summary>
        /// Số CMND/CCCD/Hộ chiếu người nộp thuế
        /// </summary>
        public string PayerIdentificationNumber { get; set; }

        /// <summary>
        /// Từ tháng thu nhập
        /// </summary>
        public short FromIncomeMonth { get; set; }

        /// <summary>
        /// Đến tháng thu nhập
        /// </summary>
        public short ToIncomeMonth { get; set; }

        #region Thông tin tổ chức chi trả thu nhập
        /// <summary>
        /// Mã số thuế tổ chức chi trả thu nhập
        /// </summary>
        public string OrganizationTaxCode { get; set; }

        /// <summary>
        /// Mã số thuế tổ chức chi trả thu nhập
        /// </summary>
        public string OrganizationLegalName { get; set; }
        #endregion

        #region Tra cứu chứng từ
        public string TransactionId { get; set; }
        #endregion
    }
}
