using System.Reflection;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared.Factory;
using Dapper;
using System;
using Core.Shared.DapperSqlTypeHandler;
using System.Collections.Generic;
using VnisCore.Core.Oracle.Domain;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.PITDeductionDocument.Infrastructure.IRepository;
using VnisCore.PITDeductionDocument.Infrastructure.Repository;

namespace VnisCore.PITDeductionDocument.Infrastructure
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule)
    )]
    public class VnisCorePITDeductionDocumentInfrastructureModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            SqlMapper.RemoveTypeMap(typeof(Guid));
            SqlMapper.RemoveTypeMap(typeof(Guid?));
            SqlMapper.AddTypeHandler(typeof(Guid), new GuidTypeHandler());
            SqlMapper.AddTypeHandler(typeof(Dictionary<string, string>), new DictionaryTypeHandler());

            context.Services.AddAutoMapperObjectMapper<VnisCorePITDeductionDocumentInfrastructureModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddProfile<VnisCorePITDeductionDocumentInfrastructureAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCorePITDeductionDocumentInfrastructureModule).GetTypeInfo().Assembly);

            #region Thêm các phụ thuộc repo, service
            context.Services.AddScoped<IPITDeductionDocumentRepository, PITDeductionDocumentRepository>();
            context.Services.AddScoped<IPITDeductionDocumentXmlRepository, PITDeductionDocumentXmlRepository>();
            context.Services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            #endregion
        }
    }
}