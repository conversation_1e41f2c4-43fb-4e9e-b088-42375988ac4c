{"App": {"SelfUrl": "http://localhost:6006", "CorsOrigins": "http://localhost:6789"}, "ConnectionStrings": {"Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoiceauth;Password=Vnis@12A", "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=einvoice50dev)))';User Id=einvoice50dev;Password=Vnis@12A", "VnisCoreMongoDbAuditLogging": "***************************************************************************************"}, "Service": {"Name": "VnisCore.Invoice03.Host", "Title": "VnisCore.Invoice03.Host", "BaseUrl": "invoice03", "AuthApiName": "VnisCore.Invoice03.Host"}, "Redis": {"IsUsing": "true", "Configuration": "**************,allowAdmin=true"}, "RabbitMQ": {"Connections": {"Default": {"HostName": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest", "VirtualHost": "/"}}, "EventBus": {"ClientName": "einvoice.invoice03", "ExchangeName": "einvoice"}}, "Minio": {"Endpoint": "**************:9000", "AccessKey": "vnis", "SecretKey": "Vnis@12A", "Region": null, "SessionToken": null, "BucketName": "dev-core50"}, "AuthServer": {"Authority": "http://localhost:6868", "RequireHttpsMetadata": "false", "ApiName": "einvoice", "SwaggerClientId": "einvoice_Swagger", "SwaggerClientSecret": "Vnis@12A"}, "Elasticsearch": {"Index": "dev.core50.invoice03", "Url": "http://**************:9200/"}, "Microservices": {"SendMail": {"Endpoint": "http://localhost:6013/", "Timeout": 5}}, "HtmlToPdfLimitCcu": 1, "FileDownload": {"FolderDocumentTemplate": "/var/www/vnis/share/cshtml", "PdfToolPath": "/var/www/vnis/share/Resources", "WkHtmlToPdfExeName": "wkhtmltopdf"}}