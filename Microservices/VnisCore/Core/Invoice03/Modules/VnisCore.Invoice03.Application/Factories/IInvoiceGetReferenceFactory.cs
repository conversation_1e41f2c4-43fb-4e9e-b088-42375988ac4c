using Core.Application.Dtos;
using Core.DependencyInjection;
using System.Threading.Tasks;
using VnisCore.Invoice03.Application.Factories.Models;
using VnisCore.Invoice03.Application.Factories.Proxy.InvoiceReplace;
using static VnisCore.Invoice03.Application.Factories.Constants.CommonInvoice03Const;

namespace VnisCore.Invoice03.Application.Factories
{
    public interface IInvoiceGetReferenceFactory : IScopedDependency
    {
        Task<PagedResultDto<InvoiceReferenceGetResponse>> HandleAsync(InvoiceReferenceGetRequest request);
    }

    public class InvoiceGetReferenceFactory : IInvoiceGetReferenceFactory
    {
        private readonly IInvoiceReplaceGetReferenceProxy _invoiceReferenceReplaceProxy;

        public InvoiceGetReferenceFactory(
            IInvoiceReplaceGetReferenceProxy invoiceReferenceReplaceProxy)
        {
            _invoiceReferenceReplaceProxy = invoiceReferenceReplaceProxy;
        }
        public async Task<PagedResultDto<InvoiceReferenceGetResponse>> HandleAsync(InvoiceReferenceGetRequest request)
        {
            switch (request.ActionType)
            {
                case GetDataActionType.ViewInvoiceReplace:
                case GetDataActionType.ModifyInvoiceReplace:
                    return await _invoiceReferenceReplaceProxy.HandleAsync(request);
                case GetDataActionType.ViewInvoice:
                default:
                    return new PagedResultDto<InvoiceReferenceGetResponse>();
            }
        }
    }
}
