using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.Invoice03.Application.Invoice03.Models
{
    public class ElastichSearchInvoiceModel : BaseElasticInvoiceHeader
    {
        /// <summary>
        /// Id (int) của invoice
        /// </summary>
        public int IdInvoice { get; set; }
        #region Thông tin thanh toán
        /// <summary>
        /// Nguyên tệ
        /// </summary>
        public string FromCurrency { get; set; }

        /// <summary>
        /// Ngoại tệ
        /// </summary>
        public string ToCurrency { get; set; }

        [Range(0, 4, ErrorMessage = "C<PERSON>u hình thập phân của tiền tệ phải nằm trong khoagr 0-4")]
        public int RoundingCurrency { get; set; }

        /// <summary>
        ///  lưu giá trị chuyển đổi từ đơn vị cao sang đơn vị thấp. vd 1 đô la = 100 cents
        /// </summary>
        public int CurrencyConversion { get; set; }

        /// <summary>
        /// Tỷ giá (Bằng 1 nếu Nguyên tệ = Ngoại tệ)
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Ngày thanh toán
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng việt)
        /// </summary>
        public string PaymentAmountWords { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng anh)
        /// </summary>
        public string PaymentAmountWordsEn { get; set; }

        /// <summary>
        /// Tổng tiền chưa thuế, chưa chiết khấu
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }
        #endregion

        #region Thông tin tạo/sửa/xóa/duyệt/in
        /// <summary>
        /// Họ tên người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameCreator { get; set; }

        /// <summary>
        /// Họ tên người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string UserNameCreator { get; set; }

        /// <summary>
        /// Ngày in chuyển đổi, khác null tức là đã in chuyển đổi
        /// </summary>
        public DateTime? PrintedTime { get; set; }

        /// <summary>
        /// Họ tên người in chuyển đổi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNamePrinter { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người in chuyển đôi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? PrintedBy { get; set; }

        /// <summary>
        /// Thời điểm duyệt hóa đơn
        /// </summary>
        public DateTime? ApprovedTime { get; set; }

        /// <summary>
        /// Họ tên người duyệt hóa đơn (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameApprover { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người duyệt hóa đơn (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? ApprovedBy { get; set; }
        #endregion

        //#region Thông tin biên bản
        ///// <summary>
        ///// Số biên bản
        ///// </summary>
        //public string DocumentNo { get; set; }

        ///// <summary>
        ///// Ngày biên bản
        ///// </summary>
        //public DateTime? DocumentDate { get; set; }

        ///// <summary>
        ///// Lý do biên bản
        ///// </summary>
        //public string DocumentReason { get; set; }
        //#endregion

        #region Thông tin Phiếu xuất kho kiêm vận chuyển nội bộ

        /// <summary>
        /// Lệnh điều động số
        /// </summary>
        public string DeliveryOrderNumber { get; set; }

        /// <summary>
        /// Người/Đơn vị đều động
        /// </summary>
        public string DeliveryOrderBy { get; set; }

        /// <summary>
        /// Tên người vận chuyển
        /// </summary>
        public string DeliveryBy { get; set; }

        /// <summary>
        /// Phương tiện vận chuyển
        /// </summary>
        public string TransportationMethod { get; set; }

        #endregion


        #region thông tin người nhận hàng

        /// <summary>
        /// Tên (Tên người nhận hàng)
        /// </summary>
        public string ReceiverName { get; set; }

        /// <summary>
        /// Mã số thuế người nhận hàng
        /// </summary>
        public string ReceiverTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người nhận hàng
        /// </summary>
        public string ReceiverAddressLine { get; set; }

        /// <summary>
        /// Họ và tên người nhận hàng
        /// </summary>
        public string ReceiverFullName { get; set; }

        #endregion

        public long InvoiceReferenceId { get; set; }

        public string TransactionData { get; set; }

        public bool IsDeclared { get; set; }

        /// <summary>
        /// mã cơ quan thuế cho trường hợp hóa đơn cấp mã
        /// </summary>
        public string VerificationCode { get; set; }

        public Dictionary<string, string> ExtraProperties { get; set; }

        public List<ElasticGetInvoice03HeaderExtraResponseModel> InvoiceHeaderExtras { get; set; }
        public List<ElasticGetInvoice03DetailResponseModel> InvoiceDetails { get; set; }

        public class ElasticGetInvoice03HeaderExtraResponseModel
        {
            public long InvoiceHeaderFieldId { get; set; }
            public long Id { get; set; }
            public string FieldValue { get; set; }
            public string FieldName { get; set; }

        }

        public class ElasticGetInvoice03DetailResponseModel
        {
            public long Id { get; set; }
            public int Index { get; set; }
            public decimal PaymentAmount { get; set; }
            public string ProductCode { get; set; }
            public long ProductId { get; set; }
            public string ProductName { get; set; }
            public string UnitName { get; set; }
            public long UnitId { get; set; }
            public decimal UnitPrice { get; set; }
            public int RoundingUnit { get; set; }
            public decimal Quantity { get; set; }
            public decimal Amount { get; set; }
            public string Note { get; set; }

            public decimal TotalAmount { get; set; }

            public Dictionary<string, string> ExtraProperties { get; set; }
            public List<ElasticGetInvoice03DetailExtraResponseModel> InvoiceDetailExtras { get; set; }

        }

        public class ElasticGetInvoice03DetailExtraResponseModel
        {
            public long Id { get; set; }
            public string FieldValue { get; set; }
            public string FieldName { get; set; }

            public long InvoiceDetailFieldId { get; set; }
        }
    }

    public class BaseElasticInvoiceHeader
    {
        public long Id { get; set; }
        public long Partition { get; set; }

        public Guid TenantId { get; set; }
        public DateTime CreationTime { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime? UpdatedTime { get; set; }
        public Guid? LastModifierId { get; set; }

        public DateTime? CancelTime { get; set; }
        public Guid? CancelId { get; set; }
        public DateTime? DeletionTime { get; set; }
        public Guid? DeleteId { get; set; }

        /// <summary>
        /// Key dùng để index, không được trùng (MST|TemplateNo|SerialNo|InvoiceNo)
        /// </summary>
        public string Indexing { get; set; }

        /// <summary>
        /// Nguồn dữ liệu từ đâu: API, Form, Excel, ShareDb
        /// </summary>
        public int Source { get; set; }

        /// <summary>
        /// Gói tạo hóa đơn
        /// </summary>
        public Guid BatchId { get; set; }

        /// <summary>
        /// Mã tra cứu. Các hóa đơn thay thế/điều chỉnh sẽ cùng mã này
        /// </summary>
        public string TransactionId { get; set; }

        /// <summary>
        /// code bản ghi mẫu hóa đơn (Template)
        /// </summary>
        public long TemplateId { get; set; }

        /// <summary>
       
        /// </summary>
        public short TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu (PT/17E)
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn gồm 7 ký tự số (0000001)
        /// </summary>
        public string InvoiceNo { get; set; }
        public int? Number { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Ngày hóa đơn NSD nhập vào. Chỉ lưu ngày, tháng, năm. Không lưu thời gian. Lưu dạng UTC
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn (int)
        /// </summary>
        public int InvoiceStatus { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn (name)
        /// </summary>
        public string InvoiceStatusName { get; set; }

        /// <summary>
        /// Trạng thái ký hóa đơn
        /// </summary>
        public int SignStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn
        /// </summary>
        public int ApproveStatus { get; set; }
        public int ApproveCancelStatus { get; set; }
        public int ApproveDeleteStatus { get; set; }

        /// <summary>
        /// Ghi bản ghi đăng ký phát hành hóa đơn, lưu lại để xác định hóa đơn được tạo theo thông báo phát hành nào
        /// </summary>
        public long? RegistrationHeaderId { get; set; }
        public long? RegistrationDetailId { get; set; }

        /// <summary>
        /// Id bản ghi bên ERP
        /// </summary>
        public string ErpId { get; set; }

        /// <summary>
        /// Tài khoản người tạo hóa đơn bên ERP
        /// </summary>
        public string CreatorErp { get; set; }

        ///// <summary>
        ///// id file bản in thể hiện
        ///// </summary>
        //public Guid? IdUnOffical { get; set; }

        ///// <summary>
        ///// id file xml trong bảng media
        ///// </summary>
        //public Guid? IdXml { get; set; }

        ///// <summary>
        ///// id file biên bản
        ///// </summary>
        public long? IdFileDocument { get; set; }

        public DateTime? IssuedTime { get; set; }

        /// <summary>
        /// kiểm tra thông tin hóa đơn đã được tra cứu ở portal chưa
        /// </summary>
        public bool IsViewed { get; set; }

        /// <summary>
        /// thời gian xem lần cuối cùng theo giờ local
        /// </summary>
        public DateTime? ViewedTime { get; set; }

        /// <summary>
        /// kiểm tra thông tin hóa đơn đã được xem chưa
        /// </summary>
        public bool IsOpened { get; set; }

        /// <summary>
        /// thời gian xem hóa đơn lần đầu theo giờ local
        /// </summary>
        public DateTime? OpenedTime { get; set; }

        #region Thông tin người bán
        public Guid SellerId { get; set; }

        /// <summary>
        /// Người đại diện pháp nhân bên bán
        /// </summary>
        public string SellerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế bên bán
        /// </summary>
        public string SellerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ bên bán
        /// </summary>
        public string SellerAddressLine { get; set; }

        /// <summary>
        /// Mã quốc gia người bán (Việt Nam là VN)
        /// </summary>
        public string SellerCountryCode { get; set; }

        /// <summary>
        /// Tên phường/xã người bán
        /// </summary>
        public string SellerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người bán
        /// </summary>
        public string SellerCityName { get; set; }

        /// <summary>
        /// Số điện thoại người bán
        /// </summary>
        public string SellerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người bán
        /// </summary>
        public string SellerFaxNumber { get; set; }

        /// <summary>
        /// Email người bán
        /// </summary>
        public string SellerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người bán
        /// </summary>
        public string SellerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người bán
        /// </summary>
        public string SellerBankAccount { get; set; }

        /// <summary>
        /// Tên công ty bán
        /// </summary>
        public string SellerFullName { get; set; }

        /// <summary>
        /// Thời điểm hóa đơn được ký
        /// </summary>
        public DateTime? SellerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string SellerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? SellerSignedId { get; set; }
        #endregion
    }
}
