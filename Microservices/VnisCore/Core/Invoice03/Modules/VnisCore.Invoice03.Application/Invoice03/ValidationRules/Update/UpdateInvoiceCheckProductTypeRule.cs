using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Core.Tvan.Constants;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRule.Update
{
    /// <summary>
    /// Validate tính chất hàng hóa
    /// </summary>
    public class UpdateInvoiceCheckProductTypeRule : IValidationRuleAsync<UpdateInvoice03HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public UpdateInvoiceCheckProductTypeRule(
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _localizer = localizer;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice03HeaderDto input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            var errorDetailDiscounts = new List<string>();
            foreach (var detail in input.InvoiceDetails)
            {
                if (detail.ProductType == TChat.Discount.GetHashCode())
                        errorDetailDiscounts.Add(detail.Index.ToString());
            }

            if (errorDetailDiscounts.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice01.ProductTypeDiscountAmountNotValid", new[] { string.Join(",", errorDetailDiscounts) }]);

            return new ValidationResult(true);
        }
    }
}
