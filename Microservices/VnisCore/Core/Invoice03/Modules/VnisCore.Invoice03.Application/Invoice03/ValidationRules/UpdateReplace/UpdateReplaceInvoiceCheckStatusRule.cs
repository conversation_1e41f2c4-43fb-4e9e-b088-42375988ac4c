using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Invoice03.Application.Factories.Services;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRule.UpdateReplace
{
    /// <summary>
    /// validate trạng thái ký và trạng thái hóa đơn xem có được sửa hóa đơn thay thế không
    /// </summary>
    class UpdateReplaceInvoiceCheckStatusRule : IValidationRuleAsync<UpdateReplaceInvoice03HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceService<Invoice03HeaderEntity, Invoice03HeaderFieldEntity, Invoice03DetailFieldEntity> _invoiceService;

        public UpdateReplaceInvoiceCheckStatusRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IInvoiceService<Invoice03HeaderEntity, Invoice03HeaderFieldEntity, Invoice03DetailFieldEntity> invoiceService)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _invoiceService = invoiceService;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice03HeaderDto input)
        {
            var invoice = _validationContext.GetItem<Invoice03HeaderEntity>("Invoice");

            if (invoice.InvoiceStatus != InvoiceStatus.ThayThe.GetHashCode())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.UpdateReplace.CannotUpdateNonOriginalInvoice"]);

            //kiểm tra trạng thái ký
            if (invoice.SignStatus > SignStatus.ChoKy.GetHashCode())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.CannotUpdateInvoice"]);

            var tenantId = _appFactory.CurrentTenant.Id.Value;

            if (await _invoiceService.HasApproveAsync(tenantId) && invoice.ApproveStatus == (short)ApproveStatus.DaDuyet)
            {
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.CannotUpdateInvoiceApproveStatus"]);
            }

            return new ValidationResult(true);
        }
    }
}
