using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRule.Create
{
    /// <summary>
    /// Validate tiền tệ
    /// </summary>
    public class CreateInvoiceCheckExistCurrencyRule : IValidationRuleAsync<CreateInvoice03HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<CurrencyEntity, long> _repoCurrencyEntity;

        public CreateInvoiceCheckExistCurrencyRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IRepository<CurrencyEntity, long> repoCurrencyEntity)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoCurrencyEntity = repoCurrencyEntity;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice03HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //Nguyên tệ
            var fromCurrency = await _repoCurrencyEntity.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.IsDefault && !x.IsDeleted);
            if (fromCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.FromCurrencyNotSet"]);

            _validationContext.AddItem<CurrencyEntity>("FromCurrency", fromCurrency);

            //Tiền tệ
            var toCurrency = await _repoCurrencyEntity.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.CurrencyCode == input.ToCurrency && !x.IsDeleted);
            if (toCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.ToCurrencyIncorrect"]);

            _validationContext.AddItem<CurrencyEntity>("ToCurrency", toCurrency);

            return new ValidationResult(true);
        }
    }
}
