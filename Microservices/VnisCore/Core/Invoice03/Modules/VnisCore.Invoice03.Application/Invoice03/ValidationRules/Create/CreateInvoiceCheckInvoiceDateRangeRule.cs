using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Invoice03.Application.Factories.Services;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRule.Create
{
    /// <summary>
    /// Validate ngày hóa đơn: >= ActiveDate và >= LastInvoiceDate và <= Now
    /// </summary>
    public class CreateInvoiceCheckInvoiceDateRangeRule : IValidationRuleAsync<CreateInvoice03HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<Invoice03HeaderEntity, Invoice03HeaderFieldEntity, Invoice03DetailFieldEntity> _invoiceService;

        public CreateInvoiceCheckInvoiceDateRangeRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IInvoiceService<Invoice03HeaderEntity, Invoice03HeaderFieldEntity, Invoice03DetailFieldEntity> invoiceService)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice03HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var range = await _invoiceService.InvoiceDateRangeAsync(tenantId, input.TemplateNo, input.SerialNo, null);
            if (range == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice.InvoiceDateRange.TemplateOutOfInvoiceNo"]);

            if (range.Min.Date > DateTime.Now.Date)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.InvoiceDateMin", new[] { range.Min.ToString("dd/MM/yyyy") }]);

            if (input.InvoiceDate > range.Max || input.InvoiceDate < range.Min)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.InvoiceDateRange", new[] { range.Min.ToString("dd/MM/yyyy"), range.Max.ToString("dd/MM/yyyy") }]);

            return new ValidationResult(true);
        }
    }
}
