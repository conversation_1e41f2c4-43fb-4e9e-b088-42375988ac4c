using Core.Shared.Factory;
using Core.Shared.Validations;
using System.Threading.Tasks;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRule.CreateReplace
{
    public class CreateReplaceInvoicePreProcess : IValidationRuleAsync<CreateReplaceInvoice03Dto, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;

        public CreateReplaceInvoicePreProcess(
            IAppFactory appFactory,
            IValidationContext validationContext)
        {
            _appFactory = appFactory;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice03Dto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var tenant = new TenantInfo
            {
                Id = _appFactory.CurrentTenant.Id.Value,
                Code = _appFactory.CurrentTenant.TenantCode,
                Address = _appFactory.CurrentTenant.Address,
                FullNameVi = _appFactory.CurrentTenant.FullNameVi,
                FullNameEn = _appFactory.CurrentTenant.FullNameEn,
                Name = _appFactory.CurrentTenant.Name,
                City = _appFactory.CurrentTenant.City,
                Country = _appFactory.CurrentTenant.Country,
                District = _appFactory.CurrentTenant.District,
                Emails = _appFactory.CurrentTenant.Emails,
                LegalName = _appFactory.CurrentTenant.LegalName,
                TaxCode = _appFactory.CurrentTenant.TaxCode,
                Phone = _appFactory.CurrentTenant.Phones,
                Fax = _appFactory.CurrentTenant.Fax,
                BankAccount = _appFactory.CurrentTenant.BankAccount,
                BankName = _appFactory.CurrentTenant.BankName,
                Metadata = _appFactory.CurrentTenant.Metadata,
            };
            //var tenantId = Utilities.TenantId;
            //var userId = Utilities.CreatorId;
            //var tenant = Utilities.TenantInfo;

            //_validationContext.GetOrAddItem("TenantId", () =>
            //{
            //    return tenantId;
            //});

            _validationContext.GetOrAddItem("Tenant", () =>
            {
                return tenant;
            });

            //_validationContext.GetOrAddItem("UserId", () =>
            //{
            //    return userId;
            //});

            return new ValidationResult(true);
        }
    }
}
