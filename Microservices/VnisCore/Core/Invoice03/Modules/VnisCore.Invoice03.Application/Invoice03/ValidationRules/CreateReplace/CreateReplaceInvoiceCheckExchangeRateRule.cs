using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRule.CreateReplace
{
    /// <summary>
    /// kiểm tra exchangerate có đúng giá trị không
    /// nếu mã tiền tệ = nguyên tệ => exchangerate = 1
    /// </summary>
    public class CreateReplaceInvoiceCheckExchangeRateRule : IValidationRule<CreateReplaceInvoice03Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public CreateReplaceInvoiceCheckExchangeRateRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateReplaceInvoice03Dto input)
        {
            var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
            var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");

            if (fromCurrency.CurrencyCode == toCurrency.CurrencyCode && input.ExchangeRate != 1)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.ExchangeRateMustBeOne"]);

            return new ValidationResult(true);
        }
    }
}
