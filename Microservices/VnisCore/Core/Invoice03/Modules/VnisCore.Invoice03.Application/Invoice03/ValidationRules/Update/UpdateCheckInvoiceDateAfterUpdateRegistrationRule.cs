using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Core.Tvan.Constants;

using Dapper;

using Microsoft.Extensions.Localization;

using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;
using VnisCore.Invoice03.Application.Invoice03.Dto;

namespace VnisCore.Invoice03.Application.Invoice03.ValidationRules.Update
{
    public class UpdateCheckInvoiceDateAfterUpdateRegistrationRule : IValidationRuleAsync<UpdateInvoice03HeaderDto, ValidationResult>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public UpdateCheckInvoiceDateAfterUpdateRegistrationRule(
             IAppFactory appFactory,
             IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _appFactory = appFactory;
            _localizer = localizer;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice03HeaderDto input)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(_appFactory.CurrentTenant.Id.Value);
            var sql = $@"SELECT
	                        ""Status"",
                            ""GDTResponseTime""
                        FROM
	                        ""NewRegistrationHeader""
                        WHERE
                            ""TenantId"" = '{rawTenantId}'
                            AND ""Status"" = {(short)TvanStatus.TCTAccept}
                            AND ""GDTResponseTime"" IS NOT NULL
                        ORDER BY
                            ""Id"" DESC 
                        FETCH FIRST 1 ROWS ONLY ";

            var registrationHeader = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<NewRegistrationHeaderEntity>(sql.ToString());
            if (registrationHeader == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.Update.NotFoundRegistration"]);

            if (registrationHeader.GDTResponseTime?.Date > input.InvoiceDate.Date)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice03.Update.CannotUpdateInvoice"]);

            return new ValidationResult(true);
        }
    }
}
