using Core.ObjectMapping;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Dapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.SyncCatalog.Application.Dto;

namespace VnisCore.SyncCatalog.Application.Factories.Repositories.Ticket
{
    public interface ITicketHeaderRepository
    {
        Task<TicketHeaderEntity> GetInvoiceHeaderRawAsync(Guid tenantId, long IdRootInvoice);

        Task<List<TicketHeaderEntity>> GetInvoiceHeadersRawByBatchIdAsync(Guid tenantId, Guid batchId);
    }
    public class TicketHeaderRepository : ITicketHeaderRepository
    {
        private readonly IAppFactory _appFactory;
        protected IObjectMapper _objectMapper { get; }

        public TicketHeaderRepository(
            IAppFactory appFactory,
            IObjectMapper objectMapper)
        {
            _appFactory = appFactory;
            _objectMapper = objectMapper;
        }


        public async Task<TicketHeaderEntity> GetInvoiceHeaderRawAsync(Guid tenantId, long IdRootInvoice)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);

            var query = $"Select * from \"TicketHeader\" where \"Id\" = {IdRootInvoice} and \"TenantId\" = '{rawTenantId}'";
            var result = await _appFactory.VnisCoreOracle.Connection.QuerySingleOrDefaultAsync<TicketHeaderDto>(query);
            var ticket = _objectMapper.Map<TicketHeaderDto, TicketHeaderEntity>(result);
            return ticket;
        }

        public async Task<List<TicketHeaderEntity>> GetInvoiceHeadersRawByBatchIdAsync(Guid tenantId, Guid batchId)
        {
            var rawBatchId = OracleExtension.ConvertGuidToRaw(batchId);
            var query = $"Select \"Id\"," +
              $"\"TenantId\"," +
              $"\"CreatorId\"" +
               $" from \"TicketHeader\" where \"BatchId\" = '{rawBatchId}' and \"IsDeleted\" = 0";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryAsync<TicketHeaderDto>(query);
            var headers = result.Select(x => _objectMapper.Map<TicketHeaderDto, TicketHeaderEntity>(x)).ToList();
            return headers;
        }
    }
}
