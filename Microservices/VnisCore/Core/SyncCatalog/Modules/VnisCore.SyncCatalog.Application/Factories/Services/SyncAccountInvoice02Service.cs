using Core;
using Core.EventBus.Distributed;
using Core.Identity;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.GenerateContentEmail;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Models;
using Core.Shared.Security;
using Core.Uow;
using Dapper;
using SixLaborsCaptcha.Core;
using System;
using System.Threading.Tasks;
using VnisCore.SyncCatalog.Application.Factories.Repositories.Invoice02;

namespace VnisCore.SyncCatalog.Application.Factories.Services
{
    public class SyncAccountInvoice02Service : ISyncAccountService
    {
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IAppFactory _appFactory;
        protected IUnitOfWork CurrentUnitOfWork { get; }
        protected IdentityUserManager UserManager { get; }

        public SyncAccountInvoice02Service(IAppFactory appFactory,
            IdentityUserManager userManager,
            IUnitOfWork currentUnitOfWork,
            IDistributedEventBus distributedEventBus)
        {
            _appFactory = appFactory;
            _distributedEventBus = distributedEventBus;
            UserManager = userManager;
            CurrentUnitOfWork = currentUnitOfWork;
        }
        public async Task SyncAccountAsync(SyncAccountEventSendData request)
        {
            var repoHeader = _appFactory.GetServiceDependency<IInvoice02HeaderRepository>();
            var buyerEmail = "";
            var buyerCode = "";
            var buyerFullName = "";

            if (string.IsNullOrEmpty(request.BuyerCode) && string.IsNullOrEmpty(request.BuyerEmail) && string.IsNullOrEmpty(request.BuyerFullName))
            {
                var invoice = await repoHeader.GetInvoiceHeaderRawAsync(request.TenantId, request.InvoiceId);
                if (invoice == null)
                    throw new UserFriendlyException($"Sync Account: Không tìm thấy hóa đơn id: {request.InvoiceId}, tenantId: {request.TenantId}");

                buyerCode = invoice.BuyerCode;
                buyerEmail = invoice.BuyerEmail;
                buyerFullName = invoice.BuyerFullName;
            }
            else
            {
                buyerCode = request.BuyerCode;
                buyerEmail = request.BuyerEmail;
                buyerFullName = request.BuyerFullName;
            }


            var query = $"Select " +
                       $"\"Id\", " +
                       $"\"TenantId\", " +
                       $"\"NormalizedUserName\"" +
                       $"from \"VnisUsers\" where \"TenantId\" = '{OracleExtension.ConvertGuidToRaw(request.TenantId)}' and \"NormalizedUserName\" = '{buyerCode.ToUpper()}'";
            var result = await _appFactory.AuthDatabase.Connection.QuerySingleOrDefaultAsync<IdentityUser>(query);

            if (result == null)
            {
                var generator = new PasswordGenerator()
                {
                    Length = IdentityUserConsts.MaxPasswordLength,
                    MinLowercases = 1,
                    MinUppercases = 1,
                    MinDigits = 1,
                    MinSpecials = 1
                };

                var password = generator.Generate();

                var user = new IdentityUser(
                   Guid.NewGuid(),
                   buyerCode,
                   buyerEmail,
                   "0",
                   "0",
                   "0",
                   request.TenantId,
                   (short)AccountType.KhachHang,
                   buyerFullName
               );
                await UserManager.CreateAsync(user, password, validatePassword: false);
                await CurrentUnitOfWork.SaveChangesAsync();

                var queryCustomer = $"Update \"Customer\" " +
                  $"set \"UserId\" = '{OracleExtension.ConvertGuidToRaw(user.Id)}' " +
                  $" where \"CustomerCode\" = '{buyerCode}' and \"TenantId\" = '{OracleExtension.ConvertGuidToRaw(request.TenantId)}'";
                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryCustomer);

                //Gửi mail tài khoản
                if (!string.IsNullOrEmpty(buyerEmail))
                    await _distributedEventBus.PublishAsync(new GenerateContentEmailEventSendData(new GenerateContentMailMessageModel<BaseGenerateContentMaillModel>
                    {
                        Action = EmailAction.SendAccountPortal.ToString(),
                        Data = new BaseGenerateContentMaillModel
                        {
                            TenantId = request.TenantId,
                            Emails = buyerEmail,
                            FullName = buyerFullName,
                            UserName = buyerCode,
                            Password = password,
                        }
                    }));
            }
            else
            {
                var queryCustomer = $"Update \"Customer\" " +
                    $"set \"UserId\" = '{OracleExtension.ConvertGuidToRaw(result.Id)}' " +
                    $" where \"CustomerCode\" = '{buyerCode}' and \"TenantId\" = '{OracleExtension.ConvertGuidToRaw(request.TenantId)}'";
                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryCustomer);
            }
        }
    }
}
