{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoiceauth50staging;Password=Vnis@12A",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SID=orcl19c)))';User Id=einvoice50staging;Password=Vnis@12A",
    "VnisCoreMongoDbAuditLogging": "*************************************************************************************************",
    "VnisCoreMongoDb": "***************************************************************************************"
  },
  "Service": {
    "Name": "VnisCore.SyncCatalog.Worker",
    "Title": "VnisCore.SyncCatalog.Worker",
    "BaseUrl": "synccatalog-worker",
    "AuthApiName": "VnisCore.SyncCatalog.Worker"
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "**************,allowAdmin=true"
  },
  "Settings": {
    //"TenantGroup": "0,1.1,1.2,1.3,1.4,1.5,1.6,2.1,2.2,2.3,2.4,2.5,2.6,3.1,3.2,3.3,3.4,3.5,3.6,4.1,4.2,4.3,4.4,4.5,4.6,5.1,5.2,5.3,5.4,5.5,5.6",
    "TenantGroup": "0, 5.1, 1.2, 1.1",
    "TotalQuantityTakeInvoice": 100
  },
  "Logging": {
    "RootFolder": {
      "Folder": "/home/<USER>/microservice-core50-staging/backgroundworkers/group-x/sync-catalog"
    }
  },
  "AuthServer": {
    "Authority": "https://auth.v50.staging.vninvoice.net",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "RabbitMQ": {
    "Connections": {
      "Default": {
        "HostName": "**************",
        "Port": 5672,
        "UserName": "vnis",
        "Password": "Vnis@12A",
        "VirtualHost": "/staging-core50"
      }
    },
    "EventBus": {
      "ClientName": "einvoice.workersynccatalog",
      "ExchangeName": "einvoice"
    }
  }
}