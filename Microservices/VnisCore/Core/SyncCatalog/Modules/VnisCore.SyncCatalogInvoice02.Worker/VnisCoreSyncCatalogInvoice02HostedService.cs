using Core;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

using Serilog;

using System.Threading;
using System.Threading.Tasks;

namespace VnisCore.SyncCatalogInvoice02.Worker
{
    public class VnisCoreSyncCatalogHostedService : BackgroundService
    {
        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using (var application = AbpApplicationFactory.Create<VnisCoreSyncCatalogModule>(options =>
            {
                options.UseAutofac();
                options.Services.AddLogging(c => c.AddSerilog());
            }))
            {
                application.Initialize();
            }

            return Task.CompletedTask;
        }
    }
}
