using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Export.Application.Factories.Models;

namespace VnisCore.Export.Application.Factories.Repositories.Invoice03
{
    public class Invoice03HeaderRepository : IInvoice03HeaderRepository
    {
        private readonly IRepository<Invoice03HeaderEntity, long> _repoInvoice03Header;

        public Invoice03HeaderRepository(IRepository<Invoice03HeaderEntity, long> repoInvoice03Header)
        {
            _repoInvoice03Header = repoInvoice03Header;
        }

        public async Task<Paged<Invoice03HeaderEntity>> PagingAsync(Guid tenantId, PagingInvoiceModel model)
        {
            var query = Filter(tenantId, model);
            query = query.OrderByDescending(x => x.InvoiceDate).ThenByDescending(x => x.Number).ThenByDescending(x => x.CreationTime);

            var paged = await query.ToPaginationAsync<Invoice03HeaderEntity>(model);

            return paged;
        }

        public async Task<int> CountAsNoTrackingAsync(Guid tenantId, PagingInvoiceModel model)
        {
            var query = await Filter(tenantId, model)
                .AsNoTracking().AsQueryable().CountAsync();
            return query;
        }

        public async Task<Paged<Invoice03HeaderEntity>> PagingAsNoTrackingAsync(Guid tenantId, PagingInvoiceModel query)
        {
            var paged = await Filter(tenantId, query).OrderByDescending(x => x.InvoiceDate).ThenByDescending(x => x.Number)
                .AsNoTracking()
                .ToPaginationAsync(query);

            return paged;
        }

        private IQueryable<Invoice03HeaderEntity> Filter(Guid tenantId, PagingInvoiceModel model)
        {
            IQueryable<Invoice03HeaderEntity> items = _repoInvoice03Header.Where(x => x.TenantId == tenantId && model.AllReadTemplateIds.Contains(x.InvoiceTemplateId));

            //lấy hóa đơn có số hoặc không số
            if (model.IsNullInvoice)
                items = items.Where(x => !x.Number.HasValue);
            else
                items = items.Where(x => x.Number.HasValue);

            if (!string.IsNullOrEmpty(model.Q))
            {
                var q = model.Q.Trim();
                int.TryParse(model.Q, out int outInvoiceNo);
                var invoiceStatus = EnumExtension.TryToEnum<InvoiceStatus>(model.Q);
                var signStatus = EnumExtension.TryToEnum<SignStatus>(model.Q);

                items = items.Where(x =>
                     (x.UserNameCreator != null && x.UserNameCreator.Contains(model.Q))
                    || (x.FullNameCreator != null && x.FullNameCreator.Contains(model.Q))
                   // || (x.BuyerEmail != null && x.BuyerEmail.Contains(model.Q))
                   // || (x.BuyerFullName != null && x.BuyerFullName.Contains(model.Q))
                   // || (x.BuyerBankAccount != null && x.BuyerBankAccount.Contains(model.Q))
                    || (x.Number == outInvoiceNo)
                   // || (x.PaymentMethod != null && x.PaymentMethod.Contains(model.Q))
                   // || (x.BuyerPhoneNumber != null && x.BuyerPhoneNumber.Contains(model.Q))
                    || (x.SerialNo != null && x.SerialNo.Contains(model.Q))
                    //|| (x.TemplateNo.Contains(query.Q))
                    || (x.ErpId != null && x.ErpId.Contains(model.Q))
                    || (x.TransactionId != null && x.TransactionId.Contains(model.Q))
                    || (x.CreatorErp != null && x.CreatorErp.Contains(model.Q))
                   // || (x.BuyerCode != null && x.BuyerCode.Contains(model.Q))
                    || (x.InvoiceStatus == invoiceStatus.GetHashCode())
                    || (x.SignStatus == signStatus.GetHashCode())
                    );
            }

            if (model.TemplateNo.HasValue)
            {
                items = items.Where(x => x.TemplateNo == model.TemplateNo);
            }

            if (model.CreateFromDate.HasValue)
            {
                var createFromDate = model.CreateFromDate.Value.Date.ToUniversalTime();
                items = items.Where(x => x.InvoiceDate >= createFromDate);
            }

            if (model.CreateToDate.HasValue)
            {
                var createToDate = model.CreateToDate.Value.Date.AddDays(1);
                items = items.Where(x => x.InvoiceDate < createToDate);
            }

            if (model.IssueFromDate.HasValue)
            {
                var issueFromDate = model.IssueFromDate.Value.Date.ToUniversalTime();
                items = items.Where(x => x.IssuedTime >= issueFromDate);
            }

            if (model.IssueToDate.HasValue)
            {
                var issueToDate = model.IssueToDate.Value.Date.AddDays(1);
                items = items.Where(x => x.IssuedTime < issueToDate);
            }

            if (model.CancelFromDate.HasValue)
                items = items.Where(x => x.LastModificationTime.HasValue && x.LastModificationTime.Value.Date >= model.CancelFromDate.Value.Date
                                           && (x.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || x.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()));

            if (model.CancelToDate.HasValue)
            {
                var cancelToDate = model.CancelToDate.Value.Date.AddDays(1);
                items = items.Where(x => x.LastModificationTime.HasValue && x.LastModificationTime.Value.Date < cancelToDate
                                            && (x.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() || x.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()));
            }

            //if (query.IssuedAt.HasValue)
            //{
            //    var issuedAt = query.IssuedAt.Value.Date;
            //    items = items.Where(x => x.IssuedAt != null && x.IssuedAt.Value.Date == issuedAt
            //                                );
            //}

            if (!string.IsNullOrEmpty(model.Customers))
            {
                //items = items.Where(x => (x.BuyerCode != null && x.BuyerCode.Contains(model.Customers))
                //  || (x.BuyerLegalName != null && x.BuyerLegalName.Contains(model.Customers))
                //  || (x.BuyerTaxCode != null && x.BuyerTaxCode.Contains(model.Customers))
                //  || (x.BuyerEmail != null && x.BuyerEmail.Contains(model.Customers)));
            }

            if (!string.IsNullOrEmpty(model.Customer))
            {
                //items = items.Where(x =>
                //    (x.BuyerFullName != null && x.BuyerFullName.Contains(model.Customer))
                //    || (x.BuyerEmail != null && x.BuyerEmail.Contains(model.Customer))
                //    || (x.BuyerPhoneNumber != null && x.BuyerPhoneNumber.Contains(model.Customer)));
            }

            if (model.InvoiceTemplateIds != null && model.InvoiceTemplateIds.Any())
                items = items.Where(x => model.InvoiceTemplateIds.Contains(x.InvoiceTemplateId));

            if (!string.IsNullOrEmpty(model.InvoiceNo))
            {
                int.TryParse(model.InvoiceNo, out int outInvoiceNo);
                items = items.Where(x => x.Number != null && x.Number == outInvoiceNo);
            }

            //if (!string.IsNullOrEmpty(model.BuyerCode))
            //    items = items.Where(x => x.BuyerCode == model.BuyerCode);

            //if (!string.IsNullOrEmpty(model.BuyerBankAccount))
            //    items = items.Where(x => x.BuyerBankAccount == model.BuyerBankAccount);

            if (!string.IsNullOrEmpty(model.UserNamePrinted))
                items = items.Where(x => x.UserNamePrinted == model.UserNamePrinted);

            if (model.InvoiceStatuses != null && model.InvoiceStatuses.Any())
                items = items.Where(x => model.InvoiceStatuses.Contains(x.InvoiceStatus));

            if (model.SignStatuses != null && model.SignStatuses.Any())
                items = items.Where(x => model.SignStatuses.Contains(x.SignStatus));

            if (model.ApproveStatuses != null && model.ApproveStatuses.Any())
                items = items.Where(x => model.ApproveStatuses.Contains(x.ApproveStatus));

            if (!string.IsNullOrEmpty(model.TransactionId))
                items = items.Where(x => x.TransactionId == model.TransactionId);

            if (!string.IsNullOrEmpty(model.ErpId))
                items = items.Where(x => x.ErpId == model.ErpId);

            if (model.FromNumber.HasValue)
                items = items.Where(x => x.Number >= model.FromNumber);

            if (model.ToNumber.HasValue)
                items = items.Where(x => x.Number <= model.ToNumber);

            if (!string.IsNullOrEmpty(model.UserNameCreator))
                items = items.Where(x => x.UserNameCreator.Contains(model.UserNameCreator));

            if (!string.IsNullOrEmpty(model.UserNameCreatorErp))
                items = items.Where(x => x.CreatorErp.Contains(model.UserNameCreatorErp));

            if (model.TotalPaymentAmount.HasValue)
                items = items.Where(x => x.TotalPaymentAmount <= model.TotalPaymentAmount);
            //if (model.Codes != null)
            //    items = items.Where(x => model.Codes.Contains(x.Code));

            return items;
        }
    }
}
