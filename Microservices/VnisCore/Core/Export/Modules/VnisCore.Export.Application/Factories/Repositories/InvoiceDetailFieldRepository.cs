using Core.Domain.Repositories;
using Core.Shared.Factory;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Export.Application.Factories.Repositories
{
    public class InvoiceDetailFieldRepository<T> : IInvoiceDetailFieldRepository<T> where T : BaseInvoiceDetailField
    {
        private readonly IAppFactory _appFactory;

        public InvoiceDetailFieldRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }
        public async Task<T> QueryByFieldNameAsync(Guid tenantId, string fieldName)
        {
            return await _appFactory.Repository<T, long>()
                .Where(x => x.TenantId == tenantId)
                .Where(x => x.FieldName == fieldName)
                .FirstOrDefaultAsync();
        }
        public async Task<List<T>> QueryByTenantCodeAsync(Guid tenantCode)
        {
            return await _appFactory.Repository<T, long>()
                .Where(x => x.TenantId == tenantCode)
                .ToListAsync();
        }

        public async Task<List<T>> QueryByFieldNamesAsNoTrackingAsync(Guid tenantId, string[] fieldNames)
        {
            return await _appFactory.Repository<T, long>()
                .Where(x => x.TenantId == tenantId && fieldNames.Contains(x.FieldName))
                .ToListAsync();
        }

    }
}
