using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using OfficeOpenXml;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Export.Application.Abstractions;
using VnisCore.Export.Application.Factories.Repositories;
using VnisCore.Export.Application.Factories.Repositories.Ticket;
using VnisCore.Export.Application.Interfaces;
using VnisCore.Export.Application.Models.NuocSachHaNoi;
using VnisCore.Export.Application.Models.Requests;
using VnisCore.Export.Application.Models.Responses;
using VnisCore.Export.Application.Models.Ticket;

namespace VnisCore.Export.Application.Services.Ticket
{
    public class TicketExportHeaderService : BaseInvoiceExportTicketService, IInvoiceExportService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAppFactory _appFactory;
        private readonly IConfiguration _configuration;

        public TicketExportHeaderService(
            IServiceProvider serviceProvider,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IConfiguration configuration) : base(serviceProvider, appFactory, localizer)
        {
            _serviceProvider = serviceProvider;
            _appFactory = appFactory;
            _configuration = configuration;
        }
        /// <summary>
        /// Export theo định dạng file csv
        /// </summary>
        /// <param name="exportJob"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public override async Task<ExportInvoiceApiResponseModel> ExportInvoiceAsync(TicketExportJobEntity exportJob, ExportInvoiceApiRequestModel request)
        {
            var invoices = await GetInvoicesAsync(request);

            //lấy cấu hình hiển thị các giá trị extra trên bảng kê
            var fieldHeaderNames = "MaVuViec;KenhPhatHanh;Taikhoan;SoButToan;NguoiduyetERP";
            var fieldDetailNames = "Mota";
            var headerFields = await GetHeaderFieldSpecificationAsync(request.TenantId, fieldHeaderNames);
            var detailFields = await GetDetailFieldSpecificationAsync(request.TenantId, fieldDetailNames);

            var repoTax = _appFactory.Repository<TaxEntity, long>();
            var taxes = await repoTax.Where(x => x.TenantId == request.TenantId).ToDictionaryAsync(x => x.Value, x => x.Code);

            // tạo file Csv
            var exportHeaders = new List<string>()
            {
                    "STT",
                    "Ngày hóa đơn",
                    "Mẫu số hoá đơn",
                    "Ký hiệu hoá đơn",
                    "Mã tra cứu",
                    "Số hóa đơn",
                    "Mã số thuế người mua",
                    "Tên người mua",
                    "Địa chỉ người mua",
                    "Doanh số bán chưa có thuế",
                    "Thuế suất GTGT",
                    "Tiền thuế GTGT",
                    "Tổng cộng tiền hàng",
                    "Số tiền chiết khấu",
                    "Tiền tệ",
                    "Tình trạng hóa đơn",
                    "User",
                    "Tình trạng ký",
                    "Mã CQT"
            };

            if (headerFields.Any())
                exportHeaders.AddRange(headerFields.Select(x => x.DisplayName));

            if (detailFields.Any())
                exportHeaders.AddRange(detailFields.Select(x => x.DisplayName));

            exportHeaders.Add("Mã khách hàng");

            var exportRecords = new List<List<string>>();
            string charReplace = string.Empty;
            if (request.isConvertToExcel)
            {
                charReplace = !_configuration["FileExport:charReplace"].IsNullOrEmpty() ? _configuration["FileExport:charReplace"] : string.Empty;
            }
            for (int i = 0; i < invoices.Count; i++)
            {
                var invoice = invoices.ElementAt(i);

                var isAdjHeader = false;
                if (invoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                {
                    isAdjHeader = true;
                }

                var invoiceStatus = _appFactory.CurrentTenant.Settings?.FirstOrDefault(x => x.Code == EnumExtension.TryToEnum<InvoiceStatus>(invoice.InvoiceStatus).ToString())?.Value;

                var signStatusExport = "";
                var signStatus = EnumExtension.TryToEnum<SignStatus>(invoice.SignStatus);
                if (signStatus != default && !int.TryParse(signStatus.ToString(), out int outSignStatus))
                    signStatusExport = signStatus.GetName();
                else
                    signStatusExport = invoice.SignStatus.ToString();


                if (invoice.InvoiceDetails == null)
                {
                    invoice.InvoiceDetails = new List<TicketDetailExportModel>
                        { new TicketDetailExportModel () };
                }

                //add detail
                if (invoice.InvoiceDetails != null && invoice.InvoiceDetails.Any())
                {
                    foreach (var item in invoice.InvoiceDetails)
                    {
                        var obj = new List<string> {
                            (i + 1).ToString(),
                            invoice.InvoiceDate.ToString("dd/MM/yyyy"),
                            invoice.TemplateNo.ToString(),
                            invoice.SerialNo,
                            invoice.TransactionId,
                            invoice.InvoiceNo,
                            invoice.BuyerTaxCode,
                            !string.IsNullOrEmpty(invoice.BuyerFullName) ? TrimData(invoice.BuyerFullName, charReplace): null,
                            !string.IsNullOrEmpty(invoice.BuyerAddressLine) ? TrimData(invoice.BuyerAddressLine, charReplace): null,
                            isAdjHeader ? "0" : invoice.TotalAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : taxes[item.VatPercent],
                            isAdjHeader ? "0" : invoice.TotalVatAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : invoice.TotalPaymentAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : invoice.TotalDiscountAmountBeforeTax.ToString().Replace(",", "."),
                            !string.IsNullOrEmpty(invoice.ToCurrency) ? TrimData(invoice.ToCurrency, charReplace): null,
                            !string.IsNullOrEmpty(invoiceStatus) ? invoiceStatus : EnumExtension.TryToEnum<InvoiceStatus>(invoice.InvoiceStatus).GetName(),
                            invoice.UserNameCreator,
                            signStatusExport,
                            invoice.VerificationCode,
                            invoice.BuyerCode
                        };

                        exportRecords.Add(obj);
                    }
                }
            }

            var exportCsv = new StringBuilder();

            exportRecords.ForEach(line =>
            {
                exportCsv.AppendLine(string.Join(",", line));
            });

            //Tạo file csv
            using var scoped = _serviceProvider.CreateScope();
            var configuation = scoped.ServiceProvider.GetService<IConfiguration>();
            var pathFolder = Path.Combine(configuation.GetSection("FileExport:PathFolder").Value.Trim(), exportJob.TaskId.ToString());

            if (!Directory.Exists(pathFolder))
                Directory.CreateDirectory(pathFolder);

            var filePath = Path.Combine(pathFolder, $"{exportJob.CurrentPage + 1}.csv");
            await File.WriteAllTextAsync(filePath, $"{string.Join(",", exportHeaders)}{Environment.NewLine}{exportCsv}".ToString(), Encoding.UTF8);
            var bytes = await File.ReadAllBytesAsync(filePath);

            //xóa file
            File.Delete(filePath);

            // Xoa Folder
            if (Directory.Exists(pathFolder))
                Directory.Delete(pathFolder);

            return new ExportInvoiceApiResponseModel
            {
                Bytes = bytes,
                FileName = Path.GetFileName(filePath)
            };
        }

        public override async Task<List<TicketExportModel>> GetInvoicesAsync(ExportInvoiceApiRequestModel request)
        {
            using var scope = _serviceProvider.CreateScope();
            var pagingModel = ToPagingModel(request);

            var repoInvoice01 = _serviceProvider.GetService<ITicketHeaderRepository>();
            var invoiceHeaders = (await repoInvoice01.PagingAsNoTrackingAsync(request.TenantId, pagingModel))
                                       .Data
                                       .Select(x => (TicketExportModel)x)
                                       .ToDictionary(x => x.Id, x => x);

            var idInvoiceHeaders = invoiceHeaders.Keys.ToList();

            //detail
            var repoInvoiceDetail = _serviceProvider.GetService<IInvoiceDetailRepository<TicketDetailEntity>>();
            var entityInvoiceDetails = await repoInvoiceDetail.QueryByInvoiceHeaderIdsAsNoTrackingAsync(idInvoiceHeaders.ToList());

            //chuyen ve model
            var invoiceDetails = entityInvoiceDetails.Select(x => (TicketDetailExportModel)x).ToList();

            //add DetailExtras
            foreach (var item in invoiceHeaders.Values.ToList())
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    if (item.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceHeaderExtras"]))
                    {
                        var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceHeaderExtras"]);
                        if (headerExtras.Any())
                            item.InvoiceHeaderExtras = headerExtras.Select(x => new TicketHeaderExtraExportModel
                            {
                                FieldValue = x.FieldValue,
                            }).ToList();
                    }
                }
            }

            var groupInvoiceDetails = invoiceDetails.GroupBy(x => x.InvoiceHeaderId)
                                                    .ToDictionary(x => x.Key, x => x.ToList());

            //add details
            foreach (var item in groupInvoiceDetails)
            {
                if (invoiceHeaders.ContainsKey(item.Key))
                    invoiceHeaders[item.Key].InvoiceDetails = item.Value;
            }

            //add headerExtras
            foreach (var item in invoiceHeaders.Values.ToList())
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    if (item.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceHeaderExtras"]))
                    {
                        var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceHeaderExtras"]);
                        if (headerExtras.Any())
                            item.InvoiceHeaderExtras = headerExtras.Select(x => new TicketHeaderExtraExportModel
                            {
                                FieldValue = x.FieldValue,
                            }).ToList();
                    }
                }
            }

            return invoiceHeaders.Select(x => x.Value).ToList();
        }

        public override async Task<byte[]> ConvertToExcelAsync(string path, List<string> lines, List<NuocSachHaNoiInvoiceModel> invoiceWatersModel, ExportInvoiceApiRequestModel request)
        {
            string charReplace = string.Empty;
            if (request.isConvertToExcel)
            {
                charReplace = !_configuration["FileExport:charReplace"].IsNullOrEmpty() ? _configuration["FileExport:charReplace"] : string.Empty;
            }
            var excelPath = Path.Combine(path, "outputConvertCSVToExcelFormats.xlsx");
            using (var package = new ExcelPackage(new FileInfo(excelPath)))
            {
                {
                    var worksheet = package.Workbook.Worksheets.Add("Worksheet1");

                    //Tiêu đề
                    var headerRow = new List<string[]>()
                {
                    lines[0].Split(",")
                };
                    worksheet.Cells[1, 1, 1, headerRow.Count()].LoadFromArrays(headerRow);

                    //
                    for (int i = 1; i < lines.Count(); i++)
                    {
                        var bodyRow = new List<string[]>()
                    {
                        lines[i].Split(",")
                    };
                        var row = bodyRow[0];
                        if (row.Count() == 1)
                            continue;
                        worksheet.Cells[i + 1, 1].Value = row[0];
                        worksheet.Cells[i + 1, 2].Value = row[1];
                        worksheet.Cells[i + 1, 3].Value = row[2];
                        worksheet.Cells[i + 1, 4].Value = row[3];
                        worksheet.Cells[i + 1, 5].Value = row[4];
                        worksheet.Cells[i + 1, 6].Value = row[5];
                        worksheet.Cells[i + 1, 7].Value = row[6];
                        worksheet.Cells[i + 1, 8].Value = row[7].Replace(charReplace, ",");
                        worksheet.Cells[i + 1, 9].Value = row[8].Replace(charReplace, ",");

                        worksheet.Cells[i + 1, 10].Value = row[9] != "" ? decimal.Parse(row[9].ToString().Replace(".", ",")) : 0;
                        worksheet.Cells[i + 1, 10].Style.Numberformat.Format = "#,##0";

                        worksheet.Cells[i + 1, 11].Value = row[10];

                        worksheet.Cells[i + 1, 12].Value = row[11] != "" ? decimal.Parse(row[11].ToString().Replace(".", ",")) : 0;
                        worksheet.Cells[i + 1, 12].Style.Numberformat.Format = "#,##0";

                        worksheet.Cells[i + 1, 13].Value = row[12] != "" ? decimal.Parse(row[12].ToString().Replace(".", ",")) : 0;
                        worksheet.Cells[i + 1, 13].Style.Numberformat.Format = "#,##0";

                        worksheet.Cells[i + 1, 14].Value = row[13].Replace(charReplace, ",");
                        worksheet.Cells[i + 1, 15].Value = row[14];
                        worksheet.Cells[i + 1, 16].Value = row[15];
                        worksheet.Cells[i + 1, 17].Value = row[16];
                        worksheet.Cells[i + 1, 18].Value = row[17];


                        for (int j = 18; j < row.Count(); j++)
                        {
                            worksheet.Cells[i + 1, j + 1].Value = row[j];
                        }
                    }
                    package.Save();
                }
                var bytes = await File.ReadAllBytesAsync(excelPath);
                return bytes;
            }

        }
    }
}
