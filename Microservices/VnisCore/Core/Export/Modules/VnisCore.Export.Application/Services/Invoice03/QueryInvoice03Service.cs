using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;

using Microsoft.Extensions.DependencyInjection;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Export.Application.Factories.Models;
using VnisCore.Export.Application.Factories.Repositories;
using VnisCore.Export.Application.Interfaces;
using VnisCore.Export.Application.Models.Invoice03s;

namespace VnisCore.Export.Application.Services.Invoice03
{
    public class QueryInvoice03Service : IQueryInvoice03Service
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;

        public QueryInvoice03Service(IAppFactory appFactory,
            IServiceProvider serviceProvider)
        {
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
        }

        public async Task<List<Invoice03ExportModel>> ConvertDetail(List<Invoice03ExportModel> input)
        {
            var invoiceHeaders = input.ToDictionary(x => x.Id, x => x);
            var idInvoiceHeaders = invoiceHeaders.Keys.ToList();

            //detail
            var repoInvoiceDetail = _serviceProvider.GetService<IInvoiceDetailRepository<Invoice03DetailEntity>>();
            var entityInvoiceDetails = await repoInvoiceDetail.QueryByInvoiceHeaderIdsAsNoTrackingAsync(idInvoiceHeaders.ToList());

            //chuyen ve model
            var invoiceDetails = entityInvoiceDetails.Select(x => (Invoice03DetailExportModel)x).ToList();

            //add DetailExtras
            foreach (var item in invoiceDetails)
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    var extraProperties = new List<InvoiceDetailExtraModel>();
                    if (item.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceDetailExtras"]))
                    {
                        extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(item.ExtraProperties["invoiceDetailExtras"]);
                    }
                    if (extraProperties.Any())
                    {
                        var detailExtras = extraProperties.GroupBy(x => x.InvoiceDetailId).ToDictionary(x => x.Key, x => x.ToList());
                        item.InvoiceDetailExtras = detailExtras[item.Id].Select(x => new Invoice03DetailExtraExportModel
                        {
                            FieldValue = x.FieldValue,
                            FieldName = x.FieldName,
                        }).ToList();
                    }
                }
            }

            var groupInvoiceDetails = invoiceDetails.GroupBy(x => x.InvoiceHeaderId)
                                                    .ToDictionary(x => x.Key, x => x.ToList());

            //add details
            foreach (var item in groupInvoiceDetails)
            {
                if (invoiceHeaders.ContainsKey(item.Key))
                    invoiceHeaders[item.Key].InvoiceDetails = item.Value;
            }

            //add headerExtras
            foreach (var item in invoiceHeaders.Values.ToList())
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    if (item.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceHeaderExtras"]))
                    {
                        var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceHeaderExtras"]);
                        if (headerExtras.Any())
                            item.InvoiceHeaderExtras = headerExtras.Select(x => new Invoice03HeaderExtraExportModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                    }
                }
            }

            return invoiceHeaders.Select(x => x.Value).ToList();
        }

        public async Task<List<Invoice03ExportModel>> ConvertHeaderExtras(List<Invoice03ExportModel> input)
        {
            var invoiceHeaders = input.ToDictionary(x => x.Id, x => x);
            var idInvoiceHeaders = invoiceHeaders.Keys.ToList();

            //add headerExtras
            foreach (var item in invoiceHeaders.Values.ToList())
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    if (item.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceHeaderExtras"]))
                    {
                        var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceHeaderExtras"]);
                        if (headerExtras.Any())
                            item.InvoiceHeaderExtras = headerExtras.Select(x => new Invoice03HeaderExtraExportModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName,
                            }).ToList();
                    }
                }
            }


            return invoiceHeaders.Select(x => x.Value).ToList();
        }

        public async Task<string> GetDrawQuery(Guid tenantId, Guid userId, PagingInvoiceModel request)
        {
            var query = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var accountType = _appFactory.CurrentUser.Type;
            var conditionInvoiceTemplateId = new StringBuilder();

            if (_appFactory.CurrentUser.Type != 2 && request.AllReadTemplateIds != null && request.AllReadTemplateIds.Any())
            {
                conditionInvoiceTemplateId.Append($@" AND ""InvoiceTemplateId"" IN({string.Join(", ", request.AllReadTemplateIds.Select(x => $@"'{x}'"))}) ");
            }

            if (request.IsNullInvoice)
                query.Append($@" AND ""Number"" IS NULL ");
            else
                query.Append($@" AND ""Number"" IS NOT NULL ");

            //OR ""BuyerCode"" LIKE N'%{request.Keyword}%'
            var queryBuyerCode = new StringBuilder();
            if (accountType == 2)
                queryBuyerCode.Append($@" OR ""BuyerCode"" = '{request.Keyword}' ");
            else
                queryBuyerCode.Append($@" OR ""BuyerCode"" LIKE N'%{request.Keyword}%' ");

            if (!string.IsNullOrEmpty(request.Keyword))
            {
                var q = request.Keyword.Trim()?.Replace("'", "''").ToLower();
                int.TryParse(q, out int outInvoiceNo);

                var invoiceStatus = EnumExtension.TryToEnum<InvoiceStatus>(request.Keyword);
                var signStatus = EnumExtension.TryToEnum<SignStatus>(request.Keyword);

                query.Append($@" AND (
                                       LOWER(""UserNameCreator"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""ReceiverFullName"") LIKE LOWER(N'%{q}%')
                                    OR LOWER(""ReceiverName"") LIKE LOWER(N'%{q}%')
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{q}%')
                                    OR ""ErpId"" LIKE '%{q}%' 
                                    OR ""TransactionId"" LIKE '%{q}%' 
                                    OR ""CreatorErp"" LIKE N'%{q}%' 
                                    ) ");
            }

            //if (request.TemplateNo.HasValue)
            //    query.Append($@" AND ""TemplateNo"" = {request.TemplateNo} ");

            if (request.CreateFromDate.HasValue)
            {
                var createFromDate = request.CreateFromDate.Value.Date.ToUniversalTime();
                query.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (request.CreateToDate.HasValue)
            {
                var createToDate = request.CreateToDate.Value.Date.AddDays(1);
                query.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (!string.IsNullOrEmpty(request.Customers))
            {
                var customers = request.Customers?.Trim()?.Replace("'", "''");
                //""BuyerCode"" LIKE '%{customers}%' OR
                query.Append($@"AND (
                                        LOWER(""BuyerLegalName"") LIKE LOWER(N'%{customers}%') 
                                        {queryBuyerCode}
                                        OR LOWER(""BuyerFullName"") LIKE LOWER(N'%{customers}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{customers}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customers}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(request.Customer))
            {
                var customer = request.Customer?.Trim()?.Replace("'", "''");
                query.Append($@"AND (""BuyerFullName"" LIKE N'%{customer}%' 
                                        OR ""BuyerPhoneNumber"" LIKE '%{customer}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customer}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(request.BuyerTaxCode))
            {
                query.Append($@" AND ""BuyerTaxCode"" LIKE '%{request.BuyerTaxCode}%' ");
            }

            if (!string.IsNullOrEmpty(request.BuyerFullName))
            {
                query.Append($@" AND LOWER(""BuyerFullName"") LIKE LOWER(N'%{request.BuyerFullName.Replace("'", "''")}%') ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            if (request.InvoiceTemplateIds != null && request.InvoiceTemplateIds.Any())
                query.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', request.InvoiceTemplateIds)}) ");
            else
            {
                if (_appFactory.CurrentUser.Type != 2 && request.AllReadTemplateIds != null && request.AllReadTemplateIds.Any())
                {
                    //lấy danh sách mẫu dc xem
                    query.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""TemplateNo"" = {6} AND ""Type"" = 3 
                                    ) ");
                }
            }

            if (!string.IsNullOrEmpty(request.InvoiceNo))
            {
                int.TryParse(request.InvoiceNo, out int outInvoiceNo);
                query.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (request.InvoiceStatuses != null && request.InvoiceStatuses.Any())
                query.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", request.InvoiceStatuses)}) ");

            if (request.SignStatuses != null && request.SignStatuses.Any())
                query.Append($@"AND ""SignStatus"" IN ({String.Join(",", request.SignStatuses)}) ");


            if (request.VerificationCodeStatuses != null && request.VerificationCodeStatuses.Any())
            {
                if (request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && !request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    query.Append($@"AND ""VerificationCode"" IS NOT NULL ");
                }
                else if (!request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    query.Append($@"AND ""VerificationCode"" IS NULL ");
                }
            }

            if (request.ApproveStatuses != null && request.ApproveStatuses.Any())
                query.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", request.ApproveStatuses)}) ");

            if (request.Source != null && request.Source.Any())
                query.Append($@"AND ""Source"" In ({String.Join(",", request.Source)}) ");

            if (request.FromNumber.HasValue)
                query.Append($@"AND ""Number"" >= {request.FromNumber.Value} ");

            if (request.ToNumber.HasValue)
                query.Append($@"AND ""Number"" <= {request.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(request.UserNameCreator))
                query.Append($@"AND ""UserNameCreator"" LIKE '%{request.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(request.UserNameCreatorErp))
                query.Append($@"AND ""CreatorErp"" LIKE '%{request.UserNameCreatorErp}%' ");

            if (!string.IsNullOrEmpty(request.BuyerCode))
            {
                if (accountType == 2)
                    query.Append($@" AND ""BuyerCode"" = '{request.BuyerCode}' ");
                else
                    query.Append($@" AND ""BuyerCode"" LIKE '%{request.BuyerCode}%' ");
            }

            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                query.Append("AND (");

                int index = 0;
                int numberOfHeaderExtra = request.InvoiceHeaderExtras.Count;

                foreach (var item in request.InvoiceHeaderExtras)
                {
                    if (!item.FieldValue.IsNullOrEmpty())
                    {
                        if ((numberOfHeaderExtra - index) == 1)
                        {
                            query.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{(item.FieldValue?.Replace("'", "''"))}\""%'");
                        }
                        else
                        {
                            query.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
                        }
                    }

                    index++;
                }

                query.Append(")");
            }

            // tìm kiếm theo detailExtra
            var joinDetailExtra = new StringBuilder();
            var conditionDetailExtra = new StringBuilder();
            if (request.InvoiceDetailExtras != null && request.InvoiceDetailExtras.Any())
            {
                conditionDetailExtra.Append("AND (");

                int index = 0;
                int numberOfDetailExtra = request.InvoiceDetailExtras.Count;

                foreach (var item in request.InvoiceDetailExtras)
                {
                    if (!item.FieldValue.IsNullOrEmpty())
                    {
                        if ((numberOfDetailExtra - index) == 1)
                        {
                            conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%'");
                        }
                        else
                        {
                            conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
                        }
                    }

                    index++;
                }

                conditionDetailExtra.Append(")");

                joinDetailExtra.Append($@"INNER JOIN (SELECT DISTINCT ""InvoiceHeaderId"" FROM ""Invoice03Detail"" WHERE ""TenantId"" = '{rawTenantId}' {conditionDetailExtra}) B ON B.""InvoiceHeaderId"" = A.""Id""");
            }

            var detailExtraSearch = new StringBuilder();
            if (!string.IsNullOrEmpty(request.DetailExtra))
            {
                detailExtraSearch.Append($@"INNER JOIN (SELECT DISTINCT ""InvoiceHeaderId"" FROM ""Invoice03Detail"" WHERE ""TenantId"" = '{rawTenantId}' AND ""ExtraProperties"" LIKE '%{request.DetailExtra}%') C ON C.""InvoiceHeaderId"" = A.""Id""");
            }

            var sql = new StringBuilder();
            sql.Append($@"SELECT *  FROM
                            (SELECT * FROM (
                                SELECT A.*, rownum rn                                                          
                                FROM                                                             
                                    (                                                                          
                                        SELECT  *                               
                                        FROM ""Invoice03Header"" A        
                                            {joinDetailExtra}
                                            {detailExtraSearch}
                                        WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 {query}                     
                                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                    ) A
                                WHERE rownum <= {(request.Page - 1) * request.Size + request.Size} 
                                )
                            WHERE rn > {(request.Page - 1) * request.Size}
                        ) 
                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC ");


            return sql.ToString();
        }

        public async Task<string> GetDrawQueryCount(Guid tenantId, Guid userId, PagingInvoiceModel request)
        {
            var query = new StringBuilder();
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);

            var accountType = _appFactory.CurrentUser.Type;
            var conditionInvoiceTemplateId = new StringBuilder();

            if (_appFactory.CurrentUser.Type != 2 && request.AllReadTemplateIds != null && request.AllReadTemplateIds.Any())
            {
                conditionInvoiceTemplateId.Append($@" AND ""InvoiceTemplateId"" IN({string.Join(", ", request.AllReadTemplateIds.Select(x => $@"'{x}'"))}) ");
            }

            if (request.IsNullInvoice)
                query.Append($@" AND ""Number"" IS NULL ");
            else
                query.Append($@" AND ""Number"" IS NOT NULL ");

            //OR ""BuyerCode"" LIKE N'%{request.Keyword}%'
            var queryBuyerCode = new StringBuilder();
            if (accountType == 2)
                queryBuyerCode.Append($@" OR ""BuyerCode"" = '{request.Keyword}' ");
            else
                queryBuyerCode.Append($@" OR ""BuyerCode"" LIKE '%{request.Keyword}%' ");

            if (!string.IsNullOrEmpty(request.Keyword))
            {
                var q = request.Keyword.Trim()?.Replace("'", "''").ToLower();
                int.TryParse(q, out int outInvoiceNo);

                var invoiceStatus = EnumExtension.TryToEnum<InvoiceStatus>(request.Keyword);
                var signStatus = EnumExtension.TryToEnum<SignStatus>(request.Keyword);

                query.Append($@" AND (
                                    LOWER(""UserNameCreator"") LIKE LOWER(N'%{q}%') 
                                    OR LOWER(""ReceiverFullName"") LIKE LOWER(N'%{q}%')
                                    OR LOWER(""ReceiverName"") LIKE LOWER(N'%{q}%')
                                    OR ""Number"" = {outInvoiceNo} 
                                    OR LOWER(""SerialNo"") LIKE LOWER('%{q}%')
                                    OR ""ErpId"" LIKE '%{q}%' 
                                    OR ""TransactionId"" LIKE '%{q}%' 
                                    OR ""CreatorErp"" LIKE N'%{q}%' 
                                    ) ");
            }
            if (request.TemplateNo.HasValue)
                query.Append($@" AND ""TemplateNo"" = {request.TemplateNo} ");

            if (request.CreateFromDate.HasValue)
            {
                var createFromDate = request.CreateFromDate.Value.Date.ToUniversalTime();
                query.Append($@"AND ""InvoiceDate"" >= '{createFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (request.CreateToDate.HasValue)
            {
                var createToDate = request.CreateToDate.Value.Date.AddDays(1);
                query.Append($@"AND ""InvoiceDate"" < '{createToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (request.IssuedTime.HasValue)
            {
                var issueTimeDate = request.IssuedTime.Value.Date;
                var issueTimePreviousDate = issueTimeDate.AddDays(-1);
                var issueTimeDateAddDate = issueTimeDate.AddDays(1);

                query.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" < '{issueTimeDateAddDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                    AND ""IssuedTime"" > '{issueTimePreviousDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' 
                                    ");
            }

            if (request.IssueFromDate.HasValue)
            {
                var issueFromDate = request.IssueFromDate.Value.Date;
                query.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" >= '{issueFromDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (request.IssueToDate.HasValue)
            {
                var issueToDate = request.IssueToDate.Value.Date.AddDays(1);
                query.Append($@"AND ""IssuedTime"" IS NOT NULL 
                                    AND ""IssuedTime"" < '{issueToDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}' ");
            }

            if (request.CancelFromDate.HasValue)
                query.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" >= '{request.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" >= '{request.CancelFromDate.Value.Date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");

            if (request.CancelToDate.HasValue)
            {
                query.Append($@"AND 
                                    (
                                        (
                                            ""CancelTime"" IS NOT NULL 
                                            AND ""CancelTime"" < '{request.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                        OR 
                                        (
                                            ""DeleteTime"" IS NOT NULL 
                                            AND ""DeleteTime"" < '{request.CancelToDate.Value.Date.AddDays(1).ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                                            AND ""InvoiceStatus"" = {(short)InvoiceStatus.XoaBo}
                                        )
                                    )");
            }

            if (!string.IsNullOrEmpty(request.Customers))
            {
                var customers = request.Customers?.Trim()?.Replace("'", "''");
                // ""BuyerCode"" LIKE '%{customers}%' OR
                query.Append($@"AND (
                                        LOWER(""BuyerLegalName"") LIKE LOWER(N'%{customers}%') 
                                        {queryBuyerCode}
                                        OR LOWER(""BuyerFullName"") LIKE LOWER(N'%{customers}%') 
                                        OR ""BuyerTaxCode"" LIKE '%{customers}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customers}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(request.Customer))
            {
                var customer = request.Customer?.Trim()?.Replace("'", "''");
                query.Append($@"AND (""BuyerFullName"" LIKE N'%{customer}%' 
                                        OR ""BuyerPhoneNumber"" LIKE '%{customer}%' 
                                        OR LOWER(""BuyerEmail"") LIKE LOWER('%{customer}%') 
                                    ) ");
            }

            if (!string.IsNullOrEmpty(request.BuyerTaxCode))
            {
                query.Append($@" AND ""BuyerTaxCode"" LIKE '%{request.BuyerTaxCode}%' ");
            }

            if (!string.IsNullOrEmpty(request.BuyerFullName))
            {
                query.Append($@" AND LOWER(""BuyerFullName"") LIKE LOWER(N'%{request.BuyerFullName.Replace("'", "''")}%') ");
            }

            // Tìm kiếm theo InvoiceTemplateIds
            if (request.InvoiceTemplateIds != null && request.InvoiceTemplateIds.Any())
                query.Append($@" AND ""InvoiceTemplateId"" in ({string.Join(',', request.InvoiceTemplateIds)}) ");
            else
            {
                if (_appFactory.CurrentUser.Type != 2 && request.AllReadTemplateIds != null && request.AllReadTemplateIds.Any())
                {
                    //lấy danh sách mẫu dc xem
                    query.Append(@$" AND ""InvoiceTemplateId"" in 
                                    (
                                        SELECT  T.""Id"" 
                                        FROM ""InvoiceTemplate"" T
                                        INNER JOIN(
                                            SELECT ""InvoiceTemplateId""
                                            FROM ""UserReadTemplate""
                                            WHERE ""UserId"" = '{rawUserId}' AND ""InvoiceTemplateId"" IS NOT NULL
                                        )
                                        A ON T.""Id"" = A.""InvoiceTemplateId""
                                        WHERE T.""TenantId"" = '{rawTenantId}' AND T.""IsDeleted"" = 0 
                                        AND T.""TemplateNo"" = {6} AND T.""Type"" = 3
                                    ) ");
                }
            }

            if (!string.IsNullOrEmpty(request.InvoiceNo))
            {
                int.TryParse(request.InvoiceNo, out int outInvoiceNo);
                query.Append($@"AND ""Number"" = {outInvoiceNo} ");
            }

            if (request.InvoiceStatuses != null && request.InvoiceStatuses.Any())
                query.Append($@"AND ""InvoiceStatus"" IN ({String.Join(",", request.InvoiceStatuses)}) ");

            if (request.SignStatuses != null && request.SignStatuses.Any())
                query.Append($@"AND ""SignStatus"" IN ({String.Join(",", request.SignStatuses)}) ");


            if (request.VerificationCodeStatuses != null && request.VerificationCodeStatuses.Any())
            {
                if (request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && !request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    query.Append($@"AND ""VerificationCode"" IS NOT NULL ");
                }
                else if (!request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.DaCapMa)
                    && request.VerificationCodeStatuses.Contains((short)VerificationCodeStatuses.ChuaCapMa))
                {
                    query.Append($@"AND ""VerificationCode"" IS NULL ");
                }
            }

            if (request.ApproveStatuses != null && request.ApproveStatuses.Any())
                query.Append($@"AND ""ApproveStatus"" IN ({String.Join(",", request.ApproveStatuses)}) ");

            if (request.Source != null && request.Source.Any())
                query.Append($@"AND ""Source"" In ({String.Join(",", request.Source)}) ");

            if (request.FromNumber.HasValue)
                query.Append($@"AND ""Number"" >= {request.FromNumber.Value} ");

            if (request.ToNumber.HasValue)
                query.Append($@"AND ""Number"" <= {request.ToNumber.Value} ");

            if (!string.IsNullOrEmpty(request.UserNameCreator))
                query.Append($@"AND ""UserNameCreator"" LIKE '%{request.UserNameCreator}%' ");

            if (!string.IsNullOrEmpty(request.UserNameCreatorErp))
                query.Append($@"AND ""CreatorErp"" LIKE '%{request.UserNameCreatorErp}%' ");

            if (!string.IsNullOrEmpty(request.BuyerBankAccount))
                query.Append($@"AND ""BuyerBankAccount"" LIKE '%{request.BuyerBankAccount.Replace("'", "''")}%' ");

            // query.Append($@"AND ""BuyerCode"" LIKE '%{request.BuyerCode}%' ");
            if (!string.IsNullOrEmpty(request.BuyerCode))
            {
                if (accountType == 2)
                    query.Append($@" AND ""BuyerCode"" = '{request.BuyerCode}' ");
                else
                    query.Append($@" AND ""BuyerCode"" LIKE '%{request.BuyerCode}%' ");
            }

            if (request.InvoiceHeaderExtras != null && request.InvoiceHeaderExtras.Any())
            {
                query.Append("AND (");

                int index = 0;
                int numberOfHeaderExtra = request.InvoiceHeaderExtras.Count;

                foreach (var item in request.InvoiceHeaderExtras)
                {
                    if (!item.FieldValue.IsNullOrEmpty())
                    {
                        if ((numberOfHeaderExtra - index) == 1)
                        {
                            query.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{(item.FieldValue?.Replace("'", "''"))}\""%'");
                        }
                        else
                        {
                            query.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
                        }
                    }

                    index++;
                }

                query.Append(")");
            }

            // tìm kiếm theo detailExtra
            var joinDetailExtra = new StringBuilder();
            var conditionDetailExtra = new StringBuilder();
            if (request.InvoiceDetailExtras != null && request.InvoiceDetailExtras.Any())
            {
                conditionDetailExtra.Append("AND (");

                int index = 0;
                int numberOfDetailExtra = request.InvoiceDetailExtras.Count;

                foreach (var item in request.InvoiceDetailExtras)
                {
                    if (!item.FieldValue.IsNullOrEmpty())
                    {
                        if ((numberOfDetailExtra - index) == 1)
                        {
                            conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%'");
                        }
                        else
                        {
                            conditionDetailExtra.Append($@"""ExtraProperties"" LIKE '%\""FieldName\"":\""{item.FieldName}\"",\""FieldValue\"":\""{item.FieldValue?.Replace("'", "''")}\""%' OR");
                        }
                    }

                    index++;
                }

                conditionDetailExtra.Append(")");

                joinDetailExtra.Append($@"INNER JOIN (SELECT DISTINCT ""InvoiceHeaderId"" FROM ""Invoice03Detail"" WHERE ""TenantId"" = '{rawTenantId}' {conditionDetailExtra}) B ON B.""InvoiceHeaderId"" = A.""Id""");
            }

            var detailExtraSearch = new StringBuilder();
            if (!string.IsNullOrEmpty(request.DetailExtra))
            {
                detailExtraSearch.Append($@"INNER JOIN (SELECT DISTINCT ""InvoiceHeaderId"" FROM ""Invoice03Detail"" WHERE ""TenantId"" = '{rawTenantId}' AND ""ExtraProperties"" LIKE '%{request.DetailExtra}%') C ON C.""InvoiceHeaderId"" = A.""Id""");
            }

            var sql = new StringBuilder();
            sql.Append($@"SELECT *  FROM
                            (SELECT * FROM                                                    
                                    (                                                                          
                                        SELECT  ""Id"",                                                         
                                                ""ErpId"",                                                       
                                                ""TransactionId"",                                             
                                                ""TemplateNo"",                                                
                                                ""SerialNo"",                                                  
                                                ""InvoiceNo"",                                                 
                                                ""Number"",                                                    
                                                ""InvoiceStatus"",                                             
                                                ""SignStatus"",                                                
                                                ""ApproveStatus"",                                             
                                                ""ApproveCancelStatus"",                                             
                                                ""ApproveDeleteStatus"",                                             
                                                ""InvoiceDate"",
                                                ""DeliveryOrderNumber"",
                                                ""DeliveryOrderBy"",
                                                ""DeliveryBy"",
                                                ""ReceiverName"",
                                                ""ReceiverTaxCode"",
                                                ""ReceiverAddressLine"",
                                                ""ReceiverFullName"",
                                                ""TotalAmount"",                                               
                                                ""TotalPaymentAmount"",                                        
                                                ""PrintedTime"",                                               
                                                ""FullNameCreator"",                                           
                                                ""UserNameCreator"",                                           
                                                ""CreatorErp"",                                                
                                                ""Note"",                                                      
                                                ""Source"",                                                    
                                                ""IsViewed"",                                                  
                                                ""IsOpened"",                                                  
                                                ""IsDeclared"",                                                  
                                                ""VerificationCode"",                                                  
                                                ""ReferenceInvoiceType"",                                                  
                                                ""ExtraProperties"",                                                            
                                                COUNT(*) OVER () TotalItems                                   
                                        FROM ""Invoice03Header"" A        
                                            {joinDetailExtra}
                                            {detailExtraSearch}
                                        WHERE ""TenantId"" = '{rawTenantId}' AND ""IsDeleted"" = 0 {query}                     
                                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC, ""CreationTime"" DESC  
                                    ) A
                        ) 
                        InvoiceHeader
                        ORDER BY ""InvoiceDate"" DESC, ""SerialNo"" DESC, ""Number"" DESC ");


            return sql.ToString();
        }
    }
}
