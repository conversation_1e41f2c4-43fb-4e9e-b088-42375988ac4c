using Core;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.DataExporting;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Export.Application.Models.InvoiceError;

namespace VnisCore.Export.Application.Services.InvoiceError.Business
{
    public interface IExportInsideSystemBusiness
    {
        Task<FileDto> ExportExcelAsync(VnisType vnisType, InvoiceErrorExportRequestDto request);
    }

    public class ExportInsideSystemBusiness : IExportInsideSystemBusiness
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice01ErrorEntity, long> _repoInvoice01Error;
        private readonly IRepository<Invoice02ErrorEntity, long> _repoInvoice02Error;
        private readonly IRepository<Invoice03ErrorEntity, long> _repoInvoice03Error;
        private readonly IRepository<Invoice04ErrorEntity, long> _repoInvoice04Error;
        private readonly IRepository<TicketErrorEntity, long> _repoTicketError;
        public ExportInsideSystemBusiness(
            IAppFactory appFactory,
            IRepository<Invoice01ErrorEntity, long> repoInvoice01Error,
            IRepository<Invoice02ErrorEntity, long> repoInvoice02Error,
            IRepository<Invoice03ErrorEntity, long> repoInvoice03Error,
            IRepository<Invoice04ErrorEntity, long> repoInvoice04Error,
            IRepository<TicketErrorEntity, long> repoTicketError
            )
        {
            _appFactory = appFactory;
            _repoInvoice01Error = repoInvoice01Error;
            _repoInvoice02Error = repoInvoice02Error;
            _repoInvoice03Error = repoInvoice03Error;
            _repoInvoice04Error = repoInvoice04Error;
            _repoTicketError = repoTicketError;
        }

        public async Task<FileDto> ExportExcelAsync(VnisType vnisType, InvoiceErrorExportRequestDto request)
        {
            switch ((short)vnisType)
            {
                case (short)VnisType._01GTKT:
                    return await ExportInvoiceError01Async(request);
                case (short)VnisType._02GTTT:
                    return await ExportInvoiceError02Async(request);
                case (short)VnisType._03XKNB:
                    return await ExportInvoiceError03Async(request);
                case (short)VnisType._04HGDL:
                    return await ExportInvoiceError04Async(request);
                case (short)VnisType._05TVDT:
                    return await ExportTicketErrorAsync(request);
                default:
                    throw new UserFriendlyException("Loại hoá đơn không đúng");
            }
        }


        private async Task<FileDto> ExportInvoiceError01Async(InvoiceErrorExportRequestDto input)
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tenant");
            }    

            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;
            var query = _repoInvoice01Error.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.VerificationCode, keyword)
                         || EF.Functions.Like(x.SerialNo, keyword)
                         )
                .WhereIf(input.SignStatuses.Any(), x => input.SignStatuses.Contains((SignStatus)x.SignStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || (TvanStatus)x.TvanStatus == TvanStatus.TvanAccept
                                || (TvanStatus)x.TvanStatus == TvanStatus.TvanReject
                                || (TvanStatus)x.TvanStatus == TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || (TvanStatus)x.TvanStatus == TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || (TvanStatus)x.TvanStatus == TvanStatus.TvanAccept
                                || (TvanStatus)x.TvanStatus == TvanStatus.TvanReject)
                .WhereIf(input.CreateFromDate.HasValue, x => x.AnnouncementDate.Date >= input.CreateFromDate.Value.Date)
                .WhereIf(input.CreateToDate.HasValue, x => x.AnnouncementDate.Date <= input.CreateToDate.Value.AddDays(1).Date)
                .WhereIf(!input.SerialNo.IsNullOrWhiteSpace(), x => x.SerialNo == input.SerialNo)
                .WhereIf(input.GroupCode.HasValue, x => x.GroupCode == input.GroupCode)
                .WhereIf(!input.NumberInvoiceError.IsNullOrWhiteSpace(), x => x.NumberInvoiceError == input.NumberInvoiceError)
                .Where(x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var errs = await query.ToListAsync();

            var groupItems = errs.GroupBy(x => new { x.GroupCode, x.TaxDepartment, x.AnnouncementDate, x.CodeTaxDepartment, x.PlaceName, x.BudgetUnitCode })
            .Select(x => new InvoiceErrorExportResponseDto
            {
                GroupCode = x.Key.GroupCode.ToString(),
                TaxDepartment = x.Key.TaxDepartment,
                AnnouncementDate = x.Key.AnnouncementDate,
                AnnouncementDateDisplay = x.Key.AnnouncementDate.ToString("dd/MM/yyyy"),
                CodeTaxDepartment = x.Key.CodeTaxDepartment,
                PlaceName = x.Key.PlaceName,
                BudgetUnitCode = x.Key.BudgetUnitCode,
                SignStatus = x.First().SignStatus,
                NumberInvoiceError = x.First().NumberInvoiceError,
                TvanStatus = x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.UnSent) != null ? (short)TvanStatus.UnSent : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.SendError) != null ? (short)TvanStatus.SendError : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.Sended) != null ? (short)TvanStatus.Sended : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss) != null ? (short)TvanStatus.TvanReject : (short)TvanStatus.TCTAccept))),
                InvoiceErrors = x.Select(t => new InvoiceErrorHeaderModel
                {
                    Id = t.Id,
                    Index = t.Index,
                    VerificationCode = t.VerificationCode,
                    InvoiceHeaderId = t.InvoiceHeaderId,
                    TemplateNo = t.TemplateNo,
                    SerialNo = t.SerialNo,
                    InvoiceNo = t.InvoiceNo,
                    InvoiceDate = t.InvoiceDate,
                    InvoiceType = t.InvoiceType,
                    Action = t.Action,
                    Reason = t.Reason,
                    SignStatus = t.SignStatus,
                    SellerSignedTime = t.SellerSignedTime,
                    SellerFullNameSigned = t.SellerFullNameSigned,
                    SellerSignedId = t.SellerSignedId,
                    TvanStatus = t.TvanStatus,
                    Number = t.Number,
                    NumberInvoiceError = t.NumberInvoiceError
                }).OrderBy(x => x.InvoiceNo).ToList()
            })
            .WhereIf(!input.VerificationCode.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.VerificationCode).Contains(input.VerificationCode))
            .WhereIf(!input.InvoiceNo.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.InvoiceNo).Contains(input.InvoiceNo) || x.InvoiceErrors.Select(y => y.Number).Contains(int.Parse(input.InvoiceNo)))
            .WhereIf(input.FromNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number >= input.FromNumber).ToList().Count == x.InvoiceErrors.Count)
            .WhereIf(input.ToNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number <= input.ToNumber).ToList().Count == x.InvoiceErrors.Count);

            var items = groupItems.ToList();

            if (!items.Any())
            {
                return new FileDto();
            }    

            var stt = 1;
            foreach (var item in items)
            {
                item.STT = stt;
                item.SignStatusDisplay = ((SignStatus)item.SignStatus).ToDisplayName();
                item.TvanStatusDisplay = item.TvanStatus == (short)TvanStatus.UnSent 
                                        ? "Chưa gửi TVAN" : (item.TvanStatus == (short)TvanStatus.SendError 
                                        ? "Gửi TVAN bị lỗi" : (item.TvanStatus == (short)TvanStatus.Sended 
                                        ? "Đã gửi lên TVAN " : (item.TvanStatus == (short)TvanStatus.TvanReject
                                        ? "CQT không tiếp nhận" : "CQT đã trả kết quả"
                                        )));

                item.InvoiceNos = string.Join(",", item.InvoiceErrors.Select(x => x.InvoiceNo).ToList());
                item.QuantityAcceptPerTotal = $"{item.InvoiceErrors.Where(x => x.TvanStatus == (short)TvanStatus.TvanAccept).Count()}/{item.InvoiceErrors.Count()}";

                stt++;
            }

            var req = new ExportFlexCelReportRequest
            {
                SampleFile = "invoice-error/TBSS_Template.xlsx",
                OutputFileNameNotExtension = $"Export_TBSS_{DateTime.Now:dd-MM-yyyy}",
                OutputFileType = OutputFileExtension.Excel,
                FlexCelAction = fr =>
                {
                    fr.AddTable("tbData", items);
                }
            };
           
            var response = await _appFactory.Mediator.Send(req);

            return response;
        }

        private async Task<FileDto> ExportInvoiceError02Async(InvoiceErrorExportRequestDto input)
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tenant");
            }

            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _repoInvoice02Error.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.VerificationCode, keyword)
                          || EF.Functions.Like(x.SerialNo, keyword)
                         )
                .WhereIf(input.SignStatuses.Any(), x => input.SignStatuses.Contains((SignStatus)x.SignStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus))
                .WhereIf(input.CreateFromDate.HasValue, x => x.AnnouncementDate.Date >= input.CreateFromDate.Value.Date)
                .WhereIf(input.CreateToDate.HasValue, x => x.AnnouncementDate.Date <= input.CreateToDate.Value.AddDays(1).Date)
                .WhereIf(!input.SerialNo.IsNullOrWhiteSpace(), x => x.SerialNo == input.SerialNo)
                .WhereIf(input.GroupCode.HasValue, x => x.GroupCode == input.GroupCode)
                .WhereIf(!input.NumberInvoiceError.IsNullOrWhiteSpace(), x => x.NumberInvoiceError == input.NumberInvoiceError)
                .Where(x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var errs = await query.ToListAsync();

            var groupItems = errs.GroupBy(x => new { x.GroupCode, x.TaxDepartment, x.AnnouncementDate, x.CodeTaxDepartment, x.PlaceName, x.BudgetUnitCode })
            .Select(x => new InvoiceErrorExportResponseDto
            {
                GroupCode = x.Key.GroupCode.ToString(),
                TaxDepartment = x.Key.TaxDepartment,
                AnnouncementDate = x.Key.AnnouncementDate,
                AnnouncementDateDisplay = x.Key.AnnouncementDate.ToString("dd/MM/yyyy"),
                CodeTaxDepartment = x.Key.CodeTaxDepartment,
                PlaceName = x.Key.PlaceName,
                BudgetUnitCode = x.Key.BudgetUnitCode,
                SignStatus = x.First().SignStatus,
                NumberInvoiceError = x.First().NumberInvoiceError,
                TvanStatus = x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.UnSent) != null ? (short)TvanStatus.UnSent : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.SendError) != null ? (short)TvanStatus.SendError : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.Sended) != null ? (short)TvanStatus.Sended : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss) != null ? (short)TvanStatus.TvanReject : (short)TvanStatus.TCTAccept))),
                InvoiceErrors = x.Select(t => new InvoiceErrorHeaderModel
                {
                    Id = t.Id,
                    Index = t.Index,
                    VerificationCode = t.VerificationCode,
                    InvoiceHeaderId = t.InvoiceHeaderId,
                    TemplateNo = t.TemplateNo,
                    SerialNo = t.SerialNo,
                    InvoiceNo = t.InvoiceNo,
                    InvoiceDate = t.InvoiceDate,
                    InvoiceType = t.InvoiceType,
                    Action = t.Action,
                    Reason = t.Reason,
                    SignStatus = t.SignStatus,
                    SellerSignedTime = t.SellerSignedTime,
                    SellerFullNameSigned = t.SellerFullNameSigned,
                    SellerSignedId = t.SellerSignedId,
                    TvanStatus = t.TvanStatus,
                    Number = t.Number,
                    NumberInvoiceError = t.NumberInvoiceError
                }).OrderBy(x => x.InvoiceNo).ToList()
            })
            .WhereIf(!input.VerificationCode.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.VerificationCode).Contains(input.VerificationCode))
            .WhereIf(!input.InvoiceNo.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.InvoiceNo).Contains(input.InvoiceNo) || x.InvoiceErrors.Select(y => y.Number).Contains(int.Parse(input.InvoiceNo))).WhereIf(input.FromNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number >= input.FromNumber).ToList().Count == x.InvoiceErrors.Count)
            .WhereIf(input.ToNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number <= input.ToNumber).ToList().Count == x.InvoiceErrors.Count);


            var items = groupItems.ToList();

            if (!items.Any())
            {
                return new FileDto();
            }

            var stt = 1;
            foreach (var item in items)
            {
                item.STT = stt;
                item.SignStatusDisplay = ((SignStatus)item.SignStatus).ToDisplayName();
                item.TvanStatusDisplay = item.TvanStatus == (short)TvanStatus.UnSent
                                        ? "Chưa gửi TVAN" : (item.TvanStatus == (short)TvanStatus.SendError
                                        ? "Gửi TVAN bị lỗi" : (item.TvanStatus == (short)TvanStatus.Sended
                                        ? "Đã gửi lên TVAN " : (item.TvanStatus == (short)TvanStatus.TvanReject
                                        ? "CQT không tiếp nhận" : "CQT đã trả kết quả"
                                        )));

                item.InvoiceNos = string.Join(",", item.InvoiceErrors.Select(x => x.InvoiceNo).ToList());
                item.QuantityAcceptPerTotal = $"{item.InvoiceErrors.Where(x => x.TvanStatus == (short)TvanStatus.TvanAccept).Count()}/{item.InvoiceErrors.Count()}";

                stt++;
            }

            var req = new ExportFlexCelReportRequest
            {
                SampleFile = "invoice-error/TBSS_Template.xlsx",
                OutputFileNameNotExtension = $"Export_TBSS_{DateTime.Now:dd-MM-yyyy}",
                OutputFileType = OutputFileExtension.Excel,
                FlexCelAction = fr =>
                {
                    fr.AddTable("tbData", items);
                }
            };

            var response = await _appFactory.Mediator.Send(req);

            return response;
        }

        private async Task<FileDto> ExportInvoiceError03Async(InvoiceErrorExportRequestDto input)
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tenant");
            }

            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _repoInvoice03Error.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.VerificationCode, keyword)
                          || EF.Functions.Like(x.SerialNo, keyword)
                         )
                .WhereIf(input.SignStatuses.Any(), x => input.SignStatuses.Contains((SignStatus)x.SignStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus))
                .WhereIf(input.CreateFromDate.HasValue, x => x.AnnouncementDate.Date >= input.CreateFromDate.Value.Date)
                .WhereIf(input.CreateToDate.HasValue, x => x.AnnouncementDate.Date <= input.CreateToDate.Value.AddDays(1).Date)
                .WhereIf(!input.SerialNo.IsNullOrWhiteSpace(), x => x.SerialNo == input.SerialNo)
                .WhereIf(input.GroupCode.HasValue, x => x.GroupCode == input.GroupCode)
                .WhereIf(!input.NumberInvoiceError.IsNullOrWhiteSpace(), x => x.NumberInvoiceError == input.NumberInvoiceError)
                .Where(x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var errs = await query.ToListAsync();

            var groupItems = errs.GroupBy(x => new { x.GroupCode, x.TaxDepartment, x.AnnouncementDate, x.CodeTaxDepartment, x.PlaceName, x.BudgetUnitCode })
            .Select(x => new InvoiceErrorExportResponseDto
            {
                GroupCode = x.Key.GroupCode.ToString(),
                TaxDepartment = x.Key.TaxDepartment,
                AnnouncementDate = x.Key.AnnouncementDate,
                AnnouncementDateDisplay = x.Key.AnnouncementDate.ToString("dd/MM/yyyy"),
                CodeTaxDepartment = x.Key.CodeTaxDepartment,
                PlaceName = x.Key.PlaceName,
                BudgetUnitCode = x.Key.BudgetUnitCode,
                SignStatus = x.First().SignStatus,
                NumberInvoiceError = x.First().NumberInvoiceError,
                TvanStatus = x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.UnSent) != null ? (short)TvanStatus.UnSent : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.SendError) != null ? (short)TvanStatus.SendError : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.Sended) != null ? (short)TvanStatus.Sended : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss) != null ? (short)TvanStatus.TvanReject : (short)TvanStatus.TCTAccept))),
                InvoiceErrors = x.Select(t => new InvoiceErrorHeaderModel
                {
                    Id = t.Id,
                    Index = t.Index,
                    VerificationCode = t.VerificationCode,
                    InvoiceHeaderId = t.InvoiceHeaderId,
                    TemplateNo = t.TemplateNo,
                    SerialNo = t.SerialNo,
                    InvoiceNo = t.InvoiceNo,
                    InvoiceDate = t.InvoiceDate,
                    InvoiceType = t.InvoiceType,
                    Action = t.Action,
                    Reason = t.Reason,
                    SignStatus = t.SignStatus,
                    SellerSignedTime = t.SellerSignedTime,
                    SellerFullNameSigned = t.SellerFullNameSigned,
                    SellerSignedId = t.SellerSignedId,
                    TvanStatus = t.TvanStatus,
                    Number = t.Number,
                    NumberInvoiceError = t.NumberInvoiceError
                }).OrderBy(x => x.InvoiceNo).ToList()
            })
            .WhereIf(!input.VerificationCode.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.VerificationCode).Contains(input.VerificationCode))
            .WhereIf(!input.InvoiceNo.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.InvoiceNo).Contains(input.InvoiceNo) || x.InvoiceErrors.Select(y => y.Number).Contains(int.Parse(input.InvoiceNo))).WhereIf(input.FromNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number >= input.FromNumber).ToList().Count == x.InvoiceErrors.Count)
            .WhereIf(input.ToNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number <= input.ToNumber).ToList().Count == x.InvoiceErrors.Count);

            var items = groupItems.ToList();

            if (!items.Any())
            {
                return new FileDto();
            }

            var stt = 1;
            foreach (var item in items)
            {
                item.STT = stt;
                item.SignStatusDisplay = ((SignStatus)item.SignStatus).ToDisplayName();
                item.TvanStatusDisplay = item.TvanStatus == (short)TvanStatus.UnSent
                                        ? "Chưa gửi TVAN" : (item.TvanStatus == (short)TvanStatus.SendError
                                        ? "Gửi TVAN bị lỗi" : (item.TvanStatus == (short)TvanStatus.Sended
                                        ? "Đã gửi lên TVAN " : (item.TvanStatus == (short)TvanStatus.TvanReject
                                        ? "CQT không tiếp nhận" : "CQT đã trả kết quả"
                                        )));

                item.InvoiceNos = string.Join(",", item.InvoiceErrors.Select(x => x.InvoiceNo).ToList());
                item.QuantityAcceptPerTotal = $"{item.InvoiceErrors.Where(x => x.TvanStatus == (short)TvanStatus.TvanAccept).Count()}/{item.InvoiceErrors.Count()}";

                stt++;
            }

            var req = new ExportFlexCelReportRequest
            {
                SampleFile = "invoice-error/TBSS_Template.xlsx",
                OutputFileNameNotExtension = $"Export_TBSS_{DateTime.Now:dd-MM-yyyy}",
                OutputFileType = OutputFileExtension.Excel,
                FlexCelAction = fr =>
                {
                    fr.AddTable("tbData", items);
                }
            };

            var response = await _appFactory.Mediator.Send(req);

            return response;
        }

        private async Task<FileDto> ExportInvoiceError04Async(InvoiceErrorExportRequestDto input)
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tenant");
            }

            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _repoInvoice04Error.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.VerificationCode, keyword)
                         || EF.Functions.Like(x.SerialNo, keyword)
                         )
                .WhereIf(input.SignStatuses.Any(), x => input.SignStatuses.Contains((SignStatus)x.SignStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus))
                .WhereIf(input.CreateFromDate.HasValue, x => x.AnnouncementDate.Date >= input.CreateFromDate.Value.Date)
                .WhereIf(input.CreateToDate.HasValue, x => x.AnnouncementDate.Date <= input.CreateToDate.Value.AddDays(1).Date)
                .WhereIf(!input.SerialNo.IsNullOrWhiteSpace(), x => x.SerialNo == input.SerialNo)
                .WhereIf(input.GroupCode.HasValue, x => x.GroupCode == input.GroupCode)
                .WhereIf(!input.NumberInvoiceError.IsNullOrWhiteSpace(), x => x.NumberInvoiceError == input.NumberInvoiceError)
                .Where(x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var errs = await query.ToListAsync();

            var groupItems = errs.GroupBy(x => new { x.GroupCode, x.TaxDepartment, x.AnnouncementDate, x.CodeTaxDepartment, x.PlaceName, x.BudgetUnitCode })
            .Select(x => new InvoiceErrorExportResponseDto
            {
                GroupCode = x.Key.GroupCode.ToString(),
                TaxDepartment = x.Key.TaxDepartment,
                AnnouncementDate = x.Key.AnnouncementDate,
                AnnouncementDateDisplay = x.Key.AnnouncementDate.ToString("dd/MM/yyyy"),
                CodeTaxDepartment = x.Key.CodeTaxDepartment,
                PlaceName = x.Key.PlaceName,
                BudgetUnitCode = x.Key.BudgetUnitCode,
                SignStatus = x.First().SignStatus,
                NumberInvoiceError = x.First().NumberInvoiceError,
                TvanStatus = x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.UnSent) != null ? (short)TvanStatus.UnSent : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.SendError) != null ? (short)TvanStatus.SendError : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.Sended) != null ? (short)TvanStatus.Sended : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss) != null ? (short)TvanStatus.TvanReject : (short)TvanStatus.TCTAccept))),
                InvoiceErrors = x.Select(t => new InvoiceErrorHeaderModel
                {
                    Id = t.Id,
                    Index = t.Index,
                    VerificationCode = t.VerificationCode,
                    InvoiceHeaderId = t.InvoiceHeaderId,
                    TemplateNo = t.TemplateNo,
                    SerialNo = t.SerialNo,
                    InvoiceNo = t.InvoiceNo,
                    InvoiceDate = t.InvoiceDate,
                    InvoiceType = t.InvoiceType,
                    Action = t.Action,
                    Reason = t.Reason,
                    SignStatus = t.SignStatus,
                    SellerSignedTime = t.SellerSignedTime,
                    SellerFullNameSigned = t.SellerFullNameSigned,
                    SellerSignedId = t.SellerSignedId,
                    TvanStatus = t.TvanStatus,
                    Number = t.Number,
                    NumberInvoiceError = t.NumberInvoiceError
                }).OrderBy(x => x.InvoiceNo).ToList()
            })
            .WhereIf(!input.VerificationCode.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.VerificationCode).Contains(input.VerificationCode))
            .WhereIf(!input.InvoiceNo.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.InvoiceNo).Contains(input.InvoiceNo) || x.InvoiceErrors.Select(y => y.Number).Contains(int.Parse(input.InvoiceNo))).WhereIf(input.FromNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number >= input.FromNumber).ToList().Count == x.InvoiceErrors.Count)
            .WhereIf(input.ToNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number <= input.ToNumber).ToList().Count == x.InvoiceErrors.Count);

            var items = groupItems.ToList();

            if (!items.Any())
            {
                return new FileDto();
            }

            var stt = 1;
            foreach (var item in items)
            {
                item.STT = stt;
                item.SignStatusDisplay = ((SignStatus)item.SignStatus).ToDisplayName();
                item.TvanStatusDisplay = item.TvanStatus == (short)TvanStatus.UnSent
                                        ? "Chưa gửi TVAN" : (item.TvanStatus == (short)TvanStatus.SendError
                                        ? "Gửi TVAN bị lỗi" : (item.TvanStatus == (short)TvanStatus.Sended
                                        ? "Đã gửi lên TVAN " : (item.TvanStatus == (short)TvanStatus.TvanReject
                                        ? "CQT không tiếp nhận" : "CQT đã trả kết quả"
                                        )));

                item.InvoiceNos = string.Join(",", item.InvoiceErrors.Select(x => x.InvoiceNo).ToList());
                item.QuantityAcceptPerTotal = $"{item.InvoiceErrors.Where(x => x.TvanStatus == (short)TvanStatus.TvanAccept).Count()}/{item.InvoiceErrors.Count()}";

                stt++;
            }

            var req = new ExportFlexCelReportRequest
            {
                SampleFile = "invoice-error/TBSS_Template.xlsx",
                OutputFileNameNotExtension = $"Export_TBSS_{DateTime.Now:dd-MM-yyyy}",
                OutputFileType = OutputFileExtension.Excel,
                FlexCelAction = fr =>
                {
                    fr.AddTable("tbData", items);
                }
            };

            var response = await _appFactory.Mediator.Send(req);

            return response;
        }

        private async Task<FileDto> ExportTicketErrorAsync(InvoiceErrorExportRequestDto input)
        {
            if (!_appFactory.CurrentTenant.Id.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin tenant");
            }

            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _repoTicketError.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.VerificationCode, keyword)
                         || EF.Functions.Like(x.SerialNo, keyword)
                         )
                .WhereIf(input.SignStatuses.Any(), x => input.SignStatuses.Contains((SignStatus)x.SignStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus))
                .WhereIf(input.CreateFromDate.HasValue, x => x.AnnouncementDate.Date >= input.CreateFromDate.Value.Date)
                .WhereIf(input.CreateToDate.HasValue, x => x.AnnouncementDate.Date <= input.CreateToDate.Value.AddDays(1).Date)
                .WhereIf(!input.SerialNo.IsNullOrWhiteSpace(), x => x.SerialNo == input.SerialNo)
                .WhereIf(input.GroupCode.HasValue, x => x.GroupCode == input.GroupCode)
                .WhereIf(!input.NumberInvoiceError.IsNullOrWhiteSpace(), x => x.NumberInvoiceError == input.NumberInvoiceError)
                .Where(x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var errs = await query.ToListAsync();

            var groupItems = errs.GroupBy(x => new { x.GroupCode, x.TaxDepartment, x.AnnouncementDate, x.CodeTaxDepartment, x.PlaceName, x.BudgetUnitCode })
            .Select(x => new InvoiceErrorExportResponseDto
            {
                GroupCode = x.Key.GroupCode.ToString(),
                TaxDepartment = x.Key.TaxDepartment,
                AnnouncementDate = x.Key.AnnouncementDate,
                AnnouncementDateDisplay = x.Key.AnnouncementDate.ToString("dd/MM/yyyy"),
                CodeTaxDepartment = x.Key.CodeTaxDepartment,
                PlaceName = x.Key.PlaceName,
                BudgetUnitCode = x.Key.BudgetUnitCode,
                SignStatus = x.First().SignStatus,
                NumberInvoiceError = x.First().NumberInvoiceError,
                TvanStatus = x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.UnSent) != null ? (short)TvanStatus.UnSent : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.SendError) != null ? (short)TvanStatus.SendError : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.Sended) != null ? (short)TvanStatus.Sended : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss) != null ? (short)TvanStatus.TvanReject : (short)TvanStatus.TCTAccept))),
                InvoiceErrors = x.Select(t => new InvoiceErrorHeaderModel
                {
                    Id = t.Id,
                    Index = t.Index,
                    VerificationCode = t.VerificationCode,
                    InvoiceHeaderId = t.InvoiceHeaderId,
                    TemplateNo = t.TemplateNo,
                    SerialNo = t.SerialNo,
                    InvoiceNo = t.InvoiceNo,
                    InvoiceDate = t.InvoiceDate,
                    InvoiceType = t.InvoiceType,
                    Action = t.Action,
                    Reason = t.Reason,
                    SignStatus = t.SignStatus,
                    SellerSignedTime = t.SellerSignedTime,
                    SellerFullNameSigned = t.SellerFullNameSigned,
                    SellerSignedId = t.SellerSignedId,
                    TvanStatus = t.TvanStatus,
                    Number = t.Number,
                    NumberInvoiceError = t.NumberInvoiceError
                }).OrderBy(x => x.InvoiceNo).ToList()
            })
            .WhereIf(!input.VerificationCode.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.VerificationCode).Contains(input.VerificationCode))
            .WhereIf(!input.InvoiceNo.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.InvoiceNo).Contains(input.InvoiceNo) || x.InvoiceErrors.Select(y => y.Number).Contains(int.Parse(input.InvoiceNo))).WhereIf(input.FromNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number >= input.FromNumber).ToList().Count == x.InvoiceErrors.Count)
            .WhereIf(input.ToNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number <= input.ToNumber).ToList().Count == x.InvoiceErrors.Count);

            var items = groupItems.ToList();

            if (!items.Any())
            {
                return new FileDto();
            }

            var stt = 1;
            foreach (var item in items)
            {
                item.STT = stt;
                item.SignStatusDisplay = ((SignStatus)item.SignStatus).ToDisplayName();
                item.TvanStatusDisplay = item.TvanStatus == (short)TvanStatus.UnSent
                                        ? "Chưa gửi TVAN" : (item.TvanStatus == (short)TvanStatus.SendError
                                        ? "Gửi TVAN bị lỗi" : (item.TvanStatus == (short)TvanStatus.Sended
                                        ? "Đã gửi lên TVAN " : (item.TvanStatus == (short)TvanStatus.TvanReject
                                        ? "CQT không tiếp nhận" : "CQT đã trả kết quả"
                                        )));

                item.InvoiceNos = string.Join(",", item.InvoiceErrors.Select(x => x.InvoiceNo).ToList());
                item.QuantityAcceptPerTotal = $"{item.InvoiceErrors.Where(x => x.TvanStatus == (short)TvanStatus.TvanAccept).Count()}/{item.InvoiceErrors.Count()}";

                stt++;
            }

            var req = new ExportFlexCelReportRequest
            {
                SampleFile = "invoice-error/TBSS_Template.xlsx",
                OutputFileNameNotExtension = $"Export_TBSS_{DateTime.Now:dd-MM-yyyy}",
                OutputFileType = OutputFileExtension.Excel,
                FlexCelAction = fr =>
                {
                    fr.AddTable("tbData", items);
                }
            };

            var response = await _appFactory.Mediator.Send(req);

            return response;

        }
    }
}
