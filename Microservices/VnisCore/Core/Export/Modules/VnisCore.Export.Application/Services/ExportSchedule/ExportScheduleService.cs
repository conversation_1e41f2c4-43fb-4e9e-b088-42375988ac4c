using Core;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository.Invoices.ExportInvoice01;
using VnisCore.Export.Application.Services.ExportSchedule.Dto;
using Core.Shared.FileManager.Interfaces;
using Core.Application.Dtos;
using Microsoft.Extensions.Configuration;
using Core.Shared.Extensions;
using System.Globalization;
using Dapper;
using Microsoft.AspNetCore.Authorization;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.Invoice01;

namespace VnisCore.Export.Application.Services.ExportSchedule
{
    [Authorize(Invoice01Permissions.Invoice01.Default)]
    public class ExportScheduleService : VnisCoreExportApplicationAppService//: AbpController//
    {
        private readonly IVnisCoreMongoExportinvoice01HeaderRepository _mongoExportinvoice01HeaderRepository;
        private readonly IVnisCoreMongoExportinvoice01DetailRepository _mongoExportinvoice01DetailRepository;
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly IConfiguration _configuration;
        public ExportScheduleService(
            IVnisCoreMongoExportinvoice01HeaderRepository mongoExportinvoice01HeaderRepository,
            IVnisCoreMongoExportinvoice01DetailRepository mongoExportinvoice01DetailRepository,
            IFileService fileService,
            IAppFactory appFactory,
            IConfiguration configuration)
        {
            _mongoExportinvoice01DetailRepository = mongoExportinvoice01DetailRepository;
            _mongoExportinvoice01HeaderRepository = mongoExportinvoice01HeaderRepository;
            _fileService = fileService;
            _appFactory = appFactory;
            _configuration = configuration;
        }

        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        [HttpPost(Utilities.ApiUrlBase + "ExportInvoiceHeader/Create")]
        public async Task CreateAsync(int month, int year)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            var daysInMonth = DateTime.DaysInMonth(year, month);
            short.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var IsEnableMongoDbLocalTime);
            int.TryParse(_configuration["Settings:IsAutoActive"], out var IsAutoActive);

            var existExportHeader = await _mongoExportinvoice01HeaderRepository.IsExistExportInvoice(tenantId.Value, month, year);
            if (!existExportHeader)
            {
                // Neu khong ton tai thi tao
                MongoExportInvoice01Header header = new MongoExportInvoice01Header();
                header.CreationTime = IsEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;
                header.TenantId = tenantId.Value;
                header.FromDate = IsEnableMongoDbLocalTime > 0 ? new DateTime(year, month, 1).AddHours(7) : new DateTime(year, month, 1);
                header.ToDate = IsEnableMongoDbLocalTime > 0 ? new DateTime(year, month, daysInMonth).AddHours(7) : new DateTime(year, month, daysInMonth);
                header.CurrentDate = header.FromDate;
                header.Month = month;
                header.Year = year;
                header.TaxCode = CurrentTenant.TaxCode;
                header.InvoiceGroup = CurrentTenant.Group;
                header.IsActive = 1;
                header.IsMerge = 0;
                var headerExportMongo = await _mongoExportinvoice01HeaderRepository.InsertAsync(header);

                // Insert Detail
                List<MongoExportInvoice01Detail> details = new List<MongoExportInvoice01Detail>();
                for (int day = 1; day <= daysInMonth; day++)
                {
                    var existExportDetail = await _mongoExportinvoice01DetailRepository.IsExistExportInvoiceDetail(CurrentTenant.Id.Value, year, month, day);
                    if (!existExportDetail)
                    {
                        //var detail = new MongoExportInvoice01DetailTest();
                        //detail.CreationTime = IsEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;
                        //detail.InvoiceDate = IsEnableMongoDbLocalTime > 0 ? new DateTime(year, month, day).AddHours(7) : new DateTime(year, month, day);
                        //detail.TenantId = tenant.Id;
                        //detail.Day = day;
                        //detail.Month = month;
                        //detail.Status = 0;
                        //detail.ExportHeaderId = header.Id;
                        //detail.IsActive = 0;

                        //details.Add(detail);

                        var detail = new MongoExportInvoice01Detail();
                        detail.CreationTime = IsEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;
                        detail.InvoiceDate = IsEnableMongoDbLocalTime > 0 ? new DateTime(year, month, day).AddHours(7) : new DateTime(year, month, day);
                        detail.TenantId = CurrentTenant.Id.Value;
                        detail.Day = day;
                        detail.Month = month;
                        detail.Year = year;
                        detail.Status = 0;
                        detail.ExportHeaderId = headerExportMongo.Id;
                        detail.TaxCode = CurrentTenant.TaxCode;
                        detail.IsActive = 0;

                        details.Add(detail);
                    }
                }
                //await _mongoExportinvoice01Detail2Repository.InsertManyAsync(detail2s);
                await _mongoExportinvoice01DetailRepository.InsertManyAsync(details);
            }
            else
            {
                throw new UserFriendlyException($@"Đã tồn tại bảng kê tháng {month} năm {year}. Thêm thất bại");
            }
        }

        [HttpPost(Utilities.ApiUrlBase + "ExportInvoiceHeader/GetList")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task<PagedResultDto<MongoExportInvoice01Header>> GetListHeader([FromBody] ExportScheduleHeaderRequest request)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            var count = await _mongoExportinvoice01HeaderRepository.CountAsync(tenantId, request.Sorting, request.MaxResultCount, request.SkipCount, request.month, request.year, request.status, request.isMerge);
            var list = await _mongoExportinvoice01HeaderRepository.
                            GetListAsync(tenantId, request.Sorting, request.MaxResultCount, request.SkipCount, request.month, request.year, request.status, request.isMerge);
            return new PagedResultDto<MongoExportInvoice01Header>
            {
                TotalCount = count,
                Items = list
            };
        }

        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        [HttpPost(Utilities.ApiUrlBase + "ExportInvoiceDetails/GetList")]
        public async Task<PagedResultDto<MongoExportInvoice01Detail>> GetListHeader([FromBody] ExportScheduleDetailRequest request)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            var count = await _mongoExportinvoice01DetailRepository.CountAsync(tenantId.Value, request.ExportInvoiceHeaderId, request.Sorting, request.MaxResultCount, request.SkipCount, request.Keyword);
            var list = await _mongoExportinvoice01DetailRepository.
                            GetListAsync(tenantId.Value, request.ExportInvoiceHeaderId, request.Sorting, request.MaxResultCount, request.SkipCount, request.Keyword);
            return new PagedResultDto<MongoExportInvoice01Detail>
            {
                TotalCount = count,
                Items = list
            };
        }
        
        [HttpPost(Utilities.ApiUrlBase + "ChangeActiveExportHeader")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task ChangeActiveExportHeader(Guid Id)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var exportHeader = await _mongoExportinvoice01HeaderRepository.GetById(Id, tenantId);

            if (exportHeader == null)
            {
                throw new UserFriendlyException("Không tìm thấy dữ liệu");
            }

            if (exportHeader.IsActive == 0)
            {
                exportHeader.IsActive = 1;
            }
            else
            {
                exportHeader.IsActive = 0;
            }

            await _mongoExportinvoice01HeaderRepository.UpdateAsync(exportHeader);
        }

        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        [HttpPost(Utilities.ApiUrlBase + "ChangeActiveExportDetail")]
        public async Task ChangeStatusExportDetail(Guid Id)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var exportDetail = await _mongoExportinvoice01DetailRepository.GetById(Id, tenantId);

            if (exportDetail == null)
            {
                throw new UserFriendlyException("Không tìm thấy dữ liệu");
            }

            if (DateTime.Now < exportDetail.InvoiceDate)
            {
                throw new UserFriendlyException("Không thể lập báo cáo vượt quá ngày hiện tại");
            }
            short.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var IsEnableMongoDbLocalTime);
            if (exportDetail.IsActive == 0)
            {
                // Update Trang thai sang cho Export, bat len
                exportDetail.IsActive = 1;
                exportDetail.Status = 3;
                exportDetail.ExportTime = IsEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;

                await _mongoExportinvoice01DetailRepository.UpdateAsync(exportDetail);

                // Update trang thai export sang chua export
                var header = await _mongoExportinvoice01HeaderRepository.GetAsync(exportDetail.ExportHeaderId);
                header.Status = 0;
                await _mongoExportinvoice01HeaderRepository.UpdateAsync(header);
            }
            else
            {
                throw new UserFriendlyException("Đang tiến hành export. Vui lòng chờ đến khi export xong");
            }
            //else
            //{
            //    exportDetail.IsActive = 0;
            //}

        }
        [HttpPost(Utilities.ApiUrlBase + "DownloadHeader")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task<FileDto> DownloadHeader(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            var exportHeader = await _mongoExportinvoice01HeaderRepository.GetById(id, tenantId.Value);

            if (exportHeader == null)
            {
                throw new UserFriendlyException("Không tìm thấy dữ liệu");
            }

            // lay cau hinh setting
            var configuration = _appFactory.Configuration;

            var guidId = CurrentTenant.Id.Value.ToString();

            var pathLocal = Path.Combine(configuration.GetSection("FileExport:PathFolder").Value.Trim(), guidId);

            if (!Directory.Exists(pathLocal))
                Directory.CreateDirectory(pathLocal);

            var pathFileMinio = exportHeader.PathFile + "/" + exportHeader.FileName;
            try
            {
                var bytes = await _fileService.DownloadAsync(pathFileMinio);

                return new FileDto
                {
                    ContentType = ContentType.Zip,
                    FileBytes = bytes,
                    FileName = exportHeader.FileName,
                    FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length)
                };

            }
            catch (Exception ex)
            {

                throw new UserFriendlyException("Không tìm thấy file minio");
            }
            //var filePagingLocalPath = Path.Combine(pathLocal, exportHeader.FileName);
            //await File.WriteAllBytesAsync(filePagingLocalPath, bytes);

            //byte[] summaryBytes;

            ////zip lại
            //string zipPath = pathLocal + ".zip";
            //ZipFile.CreateFromDirectory(pathLocal, zipPath);
            //summaryBytes = await File.ReadAllBytesAsync(zipPath);
            //var fileName = $"{exportHeader.FileName}.zip";
            ////xóa folder
            //if (Directory.Exists(pathLocal))
            //    Directory.Delete(pathLocal, true);
            //if (File.Exists(zipPath))
            //{
            //    File.Delete(zipPath);
            //}
            //return new FileDto
            //{
            //    ContentType = ContentType.Zip,
            //    FileBytes = summaryBytes,
            //    FileName = fileName,
            //    FileBase64 = Convert.ToBase64String(summaryBytes, 0, summaryBytes.Length)
            //};
        }

        [HttpPost(Utilities.ApiUrlBase + "DownloadDetail")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task<FileDto> DownloadDetail(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            var exportDetail = await _mongoExportinvoice01DetailRepository.GetById(id, tenantId.Value);

            if (exportDetail == null)
            {
                throw new UserFriendlyException("Không tìm thấy dữ liệu");
            }

            if (DateTime.Now < exportDetail.InvoiceDate)
            {
                throw new UserFriendlyException("Không thể tải báo cáo vượt quá ngày hiện tại");
            }
            // lay cau hinh setting
            var configuration = _appFactory.Configuration;

            var guidId = CurrentTenant.Id.Value.ToString();

            var pathLocal = Path.Combine(configuration.GetSection("FileExport:PathFolder").Value.Trim(), guidId);

            if (!Directory.Exists(pathLocal))
                Directory.CreateDirectory(pathLocal);

            var pathFileMinio = exportDetail.PathFile + "/" + exportDetail.FileName;

            try
            {
                var bytes = await _fileService.DownloadAsync(pathFileMinio);

                return new FileDto
                {
                    ContentType = ContentType.Zip,
                    FileBytes = bytes,
                    FileName = exportDetail.FileName,
                    FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length)
                };

            }
            catch (Exception ex)
            {

                throw new UserFriendlyException("Không tìm thấy file minio");
            }
            //var filePagingLocalPath = Path.Combine(pathLocal, exportHeader.FileName);

            //await File.WriteAllBytesAsync(filePagingLocalPath, bytes);

            //byte[] summaryBytes;

            ////zip lại
            //string zipPath = pathLocal + ".zip";
            //ZipFile.CreateFromDirectory(pathLocal, zipPath);
            //summaryBytes = await File.ReadAllBytesAsync(zipPath);
            //var fileName = $"Bangke_{guidId}_{exportHeader.InvoiceDate.ToString("yyyyMMdd")}.zip";
            ////xóa folder
            //if (Directory.Exists(pathLocal))
            //    Directory.Delete(pathLocal, true);
            //if (File.Exists(zipPath))
            //{
            //    File.Delete(zipPath);
            //}
            //return new FileDto
            //{
            //    ContentType = ContentType.Zip,
            //    FileBytes = summaryBytes,
            //    FileName = fileName,
            //    FileBase64 = Convert.ToBase64String(summaryBytes, 0, summaryBytes.Length)
            //};
        }

        [HttpPost(Utilities.ApiUrlBase + "DownloadDetails")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task<FileDto> DownloadDetail(List<Guid> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }


            // lay cau hinh setting
            var configuration = _appFactory.Configuration;

            var guidId = CurrentTenant.Id.Value.ToString();

            var pathLocal = Path.Combine(configuration.GetSection("FileExport:PathFolder").Value.Trim(), guidId);

            var extractPath = Path.Combine(pathLocal, "Extract");
            foreach (var id in ids)
            {
                var exportDetail = await _mongoExportinvoice01DetailRepository.GetById(id, tenantId.Value);

                if (exportDetail == null)
                {
                    throw new UserFriendlyException("Không tìm thấy dữ liệu");
                }

                if (DateTime.Now < exportDetail.InvoiceDate)
                {
                    throw new UserFriendlyException("Không thể tải báo cáo vượt quá ngày hiện tại");
                }

                if (!Directory.Exists(pathLocal))
                    Directory.CreateDirectory(pathLocal);

                var pathFileMinio = exportDetail.PathFile + "/" + exportDetail.FileName;
                var bytes = await _fileService.DownloadAsync(pathFileMinio);

                var filePagingLocalPath = Path.Combine(pathLocal, exportDetail.FileName);

                await System.IO.File.WriteAllBytesAsync(filePagingLocalPath, bytes);

                // Extract file zip
                ZipFile.ExtractToDirectory(filePagingLocalPath, extractPath);
            }

            byte[] summaryBytes;

            //zip lại
            string zipPath = pathLocal + ".zip";
            ZipFile.CreateFromDirectory(extractPath, zipPath);
            summaryBytes = await System.IO.File.ReadAllBytesAsync(zipPath);
            var fileName = $"Bangke_{guidId}.zip";
            //xóa folder
            if (Directory.Exists(pathLocal))
                Directory.Delete(pathLocal, true);
            if (System.IO.File.Exists(zipPath))
            {
                System.IO.File.Delete(zipPath);
            }
            return new FileDto
            {
                ContentType = ContentType.Zip,
                FileBytes = summaryBytes,
                FileName = fileName,
                FileBase64 = Convert.ToBase64String(summaryBytes, 0, summaryBytes.Length)
            };
        }
        
        [HttpPost(Utilities.ApiUrlBase + "MergeFile")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task MergeFile(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            short.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var IsEnableMongoDbLocalTime);
            var exportHeader = await _mongoExportinvoice01HeaderRepository.GetById(id, tenantId.Value);

            if (exportHeader == null)
            {
                throw new UserFriendlyException("Không tìm thấy dữ liệu");
            }

            var checkDetail = await _mongoExportinvoice01DetailRepository.IsExistExported(id);
            if(checkDetail==false)
            {
                throw new UserFriendlyException($@"Chưa có dữ liệu báo cáo tháng {exportHeader.Month}");
            }

            var status = new List<int>() { 1, 3};
            checkDetail = await _mongoExportinvoice01DetailRepository.IsExistExportInvoiceDetail(tenantId.Value, id, status);
            if(checkDetail == true)
            {
                throw new UserFriendlyException("Còn báo cáo đang export, không thể gộp dữ liệu");
            }

            exportHeader.ExportCount = 0;
            exportHeader.FileName = null;
            exportHeader.PathFile = null;
            //exportHeader.Status = 1;
            exportHeader.IsMerge = 1;
            exportHeader.MergeTime = IsEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;

            await _mongoExportinvoice01HeaderRepository.UpdateAsync(exportHeader);

        }

        [HttpPost(Utilities.ApiUrlBase + "Reload")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task Reload(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            short.TryParse(_configuration["Settings:IsEnableMongoDbLocalTime"], out var IsEnableMongoDbLocalTime);
            var exportDetail = await _mongoExportinvoice01DetailRepository.GetById(id, tenantId.Value);

            if (exportDetail == null)
            {
                throw new UserFriendlyException("Không tìm thấy dữ liệu");
            }

            if (DateTime.Now < exportDetail.InvoiceDate)
            {
                throw new UserFriendlyException("Không thể lập báo cáo vượt quá ngày hiện tại");
            }
            if (exportDetail.PathFile != null && exportDetail.FileName != null)
            {
                // Xoa file tren minio
                var pathFileMinio = exportDetail.PathFile + "/" + exportDetail.FileName;
                await _fileService.DeleteAsync(pathFileMinio);
            }

            exportDetail.FileName = null;
            exportDetail.PathFile = null;
            exportDetail.Status = 3;
            exportDetail.ExportCount = 0;
            exportDetail.IsActive = 1;
            exportDetail.ExportTime = IsEnableMongoDbLocalTime > 0 ? DateTime.Now.AddHours(7) : DateTime.Now;

            await _mongoExportinvoice01DetailRepository.UpdateAsync(exportDetail);

            // Update trang thai export sang chua export
            var header = await _mongoExportinvoice01HeaderRepository.GetById(exportDetail.ExportHeaderId, tenantId.Value);
            header.Status = 0;
            await _mongoExportinvoice01HeaderRepository.UpdateAsync(header);

            #region logic cu
            //// Update IsSyncExportSchedule = 0, Oracle
            //var updateSql = GetDrawQuery(tenantId.Value, exportDetail.InvoiceDate);
            //await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(updateSql);
            #endregion

        }

        [HttpPost(Utilities.ApiUrlBase + "ExportInvoiceHeader/Delete")]
        [Authorize(Invoice01Permissions.Invoice01.ExportExcelSchedule)]
        public async Task DeleteHeader(Guid id)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy thông tin chi nhánh");
            }
            var header = await _mongoExportinvoice01HeaderRepository.GetById(id, tenantId.Value);
            if (header == null)
            {
                throw new UserFriendlyException("Không tồn tại bảng kê để xóa");
            }
            var details = await _mongoExportinvoice01DetailRepository.GetList(tenantId.Value, id);

            await _mongoExportinvoice01DetailRepository.DeleteManyAsync(details);

            await _mongoExportinvoice01HeaderRepository.DeleteAsync(id);

        }

        public string GetDrawQuery(Guid tenantId, DateTime dateTime)
        {
            var tenantRaw = OracleExtension.ConvertGuidToRaw(tenantId);
            #region sql get header
            var updateSql = $@"
                UPDATE ""Invoice01Header"" 
                SET ""IsSyncExportSchedule"" = 0
                WHERE ""TenantId"" = '{tenantRaw}' AND ""IsDeleted"" = 0  AND ""Number"" IS NOT NULL 
                        AND  ""InvoiceDate"" = '{dateTime.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
            ";
            #endregion
            return updateSql;
        }
    }
}
