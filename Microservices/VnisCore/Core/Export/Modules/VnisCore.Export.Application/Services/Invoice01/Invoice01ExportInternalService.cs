using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Export.Application.Abstractions;
using VnisCore.Export.Application.Factories.Repositories;
using VnisCore.Export.Application.Factories.Repositories.Invoice01;
using VnisCore.Export.Application.Interfaces;
using VnisCore.Export.Application.Models.Invoice01s;
using VnisCore.Export.Application.Models.NuocSachHaNoi;
using VnisCore.Export.Application.Models.Requests;
using VnisCore.Export.Application.Models.Responses;

namespace VnisCore.Export.Application.Services
{
    public class Invoice01ExportInternalService : BaseInvoiceExport01Service, IInvoiceExportService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IAppFactory _appFactory;
        public string REQUEST_DATE;

        public Invoice01ExportInternalService(IServiceProvider serviceProvider, IAppFactory appFactory, IQueryInvoice01Service queryInvoice01Service, IStringLocalizer<CoreLocalizationResource> localizer) : base(serviceProvider, appFactory, queryInvoice01Service, localizer)
        {
            _serviceProvider = serviceProvider;
            _appFactory = appFactory;
        }

        /// <summary>
        /// Export theo định dạng file csv
        /// </summary>
        /// <param name="exportJob"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public override async Task<ExportInvoiceApiResponseModel> ExportInvoiceAsync(Invoice01ExportJobEntity exportJob, ExportInvoiceApiRequestModel request)
        {
            //lấy dữ liệu và export ra file csv
            var invoices = await GetInvoicesAsync(request);

            //lấy cấu hình hiển thị các giá trị extra trên bảng kê
            var fieldHeaderNames = "OperationName;";
            var tellSeqNames = "TellSeq;";
            var fieldDetailNames = "RefNo;";
            var headerFields = await GetHeaderFieldSpecificationAsync(request.TenantId, fieldHeaderNames);
            var detailFields = await GetDetailFieldSpecificationAsync(request.TenantId, fieldDetailNames);
            var headerFieldstellSeq = await GetHeaderFieldSpecificationAsync(request.TenantId, tellSeqNames);

            var operationName = headerFields.FirstOrDefault(x => x.FieldName == "OperationName");
            var refNo = detailFields.FirstOrDefault(x => x.FieldName == "RefNo");
            var tellSeq = headerFieldstellSeq.FirstOrDefault(x => x.FieldName == "TellSeq");

            var repoTax = _appFactory.Repository<TaxEntity, long>();
            var taxes = await repoTax.Where(x => x.TenantId == request.TenantId).ToDictionaryAsync(x => x.Value, x => x.Code);

            // tạo file Csv
            var exportHeaders = new List<string>()
            {
                    "STT",
                    "Ngày hóa đơn",
                    "Mẫu hoá đơn",
                    "Ký hiệu",
                    "Số hóa đơn",
                    "Mã số thuế người mua",
                    "Tên người mua",
                    "Địa chỉ người mua",
                    "Tên nghiệp vụ",
                    "Loại hình nghiệp nghiệp vụ",
                    "Doanh số bán chưa có thuế",
                    "Thuế suất thuế GTGT",
                    "Tiền thuế GTGT",
                    "Tổng cộng tiền hàng",
                    "Tiền tệ",
                    "Tỉ giá",
                    "Tình trạng hóa đơn",
                    "User",
                    "Tình trạng ký",
                    "Cách thức hủy (Tự động/Thủ công)",
                    "Số chứng từ (Ref)",
                    "Số bút toán",
                    "Số tài khoản",
                    "Số thẻ",
            };

            var exportRecords = new List<List<string>>();

            for (int i = 0; i < invoices.Count; i++)
            {
                var invoice = invoices.ElementAt(i);

                var isAdjHeader = false;
                if (invoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh.GetHashCode())
                {
                    isAdjHeader = true;
                }

                var invoiceStatus = _appFactory.CurrentTenant.Settings?.FirstOrDefault(x => x.Code == EnumExtension.TryToEnum<InvoiceStatus>(invoice.InvoiceStatus).ToString())?.Value;

                var signStatusExport = "";
                var signStatus = EnumExtension.TryToEnum<SignStatus>(invoice.SignStatus);
                if (signStatus != default(SignStatus) && !int.TryParse(signStatus.ToString(), out int outSignStatus))
                    signStatusExport = signStatus.GetName();
                else
                    signStatusExport = invoice.SignStatus.ToString();

                var obj = new List<string> {
                            (i + 1).ToString(),
                            invoice.InvoiceDate.ToString("dd/MM/yyyy"),
                            invoice.TemplateNo.ToString(),
                            invoice.SerialNo,
                            invoice.InvoiceNo,
                            invoice.BuyerTaxCode,
                            invoice.BuyerFullName.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty),
                            invoice.BuyerAddressLine.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty),
                            invoice.InvoiceHeaderExtras != null && invoice.InvoiceHeaderExtras.Any(x => x.FieldName == operationName?.FieldName) ? invoice.InvoiceHeaderExtras.FirstOrDefault(x => x.FieldName == operationName.FieldName).FieldValue : string.Empty,
                            string.Empty,
                            isAdjHeader ? "0" : invoice.TotalAmount.ToString().Replace(",", "."),
                            string.Empty,
                            isAdjHeader ? "0" : invoice.TotalVatAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : invoice.TotalPaymentAmount.ToString().Replace(",", "."),
                            invoice.ToCurrency.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty),
                            invoice.ExchangeRate.ToString(),
                            //(invoice.InvoiceStatus != InvoiceStatus.XoaBo.GetHashCode() && invoice.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode())
                            //?  EnumExtension.TryToEnum<InvoiceStatus>(invoice.InvoiceStatus).ToString() : (invoice.InvoiceStatus == InvoiceStatus.XoaBo.GetHashCode() ? "XoaDaDuyet" : "XoaChoDuyet"),
                            !string.IsNullOrEmpty(invoiceStatus) ? invoiceStatus : EnumExtension.TryToEnum<InvoiceStatus>(invoice.InvoiceStatus).GetName(),
                            invoice.UserNameCreator?.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty),
                            signStatusExport,
                            EnumExtension.TryToEnum<InvoiceSource>(invoice.InvoiceDeleteSource).ToString(),//Cách thức hủy
                            tellSeq != null && invoice.InvoiceHeaderExtras != null && invoice.InvoiceHeaderExtras.Any(x => x.FieldName == tellSeq.FieldName) ? invoice.InvoiceHeaderExtras.FirstOrDefault(x => x.FieldName == tellSeq.FieldName).FieldValue?.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty) : string.Empty,
                            !string.IsNullOrEmpty(invoice.TransactionId) ? invoice.TransactionId : string.Empty,//Số bút toán
                            invoice.BuyerBankAccount,//Số tài khoản
                            string.Empty,//Số thẻ
                        };

                if (invoice.InvoiceDetails == null)
                {
                    invoice.InvoiceDetails = new List<Invoice01DetailExportModel>
                        { new Invoice01DetailExportModel () };
                }

                exportRecords.Add(obj);

                //add detail
                if (invoice.InvoiceDetails != null && invoice.InvoiceDetails.Any())
                {
                    foreach (var item in invoice.InvoiceDetails)
                    {
                        obj = new List<string> {
                            string.Empty,//stt
                            string.Empty,//ngày hóa đơn
                            string.Empty,//mẫu hóa đơn
                            string.Empty,//ký hiệu
                            string.Empty,//số hóa đơn
                            string.Empty,//mst người mua
                            string.Empty,//tên người mua
                            string.Empty,//địa chỉ người mua
                            item.ProductId?.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty),
                            item.ProductName?.Replace(",", string.Empty).Replace(Environment.NewLine, string.Empty).Replace("\r", string.Empty),
                            isAdjHeader ? "0" : item?.Amount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : (item.VatPercent.HasValue ? taxes[item.VatPercent.Value] : null),
                            isAdjHeader ? "0" : item.VatAmount.ToString().Replace(",", "."),
                            isAdjHeader ? "0" : item.PaymentAmount.ToString().Replace(",", "."),
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,
                            string.Empty,//Cách thức hủy
                            item.InvoiceDetailExtras != null && item.InvoiceDetailExtras.Any(x => x.FieldName == refNo.FieldName) ? item.InvoiceDetailExtras.FirstOrDefault(x => x.FieldName == refNo.FieldName).FieldValue : string.Empty,//Số chứng từ REF
                            string.Empty,//Số bút toán
                            string.Empty,//Số tài khoản
                            string.Empty,//Số thẻ
                        };

                        exportRecords.Add(obj);
                    }
                }
            }

            var exportCsv = new StringBuilder();

            exportRecords.ForEach(line =>
            {
                exportCsv.AppendLine(string.Join(",", line));
            });

            //Tạo file csv
            using var scoped = _serviceProvider.CreateScope();
            var configuation = scoped.ServiceProvider.GetService<IConfiguration>();
            var pathFolder = Path.Combine(configuation.GetSection("FileExport:PathFolder").Value.Trim(), exportJob.TaskId.ToString());

            if (!Directory.Exists(pathFolder))
                Directory.CreateDirectory(pathFolder);

            var filePath = Path.Combine(pathFolder, $"{exportJob.CurrentPage + 1}.csv");
            await File.WriteAllTextAsync(filePath, $"{string.Join(",", exportHeaders)}{Environment.NewLine}{exportCsv}".ToString(), Encoding.UTF8);
            var bytes = await File.ReadAllBytesAsync(filePath);

            //xóa file
            File.Delete(filePath);

            // Xoa Folder
            if (Directory.Exists(pathFolder))
                Directory.Delete(pathFolder);

            return new ExportInvoiceApiResponseModel
            {
                Bytes = bytes,
                FileName = Path.GetFileName(filePath)
            };
        }

        public override async Task<List<Invoice01ExportModel>> GetInvoicesAsync(ExportInvoiceApiRequestModel request)
        {
            using var scope = _serviceProvider.CreateScope();
            //var uowInvoice01 = scope.ServiceProvider.GetService<IInvoice01UnitOfWork>();

            //List<long> ids = await GetInvoiceHeaderIdByHeaderExtra(request.TenantId, request.HeaderExtras);
            var pagingModel = ToPagingModel(request);
            //pagingModel.Ids = ids;

            var repoInvoice01 = _serviceProvider.GetService<IInvoice01HeaderRepository>();
            var invoiceHeaders = (await repoInvoice01.PagingAsNoTrackingAsync(request.TenantId, pagingModel))
                                       .Data
                                       .Select(x => (Invoice01ExportModel)x)
                                       .ToDictionary(x => x.Id, x => x);

            var idInvoiceHeaders = invoiceHeaders.Keys.ToList();

            //detail
            var repoInvoiceDetail = _serviceProvider.GetService<IInvoiceDetailRepository<Invoice01DetailEntity>>();
            var entityInvoiceDetails = await repoInvoiceDetail.QueryByInvoiceHeaderIdsAsNoTrackingAsync(idInvoiceHeaders.ToList());

            //chuyen ve model
            //var invoiceDetails = entityInvoiceDetails.Select(x => (Invoice01DetailExportModel)x).ToList();
            var invoiceDetails = _appFactory.ObjectMapper.Map<List<Invoice01DetailEntity>, List<Invoice01DetailExportModel>>(entityInvoiceDetails);

            //add DetailExtras
            foreach (var item in invoiceDetails)
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    var extraProperties = new List<InvoiceDetailExtraModel>();
                    if (item.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceDetailExtras"]))
                    {
                        extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(item.ExtraProperties["invoiceDetailExtras"]);
                    }
                    if (extraProperties.Any())
                    {
                        item.InvoiceDetailExtras = extraProperties.Select(x => new Invoice01DetailExtraExportModel
                        {
                            FieldValue = x.FieldValue,
                            FieldName = x.FieldName
                        }).ToList();
                    }
                }
            }

            var groupInvoiceDetails = invoiceDetails.GroupBy(x => x.InvoiceHeaderId)
                                                    .ToDictionary(x => x.Key, x => x.ToList());

            //add details
            foreach (var item in groupInvoiceDetails)
            {
                if (invoiceHeaders.ContainsKey(item.Key))
                    invoiceHeaders[item.Key].InvoiceDetails = item.Value;
            }

            //add headerExtras
            foreach (var item in invoiceHeaders.Values.ToList())
            {
                if (item.ExtraProperties != null && item.ExtraProperties.Any())
                {
                    if (item.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceHeaderExtras"]))
                    {
                        var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(item.ExtraProperties["invoiceHeaderExtras"]);
                        if (headerExtras.Any())
                            item.InvoiceHeaderExtras = headerExtras.Select(x => new Invoice01HeaderExtraExportModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName
                            }).ToList();
                    }
                }
            }

            return invoiceHeaders.Select(x => x.Value).ToList();
        }

        public override async Task<byte[]> ConvertToExcelAsync(string path, List<string> lines, List<NuocSachHaNoiInvoiceModel> invoiceWatersModel, ExportInvoiceApiRequestModel request)
        {
            var excelPath = Path.Combine(path, "outputConvertCSVToExcelFormats.xlsx");
            using (var package = new ExcelPackage(new FileInfo(excelPath)))
            {
                var worksheet = package.Workbook.Worksheets.Add("worksheet");

                #region Tiêu đề
                worksheet.Row(1).Style.Font.Name = "Times New Roman";
                worksheet.Row(1).Style.Font.Size = 11;
                worksheet.Row(1).Height = 15;
                worksheet.Row(1).Style.Font.Bold = true;
                worksheet.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                worksheet.Cells[1, 1].Value = _appFactory.CurrentTenant.Address;
                worksheet.Cells[1, 1, 1, 8].Merge = true;

                worksheet.Row(2).Style.Font.Name = "Times New Roman";
                worksheet.Row(2).Style.Font.Size = 11;
                worksheet.Row(2).Height = 15;
                worksheet.Row(2).Style.Font.Bold = true;
                worksheet.Row(2).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                worksheet.Cells[2, 1].Value = $"Mã số thuế: {_appFactory.CurrentTenant.TaxCode}";
                worksheet.Cells[2, 1, 2, 8].Merge = true;

                worksheet.Row(3).Style.Font.Name = "Times New Roman";
                worksheet.Row(3).Style.Font.Size = 20;
                worksheet.Row(3).Style.Font.Bold = true;
                worksheet.Row(3).Height = 25;
                worksheet.Row(3).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[3, 2].Value = "BẢNG KÊ CHI TIẾT HÀNG HÓA, DỊCH VỤ GTGT";
                worksheet.Cells[3, 2, 3, 13].Merge = true;

                worksheet.Row(4).Style.Font.Name = "Times New Roman";
                worksheet.Row(4).Style.Font.Size = 11;
                worksheet.Row(4).Height = 15;
                worksheet.Row(4).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[4, 8].Value = REQUEST_DATE;
                worksheet.Cells[4, 8, 4, 13].Merge = true;
                //Tiêu đề
                var headerRow = new List<string[]>()
                {
                    lines[0].Split(",")
                };
                //worksheet.Cells[5, 1, 5, headerRow.Count()].LoadFromArrays(headerRow);
                worksheet.Cells[5, 1].Value = headerRow[0][0];
                worksheet.Cells[5, 1, 7, 1].Merge = true;

                worksheet.Cells[5, 2].Value = headerRow[0][1];
                worksheet.Cells[5, 2, 7, 2].Merge = true;

                worksheet.Cells[5, 3].Value = headerRow[0][2];
                worksheet.Cells[5, 3, 7, 3].Merge = true;

                worksheet.Cells[5, 4].Value = headerRow[0][3];
                worksheet.Cells[5, 4, 7, 4].Merge = true;

                worksheet.Cells[5, 5].Value = headerRow[0][4];
                worksheet.Cells[5, 5, 7, 5].Merge = true;

                worksheet.Cells[5, 6].Value = headerRow[0][5];
                worksheet.Cells[5, 6, 7, 6].Merge = true;

                worksheet.Cells[5, 7].Value = headerRow[0][6];
                worksheet.Cells[5, 7, 7, 7].Merge = true;

                worksheet.Cells[5, 8].Value = headerRow[0][7];
                worksheet.Cells[5, 8, 7, 8].Merge = true;

                worksheet.Cells[5, 9].Value = "Chi tiết bán hàng";
                worksheet.Cells[5, 9].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[5, 9, 5, 10].Merge = true;

                worksheet.Cells[6, 9].Value = headerRow[0][8];
                worksheet.Cells[6, 9, 7, 9].Merge = true;

                worksheet.Cells[6, 10].Value = headerRow[0][9];
                worksheet.Cells[6, 10, 7, 10].Merge = true;

                worksheet.Cells[5, 11].Value = headerRow[0][10];
                worksheet.Cells[5, 11, 7, 11].Merge = true;

                worksheet.Cells[5, 12].Value = headerRow[0][11];
                worksheet.Cells[5, 12, 7, 12].Merge = true;

                worksheet.Cells[5, 13].Value = headerRow[0][12];
                worksheet.Cells[5, 13, 7, 13].Merge = true;

                worksheet.Cells[5, 14].Value = headerRow[0][13];
                worksheet.Cells[5, 14, 7, 14].Merge = true;

                worksheet.Cells[5, 15].Value = headerRow[0][14];
                worksheet.Cells[5, 15, 7, 15].Merge = true;

                worksheet.Cells[5, 16].Value = headerRow[0][15];
                worksheet.Cells[5, 16, 7, 16].Merge = true;

                worksheet.Cells[5, 17].Value = headerRow[0][16];
                worksheet.Cells[5, 17, 7, 17].Merge = true;

                worksheet.Cells[5, 18].Value = headerRow[0][17];
                worksheet.Cells[5, 18, 7, 18].Merge = true;

                worksheet.Cells[5, 19].Value = headerRow[0][18];
                worksheet.Cells[5, 19, 7, 19].Merge = true;

                worksheet.Cells[5, 20].Value = headerRow[0][19];
                worksheet.Cells[5, 20, 7, 20].Merge = true;

                worksheet.Cells[5, 21].Value = "Thông tin tra soát";
                worksheet.Cells[5, 21].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells[5, 21, 5, 24].Merge = true;

                worksheet.Cells[6, 21].Value = headerRow[0][20];
                worksheet.Cells[6, 21, 7, 21].Merge = true;

                worksheet.Cells[6, 22].Value = headerRow[0][21];
                worksheet.Cells[6, 22, 7, 22].Merge = true;

                worksheet.Cells[6, 23].Value = headerRow[0][22];
                worksheet.Cells[6, 23, 7, 23].Merge = true;

                worksheet.Cells[6, 24].Value = headerRow[0][23];
                worksheet.Cells[6, 24, 7, 24].Merge = true;
                #endregion

                for (int i = 1; i < lines.Count; i++)
                {
                    var bodyRow = new List<string[]>()
                    {
                        lines[i].Split(",")
                    };
                    var row = bodyRow[0];
                    if (row.Length == 1)
                        continue;

                    worksheet.Cells[i + 7, 1].Value = row[0];
                    worksheet.Cells[i + 7, 2].Value = row[1];
                    worksheet.Cells[i + 7, 3].Value = row[2];
                    worksheet.Cells[i + 7, 4].Value = row[3];
                    worksheet.Cells[i + 7, 5].Value = row[4];
                    worksheet.Cells[i + 7, 6].Value = row[5];
                    worksheet.Cells[i + 7, 7].Value = row[6];
                    worksheet.Cells[i + 7, 8].Value = row[7];
                    worksheet.Cells[i + 7, 9].Value = row[8];
                    worksheet.Cells[i + 7, 10].Value = row[9];


                    worksheet.Cells[i + 7, 11].Value = row[10] != "" ? decimal.Parse(row[10].ToString().Replace(".", ",")) : 0;
                    worksheet.Cells[i + 7, 11].Style.Numberformat.Format = "#,##0";

                    worksheet.Cells[i + 7, 12].Value = row[11];

                    worksheet.Cells[i + 7, 13].Value = row[12] != "" ? decimal.Parse(row[12].ToString().Replace(".", ",")) : 0;
                    worksheet.Cells[i + 7, 13].Style.Numberformat.Format = "#,##0";

                    worksheet.Cells[i + 7, 14].Value = row[13] != "" ? decimal.Parse(row[13].ToString().Replace(".", ",")) : 0;
                    worksheet.Cells[i + 7, 14].Style.Numberformat.Format = "#,##0";

                    worksheet.Cells[i + 7, 15].Value = row[14];
                    worksheet.Cells[i + 7, 16].Value = row[15];
                    worksheet.Cells[i + 7, 17].Value = row[16];
                    worksheet.Cells[i + 7, 18].Value = row[17];
                    worksheet.Cells[i + 7, 19].Value = row[18];
                    worksheet.Cells[i + 7, 20].Value = row[19];
                    worksheet.Cells[i + 7, 21].Value = row[20];
                    worksheet.Cells[i + 7, 22].Value = row[21];
                    worksheet.Cells[i + 7, 23].Value = row[22];
                    worksheet.Cells[i + 7, 24].Value = row[23];

                }
                package.Save();
            }
            var bytes = await File.ReadAllBytesAsync(excelPath);
            return bytes;
        }
    }
}
