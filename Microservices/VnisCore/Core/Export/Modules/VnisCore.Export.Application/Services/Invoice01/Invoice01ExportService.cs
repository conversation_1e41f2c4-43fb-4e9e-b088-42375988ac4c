using AutoMapper;

using Core.AspNetCore.Mvc;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.Invoice01;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.Bc26;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.TaxReport03;
using VnisCore.Export.Application.Business;
using VnisCore.Export.Application.Interfaces;
using VnisCore.Export.Application.Models.Invoice01s;
using VnisCore.Export.Application.Models.Requests;
using VnisCore.Export.Application.Services.Invoice01.ExportRefactor;
using static Core.Shared.Extensions.DateRangeValidator;

namespace VnisCore.Export.Application.Services
{
    [Authorize]
    [Route(Utilities.ApiUrlBase)]
    public class Invoice01ExportService : AbpController
    {
        private readonly IAppFactory _factory;
        private readonly IExportExcelInvoice01HeaderBusiness _exportExcelInvoice01HeaderBusiness;
        private readonly IFlexcelInvoice01ExportHeaderService _flexcelInvoice01ExportHeaderService;
        private readonly IFlexcelInvoice01ExportDetailService _flexcellInvoice01ExportDetailService;
        private readonly IEnumerable<IInvoiceExportService> _exportServices;
        private readonly IConfiguration _configuration;

        public Invoice01ExportService(
            IAppFactory factory,
            IExportExcelInvoice01HeaderBusiness exportExcelInvoice01HeaderBusiness,
            IFlexcelInvoice01ExportHeaderService flexcelInvoice01ExportHeaderService,
            IFlexcelInvoice01ExportDetailService flexcellInvoice01ExportDetailService,
            IEnumerable<IInvoiceExportService> exportServices,
            IConfiguration configuration)
        {
            _factory = factory;
            _exportExcelInvoice01HeaderBusiness = exportExcelInvoice01HeaderBusiness;
            _exportServices = exportServices;
            _flexcelInvoice01ExportHeaderService = flexcelInvoice01ExportHeaderService;
            _flexcellInvoice01ExportDetailService = flexcellInvoice01ExportDetailService;
            _configuration = configuration;
        }

        /// <summary>
        /// xuất excel bảng kê
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Authorize(Invoice01Permissions.Invoice01.ExportExcel)]
        [HttpPost("excel/{idTask?}")]
        public async Task<IActionResult> ExportExcel([FromRoute] Guid? idTask, [FromBody] ExportInvoice01RequestModel request)
        {
            short.TryParse(_configuration.GetSection("Settings:MonthsLimit").Value, out var monthsLimit);
            if (monthsLimit <= 0)
            {
                monthsLimit = 3;
            }

            DateRangeChecker.ValidateDateRange(request.CreateFromDate.Value.Date, request.CreateToDate.Value.Date, monthsLimit);

            switch (request.Service)
            {
                case "FlexcelInvoice01ExportHeaderService":
                    {
                        try
                        {

                            var config = new MapperConfiguration(cfg =>
                            {
                                cfg.CreateMap<ExportInvoice01RequestModel, ExportExcelInvoice01RequestModel>().ReverseMap();
                            });

                            var mapper = new Mapper(config);
                            var input = mapper.Map<ExportInvoice01RequestModel, ExportExcelInvoice01RequestModel>(request);
                            var fileDto = await _flexcelInvoice01ExportHeaderService.ExportExcelAsync(input);
                            return Ok(fileDto);
                        }
                        catch (Exception e)
                        {

                            throw;
                        }
                    }
                case "FlexcellInvoice01ExportDetailService":
                    {
                        var config = new MapperConfiguration(cfg =>
                        {
                            cfg.CreateMap<ExportInvoice01RequestModel, ExportExcelInvoice01RequestModel>().ReverseMap();
                        });

                        var mapper = new Mapper(config);
                        var input = mapper.Map<ExportInvoice01RequestModel, ExportExcelInvoice01RequestModel>(request);
                        var fileDto = await _flexcellInvoice01ExportDetailService.ExportExcelAsync(input);
                        return Ok(fileDto);
                    }
            }

            var service = _exportServices.First(x => x.GetType().Name == typeof(Invoice01ExportHeaderService).Name);
            if (!string.IsNullOrEmpty(request.Service))
            {
                service = _exportServices.FirstOrDefault(x => x.GetType().Name == request.Service);
            }

            if (service.GetType().Name == typeof(Invoice01ExportHeaderService).Name)
            {
                var config = new MapperConfiguration(cfg =>
                {
                    cfg.CreateMap<ExportInvoice01RequestModel, ExportExcelInvoice01HeaderRequestModel>().ReverseMap();
                });

                var mapper = new Mapper(config);
                var input = mapper.Map<ExportInvoice01RequestModel, ExportExcelInvoice01HeaderRequestModel>(request);
                var response = await _exportExcelInvoice01HeaderBusiness.ExportExcelAsync(input);

                return Ok(new FileDto
                {
                    ContentType = ContentType.Stream,
                    FileBytes = response.FileBytes,
                    FileName = response.FileName
                });
            }
            else
            {
                request.TaskId = idTask;
                var response = await _factory.Mediator.Send(request);
                if (!request.TaskId.HasValue)
                {
                    //nếu là lần đầu export
                    return Ok(response);
                }
                else
                {
                    //là lần t2 trở đi
                    if (response.TotalPages == response.CurrentPage && response.FileBytes != null && response.FileBytes.Length > 0)
                    {
                        //lần cuối cùng
                        //return File(response.Bytes, ContentType.Stream, response.FileName);
                        return Ok(new FileDto
                        {
                            FileBytes = response.FileBytes,
                            ContentType = ContentType.Stream,
                            FileName = response.FileName,
                        });
                    }
                    else
                        return Ok(response);
                }
            }
        }

        /// <summary>
        /// Export Excel BC26/AC
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("export-excel-bc26/{id}")]
        public async Task<FileDto> ExportBc26Excel([FromRoute] long id)
        {
            var request = new ExportExcelBc26RequestModel
            {
                Id = id
            };
            var response = await _factory.Mediator.Send(request);

            return new FileDto
            {
                FileBytes = response.Datas,
                FileName = response.FileName,
                ContentType = ContentType.Stream
            };
        }

        /// <summary>
        /// Export XML BC26/AC
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize(Bc26Permissions.Bc26.ExportXml)]
        [HttpGet("export-xml-bc26/{id}")]
        public async Task<FileDto> ExportBc26XML([FromRoute] long id)
        {
            var request = new ExportXmlBc26RequestModel
            {
                Id = id
            };
            var response = await _factory.Mediator.Send(request);

            return new FileDto
            {
                FileBytes = response.Datas,
                FileName = response.FileName,
                ContentType = ContentType.Stream
            };
        }

        /// <summary>
        /// Export Excel Taxreport03
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("export-excel-taxreport03/{id}")]
        public async Task<FileDto> ExportTaxReport03Excel([FromRoute] long Id)
        {
            var request = new ExportExcelTaxReport03RequestModel
            {
                Id = Id
            };
            var response = await _factory.Mediator.Send(request);

            return new FileDto
            {
                FileBytes = response.Datas,
                FileName = response.FileName,
                ContentType = ContentType.Stream
            };
        }

        /// <summary>
        /// Export XML Taxreport03
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [Authorize(TaxReport03Permissions.TaxReport03.ExportXml)]
        [HttpGet("export-xml-taxreport03/{id}")]
        public async Task<FileDto> ExportTaxReport03XML([FromRoute] long Id)
        {
            var request = new ExportExcelTaxReport03RequestModel
            {
                Id = Id
            };
            var response = await _factory.Mediator.Send(request);

            return new FileDto{ 
                FileBytes = response.Datas,
                FileName = response.FileName,
                ContentType = ContentType.Stream
            };
            //(response.Datas, ContentType.Files, response.FileName);
        }

        /// <summary>
        /// xuất excel bảng kê
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [Authorize(Invoice01Permissions.Invoice01.ExportExcel)]
        [HttpPost("excel-refactor")]
        public async Task<FileDto> ExportExcelRefactorAsync([FromBody] ExportExcelInvoice01HeaderRequestModel request)
        {
            var response = await _exportExcelInvoice01HeaderBusiness.ExportExcelAsync(request);

            return new FileDto
            {
                ContentType = ContentType.Stream,
                FileBytes = response.FileBytes,
                FileName = response.FileName
            };
        }
    }
}
