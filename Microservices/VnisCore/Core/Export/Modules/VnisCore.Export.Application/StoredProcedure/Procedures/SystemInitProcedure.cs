using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace VnisCore.Export.Application.StoredProcedure.Procedures
{
    public static class SystemInitProcedure
    {
        public static void CreateProcedureInvoice01GetListByImportFile(VnisCoreOracleDbContext dbContext)
        {
            #region Version 1 - Query string
            //var spQuery = $@"
            //    create or replace NONEDITIONABLE PROCEDURE {SystemProcedureName.Invoice01GetListByImportFile} 
            //    (
            //        json_conditions IN NCLOB,
            //        tenantRawId IN VARCHAR2,
            //        output_data OUT SYS_REFCURSOR
            //    )
            //    AS 
            //        v_conditions NCLOB;
            //        v_query NCLOB;
            //    BEGIN

            //        FOR REC IN(
            //            SELECT J.InvoiceDate, J.BuyerBankAccount, J.<PERSON>, J.TellSeq
            //               FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
            //                    InvoiceDate VARCHAR2(200)       PATH '$.InvoiceDate',
            //                    BuyerBankAccount VARCHAR2(200)  PATH '$.BuyerBankAccount',
            //                    PayerCode VARCHAR2(200)         PATH '$.PayerCode',
            //                    TellSeq VARCHAR2(200)           PATH '$.TellSeq'
            //               ) J
            //            ) 
            //        LOOP
            //            v_conditions := v_conditions || 'OR ( ' ||
            //                            '""InvoiceDate""=''' || REC.InvoiceDate ||
            //                            ''' AND ""BuyerBankAccount"" = ''' || REC.BuyerBankAccount ||
            //                            ''' AND ""ExtraProperties"" LIKE ''%\""FieldName\"":\""TellSeq\"",\""FieldValue\"":\""' || REC.TellSeq || '\""%'''
            //                            || ' ) ';
            //                END LOOP;

            //            v_conditions:= SUBSTR(v_conditions, 3);
            //            v_conditions:= ' ( ' || v_conditions || ' ) ';

            //            v_query:=
            //               '    SELECT B.*                                         ' ||
            //               '    FROM                                               ' ||
            //               '    (                                                  ' ||
            //               '        select                                         ' ||
            //               '            A.""TemplateNo"",                            ' ||
            //               '            A.""SerialNo"",                              ' ||
            //               '            A.""InvoiceNo"",                             ' ||
            //               '            A.""InvoiceStatus"",                         ' ||
            //               '            A.""InvoiceDate"",                           ' ||
            //               '            A.""BuyerBankAccount"",                      ' ||
            //               '            A.""ExtraProperties""                        ' ||
            //               '        FROM ""Invoice01Header"" A                       ' ||
            //               '        WHERE                                          ' ||
            //               '            ""TenantId"" = ''' || tenantRawId || ''' AND ' ||
            //               '            ""ExtraProperties"" IS NOT NULL AND          ' ||
            //                           v_conditions ||
            //               '    ) B                                                ';

            //                --DBMS_OUTPUT.PUT_LINE(v_query);

            //                OPEN output_data FOR v_query;

            //                END {SystemProcedureName.Invoice01GetListByImportFile};
            //";
            #endregion

            #region Version 2 - Join Json Table
            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {SystemProcedureName.Invoice01GetListByImportFile} 
                (
                    json_conditions IN NCLOB,
                    tenantRawId IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS 
                BEGIN
                    OPEN output_data FOR
                        SELECT*
                        FROM
                        (
                            select
                                A.""Id"",
                                A.""TemplateNo"",
                                A.""SerialNo"",
                                A.""InvoiceNo"",
                                A.""InvoiceStatus"",
                                A.""InvoiceDate"",
                                A.""BuyerBankAccount"",
                                A.""CreatorErp"",
                                A.""ExtraProperties""                
                            FROM ""Invoice01Header"" A
                            INNER JOIN (
                                SELECT J.InvoiceDate, J.BuyerBankAccount, J.PayerCode, '\""FieldName\"":\""TellSeq\"",\""FieldValue\"":\""' || J.TellSeq || '\""' TellSeq
                                FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
                                    InvoiceDate VARCHAR2(200)       PATH '$.InvoiceDate',
                                    BuyerBankAccount VARCHAR2(200)  PATH '$.BuyerBankAccount',
                                    PayerCode VARCHAR2(200)         PATH '$.PayerCode',
                                    TellSeq VARCHAR2(200)           PATH '$.TellSeq'
                                ) J
                            ) R ON(
                                A.""InvoiceDate"" = R.INVOICEDATE AND
                                A.""BuyerBankAccount"" = R.BUYERBANKACCOUNT AND 
                                A.""CreatorErp"" = R.PAYERCODE AND
                                A.""ExtraProperties"" LIKE '%' || R.TELLSEQ || '%')
                            WHERE
                                A.""TenantId"" = tenantRawId
                                --A.""ExtraProperties"" IS NOT NULL
                        );

                END {SystemProcedureName.Invoice01GetListByImportFile};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        }
    }
} 