using Core;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Export.Application.Models;
using VnisCore.Export.Application.Models.Requests;
using VnisCore.Export.Application.Models.Responses;

namespace VnisCore.Export.Application.Handlers.Queries
{
    public class ExportExcelTaxReport03QueryHandler : IRequestHandler<ExportExcelTaxReport03RequestModel, ExportExcelTaxReport03ResponseModel>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IRepository<TaxReport03HeaderEntity, long> _repoTaxReportHeader03;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public ExportExcelTaxReport03QueryHandler(
            IServiceProvider serviceProvider,
            IRepository<TaxReport03HeaderEntity, long> repoTaxReportHeader03,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory)
        {
            _repoTaxReportHeader03 = repoTaxReportHeader03;
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _localizer = localizer;
        }

        public async Task<ExportExcelTaxReport03ResponseModel> Handle(ExportExcelTaxReport03RequestModel request, CancellationToken cancellationToken)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var taxReport03 = await _repoTaxReportHeader03.Where(x => x.TenantId == tenantId && x.Id == request.Id && !x.IsDeleted).FirstOrDefaultAsync();

            if (taxReport03 == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport03.NotFound"]);

            var repoTaxDetails = _appFactory.Repository<TaxReport03DetailEntity, long>();
            var taxDetails = await repoTaxDetails.Where(x => x.TaxReportHeaderId == taxReport03.Id).Include(x => x.TaxReportDetailDatas).ToListAsync();
            taxReport03.TaxReportDetails = taxDetails;

            if (!taxReport03.TaxReportDetails.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Report.TaxReport03.DetailsNotFound"]);

            var model = new QueryTaxReport03Model
            {
                FromDate = taxReport03.FromDate,
                ToDate = taxReport03.ToDate,
                FullNameCreator = taxReport03.FullNameCreator,
                ReportDate = taxReport03.ReportDate,
                ReportMonth = taxReport03.ReportMonth,
                ReportQuarter = taxReport03.ReportQuarter,
                ReportYear = taxReport03.ReportYear,
                TaxCodeAgency = taxReport03.TaxCodeAgency,
                FullNameAgency = taxReport03.FullNameAgency,
                Representative = taxReport03.Representative,
                SumTotalAmount = taxReport03.SumTotalAmount,
                SumTotalVatAmount = taxReport03.SumTotalVatAmount,
                TaxReportDetails = taxReport03.TaxReportDetails.Select(x => new TaxReport03DetailResponseModel
                {
                    Id = x.Id,
                    SumAmount = x.SumAmount,
                    SumVatAmount = x.SumVatAmount,
                    VatPercent = x.VatPercent,
                    TaxReportDetailDatas = x.TaxReportDetailDatas.Select(y => new TaxReport03DetailDataResponseModel
                    {
                        Id = y.Id,
                        Number = y.Number,
                        Note = y.Note,
                        InvoiceDate = y.InvoiceDate,
                        BuyerFullName = y.BuyerFullName,
                        BuyerTaxCode = y.BuyerTaxCode,
                        Amount = y.Amount,
                        VatAmount = y.VatAmount,
                        VatPercent = x.VatPercent,
                    }).ToList()
                }).ToList()
            };

            var bytes = await ExportAsync(tenantId, model);
            string filename = "";
            if (taxReport03.ReportQuarter != 0)
                filename = $"{_appFactory.CurrentTenant.TaxCode}-TAXREPORT03-Q{taxReport03.ReportQuarter}{taxReport03.ReportYear}-L00.xlsx";

            if (taxReport03.ReportMonth != 0)
                filename = $"{_appFactory.CurrentTenant.TaxCode}-TAXREPORT03-M{taxReport03.ReportMonth}{taxReport03.ReportYear}-L00.xlsx";

            return new ExportExcelTaxReport03ResponseModel
            {
                Datas = bytes,
                FileName = filename
            };
        }

        public async Task<byte[]> ExportAsync(Guid tenantId, QueryTaxReport03Model model)
        {
            //lấy cấu hình số dòng trong 1 file
            var configuation = _serviceProvider.GetService<IConfiguration>();
            var pathFolder = Path.Combine(configuation.GetSection("FileExport:PathFolder").Value.Trim(), Guid.NewGuid().ToString());
            //nếu có folder bảng kê thì bỏ qua, chưa có thì tạo
            if (!Directory.Exists(pathFolder))
                Directory.CreateDirectory(pathFolder);
            var file = await ExportExcelAsync(model, tenantId, pathFolder);
            var bytes = await File.ReadAllBytesAsync(file);

            //xóa folder
            var dir = new DirectoryInfo(pathFolder);
            dir.Attributes = dir.Attributes & ~FileAttributes.ReadOnly;
            dir.Delete(true);

            return bytes;
        }
        public async Task<string> ExportExcelAsync(QueryTaxReport03Model queryTaxReport03, Guid tenantId, string pathFolder)
        {
            //var groupDetails = invoices.GroupBy(x => x.InvoiceTaxBreakdowns.GroupBy(x => x.VatPercent)).ToList();

            //var detailVatPercent5s = new List<Invoice01Header>();
            //var detailVatPercent10s = new List<Invoice01Header>();
            //foreach (var items in groupDetails)
            //{
            //    if (items.Key.Any(x => x.Key == 5))
            //    {
            //        foreach (var item in items)
            //        {
            //            detailVatPercent5s.Add(item);
            //        }
            //    }
            //    if (items.Key.Any(x => x.Key == 10))
            //    {
            //        foreach (var item in items)
            //        {
            //            detailVatPercent10s.Add(item);
            //        }
            //    }
            //}

            var filePath = Path.Combine(pathFolder, $"BaoCaoTaxReport03_{queryTaxReport03.FromDate:dd-MM-yyyy}_{queryTaxReport03.ToDate:dd-MM-yyyy}.xlsx");
            using (var package = new ExcelPackage(new FileInfo(filePath)))
            {
                #region Bảng kê tổng hợp

                #region tiêu đề sheet1
                var workSheet1 = package.Workbook.Worksheets.Add("Sheet1");

                workSheet1.DefaultColWidth = 10;
                // Tự động xuống hàng khi text quá dài
                workSheet1.Cells.Style.WrapText = true;

                workSheet1.Row(1).Style.Font.Name = "Times New Roman";
                workSheet1.Row(1).Style.Font.Size = 11;
                workSheet1.Row(1).Height = 15.75;
                workSheet1.Row(1).Style.Font.Bold = true;
                workSheet1.Row(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[1, 1].Value = "Mẫu số 03/DL-HĐĐT";
                workSheet1.Cells[1, 1, 1, 8].Merge = true;

                workSheet1.Row(2).Style.Font.Name = "Times New Roman";
                workSheet1.Row(2).Style.Font.Size = 11;
                workSheet1.Row(2).Height = 15.75;
                workSheet1.Row(2).Style.Font.Bold = true;
                workSheet1.Row(2).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[2, 1].Value = "CỘNG HOÀ XÃ HỘI CHỦ NGHĨA VIỆT NAM";
                workSheet1.Cells[2, 1, 2, 8].Merge = true;

                workSheet1.Row(3).Style.Font.Name = "Times New Roman";
                workSheet1.Row(3).Style.Font.Size = 11;
                workSheet1.Row(3).Height = 15.75;
                workSheet1.Row(3).Style.Font.Bold = true;
                workSheet1.Row(3).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[3, 1].Value = "Độc lập - Tự do - Hạnh phúc";
                workSheet1.Cells[3, 1, 3, 8].Merge = true;

                workSheet1.Row(5).Style.Font.Name = "Times New Roman";
                workSheet1.Row(5).Style.Font.Size = 11;
                workSheet1.Row(5).Height = 15.75;
                workSheet1.Row(5).Style.Font.Bold = true;
                workSheet1.Row(5).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[5, 1].Value = "TỜ KHAI";
                workSheet1.Cells[5, 1, 5, 8].Merge = true;

                workSheet1.Row(6).Style.Font.Name = "Times New Roman";
                workSheet1.Row(6).Style.Font.Size = 11;
                workSheet1.Row(6).Height = 15.75;
                workSheet1.Row(6).Style.Font.Bold = true;
                workSheet1.Row(6).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[6, 1].Value = "DỮ LIỆU HÓA ĐƠN, CHỨNG TỪ HÀNG HOÁ, DỊCH VỤ BÁN RA";
                workSheet1.Cells[6, 1, 6, 8].Merge = true;

                workSheet1.Row(8).Style.Font.Name = "Times New Roman";
                workSheet1.Row(8).Style.Font.Size = 10;
                workSheet1.Row(8).Height = 15.75;
                workSheet1.Row(8).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[8, 2].Value = $"[01] Kỳ tính thuế: Quý {queryTaxReport03.ReportQuarter} năm {queryTaxReport03.ReportYear} (từ ngày {queryTaxReport03.FromDate.ToShortDateString()} đến ngày {queryTaxReport03.ToDate.ToShortDateString()})";
                workSheet1.Cells[8, 2, 8, 8].Merge = true;

                workSheet1.Row(9).Style.Font.Name = "Times New Roman";
                workSheet1.Row(9).Style.Font.Size = 10;
                workSheet1.Row(9).Height = 12.75;
                workSheet1.Row(9).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[9, 2].Value = $"[02] Tên người nộp thuế: {_appFactory.CurrentUser.Name}";
                workSheet1.Cells[9, 2, 9, 8].Merge = true;

                workSheet1.Row(10).Style.Font.Name = "Times New Roman";
                workSheet1.Row(10).Style.Font.Size = 10;
                workSheet1.Row(10).Height = 12.75;
                workSheet1.Row(10).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[10, 2].Value = $"[03] Mã số thuế: {_appFactory.CurrentTenant.TaxCode}";
                workSheet1.Cells[10, 2, 10, 8].Merge = true;

                workSheet1.Row(11).Style.Font.Name = "Times New Roman";
                workSheet1.Row(11).Style.Font.Size = 10;
                workSheet1.Row(11).Height = 12.75;
                workSheet1.Row(11).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[11, 2].Value = $"[04] Tên đại lý thuế (nếu có): {queryTaxReport03.FullNameAgency}";
                workSheet1.Cells[11, 2, 11, 8].Merge = true;

                workSheet1.Row(12).Style.Font.Name = "Times New Roman";
                workSheet1.Row(12).Style.Font.Size = 10;
                workSheet1.Row(12).Height = 12.75;
                workSheet1.Row(12).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[12, 2].Value = $"[05] Mã số thuế: {queryTaxReport03.TaxCodeAgency}";
                workSheet1.Cells[12, 2, 12, 8].Merge = true;


                workSheet1.Row(13).Style.Font.Name = "Times New Roman";
                workSheet1.Row(13).Style.Font.Size = 10;
                workSheet1.Row(13).Height = 12.75;
                workSheet1.Row(13).Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[13, 7].Value = "Đơn vị: đồng Việt Nam";
                workSheet1.Cells[13, 7, 13, 8].Merge = true;

                workSheet1.Row(14).Style.Font.Name = "Times New Roman";
                workSheet1.Row(14).Style.Font.Size = 9;
                workSheet1.Row(14).Style.Font.Bold = true;
                workSheet1.Row(14).Height = 12.75;

                workSheet1.Row(15).Style.Font.Name = "Times New Roman";
                workSheet1.Row(15).Style.Font.Size = 9;
                workSheet1.Row(15).Style.Font.Bold = true;
                workSheet1.Row(15).Height = 35.25;

                workSheet1.Row(15).Style.Font.Name = "Times New Roman";
                workSheet1.Row(15).Style.Font.Size = 10;
                workSheet1.Row(15).Height = 12.75;

                workSheet1.Cells[14, 1, 16, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[14, 1, 16, 8].Style.Border.Top.Color.SetColor(Color.Black);
                workSheet1.Cells[14, 1, 16, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[14, 1, 16, 8].Style.Border.Right.Color.SetColor(Color.Black);
                workSheet1.Cells[14, 1, 16, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[14, 1, 16, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[14, 1, 16, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[14, 1, 16, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Center;

                workSheet1.Cells[14, 1].Value = "STT";
                //workSheet1.Cells[12, 2].AddComment("Thông tin này bắt buộc phải nhập", "vnis");
                workSheet1.Cells[14, 1, 15, 1].Merge = true;

                workSheet1.Cells[14, 2].Value = "Hóa đơn, chứng từ bán ra";
                workSheet1.Cells[14, 2, 14, 3].Merge = true;

                workSheet1.Cells[14, 4].Value = "Tên người mua";
                workSheet1.Cells[14, 4, 15, 4].Merge = true;

                workSheet1.Cells[14, 5].Value = "Mã số thuế người mua";
                workSheet1.Cells[14, 5, 15, 5].Merge = true;

                workSheet1.Cells[14, 6].Value = "Doanh thu chưa có thuế GTGT";
                workSheet1.Cells[14, 6, 15, 6].Merge = true;

                workSheet1.Cells[14, 7].Value = "Thuế GTGT";
                workSheet1.Cells[14, 7, 15, 7].Merge = true;

                workSheet1.Cells[14, 8].Value = "Ghi chú";
                workSheet1.Cells[14, 8, 15, 8].Merge = true;

                workSheet1.Cells[15, 2].Value = "Số hóa đơn";

                workSheet1.Cells[15, 3].Value = "Ngày, tháng, năm lập hóa đơn";


                for (int i = 1; i <= 8; i++)
                {
                    workSheet1.Cells[16, i].Value = $"[{i}]";
                }

                workSheet1.Column(1).Width = 9;
                workSheet1.Column(2).Width = 12;
                workSheet1.Column(3).Width = 14;
                workSheet1.Column(4).Width = 15;
                workSheet1.Column(5).Width = 15;
                workSheet1.Column(6).Width = 16;
                workSheet1.Column(7).Width = 15;
                workSheet1.Column(8).Width = 14;

                #endregion
                //đếm số dòng
                var row = 17;

                #region export detail k chịu thuế

                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 4].Value = "1. Hàng hoá, dịch vụ không chịu thuế giá trị gia tăng(GTGT):";
                workSheet1.Cells[row, 1, row, 4].Merge = true;

                row++;
                int count = 0;
                var detailsVatPercentKCT = queryTaxReport03.TaxReportDetails.Where(x => x.VatPercent == -1).FirstOrDefault();

                if (detailsVatPercentKCT.TaxReportDetailDatas != null && detailsVatPercentKCT.TaxReportDetailDatas.Any())
                {
                    foreach (var data in detailsVatPercentKCT.TaxReportDetailDatas)
                    {
                        var amount = Math.Round(data.Amount, MidpointRounding.AwayFromZero);
                        var vatAmount = Math.Round(data.VatAmount, MidpointRounding.AwayFromZero);
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Name = "Times New Roman";
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Size = 10;
                        workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                        workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheet1.Cells[row, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";

                        workSheet1.Cells[row, 1].Value = count + 1;
                        workSheet1.Cells[row, 2].Value = data.Number.ToString("000000");
                        workSheet1.Cells[row, 3].Value = data.InvoiceDate.ToString("dd/MM/yyyy");
                        workSheet1.Cells[row, 4].Value = data.BuyerFullName;
                        workSheet1.Cells[row, 5].Value = data.BuyerTaxCode;
                        workSheet1.Cells[row, 6].Value = amount;
                        workSheet1.Cells[row, 7].Value = vatAmount;
                        workSheet1.Cells[row, 8].Value = data.Note;
                        row++;
                        count++;
                    }

                }

                //tổng thuế KCT
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Row(row).Style.Font.Bold = true;
                workSheet1.Cells[row, 1].Value = "Tổng";
                workSheet1.Cells[row, 1, row, 5].Merge = true;
                workSheet1.Cells[row, 6].Value = detailsVatPercentKCT.SumAmount;
                workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 7].Value = detailsVatPercentKCT.SumVatAmount;
                workSheet1.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);

                row++;


                #endregion

                #region export detail thuế = 0
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 4].Value = "2. Hàng hoá, dịch vụ chịu thuế GTGT 0%:";
                workSheet1.Cells[row, 1, row, 4].Merge = true;

                row++;
                count = 0;
                var detailsVatPercent0 = queryTaxReport03.TaxReportDetails.Where(x => x.VatPercent == 0).FirstOrDefault();
                if (detailsVatPercent0.TaxReportDetailDatas != null && detailsVatPercent0.TaxReportDetailDatas.Any())
                {
                    foreach (var data in detailsVatPercent0.TaxReportDetailDatas)
                    {
                        var amount = Math.Round(data.Amount, MidpointRounding.AwayFromZero);
                        var vatAmount = Math.Round(data.VatAmount, MidpointRounding.AwayFromZero);
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Name = "Times New Roman";
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Size = 10;
                        workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                        workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheet1.Cells[row, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";

                        workSheet1.Cells[row, 1].Value = count + 1;
                        workSheet1.Cells[row, 2].Value = data.Number.ToString("000000");
                        workSheet1.Cells[row, 3].Value = data.InvoiceDate.ToString("dd/MM/yyyy");
                        workSheet1.Cells[row, 4].Value = data.BuyerFullName;
                        workSheet1.Cells[row, 5].Value = data.BuyerTaxCode;
                        workSheet1.Cells[row, 6].Value = amount;
                        workSheet1.Cells[row, 7].Value = vatAmount;
                        workSheet1.Cells[row, 8].Value = data.Note;
                        row++;
                        count++;
                    }

                }


                //tổng thuế 0
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Row(row).Style.Font.Bold = true;
                workSheet1.Cells[row, 1].Value = "Tổng";
                workSheet1.Cells[row, 1, row, 5].Merge = true;
                workSheet1.Cells[row, 6].Value = detailsVatPercent0.SumAmount;
                workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 7].Value = detailsVatPercent0.SumVatAmount;
                workSheet1.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);

                row++;
                #endregion

                #region export detail thuế = 5

                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 4].Value = "3. Hàng hoá, dịch vụ chịu thuế suất thuế GTGT 5%:";
                workSheet1.Cells[row, 1, row, 4].Merge = true;

                row++;
                count = 0;
                var detailsVatPercent5 = queryTaxReport03.TaxReportDetails.Where(x => x.VatPercent == 5).FirstOrDefault();
                if (detailsVatPercent5.TaxReportDetailDatas != null && detailsVatPercent5.TaxReportDetailDatas.Any())
                {
                    foreach (var data in detailsVatPercent5.TaxReportDetailDatas)
                    {
                        var amount = Math.Round(data.Amount, MidpointRounding.AwayFromZero);
                        var vatAmount = Math.Round(data.VatAmount, MidpointRounding.AwayFromZero);
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Name = "Times New Roman";
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Size = 10;
                        workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                        workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheet1.Cells[row, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";

                        workSheet1.Cells[row, 1].Value = count + 1;
                        workSheet1.Cells[row, 2].Value = data.Number.ToString("000000");
                        workSheet1.Cells[row, 3].Value = data.InvoiceDate.ToString("dd/MM/yyyy");
                        workSheet1.Cells[row, 4].Value = data.BuyerFullName;
                        workSheet1.Cells[row, 5].Value = data.BuyerTaxCode;
                        workSheet1.Cells[row, 6].Value = amount;
                        workSheet1.Cells[row, 7].Value = vatAmount;
                        workSheet1.Cells[row, 8].Value = data.Note;
                        row++;
                        count++;
                    }

                }

                //allTotalAmount += totalAmount;
                //allTotalVatAmount += totalVatAmount;
                //tổng thuế = 5
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Row(row).Style.Font.Bold = true;
                workSheet1.Cells[row, 1].Value = "Tổng";
                workSheet1.Cells[row, 1, row, 5].Merge = true;
                workSheet1.Cells[row, 6].Value = detailsVatPercent5.SumAmount;
                workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 7].Value = detailsVatPercent5.SumVatAmount;
                workSheet1.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);

                row++;

                #endregion

                #region export detail thuế = 10

                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 4].Value = "4. Hàng hoá, dịch vụ chịu thuế suất thuế GTGT 10%:";
                workSheet1.Cells[row, 1, row, 4].Merge = true;

                row++;
                count = 0;
                var detailsVatPercent10 = queryTaxReport03.TaxReportDetails.Where(x => x.VatPercent == 10).FirstOrDefault();
                if (detailsVatPercent10.TaxReportDetailDatas != null && detailsVatPercent10.TaxReportDetailDatas.Any())
                {
                    foreach (var data in detailsVatPercent10.TaxReportDetailDatas)
                    {
                        var amount = Math.Round(data.Amount, MidpointRounding.AwayFromZero);
                        var vatAmount = Math.Round(data.VatAmount, MidpointRounding.AwayFromZero);
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Name = "Times New Roman";
                        workSheet1.Cells[row, 1, row, 8].Style.Font.Size = 10;
                        workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                        workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Top.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                        workSheet1.Cells[row, 2].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        workSheet1.Cells[row, 3].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 4].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                        workSheet1.Cells[row, 5].Style.Numberformat.Format = "#,##0.00";
                        workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";

                        workSheet1.Cells[row, 1].Value = count + 1;
                        workSheet1.Cells[row, 2].Value = data.Number.ToString("000000");
                        workSheet1.Cells[row, 3].Value = data.InvoiceDate.ToString("dd/MM/yyyy");
                        workSheet1.Cells[row, 4].Value = data.BuyerFullName;
                        workSheet1.Cells[row, 5].Value = data.BuyerTaxCode;
                        workSheet1.Cells[row, 6].Value = amount;
                        workSheet1.Cells[row, 7].Value = vatAmount;
                        workSheet1.Cells[row, 8].Value = data.Note;
                        row++;
                        count++;
                    }

                }

                //tổng thuế = 10
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 1].Value = "Tổng";
                workSheet1.Cells[row, 1, row, 5].Merge = true;
                workSheet1.Cells[row, 6].Value = detailsVatPercent10.SumAmount;
                workSheet1.Cells[row, 6].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 6].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 7].Value = detailsVatPercent10.SumVatAmount;
                workSheet1.Cells[row, 7].Style.Numberformat.Format = "#,##0.00";
                workSheet1.Cells[row, 7].Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                workSheet1.Cells[row, 1, row, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 1, row, 8].Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Left.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);
                workSheet1.Cells[row, 1, row, 8].Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 1, row, 8].Style.Border.Right.Color.SetColor(Color.Black);

                row++;

                #endregion

                #endregion

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 2].Value = $"Tổng doanh thu hàng hoá, dịch vụ bán ra chịu thuế GTGT (*):";
                workSheet1.Cells[row, 2].Style.WrapText = false;
                workSheet1.Cells[row, 2, row, 5].Merge = true;
                workSheet1.Cells[row, 6].Value = queryTaxReport03.SumTotalAmount;

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 2].Value = $"Tổng số thuế GTGT của hàng hóa, dịch vụ bán ra (**):";
                workSheet1.Cells[row, 2].Style.WrapText = false;
                workSheet1.Cells[row, 2, row, 5].Merge = true;
                workSheet1.Cells[row, 6].Value = queryTaxReport03.SumTotalVatAmount;

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 12.75;
                workSheet1.Cells[row, 2].Value = $"Tôi cam đoan số liệu khai trên là đúng và chịu trách nhiệm trước pháp luật về những số liệu đã khai";
                workSheet1.Cells[row, 2].Style.WrapText = false;
                workSheet1.Cells[row, 2, row, 8].Merge = true;

                row += 2;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Row(row).Style.Font.Italic = true;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 5].Value = "......., ngày....tháng....năm....";
                workSheet1.Cells[row, 5, row, 8].Merge = true;

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 11;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Cells[row, 1, row, 5].Style.Font.Bold = true;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 5].Value = "NGƯỜI NỘI THUẾ hoặc";
                workSheet1.Cells[row, 5, row, 8].Merge = true;

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 11;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Cells[row, 1, row, 5].Style.Font.Bold = true;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 5].Value = "ĐẠI ĐIỆN HỢP PHÁP CỦA NGƯỜI NỘP THUẾ";
                workSheet1.Cells[row, 5, row, 8].Merge = true;

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Row(row).Style.Font.Italic = true;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                workSheet1.Cells[row, 5].Value = "(Chữ ký số, chữ ký điện tử của người nộp thuế)";
                workSheet1.Cells[row, 5, row, 8].Merge = true;

                row += 6;
                workSheet1.Cells[row, 2].Style.Border.Top.Style = ExcelBorderStyle.Thin;
                workSheet1.Cells[row, 2].Style.Border.Top.Color.SetColor(Color.Black);
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Row(row).Style.Font.Italic = true;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 2].Value = "Ghi chú:";

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 2].Value = "(*) Tổng doanh thu hàng hóa, dịch vụ bán ra chịu thuế GTGT là tổng cộng số liệu tại cột 6 của dòng tổng của các chỉ tiêu 2, 3, 4.";
                workSheet1.Cells[row, 2, row, 8].Merge = true;

                row++;
                workSheet1.Row(row).Style.Font.Name = "Times New Roman";
                workSheet1.Row(row).Style.Font.Size = 10;
                workSheet1.Row(row).Height = 15.75;
                workSheet1.Row(row).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                workSheet1.Cells[row, 2].Value = "(**) Tổng số thuế GTGT của hàng hóa, dịch vụ bán ra là tổng cộng số liệu tại cột 7 của dòng tổng của các chỉ tiêu 2, 3, 4.";
                workSheet1.Cells[row, 2, row, 8].Merge = true;

                package.Save();
            }
            return filePath;
        }


    }
}
