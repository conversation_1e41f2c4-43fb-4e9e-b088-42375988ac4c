using Core.Domain.Repositories.EntityFrameworkCore;
using Core.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Export.Application.Models.Invoice02s;
using Core.EntityFrameworkCore;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Text;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using Microsoft.EntityFrameworkCore;
using VnisCore.Export.Application.Models.Invoice02s.FlecCell;

namespace VnisCore.Export.Application.Repository.Invoice02
{
    public interface IInvoice02DetailRepository : IRepository<Invoice02DetailEntity, long>
    {
        Task<List<FlexCellInvoice02ExportModel>> GetDetailByInvoiceHeaderId(List<long> ids);

    }
    public class Invoice02DetailRepository : EfCoreRepository<VnisCoreOracleDbContext, Invoice02DetailEntity, long>, IInvoice02DetailRepository
    {
        public Invoice02DetailRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }
        public async Task<List<FlexCellInvoice02ExportModel>> GetDetailByInvoiceHeaderId(List<long> ids)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Set<Invoice02DetailEntity>()
                .Where(x => ids.Contains(x.InvoiceHeaderId))
                .Select(x => new FlexCellInvoice02ExportModel()
                {
                    Id = x.InvoiceHeaderId,
                    InvoiceDetailId = x.Id,
                    ProductCode = x.ProductCode,
                    ProductName = x.ProductName,
                    UnitName = x.UnitName,
                    Quantity = x.Quantity,
                    UnitPrice = x.UnitPrice,
                    Amount = x.Amount,
                    PaymentAmount = x.PaymentAmount,
                    DiscountAmount = x.DiscountAmount,
                    DiscountPercentBeforeTax=x.DiscountPercent
                })
                .ToListAsync();
        }

    }
}
