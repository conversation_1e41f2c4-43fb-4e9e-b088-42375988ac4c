using System.Collections.Generic;

namespace VnisCore.Export.Application.Models.NuocSachHaNoi
{
    public class InvoiceWatersModel
    {
        public InvoiceTypeExport InvoiceTypeExport { get; set; }
        public List<InvoiceDataModel> InvoicesDataModel { get; set; }
    }

    public class InvoiceDataModel 
    { 
        public InvoiceStatusExport InvoiceStatusExport { get; set; }
        public InvoiceHeaderModel InvoiceHeaderModel { get; set; }
    }

    public class InvoiceHeaderModel
    {
        public int Quantity { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal VatPercent { get; set; }
        public decimal TotalVatAmount { get; set; }
        public decimal FeeBVMT { get; set; }
        public decimal TotalPaymentAmount { get; set; }
        public string Note { get; set; }
    }

    public enum InvoiceTypeExport
    {
        TienNuoc = 1,
        XayLapDichVuKhac = 2,
        TruyThuTienNuoc = 3
    }

    public enum InvoiceStatusExport
    {
        HoaDonGoc = 1,
        HoaDonHuy = 2,
        HoaDonThayThe = 3,
        HoaDonDieuChinhTangGiam = 4,
        HoaDonDieuChinhDinhDanh = 5
    }
}
