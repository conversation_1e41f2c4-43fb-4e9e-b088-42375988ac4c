namespace VnisCore.Export.Application.Models.Invoice02s.FlecCell
{
    public class FlexCellInvoice02ExportModel : BaseInvoiceHeaderModel
    {

        public int RowNumber { get; set; }

        #region Thong tin chung
        public long? Stt { get; set; }
        public string InvoiceStatusDisplay { get; set; }
        public string UserName { get; set; }
        public string SignStatusDisplay { get; set; }
        public string InvoiceDateDisplay { get; set; }

        public string ToCurrency { get; set; }
        public string VerificationCode { get; set; }
        public string PaymentMethod { get; set; }
        #endregion

        public short? TemplateNoNullable { get; set; }

        #region Thong tin nguoi mua
        /// <summary>
        /// Họ tên người mua (nếu là khách lẻ không thuộc công ty nào thì đây là tên công ty)
        /// </summary>
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Đại diện pháp nhân người mua (trường hợp là công ty)
        /// </summary>
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        public string BuyerTaxCode { get; set; }
        /// <summary>
        /// Mã khach hang
        /// </summary>
        public string BuyerCode { get; set; }
        /// <summary>
        /// Dia chi khach hang
        /// </summary>
        public string BuyerAddressLine { get; set; }
        #endregion

        #region Thong tin tien thanh toan
        /// <summary>
        /// Tổng tiền chiết khấu trước thuế, là lượng điều chỉnh nếu là hóa đơn tăng/giảm
        /// </summary>
        public decimal? TotalDiscountAmount { get; set; }
        /// <summary>
        /// Tổng tiền chưa thuế, chưa chiết khấu
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }
        /// <summary>
        /// Thue suat
        /// </summary>
        public decimal? VatPercent { get; set; }
        /// <summary>
        /// Tien Thue suat
        /// </summary>
        public decimal? VatAmount { get; set; }
        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal? TotalVatAmount { get; set; }
        #endregion

        #region Thong tin chi tiet san pham
        public long InvoiceDetailId { get; set; }
        /// <summary>
        /// code bản ghi hàng hóa
        /// </summary>
        public string ProductCode { get; set; }
        /// <summary>
        /// Tên hàng hóa
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// Tên Đơn vị tính
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// Đơn giá hàng hóa
        /// </summary>
        public decimal? UnitPrice { get; set; }

        public int? RoundingUnit { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal? Quantity { get; set; }

        /// <summary>
        /// Tổng tiền trước thuế / trước chiết khấu
        /// </summary>
        public decimal? Amount { get; set; }

        /// <summary>
        /// Tổng tiền sau thuế
        /// </summary>
        public decimal? PaymentAmount { get; set; }

        /// <summary>
        /// Tiền chiết khấu
        /// </summary>
        public decimal? DiscountAmount { get; set; }
        /// <summary>
        /// Phần trăm chiết khấu
        /// </summary>
        public decimal? DiscountPercentBeforeTax { get; set; }

        /// <summary>
        /// Tỷ giá
        /// </summary>
        public decimal ExchangeRate { get; set; }
        #endregion
    }
}
