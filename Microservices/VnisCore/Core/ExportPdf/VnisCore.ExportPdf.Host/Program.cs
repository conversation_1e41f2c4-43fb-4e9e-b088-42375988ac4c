// using Core.Host.Shared;
using Core.VaultSharp;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using Serilog;
using System;

namespace VnisCore.ExportPdf.Host
{
    public class Program
    {
        public static int Main(string[] args)
        {
            // ConfigureLogging.Configure();

            try
            {
                Log.Information("Starting VnisCore.Category.Host.");
                CreateHostBuilder(args).Build().Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Shop.Service.Host terminated unexpectedly!");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        internal static IHostBuilder CreateHostBuilder(string[] args)
        {
            try
            {
                return VaultConfigure.CreateHostBuilder(args)
                                .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); })
                                .UseAutofac()
                                .UseSerilog();
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw;
            }

            #region
            //return Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder(args).ConfigureAppConfiguration((hostingContext, config) =>
            //    {
            //        config
            //            .SetBasePath(hostingContext.HostingEnvironment.ContentRootPath)
            //            .AddJsonFile($"appsettings.json", optional: true, reloadOnChange: true)
            //            .AddJsonFile($"appsettings.{hostingContext.HostingEnvironment.EnvironmentName}.json", true, true)
            //            .AddEnvironmentVariables();
            //    })
            //    .ConfigureWebHostDefaults(webBuilder => { webBuilder.UseStartup<Startup>(); })
            //    .UseAutofac()
            //    .UseSerilog();
            #endregion
        }
    }
}