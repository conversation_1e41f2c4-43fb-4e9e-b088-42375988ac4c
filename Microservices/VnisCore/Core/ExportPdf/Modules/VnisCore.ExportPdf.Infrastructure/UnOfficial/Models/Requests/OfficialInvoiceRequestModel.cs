using Core.Shared.Constants;
using Core.Shared.Dto;
using MediatR;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Models.Requests
{
    public class OfficialInvoiceRequestModel : IRequest<FileDto>
    {
        //public List<Guid> Codes { get; set; }

        [JsonIgnore]
        public Guid UserId { get; set; }

        [JsonIgnore]
        public string UserName { get; set; }

        [JsonIgnore]
        public string UserFullName { get; set; }

        public VnisType InvoiceType { get; set; }

        public List<long> InvoiceIds { get; set; }
    }
}
