using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Abstractions;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Interfaces;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Models;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Repositories;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Invoice03
{
    public class DownloadXmlInvoice03Service : DownloadXmlService<Invoice03HeaderEntity>, IDownloadXmlService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;

        public DownloadXmlInvoice03Service(
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory
            ) : base(localizer)
        {
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _localizer = localizer;
        }

        public override async Task<Invoice03HeaderEntity> GetInvoiceAsNoTrackingAsync(long invoiceId)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice03HeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdAsNoTrackingAsync(invoiceId);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public override async Task<Invoice03HeaderEntity> GetInvoices(long invocieId)
        {
            var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice03HeaderEntity>>();
            return await repoInvoiceHeader.GetByIdAsync(invocieId);
        }

        public override async Task<List<Invoice03HeaderEntity>> GetListInvoices(List<long> invoiceId)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice03HeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdsAsync(invoiceId);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public override async Task<ElasticSearchInvoiceXmlModel> ModelToEntityAsync(long invoiceHeaderId)
        {
            var repoInvoiceXml = _serviceProvider.GetService<IMediaRepository<Invoice03XmlEntity>>();
            var xml = await repoInvoiceXml.GetLastByIdAsync(invoiceHeaderId);
            if (xml == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportPdf.Xml.XmlNotFound"]);
            //var tenantId = _appFactory.CurrentTenant.Id;
            var invoiceXml = new ElasticSearchInvoiceXmlModel
            {
                Id = xml.Id,
                ContentType = xml.ContentType,
                FileName = xml.FileName,
                PhysicalFileName = xml.PhysicalFileName,
                InvoiceHeaderId = xml.InvoiceHeaderId,
                Length = xml.Length,
                Partition = xml.Partition,
                CreationTime = xml.CreationTime,
                TenantId = xml.TenantId
            };
            return invoiceXml;
        }

        public override async Task<ElasticSearchInvoiceXmlModel> GetXmlHasVerificationAsync(long invoiceId)
        {
            var repoTvanInfoInvoiceHasCode = _appFactory.Repository<TvanInfoInvoice03HasCodeEntity, long>();
            var tvanInfoInvoice03HasCode = await repoTvanInfoInvoiceHasCode.AsNoTracking().FirstOrDefaultAsync(x => x.InvoiceHeaderId == invoiceId && x.MessageTypeCode == MLTDiep._202.GetHashCode().ToString());

            if (tvanInfoInvoice03HasCode == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanInfoHasCodeNotFound"]);

            var repoTvanInvoiceHasCodeXml = _appFactory.Repository<TvanInvoice03HasCodeXmlEntity, long>();
            var tvanInvoice03HasCodeXml = await repoTvanInvoiceHasCodeXml.AsNoTracking().FirstOrDefaultAsync(x => x.Id == tvanInfoInvoice03HasCode.FileId);

            if (tvanInvoice03HasCodeXml == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanHasCodeXmlNotFound"]);

            var result = new ElasticSearchInvoiceXmlModel
            {
                Id = tvanInvoice03HasCodeXml.Id,
                ContentType = tvanInvoice03HasCodeXml.ContentType,
                FileName = tvanInvoice03HasCodeXml.FileName,
                PhysicalFileName = tvanInvoice03HasCodeXml.PhysicalFileName,
                InvoiceHeaderId = tvanInfoInvoice03HasCode.InvoiceHeaderId,
                Length = tvanInvoice03HasCodeXml.Length,
                Partition = tvanInvoice03HasCodeXml.Partition,
                TenantId = tvanInfoInvoice03HasCode.TenantId,
                CreationTime = tvanInvoice03HasCodeXml.CreationTime
            };

            return result;
        }

        public override async Task<List<Invoice03HeaderEntity>> GetInvoicesAsNoTrackingAsync(List<long> invoiceIds)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<Invoice03HeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdsAsNoTrackingAsync(invoiceIds);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }


        public override async Task<List<ElasticSearchInvoiceXmlModel>> GetXmlsHasVerificationAsync(List<long> invoiceIds)
        {
            var repoTvanInfoInvoiceHasCode = _appFactory.Repository<TvanInfoInvoice03HasCodeEntity, long>();
            var tvanInfoInvoice03HasCodes = await repoTvanInfoInvoiceHasCode.AsNoTracking()
                                                .Where(x => invoiceIds.Contains(x.InvoiceHeaderId)
                                                            && x.MessageTypeCode == MLTDiep._202.GetHashCode().ToString())
                                                .ToListAsync();

            //if (!tvanInfoInvoice03HasCodes.Any())
            //    throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanInfoHasCodeNotFound"]);

            var idFileXmls = tvanInfoInvoice03HasCodes.GroupBy(x => x.FileId).ToDictionary(x => x.Key, x => x.FirstOrDefault().InvoiceHeaderId);
            var repoTvanInvoiceHasCodeXml = _appFactory.Repository<TvanInvoice03HasCodeXmlEntity, long>();
            var tvanInvoiceHasCodeXmlIds = idFileXmls.Keys.ToList();
            var tvanInvoice03HasCodeXmls = await repoTvanInvoiceHasCodeXml.AsNoTracking().Where(x => tvanInvoiceHasCodeXmlIds.Contains(x.Id)).ToListAsync();

            //if (!tvanInvoice03HasCodeXmls.Any())
            //    throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanHasCodeXmlNotFound"]);

            var result = tvanInvoice03HasCodeXmls.Select(x => new ElasticSearchInvoiceXmlModel
            {
                Id = x.Id,
                ContentType = x.ContentType,
                FileName = x.FileName,
                PhysicalFileName = x.PhysicalFileName,
                InvoiceHeaderId = idFileXmls[x.Id],
                Length = x.Length,
                Partition = x.Partition,
                TenantId = x.TenantId,
                CreationTime = x.CreationTime
            }).ToList();

            return result;
        }

    }
}
