using Core;
using Core.Domain.Repositories;
using Core.Shared.Factory;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Interfaces;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Models;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Invoice02
{
    public class DownloadInvoiceErrorInvoice02Service : IDownloadInvoiceErrorService
    {
        private readonly IRepository<Invoice02ErrorEntity, long> _repoError;
        private readonly IAppFactory _appFactory;
        private readonly IPdfInvoiceErrorService _pdfInvoiceErrorService;
        private readonly ILogger<DownloadInvoiceErrorInvoice02Service> _logger;
        public DownloadInvoiceErrorInvoice02Service(IRepository<Invoice02ErrorEntity, long> repoError,
            IAppFactory appFactory,
            IPdfInvoiceErrorService pdfInvoiceErrorService,
            ILogger<DownloadInvoiceErrorInvoice02Service> logger
            )
        {
            _repoError = repoError;
            _appFactory = appFactory;
            _pdfInvoiceErrorService = pdfInvoiceErrorService;
            _logger = logger;
        }
        public async Task<InvoiceErrorResponseModel> PrintInvoiceError(List<long> ids)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            if (!tenantId.HasValue)
            {
                throw new UserFriendlyException("Không tìm thấy chi nhánh");
            }
            //var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId.Value);
            var invoiceErrors = _repoError.Where(x => x.TenantId == tenantId.Value && ids.Contains(x.GroupCode)).ToList();
            var groupInvoiceErrors = invoiceErrors.GroupBy(x => x.GroupCode);

            if (!invoiceErrors.Any())
            {
                throw new UserFriendlyException("Không tìm thấy hóa đơn");
            }

            List<PdfInvoiceErrorModel> list = new List<PdfInvoiceErrorModel>();
            List<InvoiceErrorInfo> invoiceInfos = new List<InvoiceErrorInfo>();
            InvoiceTemplateEntity template = new InvoiceTemplateEntity();
            var viewPath = _appFactory.Configuration["FileDownload:InvoiceErrorFolder"];

            var datas = new List<byte[]>();


            foreach (var group in groupInvoiceErrors)
            {
                var key = group.Key;
                var invoices = group.ToList();
                var invoiceError = invoices.FirstOrDefault();
                var invoiceInfo = new InvoiceErrorInfo
                {
                    Id = invoiceError.Id,
                    TemplateNo = invoiceError.TemplateNo,
                    SerialNo = invoiceError.SerialNo,
                    SellerTaxCode = _appFactory.CurrentTenant.TaxCode
                };
                invoiceInfos.Add(invoiceInfo);

                var pdf = EntityToModelAsync(invoices);

                _logger.LogDebug($"Converted invoice error {invoiceError.TemplateNo}-{invoiceError.SerialNo}-{(string.IsNullOrEmpty(invoiceError.InvoiceNo) ? invoiceError.Id.ToString() : invoiceError.InvoiceNo)} to PdfInvoiceModel");

                _logger.LogDebug($"Start generate pdf invoice {invoiceError.TemplateNo}-{invoiceError.SerialNo}-{(string.IsNullOrEmpty(invoiceError.InvoiceNo) ? invoiceError.Id.ToString() : invoiceError.InvoiceNo)}");
                var bytes = await _pdfInvoiceErrorService.GeneratePdfCoreAsync(pdf, template, viewPath);
                _logger.LogDebug($"End generate pdf invoice {invoiceError.TemplateNo}-{invoiceError.SerialNo}-{(string.IsNullOrEmpty(invoiceError.InvoiceNo) ? invoiceError.Id.ToString() : invoiceError.InvoiceNo)}");

                _logger.LogDebug($"Start save InvoiceUnOfficial {invoiceError.TemplateNo}-{invoiceError.SerialNo}-{(string.IsNullOrEmpty(invoiceError.InvoiceNo) ? invoiceError.Id.ToString() : invoiceError.InvoiceNo)}");

                _logger.LogDebug($"End save InvoiceUnOfficial {invoiceError.TemplateNo}-{invoiceError.SerialNo}-{(string.IsNullOrEmpty(invoiceError.InvoiceNo) ? invoiceError.Id.ToString() : invoiceError.InvoiceNo)}");
                datas.Add(bytes);
            }

            var response = new InvoiceErrorResponseModel();
            response.Invoices = invoiceInfos;
            response.Bytes = datas;

            return response;
        }
        private PdfInvoiceErrorModel EntityToModelAsync(Invoice02ErrorEntity entity)
        {
            PdfInvoiceErrorModel pdf = new PdfInvoiceErrorModel();

            pdf.AnnouncementDate = entity.AnnouncementDate;
            pdf.GroupCode = entity.GroupCode.ToString();
            pdf.PlaceName = entity.PlaceName;
            pdf.SellerFullName = _appFactory.CurrentTenant.FullNameVi;
            pdf.SellerTaxCode = _appFactory.CurrentTenant.TaxCode;
            pdf.SignStatus = entity.SignStatus;
            pdf.SellerSignedTime = entity.SellerSignedTime;
            pdf.SellerFullNameSigned = entity.SellerFullNameSigned;
            pdf.SellerSignedId = entity.SellerSignedId;
            pdf.TvanStatus = entity.TvanStatus;
            pdf.CodeTaxDepartment = entity.CodeTaxDepartment;
            pdf.TaxDepartment = entity.TaxDepartment;
            pdf.BudgetUnitCode = entity.BudgetUnitCode;

            PdfInvoiceErrorDetailModel detail = new PdfInvoiceErrorDetailModel();
            detail.Index = entity.Index;
            detail.VerificationCode = entity.VerificationCode;
            detail.InvoiceHeaderId = entity.InvoiceHeaderId;
            detail.TemplateNo = entity.TemplateNo;
            detail.SerialNo = entity.SerialNo;
            detail.InvoiceNo = entity.InvoiceNo;
            detail.InvoiceDate = entity.InvoiceDate;
            detail.InvoiceType = entity.InvoiceType;
            detail.Action = entity.Action;
            detail.Reason = entity.Reason;

            List<PdfInvoiceErrorDetailModel> details = new List<PdfInvoiceErrorDetailModel>();
            details.Add(detail);
            pdf.Details = details;

            return pdf;
        }
        private PdfInvoiceErrorModel EntityToModelAsync(List<Invoice02ErrorEntity> entities)
        {
            var entity = entities.FirstOrDefault();
            PdfInvoiceErrorModel pdf = new PdfInvoiceErrorModel();

            pdf.AnnouncementDate = entity.AnnouncementDate;
            pdf.GroupCode = entity.GroupCode.ToString();
            pdf.PlaceName = entity.PlaceName;
            pdf.SellerFullName = _appFactory.CurrentTenant.FullNameVi;
            pdf.SellerTaxCode = _appFactory.CurrentTenant.TaxCode;
            pdf.SignStatus = entity.SignStatus;
            pdf.SellerSignedTime = entity.SellerSignedTime;
            pdf.SellerFullNameSigned = entity.SellerFullNameSigned;
            pdf.SellerSignedId = entity.SellerSignedId;
            pdf.TvanStatus = entity.TvanStatus;
            pdf.CodeTaxDepartment = entity.CodeTaxDepartment;
            pdf.TaxDepartment = entity.TaxDepartment;
            pdf.BudgetUnitCode = entity.BudgetUnitCode;

            List<PdfInvoiceErrorDetailModel> details = new List<PdfInvoiceErrorDetailModel>();

            foreach (var item in entities)
            {
                PdfInvoiceErrorDetailModel detail = new PdfInvoiceErrorDetailModel();
                detail.Index = item.Index;
                detail.VerificationCode = item.VerificationCode;
                detail.InvoiceHeaderId = item.InvoiceHeaderId;
                detail.TemplateNo = item.TemplateNo;
                detail.SerialNo = item.SerialNo;
                detail.InvoiceNo = item.InvoiceNo;
                detail.InvoiceDate = item.InvoiceDate;
                detail.InvoiceType = item.InvoiceType;
                detail.Action = item.Action;
                detail.Reason = item.Reason;

                details.Add(detail);
            }

            pdf.Details = details.OrderBy(d => d.Index).ToList();

            return pdf;
        }
    }
}
