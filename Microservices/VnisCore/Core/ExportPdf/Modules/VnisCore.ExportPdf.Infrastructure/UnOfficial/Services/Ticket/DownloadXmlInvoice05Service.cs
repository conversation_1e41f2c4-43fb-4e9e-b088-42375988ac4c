using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Tvan.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Abstractions;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Interfaces;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Models;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Repositories;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Services.Ticket
{
    public class DownloadXmlInvoice05Service : DownloadXmlService<TicketHeaderEntity>, IDownloadXmlService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;

        public DownloadXmlInvoice05Service(
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory
            ) : base(localizer)
        {
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _localizer = localizer;
        }

        public override async Task<TicketHeaderEntity> GetInvoices(long invoiceId)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<TicketHeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdAsync(invoiceId);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public override async Task<List<TicketHeaderEntity>> GetListInvoices(List<long> invoiceId)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<TicketHeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdsAsync(invoiceId);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public override async Task<TicketHeaderEntity> GetInvoiceAsNoTrackingAsync(long invoiceId)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<TicketHeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdAsNoTrackingAsync(invoiceId);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }


        public override async Task<ElasticSearchInvoiceXmlModel> ModelToEntityAsync(long invoiceHeaderId)
        {
            var repoInvoiceXml = _serviceProvider.GetService<IMediaRepository<TicketXmlEntity>>();
            var xml = await repoInvoiceXml.GetLastByIdAsync(invoiceHeaderId);
            if (xml == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportPdf.Xml.XmlNotFound"]);
            //var tenantId = _appFactory.CurrentTenant.Id;
            var invoiceXml = new ElasticSearchInvoiceXmlModel
            {
                Id = xml.Id,
                ContentType = xml.ContentType,
                FileName = xml.FileName,
                PhysicalFileName = xml.PhysicalFileName,
                InvoiceHeaderId = xml.InvoiceHeaderId,
                Length = xml.Length,
                Partition = xml.Partition,
                CreationTime = xml.CreationTime,
                TenantId = xml.TenantId
            };
            return invoiceXml;
        }


        public override async Task<ElasticSearchInvoiceXmlModel> GetXmlHasVerificationAsync(long invoiceId)
        {
            var repoTvanInfoInvoiceHasCode = _appFactory.Repository<TvanInfoTicketHasCodeEntity, long>();
            var tvanInfoTicketHasCode = await repoTvanInfoInvoiceHasCode.AsNoTracking().FirstOrDefaultAsync(x => x.InvoiceHeaderId == invoiceId && x.MessageTypeCode == MLTDiep._202.GetHashCode().ToString());

            if (tvanInfoTicketHasCode == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanInfoHasCodeNotFound"]);

            var repoTvanInvoiceHasCodeXml = _appFactory.Repository<TvanTicketHasCodeXmlEntity, long>();
            var tvanTicketHasCodeXml = await repoTvanInvoiceHasCodeXml.AsNoTracking().FirstOrDefaultAsync(x => x.Id == tvanInfoTicketHasCode.FileId);

            if (tvanTicketHasCodeXml == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanHasCodeXmlNotFound"]);

            var result = new ElasticSearchInvoiceXmlModel
            {
                Id = tvanTicketHasCodeXml.Id,
                ContentType = tvanTicketHasCodeXml.ContentType,
                FileName = tvanTicketHasCodeXml.FileName,
                PhysicalFileName = tvanTicketHasCodeXml.PhysicalFileName,
                InvoiceHeaderId = tvanInfoTicketHasCode.InvoiceHeaderId,
                Length = tvanTicketHasCodeXml.Length,
                Partition = tvanTicketHasCodeXml.Partition,
                TenantId = tvanInfoTicketHasCode.TenantId,
                CreationTime = tvanTicketHasCodeXml.CreationTime
            };

            return result;
        }


        public override async Task<List<TicketHeaderEntity>> GetInvoicesAsNoTrackingAsync(List<long> invoiceIds)
        {
            try
            {
                var repoInvoiceHeader = _serviceProvider.GetService<IInvoiceHeaderRepository<TicketHeaderEntity>>();
                var result = await repoInvoiceHeader.GetByIdsAsNoTrackingAsync(invoiceIds);
                return result;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }


        public override async Task<List<ElasticSearchInvoiceXmlModel>> GetXmlsHasVerificationAsync(List<long> invoiceIds)
        {
            var repoTvanInfoInvoiceHasCode = _appFactory.Repository<TvanInfoTicketHasCodeEntity, long>();
            var tvanInfoTicketHasCodes = await repoTvanInfoInvoiceHasCode.AsNoTracking()
                                                .Where(x => invoiceIds.Contains(x.InvoiceHeaderId)
                                                            && x.MessageTypeCode == MLTDiep._202.GetHashCode().ToString())
                                                .ToListAsync();

            //if (!tvanInfoTicketHasCodes.Any())
            //    throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanInfoHasCodeNotFound"]);

            var idFileXmls = tvanInfoTicketHasCodes.GroupBy(x => x.FileId).ToDictionary(x => x.Key, x => x.FirstOrDefault().InvoiceHeaderId);
            var repoTvanInvoiceHasCodeXml = _appFactory.Repository<TvanTicketHasCodeXmlEntity, long>();
            var tvanInvoiceHasCodeXmlIds = idFileXmls.Keys.ToList();
            var tvanTicketHasCodeXmls = await repoTvanInvoiceHasCodeXml.AsNoTracking().Where(x => tvanInvoiceHasCodeXmlIds.Contains(x.Id)).ToListAsync();

            //if (!tvanTicketHasCodeXmls.Any())
            //    throw new UserFriendlyException(_localizer["Vnis.BE.Export.Xml.TvanHasCodeXmlNotFound"]);

            var result = tvanTicketHasCodeXmls.Select(x => new ElasticSearchInvoiceXmlModel
            {
                Id = x.Id,
                ContentType = x.ContentType,
                FileName = x.FileName,
                PhysicalFileName = x.PhysicalFileName,
                InvoiceHeaderId = idFileXmls[x.Id],
                Length = x.Length,
                Partition = x.Partition,
                TenantId = x.TenantId,
                CreationTime = x.CreationTime
            }).ToList();

            return result;
        }
    }
}
