using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Models;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Interfaces
{
    public interface IDownloadUnOfficialService
    {
        /// <summary>
        /// <PERSON><PERSON>n thể hiện
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<UnOfficialResponseModel> UnOfficial(List<long> ids, string invoiceTypeName);
    }
}
