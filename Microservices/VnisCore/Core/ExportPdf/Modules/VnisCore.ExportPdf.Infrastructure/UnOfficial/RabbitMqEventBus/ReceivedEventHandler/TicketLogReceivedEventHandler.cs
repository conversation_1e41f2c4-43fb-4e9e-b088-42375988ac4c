//using Core.DependencyInjection;
//using Core.EventBus.Distributed;
//using Core.Shared.MessageEventsData.SyncElasticCustomer;
//using System.Threading.Tasks;

//namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.RabbitMqEventBus.ReceivedEventHandler
//{
//    public class TicketLogReceivedEventHandler : IDistributedEventHandler<CustomerEventSendData>, ITransientDependency
//    {
//        public TicketLogReceivedEventHandler()
//        {
//        }

//        public async Task HandleEventAsync(CustomerEventSendData eventData)
//        {
//            var s = eventData.ErrorMessages;
//            // logic get result

//            // returl signalr
//        }
//    }
//}
