using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Models;
using Core.Shared.Services;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.ExportPdf.Infrastructure.UnOfficial.Models;
using VnisCore.Invoice01.Infrastructure.IRepository;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Abstractions
{
    public abstract class DownloadUnOfficialService<THeader, THeader<PERSON>ield, TDetailField, TInvoiceDocument>
        where THeader : BaseInvoiceHeaderModel
        where THeaderField : BaseInvoiceHeaderField
        where TDetailField : BaseInvoiceDetailField
        where TInvoiceDocument : BaseInvoiceDocument
    {
        private readonly IPdfService _pdfService;
        private readonly ISettingService _settingService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IInvoicePrintNoteRepository _invoicePrintNoteRepository;

        public DownloadUnOfficialService(IPdfService pdfService,
             ISettingService settingService,
             IStringLocalizer<CoreLocalizationResource> localizer,
             IInvoicePrintNoteRepository invoicePrintNoteRepository)
        {
            _pdfService = pdfService;
            _localizer = localizer;
            _settingService = settingService;
            _invoicePrintNoteRepository = invoicePrintNoteRepository;
        }

        public async Task<UnOfficialResponseModel> UnOfficial(List<long> ids, string invoiceTypeName)
        {
            Log.Debug($"Start get invoice by codes: {string.Join(',', ids)}");
            var invoices = await GetInvoicesAsync(ids);
            Log.Debug($"End get invoice by codes: {invoices?.Count}");

            if (!invoices.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportPdf.UnOfficial.InvoiceNotFound"]);

            var invoiceInfos = invoices.Select(x => new InvoiceInfo
            {
                Id = x.Id,
                TemplateNo = x.TemplateNo,
                SerialNo = x.SerialNo,
                InvoiceNo = x.InvoiceNo,
                SellerTaxCode = x.SellerTaxCode
            }).ToList();

            var settingPaymentMethod = await GetSettingPaymentMethodAsync(invoices.First().TenantId);
            //var headerFields = await GetHeaderFields(invoices.First().TenantId);
            //var detailFields = await GetDetailFields(invoices.First().TenantId);
            var invoiceDocuments = await GetInvoiceDocumentAsync(invoices);
            var datas = new List<byte[]>();
            foreach (var invoice in invoices)
            {
                //await UpdateIdUnOfficalAsync(invoice, null); //update là chưa in
                var pdf = await EntityToModelAsync(invoice, settingPaymentMethod, invoiceTypeName);

                #region Thêm Note ghi chú cho hóa đơn thay thế, điều chỉnh
                if (invoice.InvoiceStatus == InvoiceStatus.ThayThe 
                    || invoice.InvoiceStatus == InvoiceStatus.DieuChinh
                    || invoice.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh
                    || invoice.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam)
                {
                    var invociePrintNote = await _invoicePrintNoteRepository.GetNoteByInvoiceHeaderId(invoice.Id, pdf.InvoiceType, invoices.First().TenantId);

                    if (invociePrintNote != null)
                    {
                        if (invociePrintNote.IsShowNote)
                        {
                            pdf.PrintNote = invociePrintNote.Note;
                            pdf.IsShowNote = invociePrintNote.IsShowNote;
                        }
                    }
                }
                #endregion

                //Chuyển về null vì sau khi in chuyển đổi các giá trị này sẽ != null
                pdf.PrintedAt = null;
                pdf.PrintedBy = null;
                pdf.FullNamePrinter = null;

                Log.Debug($"Start generate pdf invoice {invoice.TemplateNo}-{invoice.SerialNo}-{(string.IsNullOrEmpty(invoice.InvoiceNo) ? invoice.Id.ToString() : invoice.InvoiceNo)}");
                var bytes = await _pdfService.GenerateAsync(new List<PdfInvoiceModel> { pdf }, invoiceDocuments);
                Log.Debug($"End generate pdf invoice {invoice.TemplateNo}-{invoice.SerialNo}-{(string.IsNullOrEmpty(invoice.InvoiceNo) ? invoice.Id.ToString() : invoice.InvoiceNo)}");

                Log.Debug($"Start save InvoiceUnOfficial {invoice.TemplateNo}-{invoice.SerialNo}-{(string.IsNullOrEmpty(invoice.InvoiceNo) ? invoice.Id.ToString() : invoice.InvoiceNo)}");
                //await InsertInvoiceUnOfficia(pdf, bytes);
                Log.Debug($"End save InvoiceUnOfficial {invoice.TemplateNo}-{invoice.SerialNo}-{(string.IsNullOrEmpty(invoice.InvoiceNo) ? invoice.Id.ToString() : invoice.InvoiceNo)}");
                datas.Add(bytes);
            }
            return new UnOfficialResponseModel
            {
                Invoices = invoiceInfos,
                Bytes = datas
            };

        }

        /// <summary>
        /// lấy biên bản theo id file
        /// </summary>
        /// <param name="idFileDocuments"></param>
        /// <returns></returns>
        public abstract Task<List<TInvoiceDocument>> GetInvoiceDocumentAsync(List<THeader> invoices);

        //public abstract Task InsertInvoiceUnOfficia(PdfInvoiceModel pdf, byte[] bytes);


        public abstract Task<List<THeader>> GetInvoicesAsync(List<long> ids);

        public abstract Task<PdfInvoiceModel> EntityToModelAsync(THeader invoice, TenantSetting settingPaymentMethod, string invoiceTypeName);

        //public abstract Task UpdateIdUnOfficalAsync(THeader invoice, Guid? idUnOffical);


        /// <summary>
        /// lấy setting paymentmethod
        /// </summary>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        private async Task<TenantSetting> GetSettingPaymentMethodAsync(Guid tenantId)
        {
            var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());

            if (setting == null || string.IsNullOrEmpty(setting.Value))
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportPdf.UnOfficial.PaymentMethodNotFound"]);

            return setting;
        }
    }
}
