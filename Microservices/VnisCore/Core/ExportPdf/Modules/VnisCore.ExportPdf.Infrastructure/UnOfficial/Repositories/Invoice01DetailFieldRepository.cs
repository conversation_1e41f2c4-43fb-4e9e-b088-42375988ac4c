using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using Core.Domain.Repositories;

namespace VnisCore.ExportPdf.Infrastructure.UnOfficial.Repositories
{

    public interface IInvoice01DetailFieldRepository
    {
        Task<List<string>> GetFieldNameByTenantIdAsync(Guid tenantId);
        Task<List<Invoice01DetailFieldEntity>> QueryByTenantIdAsync(Guid tenantId);
    }

    public class Invoice01DetailFieldRepository : IInvoice01DetailFieldRepository
    {
        private readonly IRepository<Invoice01DetailFieldEntity, long> _repoInvoice01DetailField;

        public Invoice01DetailFieldRepository(IRepository<Invoice01DetailFieldEntity, long> repoInvoice01DetailField)
        {
            _repoInvoice01DetailField = repoInvoice01DetailField;
        }

        public async Task<List<string>> GetFieldNameByTenantIdAsync(Guid tenantId)
        {
            return await _repoInvoice01DetailField.Where(x => x.TenantId == tenantId && x.DeleterId == null)
                                                .Select(x => x.FieldName)
                                                .AsQueryable()
                                                .AsNoTracking()
                                                .ToListAsync();
        }

        public async Task<List<Invoice01DetailFieldEntity>> QueryByTenantIdAsync(Guid tenantId)
        {
            return await _repoInvoice01DetailField.Where(x => x.TenantId == tenantId && x.DeleterId == null)
                                                .AsQueryable()
                                                .AsNoTracking()
                                                .ToListAsync();
        }
    }
}
