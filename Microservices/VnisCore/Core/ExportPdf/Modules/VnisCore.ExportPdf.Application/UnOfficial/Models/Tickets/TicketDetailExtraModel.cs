using System;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;

namespace VnisCore.ExportPdf.Application.UnOfficial.Models.Tickets
{
    public class TicketDetailExtraModel
    {
        public long Id { get; set; }
        public string FieldValue { get; set; }
        public long InvoiceDetailFieldId { get; set; }
        public long InvoiceDetailId { get; set; }

        //public static explicit operator TicketDetailExtraModel(TicketDetailExtraEntity entity)
        //{
        //    if (entity == null)
        //        return null;

        //    return new TicketDetailExtraModel
        //    {
        //        FieldValue = entity.FieldValue,
        //        Id = entity.Id,
        //        InvoiceDetailId = entity.InvoiceDetailId,
        //        InvoiceDetailFieldId = entity.InvoiceDetailFieldId,
        //    };
        //}
    }
}
