using Core.Shared.Constants;
using Core.Shared.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.ExportPdf.Application.UnOfficial.Models.Invoice01s
{
    public class Invoice01Model : BaseInvoiceHeaderModel
    {
        /// <summary>
        /// Id bản ghi hóa đơn gốc nếu là hóa đơn điều chỉnh, thay thế
        /// </summary>
        public long? InvoiceReferenceId { get; set; }

        /// <summary>
        /// Lưu lại thông tin hóa đơn gốc để hiển thị, không cần join bảng (Id|Mẫu số|Ký hiệu|Số hóa đơn|Ngày hóa đơn)
        /// </summary>
        public string TransactionData { get; set; }

        #region Thông tin người bán
        /// <summary>
        /// Id bản ghi người bán (Organization)
        /// </summary>
        public Guid SellerId { get; set; }

        /// <summary>
        /// Mã đơn vị người bán
        /// </summary>
        public string SellerCode { get; set; }

        /// <summary>
        /// Người đại diện pháp nhân bên bán
        /// </summary>
        public string SellerLegalName { get; set; }

        /// <summary>
        /// Địa chỉ bên bán
        /// </summary>
        public string SellerAddressLine { get; set; }

        /// <summary>
        /// Mã quốc gia người bán (Việt Nam là VN)
        /// </summary>
        public string SellerCountryCode { get; set; }

        /// <summary>
        /// Tên phường/xã người bán
        /// </summary>
        public string SellerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người bán
        /// </summary>
        public string SellerCityName { get; set; }

        /// <summary>
        /// Số điện thoại người bán
        /// </summary>
        public string SellerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người bán
        /// </summary>
        public string SellerFaxNumber { get; set; }

        /// <summary>
        /// Email người bán
        /// </summary>
        public string SellerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người bán
        /// </summary>
        public string SellerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người bán
        /// </summary>
        public string SellerBankAccount { get; set; }

        /// <summary>
        /// Tên công ty bán
        /// </summary>
        public string SellerFullName { get; set; }

        /// <summary>
        /// Thời điểm hóa đơn được ký
        /// </summary>
        public DateTime? SellerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string SellerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? SellerSignedId { get; set; }
        #endregion

        #region Thông tin thanh toán
        /// <summary>
        /// Nguyên tệ
        /// </summary>
        public string FromCurrency { get; set; }

        /// <summary>
        /// Ngoại tệ
        /// </summary>
        public string ToCurrency { get; set; }

        public int RoundingCurrency { get; set; }

        /// <summary>
        ///  lưu giá trị chuyển đổi từ đơn vị cao sang đơn vị thấp. vd 1 đô la = 100 cents
        /// </summary>
        public int CurrencyConversion { get; set; }

        /// <summary>
        /// Tỷ giá (Bằng 1 nếu Nguyên tệ = Ngoại tệ)
        /// </summary>
        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Phương thức thanh toán
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Ngày thanh toán
        /// </summary>
        public DateTime? PaymentDate { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng việt)
        /// </summary>
        public string PaymentAmountWords { get; set; }

        /// <summary>
        /// Số tiền bằng chữ (Đọc tiếng anh)
        /// </summary>
        public string PaymentAmountWordsEn { get; set; }

        /// <summary>
        /// Tổng tiền chưa thuế, chưa chiết khấu
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; }
        #endregion

        #region Thông tin tạo/sửa/xóa/duyệt/in
        /// <summary>
        /// Họ tên người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameCreator { get; set; }

        /// <summary>
        /// UserName người insert bản ghi vào DB (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string UserNameCreator { get; set; }

        /// <summary>
        /// Ngày in chuyển đổi, khác null tức là đã in chuyển đổi
        /// </summary>
        public DateTime? PrintedTime { get; set; }

        /// <summary>
        /// Họ tên người in chuyển đổi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNamePrinter { get; set; }


        /// <summary>
        /// Tài khoản đăng nhập người in chuyển đôi (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? PrintedId { get; set; }

        /// <summary>
        /// Thời điểm duyệt hóa đơn
        /// </summary>
        public DateTime? ApprovedTime { get; set; }

        /// <summary>
        /// Họ tên người duyệt hóa đơn (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string FullNameApprover { get; set; }

        /// <summary>
        /// Tài khoản đăng nhập người duyệt hóa đơn (lấy theo tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? ApprovedId { get; set; }
        #endregion

        #region Thông tin biên bản
        /// <summary>
        /// Id bản ghi file biên bản
        /// </summary>
        public long? FileDocumentId { get; set; }

        /// <summary>
        /// Số biên bản
        /// </summary>
        public string DocumentNo { get; set; }

        /// <summary>
        /// Ngày biên bản
        /// </summary>
        public DateTime? DocumentDate { get; set; }

        /// <summary>
        /// Lý do biên bản
        /// </summary>
        public string DocumentReason { get; set; }
        #endregion

        #region Thông tin người mua
        /// <summary>
        /// code bản ghi người mua (Customer), có thể null nếu là loại hóa đơn 03XKNB
        /// </summary>
        public string BuyerCode { get; set; }

        /// <summary>
        /// Mã khách hàng
        /// </summary>
        public long BuyerId { get; set; }

        /// <summary>
        /// Họ tên người mua (nếu là khách lẻ không thuộc công ty nào thì đây là tên công ty)
        /// </summary>
        public string BuyerFullName { get; set; }

        /// <summary>
        /// Đại diện pháp nhân người mua (trường hợp là công ty)
        /// </summary>
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Mã số thuế người mua
        /// </summary>
        public string BuyerTaxCode { get; set; }

        /// <summary>
        /// Địa chỉ người mua
        /// </summary>
        public string BuyerAddressLine { get; set; }

        /// <summary>
        /// Tên Quận/Huyện người mua
        /// </summary>
        public string BuyerDistrictName { get; set; }

        /// <summary>
        /// Tên thành phố người mua
        /// </summary>
        public string BuyerCityName { get; set; }

        /// <summary>
        /// Mã quốc gia người mua (Việt Nam = VN)
        /// </summary>
        public string BuyerCountryCode { get; set; }

        /// <summary>
        /// Số điện thoại người mua
        /// </summary>
        public string BuyerPhoneNumber { get; set; }

        /// <summary>
        /// Số FAX người mua
        /// </summary>
        public string BuyerFaxNumber { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        /// <summary>
        /// Tên ngân hàng người mua
        /// </summary>
        public string BuyerBankName { get; set; }

        /// <summary>
        /// Tài khoản ngân hàng người mua
        /// </summary>
        public string BuyerBankAccount { get; set; }

        /// <summary>
        /// Thời điểm người mua ký
        /// </summary>
        public DateTime? BuyerSignedTime { get; set; }

        /// <summary>
        /// Họ tên người mua ký (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string BuyerFullNameSigned { get; set; }

        /// <summary>
        /// Tài khoản người mua ký hóa đơn (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public Guid? BuyerSignedId { get; set; }
        #endregion

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế, là lượng điều chỉnh nếu là hóa đơn tăng/giảm
        /// </summary>
        public decimal TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// phần trăm chiết khấu sau thuế
        /// </summary>
        public double TotalDiscountPercentAfterTax { get; set; }

        /// <summary>
        /// tổng tiền chiết khấu sau thuế
        /// </summary>
        public decimal TotalDiscountAmountAfterTax { get; set; }

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal TotalVatAmount { get; set; }

        /// <summary>
        /// Id bản ghi người mua bên ERP
        /// </summary>
        public string BuyerErpId { get; set; }

        /// <summary>
        /// Nội dung điều chỉnh
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// loại hóa đơn có liên quan
        /// 1-Hóa đơn điện tử theo Nghị định 123/2020/NĐ-CP
        /// 2-Hóa đơn điện tử có mã xác thực theo Quyết định số 1209/QĐ-BTC 
        /// 3-hóa đơn theo Nghị định số 51/2010/NĐ-CP 
        /// 4-Hóa đơn đặt in theo Nghị định 123/2020/NĐ-CP
        /// </summary>
        public short ReferenceInvoiceType { get; set; }

        public bool IsFinancialLeaseInvoice { get; set; }

        public Dictionary<string, string> ExtraProperties { get; set; }
        public List<Invoice01DetailModel> InvoiceDetails { get; set; }

        public List<Invoice01TaxBreakdownModel> InvoiceTaxBreakdowns { get; set; }

        public List<Invoice01HeaderExtraModel> InvoiceHeaderExtras { get; set; }

        public static implicit operator Invoice01Model(Invoice01HeaderEntity entity)
        {
            if (entity == null)
                return null;

            return new Invoice01Model
            {
                Id = entity.Id,
                TenantId = entity.TenantId,
                CreatedTime = entity.CreationTime,
                LastModificationTime = entity.LastModificationTime,
                LastModifierId = entity.LastModifierId,
                CancelTime = entity.CancelTime,
                CancelId = entity.CancelId,
                DeleteTime = entity.DeleteTime,
                DeleteId = entity.DeleterId,
                Source = entity.Source,
                BatchId = entity.BatchId,
                TransactionId = entity.TransactionId,
                TemplateId = entity.InvoiceTemplateId,
                TemplateNo = entity.TemplateNo,
                SerialNo = entity.SerialNo,
                InvoiceNo = entity.InvoiceNo,
                Number = entity.Number,
                Note = entity.Note,
                InvoiceDate = entity.InvoiceDate,
                InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(entity.InvoiceStatus),
                SignStatus = EnumExtension.ToEnum<SignStatus>(entity.SignStatus),
                RegistrationHeaderId = entity.RegistrationHeaderId,
                RegistrationDetailId = entity.RegistrationDetailId,
                ErpId = entity.ErpId,
                CreatorErp = entity.CreatorErp,

                SellerId = entity.SellerId,
                SellerLegalName = entity.SellerLegalName,
                SellerTaxCode = entity.SellerTaxCode,
                SellerAddressLine = entity.SellerAddressLine,
                SellerCountryCode = entity.SellerCountryCode,
                SellerDistrictName = entity.SellerDistrictName,
                SellerCityName = entity.SellerCityName,
                SellerPhoneNumber = entity.SellerPhoneNumber,
                SellerFaxNumber = entity.SellerFaxNumber,
                SellerEmail = entity.SellerEmail,
                SellerBankName = entity.SellerBankName,
                SellerBankAccount = entity.SellerBankAccount,
                SellerFullName = entity.SellerFullName,
                SellerSignedTime = entity.SellerSignedTime,
                SellerFullNameSigned = entity.SellerFullNameSigned,
                SellerSignedId = entity.SellerSignedId,

                FromCurrency = entity.FromCurrency,
                ToCurrency = entity.ToCurrency,

                RoundingCurrency = entity.RoundingCurrency,
                CurrencyConversion = entity.CurrencyConversion,
                ExchangeRate = entity.ExchangeRate,
                PaymentMethod = entity.PaymentMethod,
                PaymentDate = entity.PaymentDate,
                PaymentAmountWords = entity.PaymentAmountWords,
                PaymentAmountWordsEn = entity.PaymentAmountWordsEn,
                TotalAmount = entity.TotalAmount,
                TotalPaymentAmount = entity.TotalPaymentAmount,
                FullNameCreator = entity.FullNameCreator,
                PrintedTime = entity.PrintedTime,
                FullNamePrinter = entity.FullNamePrinter,
                PrintedId = entity.PrintedId,
                ApprovedTime = entity.ApprovedTime,
                FullNameApprover = entity.FullNameApprover,
                ApprovedId = entity.ApprovedId,

                BuyerCode = entity.BuyerCode,
                BuyerFullName = entity.BuyerFullName,
                BuyerLegalName = entity.BuyerLegalName,
                BuyerTaxCode = entity.BuyerTaxCode,
                BuyerAddressLine = entity.BuyerAddressLine,
                BuyerDistrictName = entity.BuyerDistrictName,
                BuyerCityName = entity.BuyerCityName,
                BuyerCountryCode = entity.BuyerCountryCode,
                BuyerPhoneNumber = entity.BuyerPhoneNumber,
                BuyerFaxNumber = entity.BuyerFaxNumber,
                BuyerEmail = entity.BuyerEmail,
                BuyerBankName = entity.BuyerBankName,
                BuyerBankAccount = entity.BuyerBankAccount,
                BuyerSignedTime = entity.BuyerSignedAt,
                BuyerFullNameSigned = entity.BuyerFullNameSigned,
                BuyerSignedId = entity.BuyerSignedId,
                TotalDiscountAmountBeforeTax = entity.TotalDiscountAmountBeforeTax,
                TotalDiscountPercentAfterTax = entity.TotalDiscountPercentAfterTax,
                TotalDiscountAmountAfterTax = entity.TotalDiscountAmountAfterTax,
                TotalVatAmount = entity.TotalVatAmount,
                BuyerErpId = entity.BuyerErpId,
                VerificationCode = entity.VerificationCode,
                ReferenceInvoiceType = entity.ReferenceInvoiceType,
                ExtraProperties = entity.ExtraProperties != null ? entity.ExtraProperties.ToDictionary(x => x.Key, x => (string)x.Value) : new Dictionary<string, string>(),
                StoreCode = entity.StoreCode,
                StoreName = entity.StoreName,
                BudgetUnitCode = entity.BudgetUnitCode,
                BuyerIDNumber = entity.BuyerIDNumber,
                BuyerPassportNumber = entity.BuyerPassportNumber,
                IsFinancialLeaseInvoice = entity.IsFinancialLeaseInvoice
            };
        }

    }

}
