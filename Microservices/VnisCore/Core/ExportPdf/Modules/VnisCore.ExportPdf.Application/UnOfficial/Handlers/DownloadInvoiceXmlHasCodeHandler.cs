using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Dto;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using MediatR;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.ExportPdf.Application.UnOfficial.Interfaces;
using VnisCore.ExportPdf.Application.UnOfficial.Models.Requests;

namespace VnisCore.MediaFile.Application.Handlers
{
    public class DownloadInvoiceXmlHasCodeHandler : IRequestHandler<DownloadInvoiceXmlHasCodeRequestModel, FileDto>
    {
        private readonly IFileService _fileService;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public DownloadInvoiceXmlHasCodeHandler(
            IFileService fileService,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizer)
        {
            _appFactory = appFactory;
            _fileService = fileService;
            _localizer = localizer;
        }

        public async Task<FileDto> Handle(DownloadInvoiceXmlHasCodeRequestModel request, CancellationToken cancellationToken)
        {
            var factory = _appFactory.GetServiceDependency<IDownloadXmlFactory>();
            var service = factory.GetService(request.InvoiceType.GetHashCode());
            var xmlModel = await service.ExportXmlHasVerificationCode(request.InvoiceId);

            var mediaType = GetMediaType(request.InvoiceType);
            var pathFileMinio = $"{mediaType}/{xmlModel.TenantId}/{xmlModel.CreationTime.Year}/{xmlModel.CreationTime.Month:00}/{xmlModel.CreationTime.Day:00}/{xmlModel.CreationTime.Hour:00}/{xmlModel.PhysicalFileName}";

            try
            {
                var bytes = await _fileService.DownloadAsync(pathFileMinio);

                //check xml chỉ lấy thông tin thẻ HDon thôi
                var xml = Encoding.UTF8.GetString(bytes);
                var hdonStartIndex = xml.IndexOf("<HDon>");
                var hdonEndIndex = xml.IndexOf("</HDon>");

                xml = xml.Substring(hdonStartIndex, hdonEndIndex + 7 - hdonStartIndex);
                bytes = Encoding.UTF8.GetBytes(xml);

                return new FileDto
                {
                    ContentType = ContentType.Xml,
                    FileBytes = bytes,
                    FileBase64 = Convert.ToBase64String(bytes, 0, bytes.Length),
                    FileName = xmlModel.FileName
                };
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException(ex.Message);
            }
        }

        private MediaFileType GetMediaType(VnisType invoiceType)
        {
            switch (invoiceType)
            {
                case VnisType._01GTKT:
                    return MediaFileType.Invoice01HasCodeTvanXml;
                case VnisType._02GTTT:
                    return MediaFileType.Invoice02HasCodeTvanXml;
                case VnisType._03XKNB:
                    return MediaFileType.Invoice03HasCodeTvanXml;
                case VnisType._04HGDL:
                    return MediaFileType.Invoice04HasCodeTvanXml;
                case VnisType._05TVDT:
                    return MediaFileType.TicketHasCodeTvanXml;
                default:
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.DownloadXmlInvoiceTypeNotFound", new string[] { $"{invoiceType.GetName()}" }]);
            }
        }
    }
}
