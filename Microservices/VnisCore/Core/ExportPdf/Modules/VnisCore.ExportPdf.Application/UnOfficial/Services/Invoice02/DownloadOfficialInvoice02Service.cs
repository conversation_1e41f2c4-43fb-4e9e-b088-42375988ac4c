using Core;
using Core.Domain.Repositories;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.MultiTenancy;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Models;
using Core.Shared.Services;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using QRCoder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.ExportPdf.Application.UnOfficial.Abstractions;
using VnisCore.ExportPdf.Application.UnOfficial.Interfaces;
using VnisCore.ExportPdf.Application.UnOfficial.Models.Invoice02s;
using VnisCore.ExportPdf.Application.UnOfficial.RabbitMqEventBus.MessageEventData;
using VnisCore.ExportPdf.Application.UnOfficial.Repositories;
using VnisCore.Invoice01.Infrastructure.IRepository;

namespace VnisCore.ExportPdf.Application.UnOfficial.Services.Invoice02
{
    public class DownloadOfficialInvoice02Service : DownloadOfficalService<Invoice02Model, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity, Invoice02DocumentEntity>, IDownloadOfficialService
    {
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IInvoiceHeaderRepository<Invoice02HeaderEntity> _repoInvoice02Header;
        private readonly IInvoiceDetailRepository<Invoice02DetailEntity> _repoInvoice02Detail;
        private readonly IInvoiceReferenceRepository<Invoice02ReferenceEntity> _repoInvoice02Reference;
        private readonly IRepository<Invoice02ReferenceOldDecreeEntity, long> _repoInvoice02ReferenceOldDecree;
        private readonly IInvoiceDocumentInfoRepository<Invoice02DocumentInfoEntity> _repoInvoiceDocumentInfo;
        private readonly IMediaRepository<Invoice02DocumentEntity> _repoInvoiceDocument;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public DownloadOfficialInvoice02Service(IPdfService pdfService,
            IAppFactory appFactory,
            ISettingService settingService,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IInvoiceHeaderRepository<Invoice02HeaderEntity> repoInvoice02Header,
            IInvoiceDetailRepository<Invoice02DetailEntity> repoInvoice02Detail,
            IInvoiceReferenceRepository<Invoice02ReferenceEntity> repoInvoice02Reference,
            IRepository<Invoice02ReferenceOldDecreeEntity, long> repoInvoice02ReferenceOldDecree,
            IInvoiceDocumentInfoRepository<Invoice02DocumentInfoEntity> repoInvoiceDocumentInfo,
            IMediaRepository<Invoice02DocumentEntity> repoInvoiceDocument,
            IDistributedEventBus distributedEventBus,
            IInvoicePrintNoteRepository invoicePrintNoteRepository) : base(pdfService, settingService, localizer, invoicePrintNoteRepository)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _repoInvoice02Header = repoInvoice02Header;
            _repoInvoice02Detail = repoInvoice02Detail;
            _repoInvoice02Reference = repoInvoice02Reference;
            _repoInvoice02ReferenceOldDecree = repoInvoice02ReferenceOldDecree;
            _repoInvoiceDocumentInfo = repoInvoiceDocumentInfo;
            _repoInvoiceDocument = repoInvoiceDocument;
            _distributedEventBus = distributedEventBus;
        }

        public override async Task<PdfInvoiceModel> EntityToModelAsync(Invoice02Model invoice, TenantSetting settingPaymentMethod)
        {
            var pdf = new PdfInvoiceModel();
            pdf.Id = invoice.Id;
            pdf.TemplateNo = invoice.TemplateNo.ToString();
            pdf.SerialNo = invoice.SerialNo;
            pdf.InvoiceNo = invoice.InvoiceNo;
            pdf.TemplateId = invoice.TemplateId;
            pdf.RegistrationHeaderId = invoice.RegistrationHeaderId;
            pdf.InvoiceReferenceId = invoice.InvoiceReferenceId;
            pdf.Note = invoice.Note;
            pdf.InvoiceDate = invoice.InvoiceDate;
            pdf.RegistrationDetailId = invoice.RegistrationDetailId;
            pdf.TransactionData = invoice.TransactionData;
            pdf.TransactionId = invoice.TransactionId;
            pdf.InvoiceStatus = invoice.InvoiceStatus;
            pdf.SignStatus = invoice.SignStatus;
            pdf.InvoiceType = (short)VnisType._02GTTT.GetHashCode();

            pdf.FileDocumentId = invoice.FileDocumentId;
            pdf.DocumentNo = invoice.DocumentNo;
            pdf.DocumentDate = invoice.DocumentDate;
            pdf.DocumentReason = invoice.DocumentReason;

            pdf.SellerId = invoice.SellerId;
            pdf.SellerCode = invoice.SellerCode;
            pdf.SellerLegalName = invoice.SellerLegalName;
            pdf.SellerTaxCode = invoice.SellerTaxCode;
            pdf.SellerAddressLine = invoice.SellerAddressLine;
            pdf.SellerCountryCode = invoice.SellerCountryCode;
            pdf.SellerDistrictName = invoice.SellerDistrictName;
            pdf.SellerCityName = invoice.SellerCityName;
            pdf.SellerPhoneNumber = invoice.SellerPhoneNumber;
            pdf.SellerFaxNumber = invoice.SellerFaxNumber;
            pdf.SellerEmail = invoice.SellerEmail;
            pdf.SellerBankName = invoice.SellerBankName;
            pdf.SellerBankAccount = invoice.SellerBankAccount;
            pdf.SellerFullName = invoice.SellerFullName;
            pdf.SellerSignedTime = invoice.SellerSignedTime;
            pdf.SellerFullNameSigned = invoice.SellerFullNameSigned;
            pdf.SellerSignedId = invoice.SellerSignedId;
            pdf.BuyerCode = invoice.BuyerCode;
            pdf.BuyerFullName = invoice.BuyerFullName;
            pdf.BuyerLegalName = invoice.BuyerLegalName;
            pdf.BuyerTaxCode = invoice.BuyerTaxCode;
            pdf.BuyerAddressLine = invoice.BuyerAddressLine;
            pdf.BuyerDistrictName = invoice.BuyerDistrictName;
            pdf.BuyerCityName = invoice.BuyerCityName;
            pdf.BuyerCountryCode = invoice.BuyerCountryCode;
            pdf.BuyerPhoneNumber = invoice.BuyerPhoneNumber;
            pdf.BuyerFaxNumber = invoice.BuyerFaxNumber;
            pdf.BuyerEmail = invoice.BuyerEmail;
            pdf.BuyerBankName = invoice.BuyerBankName;
            pdf.BuyerBankAccount = invoice.BuyerBankAccount;
            pdf.BuyerSignedTime = invoice.BuyerSignedTime;
            pdf.BuyerFullNameSigned = invoice.BuyerFullNameSigned;
            pdf.BuyerSignedId = invoice.BuyerSignedId;
            pdf.FromCurrency = invoice.FromCurrency;
            pdf.ToCurrency = invoice.ToCurrency;
            pdf.ExchangeRate = invoice.ExchangeRate;
            pdf.PaymentMethod = GetPaymentMethod(invoice, settingPaymentMethod);
            pdf.PaymentDate = invoice.PaymentDate;
            pdf.PaymentAmountWords = invoice.PaymentAmountWords;
            pdf.PaymentAmountWordsEn = invoice.PaymentAmountWordsEn;
            pdf.TotalAmount = invoice.TotalAmount;
            pdf.TotalDiscountAmountBeforeTax = invoice.TotalDiscountAmount;
            pdf.TotalPaymentAmount = invoice.TotalPaymentAmount;
            pdf.FullNameCreator = invoice.FullNameCreator;
            pdf.UserNameCreator = invoice.UserNameCreator;
            pdf.PrintedAt = invoice.PrintedTime;
            pdf.QrCode = GetQrCode(invoice);
            pdf.FullNamePrinter = invoice.FullNamePrinter;
            pdf.PrintedBy = invoice.PrintedId;
            pdf.ApprovedAt = invoice.ApprovedTime;
            pdf.FullNameApprover = invoice.FullNameApprover;
            pdf.ApprovedBy = invoice.ApprovedId;
            pdf.IdErp = invoice.ErpId;
            pdf.IdBuyerErp = invoice.BuyerErpId;
            pdf.CreatorErp = invoice.CreatorErp;
            pdf.CreatedTime = invoice.CreatedTime;
            pdf.CreatedId = invoice.CreatedId;
            pdf.TenantId = invoice.TenantId;
            pdf.LastModificationTime = invoice.LastModificationTime;
            pdf.LastModifierId = invoice.LastModifierId;
            pdf.DeleteTime = invoice.DeleteTime;
            pdf.DeleteId = invoice.DeleteId;
            pdf.DocumentReference = invoice.Content;
            pdf.VerificationCode = invoice.VerificationCode;
            pdf.ReferenceInvoiceType = invoice.ReferenceInvoiceType;
            pdf.Source = invoice.Source;

            pdf.StoreCode = invoice.StoreCode;
            pdf.StoreName = invoice.StoreName;
            pdf.BudgetUnitCode = invoice.BudgetUnitCode;
            pdf.BuyerIDNumber = invoice.BuyerIDNumber;
            pdf.BuyerPassportNumber = invoice.BuyerPassportNumber;

            //xem có phải hóa đơn điều chỉnh tăng giảm không, nếu có lấy hóa đơn gốc để điền các trường điều chỉnh tăng giảm
            Invoice02HeaderEntity rootInvoice = null;
            if ((pdf.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam || pdf.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh) && invoice.InvoiceReferenceId.HasValue)
            {
                rootInvoice = await _repoInvoice02Header.GetByIdAsync(invoice.InvoiceReferenceId.Value);
            }

            if (!string.IsNullOrEmpty(invoice.TransactionData))
            {
                var invoiceReference = new PdfInvoiceReferenceModel();
                var items = invoice.TransactionData.Split('|');
                if (items.Length == 5)
                {
                    invoiceReference.SellerTaxCode = items[0];
                    invoiceReference.TemplateNo = items[1];
                    invoiceReference.SerialNo = items[2];
                    invoiceReference.InvoiceNo = items[3];
                    invoiceReference.InvoiceDate = DateTime.Parse(items[4]);
                    invoiceReference.BuyerFullName = rootInvoice != null ? rootInvoice.BuyerFullName : string.Empty;
                    invoiceReference.BuyerLegalName = rootInvoice != null ? rootInvoice.BuyerLegalName : string.Empty;
                    invoiceReference.BuyerAddressLine = rootInvoice != null ? rootInvoice.BuyerAddressLine : string.Empty;
                    invoiceReference.BuyerTaxCode = rootInvoice != null ? rootInvoice.BuyerTaxCode : string.Empty;
                    invoiceReference.BuyerEmail = rootInvoice != null ? rootInvoice.BuyerEmail : string.Empty;
                    invoiceReference.BuyerBankAccount = rootInvoice != null ? rootInvoice.BuyerBankAccount : string.Empty;
                    invoiceReference.BuyerBankName = rootInvoice != null ? rootInvoice.BuyerBankName : string.Empty;

                }
                pdf.InvoiceReference = invoiceReference;
            }

            var headerExtras = new List<PdfInvoiceHeaderExtraModel>();
            if (invoice.ExtraProperties != null && invoice.ExtraProperties.Any())
            {
                var headerExtraProperties = new List<InvoiceHeaderExtraModel>();
                if (invoice.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(invoice.ExtraProperties["invoiceHeaderExtras"]))
                {
                    headerExtraProperties = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(invoice.ExtraProperties["invoiceHeaderExtras"]);
                }
                if (headerExtraProperties.Any())
                {
                    headerExtras = headerExtraProperties.Select(x => new PdfInvoiceHeaderExtraModel
                    {
                        FieldValue = x.FieldValue,
                        FieldName = x.FieldName,
                    }).ToList();
                }
            }
            pdf.InvoiceHeaderExtras = headerExtras;

            {
                var details = new List<PdfInvoiceDetailModel>();
                foreach (var item in invoice.InvoiceDetails)
                {
                    var detail = new PdfInvoiceDetailModel
                    {
                        Id = item.Id,
                        Index = item.Index,
                        ProductId = item.ProductId,
                        ProductCode = item.ProductCode,
                        ProductName = item.ProductName,
                        UnitId = item.UnitId == null ? 0 : item.UnitId.Value,
                        UnitName = item.UnitName,
                        UnitPrice = item.UnitPrice,
                        Quantity = item.Quantity,
                        Amount = item.Amount,
                        DiscountAmountBeforeTax = item.DiscountAmount,
                        DiscountPercentBeforeTax = item.DiscountPercent,
                        PaymentAmount = item.PaymentAmount,
                        Note = item.Note,
                        HideQuantity = item.HideQuantity,
                        HideUnit = item.HideUnit,
                        HideUnitPrice = item.HideUnitPrice,
                        ExtraProperties = item.ExtraProperties,
                        ProductType = item.ProductType
                    };

                    var detailExtras = new List<PdfInvoiceDetailExtraModel>();
                    if (detail.ExtraProperties != null && detail.ExtraProperties.Any())
                    {
                        var extraProperties = new List<InvoiceDetailExtraModel>();
                        if (item.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(item.ExtraProperties["invoiceDetailExtras"]))
                        {
                            extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(item.ExtraProperties["invoiceDetailExtras"]);
                        }

                        if (extraProperties.Any())
                        {
                            detailExtras = extraProperties.Select(x => new PdfInvoiceDetailExtraModel
                            {
                                FieldName = x.FieldName,
                                FieldValue = x.FieldValue,
                            }).ToList();
                        }
                    }
                    detail.InvoiceDetailExtras = detailExtras;

                    //kiểm tra hóa đơn có phải hóa đơn điều chỉnh tăng giảm không
                    if (rootInvoice != null && pdf.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam)
                    {
                        //lấy ra detail hóa đơn gốc tương ứng với detail hiện tại
                        var rootInvoiceDetails = await _repoInvoice02Detail.QueryByIdInvoiceHeaderAsync(rootInvoice.Id);

                        var rootDetail = rootInvoiceDetails.FirstOrDefault(x => x.Id == item.Id);
                        if (rootDetail != null)
                        {
                            detail.OriginAmount = rootDetail.Amount;
                            detail.OriginDiscountAmountBeforeTax = rootDetail.DiscountAmount;
                            detail.OriginDiscountPercentBeforeTax = rootDetail.DiscountPercent;
                            detail.OriginPaymentAmount = rootDetail.PaymentAmount;
                            detail.OriginQuantity = rootDetail.Quantity;
                            detail.OriginUnitPrice = rootDetail.UnitPrice;
                            detail.PaymentAmountAfterAdjustment = rootDetail.PaymentAmount + item.PaymentAmount;
                            detail.AmountAfterAdjustment = rootDetail.Amount + item.Amount;
                            detail.DiscountAmountBeforeTaxAfterAdj = rootDetail.DiscountAmount;
                            detail.DiscountPercentBeforeTaxAfterAdj = rootDetail.DiscountPercent;
                            detail.QuantityAfterAdjustment = rootDetail.Quantity + item.Quantity;
                            detail.UnitPriceAfterAdjustment = rootDetail.UnitPrice + item.UnitPrice;
                        }
                    }

                    details.Add(detail);
                }
                pdf.InvoiceDetails = details.OrderBy(x => x.Index).ToList();

            }

            return pdf;
        }

        public override async Task<List<Invoice02Model>> GetInvoicesAsync(List<long> ids)
        {
            //lấy hóa đơn bao gồm cả detail các các thông tin khác

            var invoiceHeaders = await _repoInvoice02Header.GetByIdsAsync(ids);

            var result = new List<Invoice02Model>();
            if (!invoiceHeaders.Any())
                return result;

            var invoices = invoiceHeaders.ToDictionary(x => x.Id, x => x);
            var invoiceDetails = await _repoInvoice02Detail.QueryByInvoiceHeaderIdsAsync(invoices.Keys.ToList());
            var invoiceDetailsByids = invoiceDetails.GroupBy(x => x.Id)
                                    .ToDictionary(x => x.Key, x => x.ToList());
            var codeInvoiceDetails = invoiceDetailsByids.Keys;

            //invoice reference
            var invoiceReferences = (await _repoInvoice02Reference.GetByInvoiceHeaderIdsAsync(ids))
                                        .Where(x => x.InvoiceHeaderId != x.InvoiceReferenceId)
                                        .GroupBy(x => x.InvoiceHeaderId)
                                        .ToDictionary(x => x.Key, x => x.ToList());

            var invoiceReferenceOldDecree = _repoInvoice02ReferenceOldDecree.Where(x => ids.Contains(x.InvoiceHeaderId))
                .ToList().GroupBy(x => x.InvoiceHeaderId).ToDictionary(x => x.Key, x => x.ToList());

            //invoice document
            var invoiceFiles = (await _repoInvoiceDocumentInfo.GetByInvoiceHeaderIdsAsync(invoiceHeaders.FirstOrDefault().TenantId, ids))
                                        .GroupBy(x => x.InvoiceHeaderId)
                                        .ToDictionary(x => x.Key, x => x.ToList());

            var invoiceDetailModels = invoiceDetails.Select(x => (Invoice02DetailModel)x).ToList();
            foreach (var invoiceDetail in invoiceDetailModels)
            {
                if (invoiceDetail.ExtraProperties != null && invoiceDetail.ExtraProperties.Any())
                {
                    var extraProperties = new List<InvoiceDetailExtraModel>();
                    if (invoiceDetail.ExtraProperties.ContainsKey("invoiceDetailExtras") && !string.IsNullOrEmpty(invoiceDetail.ExtraProperties["invoiceDetailExtras"]))
                    {
                        extraProperties = JsonConvert.DeserializeObject<List<InvoiceDetailExtraModel>>(invoiceDetail.ExtraProperties["invoiceDetailExtras"]);
                    }

                    if (extraProperties.Any())
                    {
                        invoiceDetail.InvoiceDetailExtras = extraProperties.Select(x => new Invoice02DetailExtraModel
                        {
                            FieldValue = x.FieldValue,
                            FieldName = x.FieldName,
                        }).ToList();
                    }
                }
            }

            result = invoiceHeaders.Select(x => (Invoice02Model)x).ToList();

            var resultInvoiceDetails = invoiceDetailModels.GroupBy(x => x.InvoiceHeaderId)
                                        .ToDictionary(x => x.Key, x => x.ToList());

            foreach (var invoice in result)
            {
                if (resultInvoiceDetails.ContainsKey(invoice.Id))
                    invoice.InvoiceDetails = resultInvoiceDetails[invoice.Id];

                if (invoice.ExtraProperties != null && invoice.ExtraProperties.Any())
                {
                    if (invoice.ExtraProperties.ContainsKey("invoiceHeaderExtras") && !string.IsNullOrEmpty(invoice.ExtraProperties["invoiceHeaderExtras"]))
                    {
                        var headerExtras = JsonConvert.DeserializeObject<List<InvoiceHeaderExtraModel>>(invoice.ExtraProperties["invoiceHeaderExtras"]);
                        if (headerExtras.Any())
                            invoice.InvoiceHeaderExtras = headerExtras.Select(x => new Invoice02HeaderExtraModel
                            {
                                FieldValue = x.FieldValue,
                                FieldName = x.FieldName
                            }).ToList();
                    }
                }

                if (invoiceReferences.ContainsKey(invoice.Id))
                {
                    var reference = invoiceReferences[invoice.Id].FirstOrDefault();
                    invoice.InvoiceReferenceId = reference.InvoiceReferenceId;
                    invoice.TransactionData = $"{invoice.SellerTaxCode}|{reference.TemplateNoReference}|{reference.SerialNoReference}|{reference.InvoiceNoReference}|{reference.InvoiceDateReference:yyyy-MM-dd}";
                    invoice.Content = reference.Note;
                }

                if (invoiceReferenceOldDecree.ContainsKey(invoice.Id))
                {
                    var reference = invoiceReferenceOldDecree[invoice.Id].FirstOrDefault();
                    invoice.InvoiceReferenceId = reference.InvoiceHeaderId;
                    invoice.TransactionData = $"{invoice.SellerTaxCode}|{reference.TemplateNoReference}|{reference.SerialNoReference}|{reference.InvoiceNoReference}|{reference.InvoiceDateReference:yyyy-MM-dd}";
                    invoice.Content = reference.Note;
                }

                if (invoiceFiles.ContainsKey(invoice.Id))
                {
                    var document = invoiceFiles[invoice.Id].FirstOrDefault();
                    //invoice.IdFileDocument = document.FileCode;
                    invoice.DocumentDate = document.DocumentDate;
                    invoice.DocumentNo = document.DocumentNo;
                    invoice.DocumentReason = document.DocumentReason;
                    invoice.FileDocumentId = document.FileId;
                }
            }

            return result;
        }

        public override async Task SaveOfficialAsync(List<long> ids, Guid userId, string userFullName, string userName)
        {
            var invoices = await _repoInvoice02Header.GetByIdsAsync(ids);

            foreach (var item in invoices)
            {
                item.PrintedTime = DateTime.Now;
                item.PrintedId = userId;
                item.FullNamePrinter = userFullName;

            }

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            //Insert Log
            foreach (var item in invoices)
            {
                await _distributedEventBus.PublishAsync(new Invoice02LogEventSendData(new Invoice02LogModel
                {
                    InvoiceHeaderId = item.Id,
                    TenantId = item.TenantId,
                    UserId = userId,
                    UserName = userName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(item.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.Official.GetHashCode(),
                    Partition = long.Parse(item.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));
            }
            //foreach (var item in invoices)
            //{
            //    _rabbitService.Publish(new RabbitResponseModel<InvoiceCommandResponseModel>
            //    {
            //        Data = new InvoiceCommandResponseModel
            //        {
            //            Code = item.Code,
            //            TemplateNo = item.TemplateNo,
            //            SerialNo = item.SerialNo,
            //            InvoiceNo = item.InvoiceNo,
            //            Method = HubMethod.Official,
            //            Type = VnisType._01GTKT,
            //            userId = userId,
            //            TenantCode = item.TenantCode,
            //            ActionLogInvoice = ActionLogInvoice.Official,
            //            UserName = userName,
            //            UserFullName = userFullName,
            //            ActionAt = DateTime.Now,
            //            ActionAtUtc = DateTime.UtcNow
            //        }
            //    }, RabbitKey.Exchanges.Events, string.Format(RabbitMqKey.Routings.OfficialNotification, "01"));
            //}
        }

        public override async Task<List<Invoice02DocumentEntity>> GetDocuments(List<Invoice02Model> invoices)
        {
            var idFileDocuments = invoices.Where(x => x.FileDocumentId.HasValue)
                                          .Select(x => x.FileDocumentId.Value).Distinct().ToList();

            return await _repoInvoiceDocument.QueryByIdsAsync(idFileDocuments);
        }

        private string GetQrCode(Invoice02Model invoice)
        {
            string qrCode = $"{invoice.SellerFullName} - {invoice.BuyerFullName} - {(string.IsNullOrEmpty(invoice.BuyerTaxCode) ? "" : $"{invoice.BuyerTaxCode} - ")}";

            if (invoice.TotalAmount < 0)
                qrCode += $"({Math.Round(invoice.TotalAmount, invoice.RoundingCurrency, MidpointRounding.AwayFromZero)})";
            else
                qrCode += $"{Math.Round(invoice.TotalAmount, invoice.RoundingCurrency, MidpointRounding.AwayFromZero)}";

            if (invoice.TotalPaymentAmount < 0)
                qrCode += $" - ({Math.Round(invoice.TotalPaymentAmount, invoice.RoundingCurrency, MidpointRounding.AwayFromZero)})";
            else
                qrCode += $" - {Math.Round(invoice.TotalPaymentAmount, invoice.RoundingCurrency, MidpointRounding.AwayFromZero)}";

            using var generator = new QRCodeGenerator();
            QRCodeData qrCodeData = generator.CreateQrCode(qrCode, QRCodeGenerator.ECCLevel.Q);
            using var bitmap = new BitmapByteQRCode();
            bitmap.SetQRCodeData(qrCodeData);
            var bytes = bitmap.GetGraphic(20);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// lấy paymentmethod trong setting ra
        /// </summary>
        /// <param name="invoice"></param>
        /// <param name="settingPaymentMethod"></param>
        /// <returns></returns>
        private string GetPaymentMethod(Invoice02Model invoice, TenantSetting settingPaymentMethod)
        {
            if (settingPaymentMethod == null || string.IsNullOrEmpty(settingPaymentMethod.Value))
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportPdf.Official.PaymentMethodNotFound"]);

            var values = settingPaymentMethod.Value.Split(';');
            if (!values.Contains(invoice.PaymentMethod))
                throw new UserFriendlyException(_localizer["Vnis.BE.ExportPdf.Official.PaymentMethodNotFound"] + $" {invoice.PaymentMethod}");

            return invoice.PaymentMethod;
        }
    }
}
