{
  "App": {
    "SelfUrl": "https://localhost:6010",
    "CorsOrigins": "http://localhost:6789",
    "RedirectAllowedUrls": "http://localhost:4200"
  },
  "AppSelfUrl": "https://localhost:6010/",
  "ApplicationInfo": {
    "AppId": "45e45b63-96a4-4bee-a05b-1a855b3e8c26"
  },
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_AUTH_STG;Password=INV_MASS_V5_AUTH_STG",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=INVOICE)))';User Id=INV_MASS_V5_CORE_STG;Password=INV_MASS_V5_CORE_STG",
    "MASSCoreMongoDbAuditLogging": "***********************************************************************************",
    "MASSCoreMongoDbEmail": "*********************************************************************"
  },
  "Service": {
    "Name": "VnisCore.RegistrationInvoice.Host",
    "Title": "VnisCore.RegistrationInvoice.Host",
    "BaseUrl": "registrationInvoice",
    "AuthApiName": "VnisCore.RegistrationInvoice.Host"
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "************,allowAdmin=true,password=vnpayredis@123,defaultDatabase=14"
  },

  "AuthServer": {
    "Authority": "https://invoice-mass-auth.vnpaytest.vn/",
    "RequireHttpsMetadata": "false",
    "ApiName": "einvoice",
    "SwaggerClientId": "einvoice_Swagger",
    "SwaggerClientSecret": "Vnis@12A"
  },
  "Identity": {
    "SecretKey": "OPQVVMMBpbeSpAz04s3BQt6nvi9PL3Vj"
  },
  "HtmlToPdfLimitCcu": 1,
  "Minio": {
    "Endpoint": "efin-minio.vnpaytest.vn",
    //"Endpoint": "************:9000",
    "AccessKey": "minioadmin",
    "SecretKey": "Minio@123",
    "Region": null,
    "SessionToken": null,
    "BucketName": "invoice-mass-v5",
    "TrustAllCerts": "true"
  },
  "FileDownload": {
    "FolderTemplate": "D:\\Resources\\Tool",
    "PathRegistrationTemplate": "D:\\Resources",
    "PdfToolPath": "D:\\Resources",
    "PathGDTResponseTemplate": "D:\\Resources",
    "WkHtmlToPdfExeName": "wkhtmltopdf.exe"
  },
  "FileMedia": {
    "Endpoint": "https://invoice-mass-gateway.vnpaytest.vn",
    "PathFileUpload": "D:\\File upload minio\\MAU CTKTTTNCN.cshtml"
  },
  "TvanInvoice": {
    "Endpoint": "http://**************:5000/",
    "ApiKey": "2e850b565b8c4bf39dde990972b71572",
    "Timeout": 5
  },
  "SignRegistrationApi": {
    "Endpoint": "https://localhost:6010/",
    "ApiKey": "2e850b565b8c4bf39dde990972b71572",
    "Timeout": 5
  },
  "SignatureId": {
    "Id": "data",
    "Uri": "#data"
  }
}