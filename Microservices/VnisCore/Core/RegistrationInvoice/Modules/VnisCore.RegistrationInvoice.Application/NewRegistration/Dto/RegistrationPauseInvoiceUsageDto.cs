using Core.Shared.Attributes;
using Core.Shared.Constants;
using System;
using System.ComponentModel.DataAnnotations;

namespace VnisCore.RegistrationInvoice.Application.NewRegistration.Dto
{
    /// <summary>
    /// Đề nghị tạm ngừng sử dụng hóa đơn điện tử
    /// </summary>
    public class RegistrationPauseInvoiceUsageDto
    {
        public long Id { get; set; }

        /// <summary>
        /// số thứ tự
        /// </summary>
        [Required(ErrorMessage = "Số thứ tự không được để trống")]
        public short Index { get; set; }

        /// <summary>
        /// Tên tổ chức (C<PERSON> quan chứng thực/cấp/công nhận chữ ký số, chữ ký điện tử)
        /// </summary>
        [Required(ErrorMessage = "Tên tổ chức (C<PERSON> quan chứng thực/cấp/công nhận chữ ký số, chữ ký điện tử) không được để trống")]
        [MaxLength(400, ErrorMessage = "Tên tổ chức (C<PERSON> quan chứng thực/cấp/công nhận chữ ký số, chữ ký điện tử) dài tối đa 400 ký tự")]
        public string SubjectName { get; set; }

        /// <summary>
        /// Mã số thuế
        /// </summary>
        [Required(ErrorMessage = "Mã số thuế không được để trống")]
        [MaxLength(14, ErrorMessage = "Mã số thuế có độ dài tối đa 14 ký tự")]
        [TaxCode(ErrorMessage = "Mã số thuế không đúng định dạng")]
        public string TaxCode { get; set; }

        /// <summary>
        /// từ ngày
        /// </summary>
        [Required(ErrorMessage = "Thời gian từ ngày không được để trống")]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// đến ngày
        /// </summary>
        [Required(ErrorMessage = "Thời gian đến ngày không được để trống")]
        [MoreThanDate("StartDate", ErrorMessage = "Đến ngày phải lớn hơn hoặc bằng từ ngày")]
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// số serial chứng thư số
        /// </summary>
        //[Required(ErrorMessage = "SerialNumber chứng thư số không được để trống")]
        [MaxLength(40, ErrorMessage = "SerialNumber chứng thư số có độ dài tối đa 40 ký tự")]
        public string SerialNumber { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        [MaxLength(255, ErrorMessage = "Ghi chú có độ dài tối đa 255 ký tự")]
        public string Note { get; set; }

        /// <summary>
        /// Phân biệt loại bảng trong đăng ký sử dụng
        /// Enum: NewRegistrationDetailExtensionType
        /// </summary>
        public short Type
        {
            get
            {
                return (short)NewRegistrationDetailExtensionType.PauseInvoiceUsage.GetHashCode();
            }
        }
    }
}
