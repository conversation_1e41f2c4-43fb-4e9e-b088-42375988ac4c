using Core;
using Core.Application;
using Core.AutoMapper;
using Core.Modularity;
using Core.Shared;
using Core.Shared.Factory;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Services;
using Core.Shared.Services;
using Core.Shared.Validations;
using Core.Tvan;
using Core.Tvan.Interfaces;
using Core.Tvan.Services;
using MediatR;
using MediatR.Pipeline;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using VnisCore.Core.Oracle.Application.Contracts;
using VnisCore.Core.Oracle.Domain;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Abstractions;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Caching;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Interfaces;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Services.Invoice01;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Services.Invoice02;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Services.Invoice03;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Services.Invoice04;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Services.Invoice05;
using VnisCore.RegistrationInvoice.Infrastructure;
using Core.BaseApplication.Shared;
using Core.Shared.Invoice;

namespace VnisCore.RegistrationInvoice.Application
{
    [DependsOn(
        typeof(AbpDddApplicationModule),
        typeof(VnisCoreOracleDomainModule),
        typeof(VnisCoreOracleModuleContractsModule),
        typeof(AbpAutoMapperModule),
        typeof(SharedModule),
        typeof(TvanModule),
        typeof(VnisCoreRegistrationInvoiceInfrastructureModule),
        typeof(BaseApplicationSharedModule),
        typeof(SharedInvoiceModule)
    )]
    public class VnisCoreRegistrationInvoiceApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.AddScoped<IPdfService, PdfService>();
            context.Services.AddScoped<ISemaphore, HtmlToPdfSemaphore>();
            context.Services.AddScoped<IMediaTemplateDesignService, MediaTemplateDesignService>();

            context.Services.AddScoped<ITaxService, TaxService>();

            context.Services.AddScoped<IPreviewTemplateFactory, PreviewTemplateFactory>();
            context.Services.AddScoped<IPreviewTemplateService, PreviewTemplateInvoice01Service>();
            context.Services.AddScoped<IPreviewTemplateService, PreviewTemplateInvoice02Service>();
            context.Services.AddScoped<IPreviewTemplateService, PreviewTemplateInvoice03Service>();
            context.Services.AddScoped<IPreviewTemplateService, PreviewTemplateInvoice04Service>();
            context.Services.AddScoped<IPreviewTemplateService, PreviewTemplateInvoice05Service>();
            context.Services.AddScoped<IValidationContext, ValidationContext>();

            context.Services.AddHttpClient();
            context.Services.AddScoped<IDigitalSignatureService, DigitalSignatureService>();

            context.Services.AddScoped<ICachingBusiness, CachingBusiness>();
            
            context.Services.AddAutoMapperObjectMapper<VnisCoreRegistrationInvoiceApplicationModule>();
            Configure<AbpAutoMapperOptions>(options =>
            {
                options.AddMaps<VnisCoreRegistrationInvoiceApplicationAutoMapperProfile>(validate: false);
            });
            context.Services.AddTransient(typeof(IAppFactory), typeof(AppFactory));
            context.Services.AddTransient(typeof(IPipelineBehavior<,>), typeof(RequestPreProcessorBehavior<,>));
            context.Services.AddMediatR(typeof(VnisCoreRegistrationInvoiceApplicationModule).GetTypeInfo().Assembly);
        }


        public override void OnApplicationInitialization(ApplicationInitializationContext context)
        {
            //context.AddBackgroundWorker<CloneInvoiceTemplateNewYearWorker>();
        }
    }
}
