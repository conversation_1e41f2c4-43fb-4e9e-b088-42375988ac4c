using Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Models.Dtos;
using Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Models.Filters;
using Core.Application.Dtos;
using Core.BaseApplication.Shared.Application.Services;
using Core.Domain.Repositories;
using Core.Shared.Constants;
using Core.Shared.Enums;
using Core.Shared.Factory;
using Core.Shared.Helper;
using Core.Shared.Invoice.Handlers;
using Core.Tvan.Constants;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;

namespace Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Application.Proxies
{
    public interface IPITDeductionDocumentDeclarationGetPagingProxy : IBaseGetListService<long, PITDeductionDocumentDeclarationEntity, PITDeductionDocumentDeclarationDto, PITDeductionDocumentDeclarationFilter>
    {

    }
    public class PITDeductionDocumentDeclarationGetPagingProxy : IPITDeductionDocumentDeclarationGetPagingProxy
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<PITDeductionDocumentDeclarationEntity, long> _taxDocumentDeclarationRepository;
        private readonly IBaseGetListService<long, PITDeductionDocumentDeclarationEntity, PITDeductionDocumentDeclarationDto, PITDeductionDocumentDeclarationFilter> _innerService;
        private readonly IArrayHandler<List<short>> _arrayHandler;
        private readonly IStatusHandler _statusHandler;

        public PITDeductionDocumentDeclarationGetPagingProxy(IAppFactory appFactory,
            IRepository<PITDeductionDocumentDeclarationEntity, long> taxDocumentDeclarationRepository,
            IBaseGetListService<long, PITDeductionDocumentDeclarationEntity, PITDeductionDocumentDeclarationDto, PITDeductionDocumentDeclarationFilter> innerService,
            IArrayHandler<List<short>> arrayHandler,
            IStatusHandler statusHandler)
        {
            _appFactory = appFactory;
            _taxDocumentDeclarationRepository = taxDocumentDeclarationRepository;
            _innerService = innerService;
            _arrayHandler = arrayHandler;
            _statusHandler = statusHandler;
        }

        public async Task<PagedResultDto<PITDeductionDocumentDeclarationDto>> GetListAsync(PITDeductionDocumentDeclarationFilter input, IQueryable<PITDeductionDocumentDeclarationDto> queryable = null)
        {
            var tenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _taxDocumentDeclarationRepository.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(keyword),
                         x => EF.Functions.Like(x.TaxDepartment, keyword)
                         || EF.Functions.Like(x.CodeTaxDepartment, keyword)
                         || EF.Functions.Like(x.PlaceName, keyword)
                         || EF.Functions.Like(x.TaxCode, keyword))
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .WhereIf(true, x => x.IsDeleted == false)
                .WhereIf(input.FromDeclarationDate != null, x => x.DeclarationDate >= input.FromDeclarationDate)
                .WhereIf(input.ToDeclarationDate != null, x => x.DeclarationDate <= input.ToDeclarationDate)
                .WhereIf(input.FromCreationTime != null, x => x.CreationTime >= input.FromCreationTime)
                .WhereIf(input.ToCreationTime != null, x => x.CreationTime <= input.ToCreationTime)
                .WhereIf(input.FromTvanResponseTime != null, x => !x.TvanResponseTime.HasValue ? true : x.TvanResponseTime >= input.FromTvanResponseTime)
                .WhereIf(input.FromTvanResponseTime != null, x => !x.TvanResponseTime.HasValue ? true : x.TvanResponseTime <= input.FromTvanResponseTime)
                .AsQueryable();

            if (input.Types.Any())
            {
                query = query.Where(x => input.Types.Equals(x.Type)).AsQueryable();
            }

            if (input.Statuses.Any())
            {
                var predicate = PredicateBuilder.New<PITDeductionDocumentDeclarationEntity>();

                foreach (var status in input.Statuses)
                {
                    switch ((PITDeductionDocumentDeclarationStatus)status)
                    {
                        case PITDeductionDocumentDeclarationStatus.PendingApproval:
                            predicate = predicate.Or(x => x.ApprovalStatus == (short)ApproveStatus.ChoDuyet);
                            break;
                        case PITDeductionDocumentDeclarationStatus.Cancelled:
                            predicate = predicate.Or(x => x.ApprovalStatus == (short)ApproveStatus.HuyDuyet);
                            break;
                        case PITDeductionDocumentDeclarationStatus.PendingSignature:
                            predicate = predicate.Or(x => x.ApprovalStatus == (short)ApproveStatus.DaDuyet && x.SignStatus == (short)RegistrationInvoiceSignStatus.ChoKy);
                            break;
                        case PITDeductionDocumentDeclarationStatus.NotSentToTVAN:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.UnSent);
                            break;
                        case PITDeductionDocumentDeclarationStatus.SentToTVAN:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.Send);
                            break;
                        case PITDeductionDocumentDeclarationStatus.SendToTVANFailed:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.SendError);
                            break;
                        case PITDeductionDocumentDeclarationStatus.ReceivedByTCT:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.GDTReceived);
                            break;
                        case PITDeductionDocumentDeclarationStatus.RejectedByTCT:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.GDTRejectedReceive);
                            break;
                        case PITDeductionDocumentDeclarationStatus.AcceptedByTCT:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.GDTAccepted);
                            break;
                        case PITDeductionDocumentDeclarationStatus.NotAcceptedByTCT:
                            predicate = predicate.Or(x => x.SignStatus == (short)RegistrationInvoiceSignStatus.DaKy && x.TvanStatus == (short)RegistrationTvanStatus.GDTRejectedAccept);
                            break;
                    }
                }

                query = query.Where(predicate);
            }

            queryable = query.OrderByDescending(x => x.CreationTime)
            .Select(x => _appFactory.ObjectMapper.Map<PITDeductionDocumentDeclarationEntity, PITDeductionDocumentDeclarationDto>(x));

            var data = await _innerService.GetListAsync(input, queryable);

            var listType = EnumHelper.GetEnumInfos<PITDeductionDocumentDeclarationType>();
            var listUsageType = EnumHelper.GetEnumInfos<UsageType>();

            foreach (var item in data.Items)
            {
                item.TypeName = listType.FirstOrDefault(x => x.Value == item.Type)?.Description;
                item.CreationTimeText = item.CreationTime.ToString("dd/MM/yyyy HH:mm:ss");
                item.DeclarationDateText = item.DeclarationDate.ToString("dd/MM/yyyy HH:mm:ss");
                item.StatusName = await _statusHandler.PITDeductionDocumentDeclarationGetStatusNameAsync(item.ApprovalStatus, item.SignStatus, item.TvanStatus);

                var usageTypes = await _arrayHandler.ParsingAsync(item.UsageType);
                var listUsageTypeName = listUsageType.Where(x => usageTypes.Contains(x.Value)).Select(x => x.Description);
                item.UsageTypeNames = listUsageTypeName.ToList();
            }
            return data;
        }
    }
}
