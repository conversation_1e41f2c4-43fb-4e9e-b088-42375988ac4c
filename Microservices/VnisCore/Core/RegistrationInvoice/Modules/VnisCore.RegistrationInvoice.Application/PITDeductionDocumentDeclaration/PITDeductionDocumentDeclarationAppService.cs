using Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Application.Proxies;
using Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Application.Services;
using Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Models.Dtos;
using Agribank.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Models.Filters;
using Core;
using Core.Application.Dtos;
using Core.BaseApplication.Shared.AppService;
using Core.BaseApplication.Shared.Models.Results;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.RegistrationInvoice.PITDeductionDocumentDeclaration;
using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;
using VnisCore.RegistrationInvoice.Application.PITDeductionDocumentDeclaration.Application.Proxies;

namespace VnisCore.RegistrationInvoice.Application.PITDeductionDocumentDeclaration
{
    [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Default)]
    public class PITDeductionDocumentDeclarationAppService : BaseApplicationService<long,
        PITDeductionDocumentDeclarationEntity,
        PITDeductionDocumentDeclarationDto,
        PITDeductionDocumentDeclarationDto,
        PITDeductionDocumentDeclarationFilter>
    {
        public PITDeductionDocumentDeclarationAppService(IAppFactory appFactory) : base(appFactory)
        {
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Search)]
        [HttpPost(Utilities.ApiUrlActionBase)]
        public override async Task<PagedResultDto<PITDeductionDocumentDeclarationDto>> GetListAsync(PITDeductionDocumentDeclarationFilter input)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationGetPagingProxy>().GetListAsync(input);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Search)]
        [HttpGet(Utilities.ApiUrlActionBase + "/{id:long}")]
        public override async Task<PITDeductionDocumentDeclarationDto> GetByIdAsync([FromRoute] long id)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationGetByIdProxy>().GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Create)]
        [HttpPost(Utilities.ApiUrlActionBase)]
        public override async Task<ResponseResult> CreateAsync(PITDeductionDocumentDeclarationDto input)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationCreateProxy>().CreateAsync(input);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Update)]
        [HttpPost(Utilities.ApiUrlActionBase)]
        public override async Task<ResponseResult> UpdateAsync(PITDeductionDocumentDeclarationDto input)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationUpdateProxy>().UpdateAsync(input);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Delete)]
        [HttpPost(Utilities.ApiUrlActionBase + "/{id:long}")]
        public override async Task<ResponseResult> DeleteAsync([FromRoute] long id)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationDeleteProxy>().DeleteAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Approve)]
        [HttpPost(Utilities.ApiUrlActionBase)]
        public async Task<ResponseResult> ApproveAsync(PITDeductionDocumentDeclarationFilter input)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationApproveProxy>().HandleAsync(input, ApproveStatus.DaDuyet);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Update)]
        [HttpPost(Utilities.ApiUrlActionBase)]
        public async Task<ResponseResult> CancelAsync(PITDeductionDocumentDeclarationFilter input)
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationCancelProxy>().HandleAsync(input, ApproveStatus.HuyDuyet);
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }

        [Authorize(PITDeductionDocumentDeclarationPermissions.PITDeductionDocumentDeclaration.Default)]
        [HttpGet(Utilities.ApiUrlActionBase)]
        public async Task<PITDeductionDocumentDeclarationDto> GetNewestAsync()
        {
            try
            {
                return await _appFactory.GetServiceDependency<IPITDeductionDocumentDeclarationGetNewestService>().HandleAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex.StackTrace);
                if (ex is BusinessException)
                    throw new UserFriendlyException(ex.Message);
                throw;
            }
        }
    }
}
