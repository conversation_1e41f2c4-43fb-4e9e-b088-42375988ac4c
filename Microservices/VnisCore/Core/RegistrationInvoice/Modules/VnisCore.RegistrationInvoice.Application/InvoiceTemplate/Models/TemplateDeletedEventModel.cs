using MediatR;
using System;

namespace VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Models
{
    public class TemplateDeletedEventModel : INotification
    {
        public long Id { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public Guid UserId { get; set; }
        public Guid TenantId { get; set; }
        public long? IdFile { get; set; }
    }
}
