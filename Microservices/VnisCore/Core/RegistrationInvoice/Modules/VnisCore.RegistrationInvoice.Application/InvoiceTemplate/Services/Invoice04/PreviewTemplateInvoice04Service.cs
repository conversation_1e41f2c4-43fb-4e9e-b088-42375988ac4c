using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using QRCoder;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Abstractions;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Interfaces;

namespace VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Services.Invoice04
{
    public class PreviewTemplateInvoice04Service : BasePreviewTemplateService, IPreviewTemplateService
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public PreviewTemplateInvoice04Service(IAppFactory appFactory,
            IServiceProvider serviceProvider,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IPdfService pdfService) : base(appFactory, pdfService, localizier)
        {
            _appFactory = appFactory;
            _serviceProvider = serviceProvider;
            _localizier = localizier;
        }

        public override async Task<PdfInvoiceModel> FakeDataAsync(InvoiceTemplateEntity template, Guid idUser)
        {
            var tenantInfo = _appFactory.CurrentTenant;
            var pdf = new PdfInvoiceModel();
            pdf.TemplateNo = template.TemplateNo.ToString();
            pdf.SerialNo = template.SerialNo;
            pdf.InvoiceNo = "0000001";
            pdf.PaymentMethod = "Tiền mặt hoặc chuyển khoản";
            pdf.TransactionId = "85b21411";

            var currency = await GetDefaultCurrencyAsync(tenantInfo.Id ?? Guid.Empty);
            currency = new CurrencyEntity
            {
                NameVi = "Đồng",
                MinimumNameVi = "dong",
                MinimumNameEn = "dong",
                Conversion = 0
            };

            if (currency == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice03.FromCurrencyIsNotYet"]);

            pdf.TotalPaymentAmount = 0;

            //Thông tin người bán
            pdf.SellerFullName = tenantInfo.FullNameVi;
            pdf.SellerAddressLine = tenantInfo.Address;
            pdf.SellerTaxCode = tenantInfo.TaxCode;
            pdf.SellerCityName = tenantInfo.City;
            pdf.SellerSignedId = idUser;
            pdf.SellerSignedTime = DateTime.Now;
            pdf.InvoiceDate = DateTime.Now;
            pdf.SellerFullNameSigned = tenantInfo.FullNameVi;
            //pdf.SellerBankAccount = tenantInfo.ba(tenantInfo, TenantMetaData.BankAccount.ToString());
            //pdf.SellerBankName = tenantInfo.GetTenantMetaDataByKey(tenantInfo, TenantMetaData.BankName.ToString());
            pdf.SellerPhoneNumber = tenantInfo.Phones;
            pdf.SellerLegalName = tenantInfo.LegalName;
            pdf.SellerFaxNumber = tenantInfo.Fax;
            pdf.SellerEmail = tenantInfo.Emails;

            //thông tin người mua
            pdf.BuyerName = "VNIs";
            pdf.BuyerFullName = "CÔNG TY CỔ PHẦN GIẢI PHÁP HÓA ĐƠN ĐIỆN TỬ VIỆT NAM";
            pdf.BuyerAddressLine = "Tầng 3 - Tòa nhà Reeco, Số 98 Hoàng Ngân - Cầu Giấy - Hà Nội.";
            pdf.BuyerBankAccount = "**********";
            pdf.BuyerTaxCode = "**********-001";
            pdf.BuyerEmail = "<EMAIL>";

            //thông tin Phiếu xuất kho hàng giao gửi đại lý
            pdf.EconomicContractNumber = "            ";
            pdf.EconomicContractDate = DateTime.Now;
            pdf.DeliveryOrderBy = "            ";
            pdf.DeliveryBy = "            ";
            pdf.ContractNumber = "            ";
            pdf.TransportationMethod = "            ";

            pdf.QrCode = GetQrCode(pdf);
            pdf.SignStatus = SignStatus.DaKy;
            pdf.InvoiceStatus = InvoiceStatus.Goc;
            pdf.Note = "test";

            //Dữ liệu hóa đơn
            var details = new List<PdfInvoiceDetailModel>();

            pdf.PaymentAmountWords = MoneyExtensions.ReadMoneyVi(Math.Abs(pdf.TotalPaymentAmount), currency.NameVi, currency.MinimumNameVi, currency.Rounding, currency.Conversion);
            pdf.InvoiceDetails = details;
            return pdf;


        }

        private string GetQrCode(PdfInvoiceModel invoice)
        {
            string qrCode = $"{invoice.SellerFullName} - {invoice.ReceiverName} - {(string.IsNullOrEmpty(invoice.ReceiverTaxCode) ? "" : $"{invoice.ReceiverTaxCode} - ")}{invoice.TotalAmount} - {invoice.TotalPaymentAmount}";

            using var generator = new QRCodeGenerator();
            QRCodeData qrCodeData = generator.CreateQrCode(qrCode, QRCodeGenerator.ECCLevel.Q);
            using var bitmap = new BitmapByteQRCode();
            bitmap.SetQRCodeData(qrCodeData);
            var bytes = bitmap.GetGraphic(20);
            return Convert.ToBase64String(bytes);
        }

        public async Task<CurrencyEntity> GetDefaultCurrencyAsync(Guid tenantId)
        {
            var repoCurrency = _appFactory.Repository<CurrencyEntity, long>();
            var currency = await repoCurrency.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.IsDefault && !x.IsDeleted);

            if (currency == null)
                return null;

            return currency;
        }
    }
}
