using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using MediatR;
using Microsoft.Extensions.Localization;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Interfaces;
using VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Models;

namespace VnisCore.RegistrationInvoice.Application.InvoiceTemplate.Events
{
    public class TemplateUpdatedEventHandler : INotificationHandler<TemplateUpdatedEventModel>
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public TemplateUpdatedEventHandler(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _appFactory = appFactory;
            _localizier = localizier;
        }

        public async Task Handle(TemplateUpdatedEventModel notification, CancellationToken cancellationToken)
        {
            //update vào monitor
            var monitorService = _appFactory.GetServiceDependency<IMonitorInvoiceTemplateService>();

            var monitorTemplate = await monitorService.GetAsync(notification.Id);
            if (monitorTemplate == null)
            {
                throw new UserFriendlyException(_localizier["Vnis.BE.InvoiceTemplate.Update.MonitorNotFound"]);
            }

            monitorTemplate.RegistrationHeaderId = notification.RegistrationHeaderId;
            monitorTemplate.ActiveDate = notification.GDTResponseTime.HasValue ? notification.GDTResponseTime.Value : null;

            await monitorService.UpdateAsync(monitorTemplate.Id, monitorTemplate);
        }
    }
}
