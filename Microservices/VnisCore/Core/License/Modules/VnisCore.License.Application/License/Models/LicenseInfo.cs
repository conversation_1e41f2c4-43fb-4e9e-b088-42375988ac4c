using System;

namespace VnisCore.License.Application.License.Models
{
    public class LicenseInfo
    {
        public Guid? LicenseKey { get; set; }

        public string TaxCode { get; set; }

        /// <summary>
        /// loại
        /// </summary>
        public string Type { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// tần suất sử dụng
        /// </summary>
        public int? FrequencyOfUse { get; set; }
        public string Status { get; set; }

        /// <summary>
        /// đã sử dụng
        /// </summary>
        public int Used { get; set; }

        /// <summary>
        /// còn c<PERSON> thể sử dụng
        /// </summary>
        public int? Available { get; set; }

        /// <summary>
        /// tổng dki
        /// </summary>
        public int? Total { get; set; }

        public string AppId { get; set; }

        public string EncodedLicense { get; set; }

        public int DeleteIndex { get; set; }
    }
}
