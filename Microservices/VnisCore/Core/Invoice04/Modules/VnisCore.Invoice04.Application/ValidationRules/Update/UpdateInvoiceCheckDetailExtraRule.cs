using Core.Shared.Validations;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using Core.Domain.Repositories;
using VnisCore.Invoice04.Application.Invoice04.Dto;
using Core.Shared.Factory;
using Core.Localization.Resources.AbpLocalization;
using Microsoft.Extensions.Localization;

namespace VnisCore.Invoice04.Application.Invoice04.ValidationRule.Update
{
    /// <summary>
    /// kiểm tra field name detail Extra
    /// </summary>
    class UpdateInvoiceCheckDetailExtraRule : IValidationRuleAsync<UpdateInvoice04HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice04DetailFieldEntity, long> _repoInvoice04DetailField;
        private readonly IValidationContext _validationContext;

        public UpdateInvoiceCheckDetailExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IRepository<Invoice04DetailFieldEntity, long> repoInvoice04DetailField,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoice04DetailField = repoInvoice04DetailField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice04HeaderDto input)
        {
            if (input.InvoiceDetails == null || !input.InvoiceDetails.Any())
                return new ValidationResult(true);

            var commandDetailExtras = input.InvoiceDetails
                                           .Where(x => x.InvoiceDetailExtras != null)
                                           .SelectMany(x => x.InvoiceDetailExtras)
                                           .ToList();
            if (!commandDetailExtras.Any())
                return new ValidationResult(true);

            var commandDetailFieldNames = commandDetailExtras.Select(x => x.FieldName).Distinct();

            if (!commandDetailFieldNames.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các detail field
            var detailFieldNames = await _repoInvoice04DetailField.Where(x => x.TenantId == tenantId)
                                                                  .Select(x => x.FieldName)
                                                                  .AsQueryable()
                                                                  .ToListAsync();

            var expects = commandDetailFieldNames.Except(detailFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.DetailExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
