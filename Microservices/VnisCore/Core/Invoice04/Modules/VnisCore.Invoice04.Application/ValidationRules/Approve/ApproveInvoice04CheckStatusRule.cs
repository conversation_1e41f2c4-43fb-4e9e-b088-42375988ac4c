using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Invoice04.Application.Factories.Repositories;
using VnisCore.Invoice04.Application.Invoice04.Models.Requests.Commands;

namespace VnisCore.Invoice04.Application.Invoice04.ValidationRules.Approve
{
    public class ApproveInvoice04CheckStatusRule : IValidationRuleAsync<ApproveInvoice04RequestModel, ValidationResult>
    {
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceHeaderRepository<Invoice04HeaderEntity> _repoHeader;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public ApproveInvoice04CheckStatusRule(
            IValidationContext validationContext,
            IInvoiceHeaderRepository<Invoice04HeaderEntity> repoHeader,
            IStringLocalizer<CoreLocalizationResource> localizier
            )
        {
            _localizier = localizier;
            _validationContext = validationContext;
            _repoHeader = repoHeader;
        }

        public async Task<ValidationResult> HandleAsync(ApproveInvoice04RequestModel input)
        {
            var invoices = await _repoHeader.GetByIdsAsync(input.Invoices.Select(x => x.Id).ToList());
            var index = new List<ApproveInvoice04InfoRequest>();

            foreach (var invoice in invoices)
            {
                //if (invoice.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode() 
                //    && invoice.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode() 
                //    && invoice.ApproveDeleteStatus != ApproveStatus.ChoDuyet.GetHashCode())
                //    return new ValidationResult(false, $"Trạng thái duyệt hóa đơn {invoice.TemplateNo} - {invoice.SerialNo} - {invoice.InvoiceNo} phải là chờ duyệt");

                if (invoice.SignStatus == SignStatus.DaKy.GetHashCode() && invoice.ApproveDeleteStatus != ApproveStatus.ChoDuyet.GetHashCode())
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice03.Api.Intergration.Approve.InvoiceStatusIncorrectDelete", new string[] { invoice.TemplateNo.ToString(), invoice.SerialNo, invoice.InvoiceNo }]);

                if (invoice.SignStatus != SignStatus.DaKy.GetHashCode() && invoice.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode() && invoice.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode())
                    return new ValidationResult(false, _localizier["Vnis.BE.Invoice04.Api.Intergration.Approve.InvoiceStatusIncorrect"]);

                index.Add(new ApproveInvoice04InfoRequest
                {
                    Id = invoice.Id ,
                    InvoiceNo = invoice.InvoiceNo,
                    SerialNo = invoice.SerialNo,
                    TemplateNo = invoice.TemplateNo
                });
            }

            _validationContext.GetOrAddItem("Invoices", () =>
            {
                return index;
            });

            return new ValidationResult(true);
        }
    }
}
