using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Invoice04.Application.Factories.Repositories;
using VnisCore.Invoice04.Application.Invoice04.Dto;

namespace VnisCore.Invoice04.Application.Invoice04.ValidationRules.Cancel
{
    /// <summary>
    /// check điều kiện của hóa đơn có được xóa hủy không
    /// </summary>
    public class CancelInvoiceCheckStatusRule : IValidationRuleAsync<CancelInvoice04HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;

        public CancelInvoiceCheckStatusRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CancelInvoice04HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            if (input.Ids == null || !input.Ids.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.Cancel.InvoiceIdsNotEmpty"]);

            var repoHeader = _appFactory.GetServiceDependency<IInvoiceHeaderRepository<Invoice04HeaderEntity>>();
            var invoices = await repoHeader.GetByIdsAsync(input.Ids);
            if (invoices == null || !invoices.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.InvoiceNotFound"]);

            if (invoices.Any(x => x.InvoiceNo == null || x.InvoiceNo == ""))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.Cancel.ExistInvoiceWithoutNumberCannotBeDeleted"]);

            if (invoices.Any(x => x.InvoiceStatus == InvoiceStatus.XoaHuy.GetHashCode()))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.Cancel.ExistInvoiceHasBeenDeleted"]);

            if (invoices.Any(x => x.SignStatus == SignStatus.DangKy.GetHashCode()
                || x.SignStatus == SignStatus.DaKy.GetHashCode()))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.Cancel.ExistInvoiceCannotBeDeleted"]);

            // nếu là hóa đơn chờ duyệt xóa hủy thì ko được xóa hủy
            if (invoices.Any(x => x.ApproveCancelStatus == ApproveStatus.ChoDuyet.GetHashCode()))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.Cancel.ExistInvoiceApproveCancel"]);

            _validationContext.GetOrAddItem<List<Invoice04HeaderEntity>>("Invoices", () =>
            {
                return invoices;
            });

            return new ValidationResult(true);
        }
    }
}
