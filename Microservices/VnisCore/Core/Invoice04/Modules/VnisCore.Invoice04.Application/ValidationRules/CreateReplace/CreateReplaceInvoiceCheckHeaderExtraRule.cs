using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Invoice04.Application.Factories.Repositories;
using VnisCore.Invoice04.Application.Invoice04.Dto;

namespace VnisCore.Invoice04.Application.Invoice04.ValidationRule.CreateReplace
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class CreateReplaceInvoiceCheckHeaderExtraRule : IValidationRuleAsync<CreateReplaceInvoice04Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceHeaderFieldRepository<Invoice04HeaderFieldEntity> _repoInvoiceHeaderField;
        private readonly IValidationContext _validationContext;

        public CreateReplaceInvoiceCheckHeaderExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IInvoiceHeaderFieldRepository<Invoice04HeaderFieldEntity> repoInvoiceHeaderField,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoiceHeaderField = repoInvoiceHeaderField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateReplaceInvoice04Dto input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            var headerFieldNames = await _repoInvoiceHeaderField.GetFieldNameByTenantCodeAsync(tenantId);

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.HeaderExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
