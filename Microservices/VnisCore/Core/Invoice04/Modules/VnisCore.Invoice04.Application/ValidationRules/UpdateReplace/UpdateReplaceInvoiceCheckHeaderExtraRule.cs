using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Invoice04.Application.Invoice04.Dto;

namespace VnisCore.Invoice04.Application.Invoice04.ValidationRule.UpdateReplace
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class UpdateReplaceInvoiceCheckHeaderExtraRule : IValidationRuleAsync<UpdateReplaceInvoice04HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice04HeaderFieldEntity, long> _repoInvoice04HeaderField;
        private readonly IValidationContext _validationContext;

        public UpdateReplaceInvoiceCheckHeaderExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IRepository<Invoice04HeaderFieldEntity, long> repoInvoice04HeaderField,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoice04HeaderField = repoInvoice04HeaderField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice04HeaderDto input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            var headerFieldNames = await _repoInvoice04HeaderField.Where(x => x.TenantId == tenantId)
                                                                  .Select(x => x.FieldName)
                                                                  .AsQueryable()
                                                                  .ToListAsync();

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice04.HeaderExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
