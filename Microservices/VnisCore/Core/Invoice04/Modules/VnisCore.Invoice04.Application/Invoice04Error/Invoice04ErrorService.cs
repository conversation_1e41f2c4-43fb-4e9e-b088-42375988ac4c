using Core;
using Core.Application.Dtos;
using Core.Application.Services;
using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.InvoiceExtra;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;
using VnisCore.Invoice04.Application.Invoice04Error.Dto;
using VnisCore.Invoice04.Application.Invoice04Error.Models;

namespace VnisCore.Invoice04.Application.Invoice04Error
{
    [Authorize]
    public class Invoice04ErrorService : ApplicationService, IInvoice04ErrorService
    {
        private readonly IAppFactory _appFactory;
        private readonly IRepository<Invoice04ErrorEntity, long> _repoError;
        private readonly IRepository<Invoice04HeaderEntity, long> _repoInvoiceHeader;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IRepository<TvanInfoInvoice04ErrorEntity, long> _repoTvanInfoInvoiceError;

        public Invoice04ErrorService(IRepository<Invoice04ErrorEntity, long> repoError,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IRepository<Invoice04HeaderEntity, long> repoInvoiceHeader,
            IRepository<TvanInfoInvoice04ErrorEntity, long> repoTvanInfoInvoiceError)
        {
            _appFactory = appFactory;
            _localizer = localizer;
            _repoError = repoError;
            _repoInvoiceHeader = repoInvoiceHeader;
            _repoTvanInfoInvoiceError = repoTvanInfoInvoiceError;
        }

        [Authorize(InvoiceErrorPermissions.InvoiceError.Default)]
        [HttpPost(Utilities.ApiUrlBase + "GetList")]
        public async Task<PagedResultDto<Invoice04ErrorReponse>> GetListAsync(Invoice04ErrorPagedRequestDto input)
        {
            input.TenantId = _appFactory.CurrentTenant.Id;
            var keyword = input.Keyword;

            var query = _repoError.AsNoTracking()
                .WhereIf(
                         !string.IsNullOrEmpty(input.Keyword),
                         x => EF.Functions.Like(x.VerificationCode, keyword)
                         || EF.Functions.Like(x.SerialNo, keyword)
                         )
                .WhereIf(input.SignStatuses.Any(), x => input.SignStatuses.Contains((SignStatus)x.SignStatus))
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject)
                .WhereIf(input.TvanStatuses.Any()
                        && input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus)
                                || x.TvanStatus == (short)TvanStatus.TvanAccept
                                || x.TvanStatus == (short)TvanStatus.TvanReject
                                || x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                .WhereIf(input.TvanStatuses.Any()
                        && !input.TvanStatuses.Contains(TvanStatus.TCTAccept)
                        && !input.TvanStatuses.Contains(TvanStatus.TCTReject),
                                x => input.TvanStatuses.Contains((TvanStatus)x.TvanStatus))
                .WhereIf(input.CreateFromDate.HasValue, x => x.AnnouncementDate.Date >= input.CreateFromDate.Value.Date)
                .WhereIf(input.CreateToDate.HasValue, x => x.AnnouncementDate.Date <= input.CreateToDate.Value.AddDays(1).Date)
                .WhereIf(!input.SerialNo.IsNullOrWhiteSpace(), x => x.SerialNo == input.SerialNo)
                .WhereIf(input.GroupCode.HasValue, x => x.GroupCode == input.GroupCode)
                .WhereIf(!input.NumberInvoiceError.IsNullOrWhiteSpace(), x => x.NumberInvoiceError == input.NumberInvoiceError)
                .Where(x => x.TenantId == input.TenantId)
                .OrderByDescending(x => x.CreationTime);

            var errs = await query.ToListAsync();

            var groupItems = errs.GroupBy(x => new { x.GroupCode, x.TaxDepartment, x.AnnouncementDate, x.CodeTaxDepartment, x.PlaceName, x.BudgetUnitCode })
            .Select(x => new Invoice04ErrorReponse
            {
                GroupCode = x.Key.GroupCode.ToString(),
                TaxDepartment = x.Key.TaxDepartment,
                AnnouncementDate = x.Key.AnnouncementDate,
                CodeTaxDepartment = x.Key.CodeTaxDepartment,
                PlaceName = x.Key.PlaceName,
                BudgetUnitCode = x.Key.BudgetUnitCode,
                SignStatus = x.First().SignStatus,
                TvanStatus = x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.UnSent) != null ? (short)TvanStatus.UnSent : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.SendError) != null ? (short)TvanStatus.SendError : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.Sended) != null ? (short)TvanStatus.Sended : (x.FirstOrDefault(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss) != null ? (short)TvanStatus.TvanReject : (short)TvanStatus.TCTAccept))),
                InvoiceErrors = x.Select(t => new Invoice04ErrorHeaderModel
                {
                    Id = t.Id,
                    Index = t.Index,
                    VerificationCode = t.VerificationCode,
                    InvoiceHeaderId = t.InvoiceHeaderId,
                    TemplateNo = t.TemplateNo,
                    SerialNo = t.SerialNo,
                    InvoiceNo = t.InvoiceNo,
                    InvoiceDate = t.InvoiceDate,
                    InvoiceType = t.InvoiceType,
                    Action = t.Action,
                    Reason = t.Reason,
                    SignStatus = t.SignStatus,
                    SellerSignedTime = t.SellerSignedTime,
                    SellerFullNameSigned = t.SellerFullNameSigned,
                    SellerSignedId = t.SellerSignedId,
                    TvanStatus = t.TvanStatus,
                    Number = t.Number,
                    NumberInvoiceError = t.NumberInvoiceError
                }).OrderBy(x => x.InvoiceNo).ToList()
            })
            .WhereIf(!input.VerificationCode.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.VerificationCode).Contains(input.VerificationCode))
.WhereIf(!input.InvoiceNo.IsNullOrEmpty(), x => x.InvoiceErrors.Select(y => y.InvoiceNo).Contains(input.InvoiceNo) || x.InvoiceErrors.Select(y => y.Number).Contains(int.Parse(input.InvoiceNo))).WhereIf(input.FromNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number >= input.FromNumber).ToList().Count == x.InvoiceErrors.Count)
            .WhereIf(input.ToNumber.HasValue, x => x.InvoiceErrors.Select(y => y.Number).Where(number => number <= input.ToNumber).ToList().Count == x.InvoiceErrors.Count);
            var items = groupItems.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();

            return new PagedResultDto<Invoice04ErrorReponse>
            {
                TotalCount = groupItems.Count(),
                Items = items
            };
        }

        [HttpGet(Utilities.ApiUrlBase + "GetByGroupCode/{groupCode:long}")]
        public async Task<List<GetInvoice04ErrorResponseModel>> GetByGroupCodeAsync(long groupCode)
        {
            if (!await _repoError.AnyAsync(x => x.GroupCode == groupCode))
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Sign.InvoiceError.NotFound"]);
            }

            var errors = await _repoError.Where(x => x.GroupCode == groupCode).ToListAsync();

            var result = new List<GetInvoice04ErrorResponseModel>();

            foreach (var item in errors)
            {
                result.Add(MapToDto(item));
            }
            return result;
        }

        /// <summary>
        /// API trả ra lý do không tiếp nhận của từng hóa đơn khi gửi TBSS lên CQT
        /// </summary>
        /// <param name="queryReasonRejectTbssModel"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase + "GetReasonRejectTbss")]
        public async Task<List<ResponseReasonRejectTbssModel>> GetReasonRejectTbssAsync(QueryReasonRejectTbssModel queryReasonRejectTbssModel)
        {
            var tvanInfo = await _repoTvanInfoInvoiceError.Where(x => x.InvoiceHeaderId == queryReasonRejectTbssModel.InvoiceHeaderId
                                                                && x.MessageCodeReference == queryReasonRejectTbssModel.MessageCode
                                                                && (x.MessageTypeCode == MLTDiep._301.GetHashCode().ToString() || x.MessageTypeCode == MLTDiep._204.GetHashCode().ToString()))
                                                        .FirstOrDefaultAsync();

            if (tvanInfo != null)
            {
                return JsonConvert.DeserializeObject<List<ResponseReasonRejectTbssModel>>(tvanInfo.Reason);
            }
            else
            {
                return new List<ResponseReasonRejectTbssModel>();
            }
        }

        [Authorize(InvoiceErrorPermissions.InvoiceError.Create)]
        [HttpPost(Utilities.ApiUrlBase + "create")]
        public async Task<CreateInvoice04ErrorResponseModel> CreateAsync([FromBody] CreateInvoice04ErrorModel request)
        {
            var userId = _appFactory.CurrentUser.Id.Value;
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var invoiceHeaders = new List<Invoice04HeaderEntity>();
            var invoiceErrs = request.InvoiceErrors;
            foreach (var item in invoiceErrs)
            {
                var invoiceHeader = await _repoInvoiceHeader.FirstOrDefaultAsync(x => x.TemplateNo == item.TemplateNo
                                                                                    && x.SerialNo == item.SerialNo
                                                                                    && x.InvoiceNo == item.InvoiceNo
                                                                                    && x.InvoiceDate.Date == item.InvoiceDate.Date
                                                                                    && x.TenantId == tenantId);
                if (invoiceHeader == null)
                {
                    throw new UserFriendlyException($@"Hóa đơn: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} không có trong hệ thống. Vui lòng kiểm tra lại.");
                }

                if (invoiceHeader.SignStatus != (short)SignStatus.DaKy)
                {
                    throw new UserFriendlyException($@"Hóa đơn: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} chưa ký. Vui lòng kiểm tra lại");
                }

                if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.DieuChinh)
                {
                    throw new UserFriendlyException($@"Hóa đơn điều chỉnh: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} không thể lập TBSS. Vui lòng kiểm tra lại");
                }

                if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.XoaBo)
                {
                    throw new UserFriendlyException($@"Hóa đơn xóa bỏ: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} không thể lập TBSS. Vui lòng kiểm tra lại");
                }

                //if (invoiceHeader != null && (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinh
                //    || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiThayThe
                //    || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinhDinhDanh
                //    || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinhTangGiam
                //    ) && item.Action == TThaiTBao.Huy)
                //{
                //    var message = invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiThayThe ? "bị thay thế" : "bị điều chỉnh";
                //    throw new UserFriendlyException($@"Hóa đơn số {invoiceHeader.InvoiceNo}, ký hiệu {invoiceHeader.SerialNo} đã {message}. Vui lòng kiểm tra lại.");
                //}

                var invoicesError = await _repoError.Where(x => x.InvoiceHeaderId == invoiceHeader.Id
                                                            && x.SignStatus != (short)SignStatus.Huy
                                                            && x.TenantId == tenantId)
                                                    .ToListAsync();
                if (invoicesError.Any(x => x.SignStatus != SignStatus.DaKy.GetHashCode()))
                {
                    throw new UserFriendlyException($@"Hoá đơn số {invoicesError.FirstOrDefault().InvoiceNo}, Ký hiệu {invoicesError.FirstOrDefault().SerialNo} đã thêm vào TBSS chưa được ký số thành công. Vui lòng kiểm tra lại");
                }

                if (invoicesError.Any(x => x.TvanStatus == TvanStatus.UnSent.GetHashCode() || x.TvanStatus == TvanStatus.SendError.GetHashCode()))
                {
                    throw new UserFriendlyException($@"Hóa đơn số {invoicesError.FirstOrDefault().InvoiceNo}, ký hiệu {invoicesError.FirstOrDefault().SerialNo} đã thêm vào TBSS chưa gửi CQT. Vui lòng kiểm tra lại");
                }

                //if (invoicesError.Any(x => x.TvanStatus != TvanStatus.TCTReject.GetHashCode()
                //    && x.TvanStatus != TvanStatus.TCTRejectTbss.GetHashCode()
                //    && x.TvanStatus != TvanStatus.TvanReject.GetHashCode()
                //    && x.Action == (short)TThaiTBao.Huy))
                //{
                //    throw new UserFriendlyException($@"Hóa đơn số {invoicesError.FirstOrDefault().InvoiceNo}, ký hiệu {invoicesError.FirstOrDefault().SerialNo} đã gửi TBSS với tính chất Hủy. Vui lòng kiểm tra lại");
                //}

                item.Id = invoiceHeader.Id;
                item.VerificationCode = invoiceHeader.VerificationCode;
                item.Number = invoiceHeader.Number.Value;
                item.TemplateNo = invoiceHeader.TemplateNo;
                item.SerialNo = invoiceHeader.SerialNo;
                item.InvoiceDate = invoiceHeader.InvoiceDate;
            }

            var invoiceErrors = new List<Invoice04ErrorEntity>();
            var groupCode = DateTime.Now.Ticks;
            var announcementDate = request.AnnouncementDate;

            foreach (var item in invoiceErrs)
            {
                invoiceErrors.Add(new Invoice04ErrorEntity
                {
                    CreationTime = DateTime.Now,
                    CreatorId = userId,
                    TenantId = tenantId,
                    InvoiceHeaderId = item.Id,
                    TemplateNo = item.TemplateNo,
                    SerialNo = item.SerialNo,
                    InvoiceNo = item.InvoiceNo,
                    InvoiceDate = item.InvoiceDate,
                    Index = (short)item.Index,
                    InvoiceType = (short)item.InvoiceType.GetHashCode(),
                    VerificationCode = item.VerificationCode,
                    SignStatus = (short)SignStatus.ChoKy.GetHashCode(),
                    Reason = item.Reason,
                    AnnouncementDate = announcementDate.Date,
                    GroupCode = groupCode,
                    //Action = (short)item.Action.GetHashCode(),
                    BudgetUnitCode = request.BudgetUnitCode,
                    CodeTaxDepartment = request.CodeTaxDepartment,
                    TaxDepartment = request.TaxDepartment,
                    PlaceName = request.PlaceName,
                    Number = item.Number,
                    Emails = request.Emails
                });
            }

            var repoInvoiceError = _appFactory.Repository<Invoice04ErrorEntity, long>();

            await repoInvoiceError.InsertManyAsync(invoiceErrors);

            return new CreateInvoice04ErrorResponseModel { GroupCode = groupCode.ToString() };
        }

        [Authorize(InvoiceErrorPermissions.InvoiceError.Update)]
        [HttpPost(Utilities.ApiUrlBase + "Update")]
        public async Task UpdateAsync(UpdateInvoice04ErrorRequestModel input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            var invoiceErrorByGroupCode = await _repoError.Where(x => x.GroupCode == input.GroupCode && x.TenantId == tenantId).ToListAsync();
            if (invoiceErrorByGroupCode.Any(x => x.SignStatus != (short)SignStatus.ChoKy
                                            && x.SignStatus != (short)SignStatus.KyLoi
                                            && x.SignStatus != (short)SignStatus.DangKy))
            {
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice01Error.CannotUpdateSignedError"]);
            }

            foreach (var item in input.InvoiceErrors)
            {
                var invoiceHeader = await _repoInvoiceHeader.FirstOrDefaultAsync(x => x.TemplateNo == item.TemplateNo
                                                                                    && x.SerialNo == item.SerialNo
                                                                                    && x.InvoiceNo == item.InvoiceNo
                                                                                    && x.InvoiceDate.Date == item.InvoiceDate.Date
                                                                                    && x.TenantId == tenantId);
                if (invoiceHeader == null)
                {
                    throw new UserFriendlyException($@"Hóa đơn: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} không có trong hệ thống. Vui lòng kiểm tra lại.");
                }

                if (invoiceHeader.SignStatus != (short)SignStatus.DaKy)
                {
                    throw new UserFriendlyException($@"Hóa đơn: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} chưa ký. Vui lòng kiểm tra lại");
                }

                if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.DieuChinh)
                {
                    throw new UserFriendlyException($@"Hóa đơn điều chỉnh: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} không thể lập TBSS. Vui lòng kiểm tra lại");
                }

                if (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.XoaBo)
                {
                    throw new UserFriendlyException($@"Hóa đơn xóa bỏ: {item.TemplateNo} - {item.SerialNo} - {item.InvoiceNo} không thể lập TBSS. Vui lòng kiểm tra lại");
                }

                //if (invoiceHeader != null && (invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinh
                //    || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiThayThe
                //    || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinhDinhDanh
                //    || invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiDieuChinhTangGiam
                //    ) && item.Action == TThaiTBao.Huy)
                //{
                //    var message = invoiceHeader.InvoiceStatus == (short)InvoiceStatus.BiThayThe ? "bị thay thế" : "bị điều chỉnh";
                //    throw new UserFriendlyException($@"Hóa đơn số {invoiceHeader.InvoiceNo}, ký hiệu {invoiceHeader.SerialNo} đã {message}. Vui lòng kiểm tra lại.");
                //}

                var invoicesError = await _repoError.Where(x => x.InvoiceHeaderId == invoiceHeader.Id
                                                            && x.SignStatus != (short)SignStatus.Huy
                                                            && x.GroupCode != input.GroupCode
                                                            && x.TenantId == tenantId).ToListAsync();

                if (invoicesError.Any(x => x.SignStatus != SignStatus.DaKy.GetHashCode()))
                {
                    throw new UserFriendlyException($@"Hoá đơn số {invoicesError.FirstOrDefault().InvoiceNo}, Ký hiệu {invoicesError.FirstOrDefault().SerialNo} đã thêm vào TBSS chưa được ký số thành công. Vui lòng kiểm tra lại");
                }

                if (invoicesError.Any(x => x.TvanStatus == TvanStatus.UnSent.GetHashCode() || x.TvanStatus == TvanStatus.SendError.GetHashCode()))
                {
                    throw new UserFriendlyException($@"Hóa đơn số {invoicesError.FirstOrDefault().InvoiceNo}, ký hiệu {invoicesError.FirstOrDefault().SerialNo} đã thêm vào TBSS chưa gửi CQT. Vui lòng kiểm tra lại");
                }

                //if (invoicesError.Any(x => x.TvanStatus != TvanStatus.TCTReject.GetHashCode()
                //    && x.TvanStatus != TvanStatus.TCTRejectTbss.GetHashCode()
                //    && x.TvanStatus != TvanStatus.TvanReject.GetHashCode()
                //    && x.Action == (short)TThaiTBao.Huy))
                //{
                //    throw new UserFriendlyException($@"Hóa đơn số {invoicesError.FirstOrDefault().InvoiceNo}, ký hiệu {invoicesError.FirstOrDefault().SerialNo} đã gửi TBSS với tính chất Hủy. Vui lòng kiểm tra lại");
                //}

                item.Id = invoiceHeader.Id;
                item.VerificationCode = invoiceHeader.VerificationCode;
                item.Number = invoiceHeader.Number.Value;
                item.TemplateNo = invoiceHeader.TemplateNo;
                item.SerialNo = invoiceHeader.SerialNo;
                item.InvoiceDate = invoiceHeader.InvoiceDate;
                item.InvoiceHeaderId = invoiceHeader.Id;
            }

            var updateErrs = new List<Invoice04ErrorEntity>();
            var insertErrs = new List<Invoice04ErrorEntity>();
            var idsUpdate = new List<long>();

            foreach (var item in input.InvoiceErrors)
            {
                var invoiceErr = invoiceErrorByGroupCode.FirstOrDefault(x => x.InvoiceHeaderId == item.InvoiceHeaderId
                                                            && x.TemplateNo == item.TemplateNo
                                                            && x.SerialNo == item.SerialNo
                                                            && x.InvoiceNo == item.InvoiceNo
                                                            && x.TenantId == tenantId);
                // nếu có thì là update
                if (invoiceErr != null)
                {
                    invoiceErr.LastModificationTime = DateTime.Now;
                    invoiceErr.LastModifierId = _appFactory.CurrentUser.Id.Value;
                    //invoiceErr.Action = (short)item.Action.GetHashCode();
                    invoiceErr.Reason = item.Reason;
                    invoiceErr.Emails = input.Emails;
                    invoiceErr.CodeTaxDepartment = input.CodeTaxDepartment;
                    invoiceErr.TaxDepartment = input.TaxDepartment;
                    invoiceErr.AnnouncementDate = input.AnnouncementDate;
                    invoiceErr.PlaceName = input.PlaceName;

                    // bổ sung các trường trong TH cho khách hàng sửa tất cả các thông tin
                    invoiceErr.Index = item.Index;
                    invoiceErr.TemplateNo = item.TemplateNo;
                    invoiceErr.VerificationCode = item.VerificationCode;
                    invoiceErr.SerialNo = item.SerialNo;
                    invoiceErr.InvoiceNo = item.InvoiceNo;
                    invoiceErr.InvoiceDate = item.InvoiceDate;
                    invoiceErr.InvoiceType = (short)item.InvoiceType;

                    updateErrs.Add(invoiceErr);

                    idsUpdate.Add(invoiceErr.Id);
                }
                else // thêm mới
                {
                    var invoiceErrorEntity = new Invoice04ErrorEntity
                    {
                        CreationTime = DateTime.Now,
                        CreatorId = userId,
                        TenantId = tenantId,
                        InvoiceHeaderId = item.Id,
                        TemplateNo = item.TemplateNo,
                        SerialNo = item.SerialNo,
                        InvoiceNo = item.InvoiceNo,
                        Number = item.Number,
                        InvoiceDate = item.InvoiceDate,
                        Index = (short)item.Index,
                        InvoiceType = (short)item.InvoiceType,
                        VerificationCode = item.VerificationCode,
                        SignStatus = (short)SignStatus.ChoKy.GetHashCode(),
                        Reason = item.Reason,
                        AnnouncementDate = input.AnnouncementDate.Date,
                        GroupCode = input.GroupCode,
                        //Action = (short)item.Action.GetHashCode(),
                        BudgetUnitCode = invoiceErrorByGroupCode.FirstOrDefault().BudgetUnitCode,
                        CodeTaxDepartment = invoiceErrorByGroupCode.FirstOrDefault().CodeTaxDepartment,
                        TaxDepartment = input.TaxDepartment,
                        PlaceName = input.PlaceName,
                        Emails = input.Emails
                    };

                    insertErrs.Add(invoiceErrorEntity);
                }
            }

            if (updateErrs.Any())
            {
                await _repoError.UpdateManyAsync(updateErrs);
            }

            if (insertErrs.Any())
            {
                await _repoError.InsertManyAsync(insertErrs);
            }

            if (idsUpdate.Any())
            {
                var idsDel = (invoiceErrorByGroupCode.Select(x => x.Id).ToList()).Except(idsUpdate).ToList();
                if (idsDel.Any())
                {
                    await _repoError.DeleteManyByIdsAsync(idsDel);
                }
            }
        }

        [Authorize(InvoiceErrorPermissions.InvoiceError.Default)]
        [HttpPost(Utilities.ApiUrlBase + "Delete")]
        public async Task DeleteAsync(List<long> groupCodes)
        {
            var invoiceErrors = await _repoError.Where(x => groupCodes.Contains(x.GroupCode) && x.SignStatus != (short)SignStatus.DaKy).ToListAsync();

            if (!invoiceErrors.Any())
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice04Error.CannotDeleteSignedError"]);

            try
            {
                await _repoError.DeleteManyAsync(invoiceErrors.Select(x => x.Id).ToList());
            }
            catch (Exception ex)
            {
                Log.Error(ex, ex.Message);
                throw new UserFriendlyException($@"Xoá không thành công {invoiceErrors.Count()} bản ghi TBSS");
            }

            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();
        }


        /// <summary>
        /// Hủy TBSS
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Authorize(InvoiceErrorPermissions.InvoiceError.Cancel)]
        [HttpPost(Utilities.ApiUrlBase + "cancel")]
        public async Task CancelAsync(long groupCode)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var invoiceErrByGroupCode = await _repoError.Where(x => x.TenantId == tenantId && x.GroupCode == groupCode).ToListAsync();
            if (invoiceErrByGroupCode == null)
            {
                throw new UserFriendlyException("Không tìm thấy TBSS để hủy");
            }

            // Báo lỗi: Hóa đơn đã thực hiện gửi lên CQT trước khi thực hiện yêu cầu Hủy
            if (invoiceErrByGroupCode.Any(x => x.TvanStatus == (short)TvanStatus.Sended)
                || invoiceErrByGroupCode.Any(x => x.TvanStatus == (short)TvanStatus.TCTRejectTbss)
                || invoiceErrByGroupCode.Any(x => x.TvanStatus == (short)TvanStatus.TCTAccept)
                || invoiceErrByGroupCode.Any(x => x.TvanStatus == (short)TvanStatus.TvanReject))
            {
                throw new UserFriendlyException("Lỗi. Thông báo sai sót đã được gửi CQT trước khi thực hiện yêu cầu. Vui lòng kiểm tra lại");
            }

            // Báo lỗi: Hóa đơn đã hủy trước khi thực hiện yêu cầu hủy
            if (invoiceErrByGroupCode.Any(x => x.SignStatus == (short)SignStatus.Huy))
            {
                throw new UserFriendlyException("Lỗi. Thông báo sai sót đã bị hủy trước khi thực hiện yêu cầu. Vui lòng kiểm tra lại");
            }

            if (invoiceErrByGroupCode.Any(x => x.SignStatus == (short)SignStatus.ChoKy)
                && invoiceErrByGroupCode.Any(x => x.TvanStatus == (short)TvanStatus.UnSent))
            {
                throw new UserFriendlyException("Không thể hủy TBSS có trạng thái ký khác đã ký hoặc trạng CQT khác chưa gửi TVAN/Gửi TVAN lỗi");
            }

            // update TBSS về trạng thái Hủy
            foreach (var item in invoiceErrByGroupCode)
            {
                item.SignStatus = (short)SignStatus.Huy;
                item.LastModificationTime = DateTime.Now;
                item.LastModifierId = _appFactory.CurrentUser.Id.Value;
            }

            await _repoError.UpdateManyAsync(invoiceErrByGroupCode);
        }

        private GetInvoice04ErrorResponseModel MapToDto(Invoice04ErrorEntity entity)
        {
            return new GetInvoice04ErrorResponseModel
            {
                Id = entity.Id,
                GroupCode = entity.GroupCode.ToString(),
                Index = entity.Index,
                CreationTime = entity.CreationTime,
                CreatorId = entity.CreatorId,
                AnnouncementDate = entity.AnnouncementDate,
                VerificationCode = entity.VerificationCode,
                InvoiceHeaderId = entity.InvoiceHeaderId,
                TemplateNo = entity.TemplateNo,
                SerialNo = entity.SerialNo,
                InvoiceNo = entity.InvoiceNo,
                InvoiceDate = entity.InvoiceDate,
                InvoiceType = entity.InvoiceType,
                Action = entity.Action,
                Reason = entity.Reason,
                SignStatus = entity.SignStatus,
                SellerFullNameSigned = entity.SellerFullNameSigned,
                SellerSignedId = entity.SellerSignedId,
                SellerSignedTime = entity.SellerSignedTime,
                TvanStatus = entity.TvanStatus,
                LastModificationTime = entity.LastModificationTime,
                LastModifierId = entity.LastModifierId,
                TenantId = entity.TenantId,
                CodeTaxDepartment = entity.CodeTaxDepartment,
                TaxDepartment = entity.TaxDepartment,
                PlaceName = entity.PlaceName,
                BudgetUnitCode = entity.BudgetUnitCode,
                Emails = entity.Emails,
                MessageCode = entity.MessageCode
            };
        }
    }
}
