using Core.Caching;
using Core.SettingManagement;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.Models;
using Dapper;
using Microsoft.Extensions.Caching.Distributed;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;
using static VnisCore.Invoice04.Application.Factories.Constants.CommonInvoice04Const;

namespace VnisCore.Invoice04.Application.Factories.Services
{
    public interface IRegistrationInvoiceService
    {
        Task CreateTemplateBySettingAsync(Guid tenantId, Guid userId, short templateNo, string serialNo);
    }

    public class RegistrationInvoiceService : IRegistrationInvoiceService
    {
        private readonly IAppFactory _appFactory;

        public RegistrationInvoiceService(
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task CreateTemplateBySettingAsync(Guid tenantId, Guid userId, short templateNo, string serialNo)
        {
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var rawUserId = OracleExtension.ConvertGuidToRaw(userId);
            var chartJump = serialNo.Substring(serialNo.Length - 2, 1);
            var serialNoMissJump = serialNo.Substring(0, serialNo.Length - 2);
            var keyTemplateSerial = $"{templateNo}{serialNoMissJump}x";

            var setting = await GetSettingAsync(tenantId, rawTenantId, GroupSettingKey.RegistrationInvoice.ToString(), SettingKey.AutoRegistration.ToString());
            if (setting == null)
                return;

            var extraProperties = setting.ExtraProperties;

            var setTemplateSerials = JsonConvert.DeserializeObject<List<RegistrationInvoiceSettingModel>>(extraProperties["key"].ToString());
            var configs = setTemplateSerials.First(x => x.Key == keyTemplateSerial).Value;

            // TODO: Gộp query lại sau
            var invoiceTemplate = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<InvoiceTemplateEntity>($@"
                                                    SELECT *
                                                    FROM ""InvoiceTemplate"" 
                                                    WHERE ""TenantId"" = '{rawTenantId}' AND ""TemplateNo"" = {templateNo} AND ""SerialNo"" = '{serialNo}' AND ""IsDeleted"" = 0");

            if (invoiceTemplate == null)
                return;

            var monitor = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<MonitorInvoiceTemplateEntity>($@"
                                                    SELECT * 
                                                    FROM ""MonitorInvoiceTemplate"" 
                                                    WHERE ""Id"" = {invoiceTemplate.Id}
                                                    FETCH FIRST 1 ROWS ONLY");

            var remainingInvoiceThreshold = int.Parse(configs.First(x => x.Label == "remainingInvoiceThreshold").Value);
            if ((monitor.EndNumber - monitor.CurrentNumber) > remainingInvoiceThreshold)
                return;

            var registrationHeader = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<NewRegistrationHeaderEntity>($@"
                                                    SELECT * 
                                                    FROM ""NewRegistrationHeader"" 
                                                    WHERE ""TenantId"" = '{rawTenantId}' AND ""InvoiceTypes"" IS NOT NULL AND ""IsDeleted"" = 0 
                                                    AND ""Status"" = {RegistrationTvanStatus.GDTAccepted.GetHashCode()} AND ""GDTResponseTime"" IS NOT NULL
                                                    ORDER BY ""GDTResponseTime"" DESC
                                                    FETCH FIRST ROW ONLY");

            if (registrationHeader == null)
                return;

            await CreateInvoiceTemplateAsync(rawTenantId, rawUserId, chartJump, serialNo, configs, invoiceTemplate, registrationHeader);
        }

        private async Task CreateInvoiceTemplateAsync(
            string rawTenantId,
            string rawUserId,
            string chartJump,
            string serialNoMissJump,
            List<RegistrationInvoiceSettingModel.PropertyModel> settings,
            InvoiceTemplateEntity invoiceTemplate,
            NewRegistrationHeaderEntity registrationHeader)
        {
            var jump = settings.First(x => x.Label == StaticData.JumpRegistrationInvoice).Value;
            var i = 0;
            var dictJump = (String.IsNullOrEmpty(jump)) ? StaticData.JumpAlphaBetaDefaults : jump.Split(",").ToDictionary(x => x, y => i++);
            var nextJump = dictJump.FirstOrDefault(x => x.Value == dictJump[chartJump] + 1).Key;
            var newSerialNo = $"{serialNoMissJump}{chartJump}";

            var templateId = await GetSEQsNextVal(SequenceName.InvoiceTemplate);

            var query = RenderRawSqlRegistrationInvoiceWithJump(templateId, rawTenantId, rawUserId, newSerialNo, 1, StaticData.MaxInvoiceNo, invoiceTemplate, registrationHeader);

            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);
        }

        private string RenderRawSqlRegistrationInvoiceWithJump(
            long templateId,
            string rawTenantId,
            string rawUserId,
            string newSerialNo,
            int startNumber,
            int endNumber,
            InvoiceTemplateEntity invoiceTemplate,
            NewRegistrationHeaderEntity registrationHeader)
        {
            var sql = new StringBuilder($@"BEGIN ");

            // tạo InvoiceTemplate với serialNo theo bước nhảy
            sql.Append($@" INSERT INTO ""InvoiceTemplate"" (
                                ""Id"",
                                ""Type"",
                                ""Name"",
                                ""TemplateNo"",
                                ""SerialNo"",
                                ""FileId"",
                                ""Width"",
                                ""Height"",
                                ""MarginTop"",
                                ""MarginRight"",
                                ""MarginBottom"",
                                ""MarginLeft"",
                                ""IsLocked"",
                                ""CreatorId"",
                                ""TenantId""
                            ) VALUES (
                                {templateId},
                                {invoiceTemplate.Type},
                                N'{invoiceTemplate.Name}',
                                {invoiceTemplate.TemplateNo},
                                '{newSerialNo}',
                                {(invoiceTemplate.FileId.HasValue ? invoiceTemplate.FileId.Value : null)},
                                {invoiceTemplate.Width},
                                {invoiceTemplate.Height},
                                {invoiceTemplate.MarginTop},
                                {invoiceTemplate.MarginRight},
                                {invoiceTemplate.MarginBottom},
                                {invoiceTemplate.MarginLeft},
                                0,
                                '{rawUserId}', 
                                '{rawTenantId}'
                            ); ");

            //insert or update monitor
            sql.Append($@" INSERT INTO ""MonitorInvoiceTemplate"" (
                                ""Id"",
                                ""TenantId"",
                                ""CurrentNumber"",
                                ""LastDocumentDate"",
                                ""RegistrationHeaderId"",
                                ""RegistrationDetailId"",
                                ""ActiveDate"",
                                ""StartNumber"",
                                ""EndNumber"",
                                ""ConcurrencyStamp"") 
                                VALUES (
                                {templateId},
                                '{rawTenantId}',
                                0,
                                NULL,
                                {registrationHeader.Id},
                                NULL,
                                {registrationHeader.GDTResponseTime.Value.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))},
                                {startNumber},
                                {endNumber},
                                {DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}
                            ); ");


            sql.Append($@" END; ");

            return sql.ToString();
        }

        public async Task<long> GetSEQsNextVal(string sequenceName)
        {
            var sql = $@"   SELECT ""{sequenceName}"".NEXTVAL
                            FROM(
                               SELECT level
                               FROM dual
                            ) ";

            var result = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<long>(sql);

            return result;
        }


        private async Task<Setting> GetSettingAsync(Guid tenantId, string rawTenantId, string groupCode, string code)
        {
            var cache = _appFactory.GetServiceDependency<IDistributedCache<Setting>>();
            var cacheKey = SettingCacheItem.CalculateCacheKey($"{tenantId}:{code}", null, null);

            var setting = await cache.GetAsync(cacheKey);
            if (setting != null)
                return setting;

            setting = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<Setting>($@"
                                                    SELECT * 
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""GroupCode"" = '{groupCode}' AND ""Code"" = '{code}' 
                                                    AND ""TenantId"" = '{rawTenantId}' And ""IsDeleted"" = 0");

            if (setting == null)
                return null;

            await cache.SetAsync(cacheKey, setting, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
            });

            return setting;
        }
    }
}
