using Core.Shared.Services;
using VnisCore.Invoice04.Application.Invoice04Document.Dto;

namespace VnisCore.Invoice04.Application.Invoice04Document
{
    public interface IInvoice04DocumentInfoService :
        ICrudAppService< //Defines CRUD methods
            Invoice04DocumentInfoDto, //Used to show books
            long, //Primary key of the Store entity
            Invoice04DocumentInfoPagedRequestDto, //Used for paging/sorting on getting a list of books
            Invoice04DocumentInfoDto, //Used to create a new Store
            Invoice04DocumentInfoDto> //Used to update a Store
    {

    }
}