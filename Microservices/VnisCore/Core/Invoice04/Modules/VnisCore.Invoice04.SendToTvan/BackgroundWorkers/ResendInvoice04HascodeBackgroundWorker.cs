using Core.BackgroundWorkers;
using Core.Threading;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

using System.Threading.Tasks;

using VnisCore.Invoice04.SendToTvan.Business;

namespace VnisCore.Invoice04.SendToTvan.BackgroundWorkers
{
    public class ResendInvoice04HascodeBackgroundWorker : AsyncPeriodicBackgroundWorkerBase
    {
        private readonly IConfiguration _configuration;
        public ResendInvoice04HascodeBackgroundWorker(
            AbpAsyncTimer timer,
            IConfiguration configuration,
            IServiceScopeFactory serviceScopeFactory) : base(timer, serviceScopeFactory)
        {
            _configuration = configuration;
            Timer.Period = 100; //1 s
            int.TryParse(_configuration.GetSection("Settings:TimePeriodResendInvoiceError").Value, out var period);
            if (period > 0)
                timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            int.TryParse(_configuration.GetSection("Settings:IsResendInvoicesError").Value, out var isResendInvoicesError);
            if (isResendInvoicesError > 0)
            {
                await workerContext
                .ServiceProvider
                .GetRequiredService<IResendInvoiceHasCodeToTvanBusiness>()
                .ResendInvoiceAsync();
            }    
        }
    }
}
