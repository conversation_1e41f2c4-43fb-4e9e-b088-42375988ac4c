using Core.EventBus;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.ImportTicket.MessageEventData
{
    [EventName("ticket.importticketresult")]
    public class ImportTicketEventResultData
    {
        public ImportTicketEventResultData()
        {

        }

        public ImportTicketEventResultData(long id)
        {
            InvoiceId = id;
        }

        public long InvoiceId { get; set; }
    }
}