using Core.EventBus;
using Core.Shared.Constants;
using System;
using VnisCore.Ticket.Application.Ticket.Dto;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.DeleteTicket.MessageEventData
{
    [EventName("ticket.deleteticket")]
    public class DeleteTicketHeaderEventSendData : DeleteTicketHeaderDto
    {
        public DeleteTicketHeaderEventSendData()
        {

        }

        public DeleteTicketHeaderEventSendData(DeleteTicketHeaderDto item)
        {
            Id = item.Id;
            DocumentReason = item.DocumentReason;
            DocumentDate = item.DocumentDate;
            DocumentNo = item.DocumentNo;
            IdFileDocument = item.IdFileDocument;
            IsUploadFile = item.IsUploadFile;
        }

        public InvoiceSource Resource { get; set; }

        //Context
        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string UserFullName { get; set; }
    }
}