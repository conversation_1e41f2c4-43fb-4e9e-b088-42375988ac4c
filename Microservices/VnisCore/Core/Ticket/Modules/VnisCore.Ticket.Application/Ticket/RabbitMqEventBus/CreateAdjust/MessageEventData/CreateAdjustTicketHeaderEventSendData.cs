using Core.EventBus;
using Core.Shared.Constants;
using System;
using VnisCore.Ticket.Application.Ticket.Dto;
using VnisCore.Ticket.Application.Ticket.Models;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CreateReplaceTicket.MessageEventData
{
    [EventName("ticket.createadjustticket")]
    public class CreateAdjustTicketHeaderEventSendData : CreateAdjustTicketHeaderDto
    {
        public CreateAdjustTicketHeaderEventSendData()
        {

        }

        public CreateAdjustTicketHeaderEventSendData(CreateAdjustTicketHeaderDto item)
        {
            Id = item.Id;
            TemplateNo = item.TemplateNo;
            SerialNo = item.SerialNo;
            InvoiceReferenceId = item.InvoiceReferenceId;
            ErpId = item.ErpId;
            CreatorErp = item.CreatorErp;
            InvoiceDate = item.InvoiceDate;
            Note = item.Note;
            Content = item.Content;
            PaymentMethod = item.PaymentMethod;
            ToCurrency = item.ToCurrency;
            ExchangeRate = item.ExchangeRate;
            TotalAmount = item.TotalAmount;
            TotalVatAmount = item.TotalVatAmount;
            TotalPaymentAmount = item.TotalPaymentAmount;
            TotalDiscountAmountBeforeTax = item.TotalDiscountAmountBeforeTax;
            TotalDiscountPercentAfterTax = item.TotalDiscountPercentAfterTax;
            TotalDiscountAmountAfterTax = item.TotalDiscountAmountAfterTax;
            BuyerCode = item.BuyerCode;
            BuyerEmail = item.BuyerEmail;
            BuyerFullName = item.BuyerFullName;
            BuyerLegalName = item.BuyerLegalName;
            BuyerTaxCode = item.BuyerTaxCode;
            BuyerAddressLine = item.BuyerAddressLine;
            BuyerDistrictName = item.BuyerDistrictName;
            BuyerCityName = item.BuyerCityName;
            BuyerCountryCode = item.BuyerCountryCode;
            BuyerPhoneNumber = item.BuyerPhoneNumber;
            BuyerFaxNumber = item.BuyerFaxNumber;
            BuyerBankAccount = item.BuyerBankAccount;
            BuyerBankName = item.BuyerBankName;

            DocumentReason = item.DocumentReason;
            DocumentDate = item.DocumentDate;
            DocumentNo = item.DocumentNo;
            IdFileDocument = item.IdFileDocument;
            IsUploadFile = item.IsUploadFile;

            InvoiceDetails = item.InvoiceDetails;
            InvoiceHeaderExtras = item.InvoiceHeaderExtras;
            InvoiceTaxBreakdowns = item.InvoiceTaxBreakdowns;

            InfosNeedCreateInvoice = new InfosNeedCreateTicketModel();

            StoreCode = item.StoreCode;
            StoreName = item.StoreName;
            BudgetUnitCode = item.BudgetUnitCode;
            BuyerIDNumber = item.BuyerIDNumber;
            BuyerPassportNumber = item.BuyerPassportNumber;
        }

        public InvoiceSource Resource { get; set; }

        //Context
        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string UserFullName { get; set; }
        public string Token { get; set; }
        public InfosNeedCreateTicketModel InfosNeedCreateInvoice { get; set; }
    }
}