using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Shared.Invoice.MessageEventsData.Invoices;
using System.Threading.Tasks;
using VnisCore.Ticket.Application.Factories.Services;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.CreateTicket.ReceivedEventHandler
{
    public class CreateInvoiceTemplateAutoReceivedEventHandler : IDistributedEventHandler<CreateInvoiceTemplateAutoEventResultData>, ITransientDependency
    {
        private readonly RegistrationInvoiceService _registrationInvoiceService;
        public CreateInvoiceTemplateAutoReceivedEventHandler(RegistrationInvoiceService registrationInvoiceService)
        {
            _registrationInvoiceService = registrationInvoiceService;
        }

        public async Task HandleEventAsync(CreateInvoiceTemplateAutoEventResultData eventData)
        {
            await _registrationInvoiceService.CreateTemplateBySettingAsync(eventData.TenantId, eventData.UserId, eventData.TemplateNo, eventData.SerialNo);
        }
    }
}
