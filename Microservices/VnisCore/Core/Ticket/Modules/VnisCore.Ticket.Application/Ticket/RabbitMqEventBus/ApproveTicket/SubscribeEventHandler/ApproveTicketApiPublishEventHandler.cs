using Core;
using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.StatisticSummary;
using Core.Shared.Messages;
using Dapper;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.Factories.Repositories;
using VnisCore.Ticket.Application.Factories.Services;
using VnisCore.Ticket.Application.Ticket.Models;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.ApproveTicket.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.AutoSignServer;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.TicketLog.MessageEventData;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.ApproveTicket.SubscribeEventHandler
{
    public class ApproveTicketApiPublishEventHandler : IDistributedEventHandler<ApproveTicketHeaderApiEventSendData>, ITransientDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<ApproveTicketApiPublishEventHandler> _logger;
        private readonly IAppFactory _appFactory;
        private readonly IElasticService _elasticService;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;

        public ApproveTicketApiPublishEventHandler(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ILogger<ApproveTicketApiPublishEventHandler> logger,
            IAppFactory appFactory,
            IElasticService elasticService,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService)
        {
            _localizer = localizer;
            _logger = logger;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _elasticService = elasticService;
        }

        public async Task HandleEventAsync(ApproveTicketHeaderApiEventSendData eventData)
        {
            try
            {
                var ticketHeaderService = _appFactory.GetServiceDependency<IInvoiceHeaderRepository<TicketHeaderEntity>>();
                var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
                var invoice = await ticketHeaderService.GetInvoiceHeaderRawAsync(eventData.TenantId, eventData.Id);

                if (invoice == null)
                    throw new UserFriendlyException(_localizer["Vnis.BE.Sign.Invoice.InvoiceIsEmptyy"]);

                var approveStatus = invoice.ApproveStatus;
                var approveCancelStatus = invoice.ApproveCancelStatus;
                var approveDeleteStatus = invoice.ApproveDeleteStatus;
                var invoiceStatus = invoice.InvoiceStatus;

                // nếu hóa đơn đã ký => duyệt xóa bỏ
                if (invoice.SignStatus == SignStatus.DaKy.GetHashCode() && invoice.ApproveDeleteStatus == ApproveStatus.ChoDuyet.GetHashCode())
                {
                    var queryHeader = $"Update \"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}\" " +
                                      $"Set \"ApproveDeleteStatus\" = :ApproveDeleteStatus, \"ApprovedDeleteTime\" = :ApprovedDeleteTime, \"ApprovedDeleteId\" = :ApprovedDeleteId, \"FullNameApproverDelete\" = :FullNameApproverDelete, \"InvoiceStatus\" = :InvoiceStatus, \"InvoiceDeleteSource\" = :InvoiceDeleteSource " +
                                      $"Where \"Id\" = :Id";

                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                    {
                        Id = invoice.Id,
                        ApprovedDeleteTime = DateTime.Now,
                        ApprovedDeleteId = OracleExtension.ConvertGuidToRaw(eventData.UserId),
                        FullNameApproverDelete = eventData.UserName,
                        ApproveDeleteStatus = (short)ApproveStatus.DaDuyet,
                        InvoiceStatus = (short)InvoiceStatus.XoaBo,
                        InvoiceDeleteSource = (short)InvoiceDeleteSource.Form,
                    });

                    approveDeleteStatus = (short)ApproveStatus.DaDuyet;
                    invoiceStatus = (short)InvoiceStatus.XoaBo;

                    //update lại ngày hóa đơn cuối cùng
                    await _invoiceService.UpdateLastInvoiceDateCancelDeleteAsync(eventData.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);
                }
                if (invoice.SignStatus != SignStatus.DaKy.GetHashCode())
                {
                    // nếu hóa đơn chưa ký ưu tiên check chờ duyệt của xóa hủy trước
                    if (invoice.ApproveCancelStatus == ApproveStatus.ChoDuyet.GetHashCode())
                    {
                        var queryHeader = $"Update \"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}\" " +
                                          $"Set \"ApprovedCancelTime\" = :ApprovedCancelTime, \"ApprovedCancelId\" = :ApprovedCancelId, \"FullNameApproverCancel\" = :FullNameApproverCancel, \"ApproveCancelStatus\" = :ApproveCancelStatus, \"InvoiceStatus\" = :InvoiceStatus, \"InvoiceDeleteSource\" = :InvoiceDeleteSource " +
                                          $"Where \"Id\" = :Id";

                        await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                        {
                            Id = invoice.Id,
                            ApprovedCancelTime = DateTime.Now,
                            ApprovedCancelId = OracleExtension.ConvertGuidToRaw(eventData.UserId),
                            FullNameApproverCancel = eventData.UserName,
                            ApproveCancelStatus = (short)ApproveStatus.DaDuyet,
                            InvoiceStatus = (short)InvoiceStatus.XoaHuy,
                            InvoiceDeleteSource = (short)InvoiceDeleteSource.Form,
                        });

                        approveCancelStatus = (short)ApproveStatus.DaDuyet;
                        invoiceStatus = (short)InvoiceStatus.XoaHuy;

                        //update lại ngày hóa đơn cuối cùng
                        await _invoiceService.UpdateLastInvoiceDateCancelDeleteAsync(eventData.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);
                    }
                    // nếu hóa đơn chưa ký và ko phải chờ duyệt xóa hủy => chờ duyệt ký
                    else if (invoice.ApproveStatus == ApproveStatus.ChoDuyet.GetHashCode())
                    {
                        var queryHeader = $"Update \"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}\" " +
                                          $"Set \"ApproveStatus\" = :ApproveStatus, \"ApprovedId\" = :ApprovedId, \"ApprovedTime\" = :ApprovedTime, \"FullNameApprover\" = :FullNameApprover " +
                                          $"Where \"Id\" = :Id";

                        await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync<long>(queryHeader, new
                        {
                            Id = invoice.Id,
                            ApproveStatus = (short)ApproveStatus.DaDuyet.GetHashCode(),
                            ApprovedId = OracleExtension.ConvertGuidToRaw(eventData.UserId),
                            ApprovedTime = DateTime.Now,
                            FullNameApprover = eventData.UserFullName,
                        });

                        approveStatus = (short)ApproveStatus.DaDuyet.GetHashCode();
                    }
                }

                var result = new InvoiceCommandResponseModel
                {
                    //ProcessCode = message.ProcessCode.Value,
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    //Type = message.Type,
                    UserId = eventData.UserId,
                    UserFullName = eventData.UserFullName,
                    UserName = eventData.UserName,
                    Method = HubMethod.ApproveInvoice,
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                    ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(approveStatus),
                    ApproveCancelStatus = EnumExtension.ToEnum<ApproveStatus>(approveCancelStatus),
                    ApproveDeleteStatus = EnumExtension.ToEnum<ApproveStatus>(approveDeleteStatus),
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoiceStatus),
                    InvoiceNo = invoice.InvoiceNo,
                    Number = invoice.Number,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo,
                    Resource = InvoiceSource.Form,
                    State = InvoiceActionState.Approve,
                    ActionLogInvoice = ActionLogInvoice.Approve,
                    Action = InvoiceAction.Approve,
                    //ConnectionId = message.ConnectionId,
                    InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                    {
                        OldInvoiceDate = invoice.InvoiceDate,
                        OldTotalAmount = invoice.TotalAmount,
                        OldTotalPaymentAmount = invoice.TotalPaymentAmount,
                        OldTotalVatAmount = invoice.TotalVatAmount
                    }
                };
                // publish dashboard
                await distributedEventBus.PublishAsync(new StatisticSummaryApiEventResultData(result));

                // sync elastic 
                //await _elasticService.SyncTicketElastic(eventData.TenantId, eventData.Id);

                await distributedEventBus.PublishAsync(new ApproveTicketHeaderApiEventResultData(eventData));

                await distributedEventBus.PublishAsync(new ApproveAndSignTicketEventSendData
                {
                    Id = eventData.Id,
                    Token = eventData.Token,
                    TenantId = eventData.TenantId
                });

                //Insert Log
                await distributedEventBus.PublishAsync(new TicketLogEventSendData(new TicketLogModel
                {
                    InvoiceHeaderId = invoice.Id,
                    TenantId = eventData.TenantId,
                    UserId = eventData.UserId,
                    UserName = eventData.UserName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(invoice.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.Approve.GetHashCode(),
                    Partition = long.Parse(invoice.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent AprroveAsync Error: {ex.Message}");
                _logger.LogError($"HandleEvent AprroveAsync Error: {ex.InnerException}");

                // Publish SignalR
            }
        }
    }
}
