using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.StatisticSummary;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Messages;

using Dapper;

using Microsoft.Extensions.Logging;

using System;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.Factories.Services;
using VnisCore.Ticket.Application.Ticket.Models;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.AutoSignServer;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.TicketLog.MessageEventData;
using VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.UpdateTicket.MessageEventData;

namespace VnisCore.Ticket.Application.Ticket.RabbitMqEventBus.UpdateTicket.SubscribeEventHandler
{
    public class UpdateTicketHeaderPublishEventHandler : IDistributedEventHandler<UpdateTicketHeaderEventSendData>, ITransientDependency
    {
        private readonly ILogger<UpdateTicketHeaderPublishEventHandler> _logger;
        private readonly IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> _invoiceService;
        private readonly Factories.Services.ITicketService _ticketService;
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IElasticService _elasticService;

        public UpdateTicketHeaderPublishEventHandler(
            ILogger<UpdateTicketHeaderPublishEventHandler> logger,
            IInvoiceService<TicketHeaderEntity, TicketHeaderFieldEntity, TicketDetailFieldEntity> invoiceService,
            Factories.Services.ITicketService ticketService,
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus,
            IElasticService elasticService)
        {
            _logger = logger;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _ticketService = ticketService;
            _elasticService = elasticService;
            _distributedEventBus = distributedEventBus;
        }

        public async Task HandleEventAsync(UpdateTicketHeaderEventSendData eventData)
        {
            try
            {
                var query = await _ticketService.GenerateDrawUpdateInvoice(eventData);

                var data = await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                //nếu có số hóa đơn thì mới update lại ngày hóa đơn cuối cùng
                var invoice = eventData.InfosNeedUpdateInvoice.Invoice;
                if (invoice.Number.HasValue)
                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);

                var result = new InvoiceCommandResponseModel
                {
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    //Type = eventData.Type,
                    UserId = eventData.UserId,
                    UserFullName = eventData.UserFullName,
                    UserName = eventData.UserName,
                    Method = HubMethod.UpdateInvoice,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                    ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveStatus),
                    ApproveCancelStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveCancelStatus),
                    ApproveDeleteStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveDeleteStatus),
                    Number = invoice.Number,
                    InvoiceNo = invoice.InvoiceNo,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo,
                    Resource = eventData.Resource,
                    State = InvoiceActionState.UpdateRoot,
                    ActionLogInvoice = ActionLogInvoice.Update,
                    Action = InvoiceAction.UpdateRoot,
                    ActionAt = DateTime.Now,
                    ActionAtUtc = DateTime.UtcNow,
                    //ConnectionId = message.ConnectionId,
                    InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                    {
                        NewInvoiceDate = eventData.InvoiceDate,
                        NewTotalAmount = eventData.TotalAmount,
                        NewTotalPaymentAmount = eventData.TotalPaymentAmount,
                        NewTotalVatAmount = eventData.TotalVatAmount,
                        OldInvoiceDate = eventData.InfosNeedUpdateInvoice.Invoice.InvoiceDate,
                        OldTotalAmount = eventData.InfosNeedUpdateInvoice.Invoice.TotalAmount,
                        OldTotalPaymentAmount = eventData.InfosNeedUpdateInvoice.Invoice.TotalPaymentAmount,
                        OldTotalVatAmount = eventData.InfosNeedUpdateInvoice.Invoice.TotalVatAmount
                    }
                };
                // publish dashboard
                await _distributedEventBus.PublishAsync(new StatisticSummaryApiEventResultData(result));

                //Sync elastic
                //await _elasticService.SyncTicketElastic(eventData.TenantId, eventData.Id);

                // 
                await _distributedEventBus.PublishAsync(new UpdateTicketHeaderEventResultData(eventData));

                //sync Customer
                await _distributedEventBus.PublishAsync(new SyncCustomerEventSendData(new SyncCustomerRequestModel
                {
                    IdInvoice = eventData.Id,
                    TenantId = eventData.TenantId,
                    Type = VnisType._05TVDT,
                }));

                await _distributedEventBus.PublishAsync(new SyncUnitEventSendData(new SyncUnitRequestModel
                {
                    IdInvoice = eventData.Id,
                    TenantId = eventData.TenantId,
                    Type = VnisType._05TVDT,
                }));

                //Insert Log
                await _distributedEventBus.PublishAsync(new TicketLogEventSendData(new TicketLogModel
                {
                    InvoiceHeaderId = eventData.Id,
                    TenantId = eventData.TenantId,
                    UserId = eventData.UserId,
                    UserName = eventData.UserName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(eventData.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.Update.GetHashCode(),
                    Partition = long.Parse(eventData.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));

                //// check có quy trình duyệt hay không
                //// nếu không có quy trình duyệt và có cấu hình ký tự động bằng signserver thì
                //// sau khi tạo hóa đơn xong thì ký luôn
                await _distributedEventBus.PublishAsync(new AutoSignTicketEventSendData
                {
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    Token = eventData.Token
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent UpdateAsync Error: {ex.Message}");
                _logger.LogError($"HandleEvent UpdateAsync Error: {ex.InnerException}");
            }
        }
    }
}