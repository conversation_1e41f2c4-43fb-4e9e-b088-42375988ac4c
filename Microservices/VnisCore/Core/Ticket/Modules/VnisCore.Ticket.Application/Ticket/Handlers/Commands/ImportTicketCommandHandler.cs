using Core;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Services;

using MediatR;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Ticket.Application.Ticket.Models.Requests.Commands;
using VnisCore.Ticket.Application.Ticket.Services;

namespace VnisCore.Ticket.Application.Ticket.Handlers.Commands
{
    public class ImportTicketCommandHandler : AsyncRequestHandler<ImportTicketRequestModel>
    {
        private readonly IAppFactory _appFactory;
        private readonly IServiceProvider _serviceProvider;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly ILogger<ImportTicketCommandHandler> _logger;
        private readonly ISettingService _settingService;

        public ImportTicketCommandHandler(
            IFileService fileService,
             IServiceProvider serviceProvider,
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IDistributedEventBus distributedEventBus,
            ILogger<ImportTicketCommandHandler> logger,
            ISettingService settingService)
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _settingService = settingService;
        }

        protected override async Task Handle(ImportTicketRequestModel request, CancellationToken cancellationToken)
        {
            var userId = _appFactory.CurrentUser.Id.Value;
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            if (request.File.Length == 0)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.ImportExcel.FileIsRequired"]);

            if (request.File.Length > 5 * 1024 * 1024)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.ImportExcel.FileLength"]);

            var parameters = new Dictionary<string, string>();
            if (!string.IsNullOrEmpty(request.Parameters))
                parameters = JsonConvert.DeserializeObject<Dictionary<string, string>>(request.Parameters);

            //check thông tin khách hàng có thay đổi với tvan
            //var repoSetting = _coreUoW.GetRepository<ISettingRepository>();
            //var settingServiceImport = await repoSetting.GetByKeyAsync(tenantCode, SettingKey.ImportExcel01ServiceName.ToString());
            //if (settingServiceImport == null || settingServiceImport.Value == null)
            //    throw new Exception("Chưa có cấu hình service import hóa đơn 01GTKT");

            var fileName = $"ImportExcel_{DateTime.Now:yyyyMMdd}_{Guid.NewGuid()}{Path.GetExtension(request.File.FileName)}";
            var filePathMinio = $"Temps/{tenantId}/{fileName}";
            byte[] bytes = new byte[0];
            using (var stream = new MemoryStream())
            {
                request.File.CopyTo(stream);
                bytes = stream.ToArray();
            }

            if (bytes.Length == 0)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.ImportExcel.FileDataIsNull"]);

            //upfile lên minio
            //await _fileService.UploadAsync(filePathMinio, bytes);

            using var scope = _serviceProvider.CreateScope();
            var importServices = scope.ServiceProvider.GetServices<IImportTicketService>();

            var repoTicketHeader = _appFactory.Repository<TicketHeaderEntity, long>();

            //lấy cấu hình tên service import
            var repoSetting = scope.ServiceProvider.GetService<ISettingService>();
            var settingServiceImport = await repoSetting.GetByCodeAsync(tenantId, SettingKey.ImportExcelTicketServiceName.ToString());

            var importService = importServices.FirstOrDefault();
            // lấy service theo cấu hình
            if (settingServiceImport == null || string.IsNullOrWhiteSpace(settingServiceImport.Value) || settingServiceImport.Value == "#")//giá trị mặc định
                importService = importServices.FirstOrDefault(x => x.GetType().Name == typeof(ImportTicketService).Name);
            else if (!string.IsNullOrEmpty(request.Service))
                importService = importServices.FirstOrDefault(x => x.GetType().Name == request.Service);
            else
                importService = importServices.FirstOrDefault(x => x.GetType().Name == settingServiceImport.Value);

            if (importService == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.Ticket.ImportService"]);

            //lấy kết quả đọc file excel về để đẩy vào queue tạo hóa đơn
            var invoiceHeaders = await importService.ParseInvoicesAsync(tenantId, userId, _appFactory.CurrentUser.Name, _appFactory.CurrentUser.UserName, _appFactory.CurrentUser.CashierCode, bytes);

            // checklicense
            // check cấu hình bỏ qua check license
            var SkipCheckLicense = Enum.GetName(typeof(SettingKey), SettingKey.SkipCheckLicenseForCreateInvoice);
            var setting = await _settingService.GetByCodeAsync(_appFactory.CurrentTenant.Id.Value, SkipCheckLicense);

            if (setting != null && setting.Value == "0") // không nên hardcode như này
            {
                var licenseSharedService = scope.ServiceProvider.GetService<ILicenseSharedService>();
                var cacheLicense = await licenseSharedService?.GetAsync(tenantId)!;
                if (cacheLicense.LicenseKey == null)
                    throw new UserFriendlyException("License hết hạn hoặc chưa đăng ký license");

                if (!licenseSharedService.CheckUseLicense(cacheLicense, invoiceHeaders.Count))
                    throw new UserFriendlyException("License không đủ để tạo hết hóa đơn import");
            }

            //kiểm tra ngày hóa đơn
            var validateInvoiceDateMess = await importService.ValidateInvoiceDateAsync(invoiceHeaders, tenantId);
            if (!string.IsNullOrEmpty(validateInvoiceDateMess))
                //throw new UserFriendlyException($"Ngày hóa đơn của hóa đơn {validateInvoiceDateMess} không đúng");
                throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.InvoiceDateIncorrect", new string[] { validateInvoiceDateMess }]);


            _logger.LogDebug($"Đọc và validate file excel thành công. Bắt đầu tạo hóa đơn : {invoiceHeaders.Count} message");

            await importService.CreateInvoicesAsync(invoiceHeaders, string.Empty);
            _logger.LogDebug($"Đã tạo hóa đơn : {invoiceHeaders.Count} message");
        }
    }
}
