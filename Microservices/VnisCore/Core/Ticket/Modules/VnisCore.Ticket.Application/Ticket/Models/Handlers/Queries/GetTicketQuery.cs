using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Ticket.Application.Factories.Models;
using VnisCore.Ticket.Application.Ticket.Models.Responses.Queries;

namespace VnisCore.Ticket.Application.Ticket.Models.Handlers.Queries
{
    public class GetTicketQuery : InvoiceCommandRequestModel, IRequest<GetTicketResponse>
    {
    }
}
