using System;

namespace VnisCore.Ticket.Application.Ticket.Models.Responses.Queries
{
    public class ViewReferenceInvoiceModel
    {
        public long Id { get; set; }

        public string TemplateNo { get; set; }

        public string SerialNo { get; set; }

        public string InvoiceNo { get; set; }

        public short InvoiceStatus { get; set; }

        public short SignStatus { get; set; }

        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn để ký
        /// </summary>
        public short ApproveStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn xóa hủy
        /// </summary>
        public short ApproveCancelStatus { get; set; }

        /// <summary>
        /// Trạng thái duyệt hóa đơn xóa bỏ
        /// </summary>
        public short ApproveDeleteStatus { get; set; }


        #region Thông tin TVAN
        /// <summary>
        /// Trạng thái hóa đơn vs Tvan
        /// </summary>
        public short StatusTvan { get; set; }

        /// <summary>
        /// mã cơ quan thuế cho trường hợp hóa đơn cấp mã
        /// </summary>
        public string VerificationCode { get; set; }

        /// <summary>
        /// Trạng thái hóa đơn đã kê khai chưa
        /// </summary>
        public bool IsDeclared { get; set; }
        #endregion
    }
}
