using System;
using System.Collections.Generic;

namespace VnisCore.Ticket.Application.Ticket.Models
{
    public class ImportTicketModel
    {
        /// <summary>
        /// Mẫu số của hóa đơn
        /// </summary>
        public short TemplateNo { get; set; }

        /// <summary>
        /// K<PERSON> hiệu hóa đơn
        /// </summary>
        public string SerialNo { get; set; }

        public string ErpId { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Phương thức thanh toán 
        /// </summary>
        public string PaymentMethod { get; set; }

        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// Chuyến đến tiền tệ
        /// </summary>
        public string Currency { get; set; }

        public decimal ExchangeRate { get; set; }

        /// <summary>
        /// Mã người mua
        /// </summary>
        public long? BuyerId { get; set; } // Mã người mua map sang Core là IdBuyerErp

        /// <summary>
        /// Người đại diện
        /// </summary>
        public string BuyerLegalName { get; set; }

        /// <summary>
        /// Họ tên người mua
        /// </summary>
        public string BuyerFullName { get; set; }
        public string TransactionId { get; set; }

        public string BuyerTaxCode { get; set; }

        public string BuyerAddressLine { get; set; }

        public string BuyerCityName { get; set; }

        public string BuyerDistrictName { get; set; }

        public string BuyerBankAccount { get; set; }

        public string BuyerBankName { get; set; }

        /// <summary>
        /// Email người mua
        /// </summary>
        public string BuyerEmail { get; set; }

        public string BuyerPhone { get; set; }

        public string BuyerFax { get; set; }


        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Tổng tiền hàng trước thuế
        /// </summary>
        public decimal TotalAmount { get; set; }

        public double TotalDiscountPercent { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu trước thuế
        /// </summary>
        public decimal TotalDiscountAmountBeforeTax { get; set; }

        /// <summary>
        /// Tổng tiền chiết khấu sau thuế
        ///</summary>
        public decimal TotalDiscountAmountAfterTax { get; set; } // tổng tiền chiết khấu sau thuế

        /// <summary>
        /// Tổng tiền thuế
        /// </summary>
        public decimal TotalVatAmount { get; set; } // tổng tiền thuế

        /// <summary>
        /// Tổng tiền phải trả
        /// </summary>
        public decimal TotalPaymentAmount { get; set; } // tổng thành tiền sau thuế

        // detail
        public string ProductCode { get; set; } // mã hàng hóa map sang Core là IdProductErp
        public string BuyerCode { get; set; } 

        public string ProductName { get; set; }
        public short ProductType { get; set; }

        public string DetailNote { get; set; }

        //public string UnitCode { get; set; } // mã đơn vị tính map sang Core là IdUnitErp

        public string UnitName { get; set; }

        public decimal Quantity { get; set; }

        public decimal UnitPrice { get; set; }

        public decimal DiscountPercent { get; set; }

        public decimal DiscountAmount { get; set; } // tiền chiết khấu từng mặt hàng

        public decimal Amount { get; set; } // tiền hàng trước thuế từng mặt hàng

        public decimal VatPercent { get; set; }

        public decimal VatAmount { get; set; } // tiền thuế từng mặt hàng

        public decimal PaymentAmount { get; set; } // thành tiền từng mặt hàng

        /// <summary>
        /// Dữ liệu khác, ko đưa dc vào field nào nhưng cần lưu lại HeaderExtra
        /// </summary>
        public Dictionary<string, string> MetadataHeaderExtra { get; set; }
        /// <summary>
        /// Dữ liệu khác, ko đưa dc vào field nào nhưng cần lưu lại DetailExtra
        /// </summary>
        public Dictionary<string, string> MetadataDetailExtra { get; set; }

    }
}
