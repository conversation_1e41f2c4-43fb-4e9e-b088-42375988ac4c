using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Ticket.Application.Ticket.Dto;
using VnisCore.Ticket.Application.Ticket.Models.Requests.Commands;

namespace VnisCore.Ticket.Application.Ticket.Business.Approve
{
    public interface IApproveBusiness
    {
        Task ApproveSignAsync(List<ApproveTicketInfoRequest> invoices);

        Task CancelApproveAsync(CancelTicketHeaderDto tickets);
    }
}
