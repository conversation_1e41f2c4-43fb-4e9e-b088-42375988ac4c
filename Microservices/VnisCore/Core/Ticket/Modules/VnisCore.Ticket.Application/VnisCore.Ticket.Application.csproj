<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <Version>5.28.3</Version>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Application.Contracts\VnisCore.Core.Oracle.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Domain.Shared\VnisCore.Core.Oracle.Domain.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Databases\VnisCore\Core\Oracle\VnisCore.Core.Oracle.Domain\VnisCore.Core.Oracle.Domain.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.AspNetCore.Mvc\Core.AspNetCore.Mvc.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.AutoMapper\Core.AutoMapper.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Core\Core.Ddd.Application\Core.Ddd.Application.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Modules\Identity\Core.Identity.Domain\Core.Identity.Domain.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Modules\TenantManagement\Core.TenantManagement.Domain\Core.TenantManagement.Domain.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Shared.Invoice\Core.Shared.Invoice.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Shared\Core.Shared.csproj" />
    <ProjectReference Include="..\..\..\..\..\..\Framework\Shared\Core.Tvan\Core.Tvan.csproj" />
    <ProjectReference Include="..\VnisCore.Ticket.Infrastructure\VnisCore.Ticket.Infrastructure.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="EPPlus" Version="4.5.3.3" />
    <PackageReference Include="NEST" Version="7.14.1" />
  </ItemGroup>
</Project>
