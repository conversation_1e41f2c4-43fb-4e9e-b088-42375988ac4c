using Core.Domain.Repositories.EntityFrameworkCore;
using Core.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;
using VnisCore.Ticket.Infrastructure.IRepository;

namespace VnisCore.Ticket.Infrastructure.Repository
{
    public class TicketDetailFieldRepository : EfCoreRepository<VnisCoreOracleDbContext, TicketDetailFieldEntity, long>, ITicketDetailFieldRepository
    {
        public TicketDetailFieldRepository(IDbContextProvider<VnisCoreOracleDbContext> dbContextProvider)
        : base(dbContextProvider)
        {

        }

        public async Task<List<TicketDetailFieldEntity>> GetByTenantId(Guid tenantId)
        {
            var dbContext = await GetDbContextAsync();
            return await dbContext.Set<TicketDetailFieldEntity>()
                        .Where(x => x.TenantId == tenantId && x.IsDeleted == false)
                        .ToListAsync();
        }
    }
}
