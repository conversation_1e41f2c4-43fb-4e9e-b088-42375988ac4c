using Core;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Invoice.Repositories;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Media;
using VnisCore.Invoice02.Application.Factories.Models;

namespace Einvoice.Module.Invoice.Services
{
    public abstract class InvoiceDocumentService<THeader, THeaderDocument, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TInvoiceReference>
        where THeader : BaseInvoiceHeaderModel
        where THeaderDocument : BaseInvoiceDocument
        where THeaderField : BaseInvoiceHeaderField
        where TDetailField : BaseInvoiceDetailField
        where TInvoiceReference : BaseInvoiceReference

    {
        private readonly IPdfInvoiceDocumentService _pdfInvoiceDocumentService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;

        public InvoiceDocumentService(
            IPdfInvoiceDocumentService pdfInvoiceDocumentService,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IAppFactory appFactory,
            IFileService fileService)
        {
            _pdfInvoiceDocumentService = pdfInvoiceDocumentService;
            _appFactory = appFactory;
            _localizier = localizier;
            _fileService = fileService;

        }

        public async Task<KeyValuePair<string, byte[]>> GenerateDocumentTemplateAsync(long invoiceId, string documentNo, DateTime documentDate, string documentReason)
        {
            try
            {
                var invoice = await GetInvoiceAsync(invoiceId);
                if (invoice == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.DocumentInfo.InvoiceNotFound"]);

                //kiểm tra có phải đủ điều kiện để in biên bản không
                //await CheckCanGenerateDocumentAsync(invoice);

                var pdfInvoiceDocumentModel = await EntityToModelAsync(invoice);
                pdfInvoiceDocumentModel.DocumentDate = documentDate;
                pdfInvoiceDocumentModel.DocumentNo = documentNo;
                pdfInvoiceDocumentModel.DocumentReason = documentReason;

                var documentTemplate = await GetDocumentTemplateAsync(invoice);
                var datas = await _pdfInvoiceDocumentService.GenerateAsync(pdfInvoiceDocumentModel, documentTemplate);

                return new KeyValuePair<string, byte[]>($"{invoice.SellerTaxCode}_{invoice.TemplateNo}_{invoice.SerialNo}_{invoice.InvoiceNo}_BB.pdf".Replace("/", "-"), datas);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

        }

        public async Task<KeyValuePair<string, byte[]>> GenerateInvoiceDocumentAsync(long invoiceId, string documentNo, DateTime documentDate, string documentReason)
        {
            try
            {
                var invoice = await GetInvoiceAsync(invoiceId);
                if (invoice == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.DocumentInfo.InvoiceNotFound"]);

                //kiểm tra có phải đủ điều kiện để in biên bản không
                //await CheckCanGenerateDocumentAsync(invoice);

                var pdfInvoiceDocumentModel = await EntityToModelAsync(invoice);
                pdfInvoiceDocumentModel.DocumentDate = documentDate;
                pdfInvoiceDocumentModel.DocumentNo = documentNo;
                pdfInvoiceDocumentModel.DocumentReason = documentReason;

                var documentFile = await GetInvoiceDocumentAsync(invoice);

                if (documentFile == null)
                    throw new UserFriendlyException(_localizier["Vnis.BE.Invoice02.Document.NotFindDocumentInvoice"]);

                var pathFileDocumentMinio = $"{GetMediaFileType<THeaderDocument>()}/{documentFile.TenantId}/{documentFile.CreatedAt.Year}/{documentFile.CreatedAt.Month:00}/{documentFile.CreatedAt.Day:00}/{documentFile.CreatedAt.Hour:00}/{documentFile.PhysicalFileName}";
                var datas = await _fileService.DownloadAsync(pathFileDocumentMinio);

                return new KeyValuePair<string, byte[]>($"{invoice.SellerTaxCode}_{invoice.TemplateNo}_{invoice.SerialNo}_{invoice.InvoiceNo}_BB.pdf".Replace("/", "-"), datas);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

        }

        public abstract Task<THeader> GetInvoiceAsync(long invoiceId);

        public abstract Task<PdfInvoiceDocumentModel> EntityToModelAsync(THeader invoice);


        private async Task<DocumentTemplateModel> GetDocumentTemplateAsync(THeader invoiceHeader)
        {
            DocumentTemplateType type;

            if (invoiceHeader.InvoiceStatus == InvoiceStatus.ThayThe)
                type = DocumentTemplateType.Replace;
            else if (invoiceHeader.InvoiceStatus == InvoiceStatus.DieuChinhDinhDanh)
                type = DocumentTemplateType.AdjustmentHeader;
            else if (invoiceHeader.InvoiceStatus == InvoiceStatus.DieuChinhTangGiam)
                type = DocumentTemplateType.AdjustmentDetail;
            else if (invoiceHeader.InvoiceStatus == InvoiceStatus.XoaBo)
                type = DocumentTemplateType.Delete;
            else
                throw new UserFriendlyException("Không có biên bản cho các hóa đơn không phải xóa bỏ/thay thế/điều chỉnh định danh/điều chỉnh tăng giảm");

            //lấy thông tin loại biên bản
            var repoInvoiceDocument = _appFactory.Repository<DocumentTemplateEntity, long>();
            var documentTemplate = await repoInvoiceDocument.Where(x => (x.TenantId == invoiceHeader.TenantId || x.TenantId == Guid.Empty) && x.Type == type.GetHashCode()).FirstOrDefaultAsync();

            if (documentTemplate == null)
                throw new UserFriendlyException("Không tìm thấy mẫu biên bản");

            return new DocumentTemplateModel
            {
                CreatedAt = documentTemplate.CreationTime,
                FileName = documentTemplate.FileName,
                Id = documentTemplate.Id,
                PhysicalFileName = documentTemplate.PhysicalFileName,
                TenantId = documentTemplate.TenantId
            };
        }

        private async Task<DocumentTemplateModel> GetInvoiceDocumentAsync(THeader invoiceHeader)
        {
            //lấy thông tin loại biên bản
            var repoInvoiceDocument = _appFactory.Repository<THeaderDocument, long>();
            var invoiceDocument = await repoInvoiceDocument.Where(x => x.TenantId == invoiceHeader.TenantId && x.InvoiceHeaderId == invoiceHeader.Id).FirstOrDefaultAsync();

            if (invoiceDocument == null)
                return null;

            return new DocumentTemplateModel
            {
                CreatedAt = invoiceDocument.CreationTime,
                FileName = invoiceDocument.FileName,
                Id = invoiceDocument.Id,
                PhysicalFileName = invoiceDocument.PhysicalFileName,
                TenantId = invoiceDocument.TenantId
            };
        }

        private MediaFileType GetMediaFileType<TDocument>() where TDocument : BaseInvoiceDocument
        {
            if (typeof(TDocument) == typeof(Invoice02DocumentEntity))
                return MediaFileType.Invoice02Document;
            else if (typeof(TDocument) == typeof(Invoice02DocumentEntity))
                return MediaFileType.Invoice02Document;
            else if (typeof(TDocument) == typeof(Invoice03DocumentEntity))
                return MediaFileType.Invoice03Document;
            else if (typeof(TDocument) == typeof(Invoice04DocumentEntity))
                return MediaFileType.Invoice04Document;
            else
                return MediaFileType.TicketDocument;
        }


    }
}
