using Core;
using Core.Application.Services;
using Core.Shared.Dto;
using Core.Shared.Factory;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.InvoiceDocument;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Repositories;
using VnisCore.Invoice02.Application.Invoice02Document.Models.Requests;
using VnisCore.Invoice02.Application.Invoice02Document.Models.Responses;

namespace VnisCore.Invoice02.Application.Invoice02Document
{
    [Authorize(InvoiceDocumentPermissions.InvoiceDocument.Default)]
    public class Invoice02DocumentService : ApplicationService, IInvoice02DocumentService
    {
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceDocumentInfoRepository<Invoice02DocumentInfoEntity> _repoInvoiceDocumentInfo;

        public Invoice02DocumentService(
            IInvoiceDocumentInfoRepository<Invoice02DocumentInfoEntity> repoInvoiceDocumentInfo,
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
            _repoInvoiceDocumentInfo = repoInvoiceDocumentInfo;
        }

        /// <summary>
        /// tải biên bản theo id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase + "Download/{id}")]
        public async Task<FileDto> DownloadByIdAsync([FromRoute] long id)
        {
            return await _appFactory.Mediator.Send(new DownloadInvoice02DocumentByIdRequestModel
            {
                Id = id
            });
        }

        /// <summary>
        /// xem trươc biên bản Vnís
        /// </summary>
        /// <param name="request"></param>
        /// <param name="documentInfo"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase + "Preview")]
        public async Task<FileDto> PreviewAsync([FromQuery] PreviewDocumentTemplateRequestModel request, [FromBody] PreviewDocumentTemplateRequestBodyModel documentInfo)
        {
            var response = await _appFactory.Mediator.Send(new PreviewDocumentTemplateRequestModel
            {
                InvoiceHeaderId = request.InvoiceHeaderId,
                DocumentDate = documentInfo.DocumentDate,
                DocumentNo = documentInfo.DocumentNo,
                DocumentReason = documentInfo.DocumentReason
            });

            return response;
        }

        /// <summary>
        /// tải biên bản theo hóa đơn
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase + "Download")]
        public async Task<FileDto> DownloadByInvoiceAsync([FromQuery] DownloadInvoice02DocumentRequestModel request)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            //lấy thông tin biên bản
            var documentInfo = await _repoInvoiceDocumentInfo.GetByCodeInvoiceHeaderAsync(tenantId, request.InvoiceHeaderId);
            if (documentInfo == null)
                throw new UserFriendlyException(L["Vnis.BE.Invoice02.DownloadDocument.NotFindDocumentInvoice"]);

            var response = await _appFactory.Mediator.Send(new PreviewInvoice02DocumentRequestModel
            {
                InvoiceHeaderId = request.InvoiceHeaderId,
                DocumentDate = documentInfo.DocumentDate.Value,
                DocumentNo = documentInfo.DocumentNo,
                DocumentReason = documentInfo.DocumentReason
            });

            return response;
        }

        /// <summary>
        /// upload file biên bản
        /// </summary>
        /// <param name="request"></param>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase + "Upload")]
        public async Task<UploadInvoice02DocumentResponseModel> UploadAsync([FromQuery] UploadInvoice02DocumentModel request, IFormFile formFile)
        {
            var response = await _appFactory.Mediator.Send(new UploadInvoice02DocumentRequestModel
            {
                File = formFile,
                InvoiceNo = request.InvoiceNo,
                SerialNo = request.SerialNo,
                TemplateNo = request.TemplateNo
            });

            return response;
        }

        /// <summary>
        /// xóa file biên bản
        /// </summary>
        /// <param name="idDocument"></param>
        /// <returns></returns>
        [HttpPost(Utilities.ApiUrlBase + "Delete")]
        public async Task DeleteAsync([FromRoute] long idDocument)
        {
            var response = await _appFactory.Mediator.Send(new DeleteInvoice02DocumentRequestModel
            {
                Id = idDocument
            });
        }

    }
}
