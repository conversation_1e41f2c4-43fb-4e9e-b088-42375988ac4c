using System;
using System.ComponentModel.DataAnnotations;
using Core.Application.Dtos;
using Newtonsoft.Json;

namespace VnisCore.Invoice02.Application.Invoice02HeaderField.Dto
{
    public class Invoice02HeaderFieldDto : EntityDto<long>
    {
        public new long Id { get; set; }
        [Required(ErrorMessage = "Tên trường không được để trống")]
        [MaxLength(250, ErrorMessage = "Tên trường tối đa 250 ký tự")]
        public string FieldName { get; set; }
        [Required(ErrorMessage = "Tên trường hiển thị không được để trống")]
        [MaxLength(500, ErrorMessage = "Tên trường hiển thị tối đa 500 ký tự")]
        public string DisplayName { get; set; }
        public string Metadata { get; set; } //Dữ liệu là các attribute của thẻ HTML
        public string ErrorMessages { get; set; }

        [JsonIgnore]
        public Guid CreatorId { get; set; }

        [JsonIgnore]
        public DateTime CreationTime { get; set; }

        [JsonIgnore]
        public DateTime? LastModificationTime { get; set; }

        [JsonIgnore]
        public Guid? LastModifierId { get; set; }

        [JsonIgnore]
        public Guid TenantId { get; set; }

    }
}
