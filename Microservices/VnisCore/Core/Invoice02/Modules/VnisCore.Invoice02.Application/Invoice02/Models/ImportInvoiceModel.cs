using System;
using System.Collections.Generic;

namespace VnisCore.Invoice02.Application.Invoice02.Models
{
    public class ImportInvoiceModel
    {
        //public IFormFile File { get; set; }
        public string FileName { get; set; }

        /// <summary>
        /// định dạng là json của 1 dictionary(string,string)
        /// </summary>
        public string Parameters { get; set; }

        public Guid TenantId { get; set; }
        public Guid UserId { get; set; }
        public string UserName { get; set; }
        public string UserFullName { get; set; }
        public string Service { get; set; }
        public string ConnectionId { get; set; }
        public bool? AutoRounding { get; set; }
    }
}
