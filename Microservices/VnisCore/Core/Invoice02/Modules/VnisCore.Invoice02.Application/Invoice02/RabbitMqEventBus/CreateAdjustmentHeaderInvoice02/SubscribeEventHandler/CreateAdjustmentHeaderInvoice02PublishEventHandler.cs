using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.MessageEventsData.GenerateContentEmail;
using Core.Shared.MessageEventsData.IncreaseLicense;
using Core.Shared.MessageEventsData.StatisticSummary;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Messages;
using Core.Shared.Models;
using Core.Shared.Services;

using Dapper;

using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

using System;
using System.Globalization;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Repositories;
using VnisCore.Invoice02.Application.Factories.Services;
using VnisCore.Invoice02.Application.Invoice02.Models;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.AutoSignServer;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.CreateAdjustmentHeaderInvoice02.MessageEventData;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.Invoice02Log.MessageEventData;

namespace VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.CreateAdjustmentHeaderInvoice02.SubscribeEventHandler
{
    public class CreateAdjustmentHeaderInvoice02PublishEventHandler : IDistributedEventHandler<CreateAdjustmentHeaderInvoice02EventSendData>, ITransientDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<CreateAdjustmentHeaderInvoice02PublishEventHandler> _logger;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly Factories.Services.IInvoice02Service _invoice02Service;
        private readonly INumberService<Invoice02HeaderEntity> _numberService;
        private readonly IAppFactory _appFactory;
        private readonly IElasticService _elasticService;
        public CreateAdjustmentHeaderInvoice02PublishEventHandler(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ILogger<CreateAdjustmentHeaderInvoice02PublishEventHandler> logger,
            Factories.Services.IInvoice02Service invoice02Service,
            INumberService<Invoice02HeaderEntity> numberService,
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus,
            IElasticService elasticService)
        {
            _localizer = localizer;
            _logger = logger;
            _appFactory = appFactory;
            _invoice02Service = invoice02Service;
            _numberService = numberService;
            _distributedEventBus = distributedEventBus;
            _elasticService = elasticService;
        }

        public async Task HandleEventAsync(CreateAdjustmentHeaderInvoice02EventSendData eventData)
        {
            try
            {
                var query = await _invoice02Service.GenerateDrawCreateAdjustmentHeaderInvoice(eventData);

                var data = await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                //Sinh số
                var number = await _numberService.GenerateNumberAsync(eventData.TenantId, eventData.TemplateNo, eventData.SerialNo, eventData.InvoiceDate);
                if (number == null || number.Number == 0)
                    //throw new Exception("Quá trình sinh số xảy ra lỗi");
                    throw new Exception(_localizer["Vnis.BE.Invoice02.GenerateNumberFail"]);

                var invoiceservice = _appFactory.GetServiceDependency<IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity>>();
                var invoice02HeaderService = _appFactory.GetServiceDependency<IInvoiceHeaderRepository<Invoice02HeaderEntity>>();
                var rootInvoice = await invoice02HeaderService.GetInvoiceHeaderRawAsync(eventData.TenantId, eventData.InvoiceReferenceId);
                var approveStatus = await invoiceservice.GetApproveStatusAsync(eventData.TenantId);
                var approveCancelStatus = await invoiceservice.GetApproveCancelStatusAsync(eventData.TenantId);
                var approveDeleteStatus = await invoiceservice.GetApproveDeleteStatusAsync(eventData.TenantId);
                //Lưu số
                await _numberService.SaveNumberAsync(eventData.Id, number);

                // Increase license
                await _distributedEventBus.PublishAsync(new IncreaseLicenseEventSendData
                {
                    Quantity = 1,
                    TenantId = eventData.TenantId
                });

                //return id;
                var result = new InvoiceCommandResponseModel
                {
                    //ProcessCode = eventData.ProcessCode.Value,
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    //Type = message.Type,
                    UserId = eventData.UserId,
                    UserFullName = eventData.UserFullName,
                    UserName = eventData.UserName,
                    Method = HubMethod.CreateInvoice,
                    InvoiceReferenceId = eventData.InvoiceReferenceId,
                    InvoiceStatus = InvoiceStatus.DieuChinhDinhDanh,
                    SignStatus = SignStatus.ChoKy,
                    ApproveStatus = approveStatus,
                    ApproveCancelStatus = approveCancelStatus,
                    ApproveDeleteStatus = approveDeleteStatus,
                    //Number = number.Number,
                    //InvoiceNo = number.InvoiceNo,
                    TemplateNo = eventData.TemplateNo,
                    SerialNo = eventData.SerialNo,
                    Resource = eventData.Resource,
                    State = InvoiceActionState.CreateAdjustmentHeader,
                    ActionLogInvoice = ActionLogInvoice.CreateAdjustmentHeader,
                    Action = InvoiceAction.CreateAdjustmentHeader,
                    ActionAt = DateTime.Now,
                    ActionAtUtc = DateTime.UtcNow,
                    //ConnectionId = eventData.ConnectionId,
                    InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                    {
                        OldInvoiceDate = rootInvoice.InvoiceDate,
                        OldTotalAmount = 0,
                        OldTotalPaymentAmount = 0,
                        OldTotalVatAmount = 0,
                        NewTotalAmount = 0,
                        NewInvoiceDate = eventData.InvoiceDate,
                        NewTotalPaymentAmount = 0,
                        NewTotalVatAmount = 0
                    }
                };

                // publish dashboard
                await _distributedEventBus.PublishAsync(new StatisticSummaryApiEventResultData(result));

                var syncCustomer = new SyncCustomerRequestModel
                {
                    IdInvoice = eventData.Id,
                    TenantId = eventData.TenantId,
                    Type = VnisType._02GTTT,
                };

                //sync Customer
                await _distributedEventBus.PublishAsync(new SyncCustomerEventSendData(syncCustomer));

                //Sync elastic
                //await _elasticService.SyncInvoice02Elastic(eventData.TenantId, eventData.Id);

                //Thêm invoice reference
                var rawTenantId = OracleExtension.ConvertGuidToRaw(eventData.TenantId);
                var rawUserId = OracleExtension.ConvertGuidToRaw(eventData.UserId);

                var invoice02Reference = _appFactory.GetServiceDependency<IInvoiceReferenceRepository<Invoice02ReferenceEntity>>();
                var invoice02Ref = await invoice02Reference.GetInvoiceReferenceRawAsync(eventData.Id);

                StringBuilder raw = new StringBuilder();
                if (invoice02Ref != null)
                {
                    var rootInvoiceHeader = await invoice02HeaderService.GetInvoiceHeaderRawAsync(eventData.TenantId, invoice02Ref.InvoiceReferenceId);
                    raw.Append($"BEGIN Update \"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}\" " +
                            $"Set \"InvoiceStatus\" = '{InvoiceStatus.BiDieuChinhDinhDanh.GetHashCode()}'" +
                            $"Where \"Id\" = {invoice02Ref.InvoiceReferenceId} ;");

                    StringBuilder sqlInsertInvoice02Reference = new StringBuilder();

                    sqlInsertInvoice02Reference.Append($@"  INSERT INTO ""Invoice02Reference"" (             
                                                ""CreationTime"",                           
                                                ""CreatorId"",                                  
                                                ""TenantId"",                                      
                                                ""InvoiceHeaderId"",                                 
                                                ""InvoiceReferenceId"",                           
                                                ""TemplateNoReference"",                                
                                                ""SerialNoReference"",                         
                                                ""InvoiceNoReference"",                                  
                                                ""NumberReference"",                                  
                                                ""InvoiceDateReference"",                                  
                                                ""InvoiceStatus"",                                  
                                                ""Partition""                                                                 
                                            )                                                  
                                            VALUES ");

                    //Add values
                    sqlInsertInvoice02Reference.Append($@"  (                                                               
                                                    '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',                                                      
                                                    '{ OracleExtension.ConvertGuidToRaw(rootInvoiceHeader.CreatorId.Value) }',                                             
                                                    '{ OracleExtension.ConvertGuidToRaw(rootInvoiceHeader.TenantId) }',                                               
                                                    { rootInvoiceHeader.Id },                                            
                                                    { eventData.Id},                                      
                                                    { eventData.TemplateNo },                                           
                                                    '{ eventData.SerialNo }',                                              
                                                    '{ number.InvoiceNo}',    
                                                    { number.Number},    
                                                    '{ eventData.InvoiceDate.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}',    
                                                    { rootInvoiceHeader.InvoiceStatus},    
                                                    { long.Parse(rootInvoiceHeader.InvoiceDate.ToString($"yyyyMMddHHmm"))}    
                                                ) ");

                    sqlInsertInvoice02Reference.Append("; ");


                    if (sqlInsertInvoice02Reference.Length > 0)
                        raw.Append(sqlInsertInvoice02Reference);

                    var queryResult = raw.Append($" END; ").ToString();
                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(queryResult);

                    //Sync elastic
                    //await _elasticService.SyncInvoice02Elastic(eventData.TenantId, rootInvoiceHeader.Id);
                }

                // Signalr
                //await _distributedEventBus.PublishAsync(new ReceivedInvoiceMessageDto(eventData.UserId, "Tạo hóa đơn điều chỉnh định danh thành công", "success", VnisType._02GTTT));

                // logic insert
                await _distributedEventBus.PublishAsync(new CreateAdjustmentHeaderInvoice02EventResultData(eventData));

                //Insert Log
                await _distributedEventBus.PublishAsync(new Invoice02LogEventSendData(new Invoice02LogModel
                {
                    InvoiceHeaderId = eventData.Id,
                    TenantId = eventData.TenantId,
                    UserId = eventData.UserId,
                    UserName = eventData.UserName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(eventData.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.CreateAdjustmentHeader.GetHashCode(),
                    Partition = long.Parse(eventData.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));

                await _distributedEventBus.PublishAsync(new Invoice02LogEventSendData(new Invoice02LogModel
                {
                    InvoiceHeaderId = rootInvoice.Id,
                    TenantId = eventData.TenantId,
                    UserId = eventData.UserId,
                    UserName = eventData.UserName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(rootInvoice.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.AdjustmentedHeader.GetHashCode(),
                    Partition = long.Parse(rootInvoice.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));

                var sendMailChange = false;

                var settingService = _appFactory.GetServiceDependency<ISettingService>();
                var setting = (await settingService.GetByCodeAsync(eventData.TenantId, SettingKey.SendMailInvoiceChangeAfterSign.ToString()))?.Value;

                if (!string.IsNullOrEmpty(setting) && setting == "1")
                    sendMailChange = true;

                //Send mail
                if (sendMailChange)
                    await _distributedEventBus.PublishAsync(new GenerateContentEmailEventSendData(new GenerateContentMailMessageModel<BaseGenerateContentMaillModel>
                    {
                        Action = EmailAction.SendInvoiceChange.ToString(),
                        Data = new BaseGenerateContentMaillModel
                        {
                            TenantId = eventData.TenantId,
                            Type = VnisType._02GTTT,
                            Emails = rootInvoice.BuyerEmail,
                            InvoiceHeaderId = rootInvoice.Id,
                            UserId = eventData.UserId,
                            FullName = eventData.UserFullName,
                            UserName = eventData.UserName,
                        }
                    }));

                //// check có quy trình duyệt hay không
                //// nếu không có quy trình duyệt và có cấu hình ký tự động bằng signserver thì
                //// sau khi tạo hóa đơn xong thì ký luôn
                await _distributedEventBus.PublishAsync(new AutoSignInvoice02EventSendData
                {
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    Token = eventData.Token
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent CreateAdjustmentHeader Error: {ex.Message}");
                _logger.LogError($"HandleEvent CreateAdjustmentHeader Error: {ex.InnerException}");

                // Publish SignalR
            }
        }
    }
}