using System;
using System.Linq;
using System.Threading.Tasks;
using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Core.Shared.MessageEventsData.AutoSignServerGroupCustomer;
using Core.Shared.MessageEventsData.IncreaseLicense;

using Dapper;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Services;
using VnisCore.Invoice02.Application.Invoice02.Models;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.CreateByGroupCustomerInvoice02.MessageEventData;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.Invoice02Log.MessageEventData;
using static VnisCore.Invoice02.Application.Factories.Constants.CommonInvoice02Const;

namespace VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.CreateByGroupCustomerInvoice02.SubscribeEventHandler
{
    public class CreateByGroupCustomerInvoice02HeaderPublishEventHandler : IDistributedEventHandler<CreateByGroupCustomerInvoice02HeaderEventSendData>, ITransientDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<CreateByGroupCustomerInvoice02HeaderPublishEventHandler> _logger;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly Factories.Services.IInvoice02Service _invoice02Service;
        private readonly INumberService<Invoice02HeaderEntity> _numberService;
        private readonly IAppFactory _appFactory;
        public CreateByGroupCustomerInvoice02HeaderPublishEventHandler(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ILogger<CreateByGroupCustomerInvoice02HeaderPublishEventHandler> logger,
            Factories.Services.IInvoice02Service invoice02Service,
            INumberService<Invoice02HeaderEntity> numberService,
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus)
        {
            _localizer = localizer;
            _logger = logger;
            _appFactory = appFactory;
            _invoice02Service = invoice02Service;
            _numberService = numberService;
            _distributedEventBus = distributedEventBus;
        }

        public async Task HandleEventAsync(CreateByGroupCustomerInvoice02HeaderEventSendData eventData)
        {
            try
            {
                var headerIds = await _invoice02Service.GetSEQsNextVal(eventData.InfosNeedCreateInvoice.Customers.Count, SEQ_Name.SEQ_Invoice02Header);

                var query = await _invoice02Service.GenerateDrawCreateByGroupCustomerInvoice(eventData, headerIds);

                var data = await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                //Sinh số
                var numbers = await _numberService.GenerateBatchNumberAsync(eventData.TenantId, eventData.TemplateNo, eventData.SerialNo, eventData.InvoiceDate, headerIds.Count);
                if (numbers == null || numbers.Any(x => x.Number == 0))
                    //throw new Exception("Quá trình sinh số xảy ra lỗi");
                    throw new Exception(_localizer["Vnis.BE.Invoice02.GenerateNumberFail"]);

                //Lưu số
                await _numberService.SaveBatchNumberAsync(headerIds, numbers);

                // Increase license
                await _distributedEventBus.PublishAsync(new IncreaseLicenseEventSendData
                {
                    Quantity = headerIds.Count,
                    TenantId = eventData.TenantId
                });

                //Insert Log
                foreach (var headerId in headerIds)
                {
                    await _distributedEventBus.PublishAsync(new Invoice02LogEventSendData(new Invoice02LogModel
                    {
                        InvoiceHeaderId = headerId,
                        TenantId = eventData.TenantId,
                        UserId = eventData.UserId,
                        UserName = eventData.UserName,
                        InvoiceType = EnumExtension.ToEnum<VnisType>(eventData.TemplateNo).GetHashCode(),
                        Action = ActionLogInvoice.Create.GetHashCode(),
                        Partition = long.Parse(eventData.InvoiceDate.ToString("yyyyMMddHHmm")),
                        ActionTime = DateTime.Now
                    }));
                }

                //// check có quy trình duyệt hay không
                //// nếu không có quy trình duyệt và có cấu hình ký tự động bằng signserver thì
                //// sau khi tạo hóa đơn xong thì ký luôn
                await _distributedEventBus.PublishAsync(new AutoSignServerGroupCustomerEventSendData
                {
                    Ids = headerIds,
                    VnisType = VnisType._02GTTT,
                    TenantId = eventData.TenantId,
                    Token = eventData.Token
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent CreateByGroupCustomer Error: {ex.Message}");
                _logger.LogError($"HandleEvent CreateByGroupCustomer Error: {ex.InnerException}");

                // Publish SignalR
            }
        }
    }
}