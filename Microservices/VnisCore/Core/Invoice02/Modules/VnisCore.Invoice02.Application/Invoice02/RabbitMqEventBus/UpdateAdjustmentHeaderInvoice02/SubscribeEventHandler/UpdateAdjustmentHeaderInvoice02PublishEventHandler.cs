using Core.DependencyInjection;
using Core.EventBus.Distributed;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;

using Core.Shared.MessageEventsData.StatisticSummary;
using Core.Shared.MessageEventsData.SyncCatalog;
using Core.Shared.Messages;
using Dapper;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Services;
using VnisCore.Invoice02.Application.Invoice02.Models;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.AutoSignServer;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.Invoice02Log.MessageEventData;
using VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.UpdateAdjustmentHeaderInvoice02.MessageEventData;

namespace VnisCore.Invoice02.Application.Invoice02.RabbitMqEventBus.UpdateAdjustmentHeaderInvoice02.SubscribeEventHandler
{
    public class UpdateAdjustmentHeaderInvoice02PublishEventHandler : IDistributedEventHandler<UpdateAdjusmentHeaderInvoice02EventSendData>, ITransientDependency
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly ILogger<UpdateAdjustmentHeaderInvoice02PublishEventHandler> _logger;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;
        private readonly Factories.Services.IInvoice02Service _invoice02Service;
        private readonly IAppFactory _appFactory;
        private readonly IDistributedEventBus _distributedEventBus;
        private readonly IElasticService _elasticService;

        public UpdateAdjustmentHeaderInvoice02PublishEventHandler(
            IStringLocalizer<CoreLocalizationResource> localizer,
            ILogger<UpdateAdjustmentHeaderInvoice02PublishEventHandler> logger,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService,
            Factories.Services.IInvoice02Service invoice02Service,
            IAppFactory appFactory,
            IDistributedEventBus distributedEventBus,
            IElasticService elasticService)
        {
            _localizer = localizer;
            _logger = logger;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
            _invoice02Service = invoice02Service;
            _distributedEventBus = distributedEventBus;
            _elasticService = elasticService;
        }

        public async Task HandleEventAsync(UpdateAdjusmentHeaderInvoice02EventSendData eventData)
        {
            try
            {
                var query = await _invoice02Service.GenerateDrawUpdateAdjustmentHeaderInvoice(eventData);

                var data = await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(query);

                //nếu có số hóa đơn thì mới update lại ngày hóa đơn cuối cùng
                var invoice = eventData.InfosNeedUpdateInvoice.Invoice;
                if (invoice.Number.HasValue)
                    await _invoiceService.UpdateLastInvoiceDateAsync(invoice.TenantId, invoice.InvoiceTemplateId, invoice.Number.Value);

                var result = new InvoiceCommandResponseModel
                {
                    // ProcessCode = message.ProcessCode.Value,
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    // Type = message.Type,
                    UserId = eventData.UserId,
                    UserFullName = eventData.UserFullName,
                    UserName = eventData.UserName,
                    Method = HubMethod.UpdateInvoice,
                    InvoiceStatus = EnumExtension.ToEnum<InvoiceStatus>(invoice.InvoiceStatus),
                    SignStatus = EnumExtension.ToEnum<SignStatus>(invoice.SignStatus),
                    ApproveStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveStatus),
                    ApproveCancelStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveCancelStatus),
                    ApproveDeleteStatus = EnumExtension.ToEnum<ApproveStatus>(invoice.ApproveDeleteStatus),
                    Number = invoice.Number,
                    InvoiceNo = invoice.InvoiceNo,
                    TemplateNo = invoice.TemplateNo,
                    SerialNo = invoice.SerialNo,
                    Resource = eventData.Resource,
                    State = InvoiceActionState.UpdateAdjustmentHeader,
                    ActionLogInvoice = ActionLogInvoice.Update,
                    Action = InvoiceAction.UpdateAdjustmentHeader,
                    ActionAt = DateTime.Now,
                    ActionAtUtc = DateTime.UtcNow,
                    //ConnectionId = message.ConnectionId,
                    InvoiceSummaryStatistic = new InvoiceSummaryStatisticResponseModel
                    {
                        NewInvoiceDate = eventData.InvoiceDate,
                        NewTotalAmount = 0,
                        NewTotalPaymentAmount = 0,
                        NewTotalVatAmount = 0,
                        OldInvoiceDate = invoice.InvoiceDate,
                        OldTotalAmount = 0,
                        OldTotalPaymentAmount = 0,
                        OldTotalVatAmount = 0,
                    }
                };

                // publish dashboard
                await _distributedEventBus.PublishAsync(new StatisticSummaryApiEventResultData(result));

                //sync Customer
                await _distributedEventBus.PublishAsync(new SyncCustomerEventSendData(new SyncCustomerRequestModel
                {
                    IdInvoice = eventData.Id,
                    TenantId = eventData.TenantId,
                    Type = VnisType._02GTTT,
                }));

                //Sync elastic
                //await _elasticService.SyncInvoice02Elastic(eventData.TenantId, eventData.Id);

                // 
                await _distributedEventBus.PublishAsync(new UpdateAdjustmentHeaderInvoice02EventResultData(eventData));

                //Insert Log
                await _distributedEventBus.PublishAsync(new Invoice02LogEventSendData(new Invoice02LogModel
                {
                    InvoiceHeaderId = eventData.Id,
                    TenantId = eventData.TenantId,
                    UserId = eventData.UserId,
                    UserName = eventData.UserName,
                    InvoiceType = EnumExtension.ToEnum<VnisType>(eventData.TemplateNo).GetHashCode(),
                    Action = ActionLogInvoice.Update.GetHashCode(),
                    Partition = long.Parse(eventData.InvoiceDate.ToString("yyyyMMddHHmm")),
                    ActionTime = DateTime.Now
                }));

                //// check có quy trình duyệt hay không
                //// nếu không có quy trình duyệt và có cấu hình ký tự động bằng signserver thì
                //// sau khi tạo hóa đơn xong thì ký luôn
                await _distributedEventBus.PublishAsync(new AutoSignInvoice02EventSendData
                {
                    Id = eventData.Id,
                    TenantId = eventData.TenantId,
                    Token = eventData.Token
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"HandleEvent UpdateAdjusmentHeader Error: {ex.Message}");
                _logger.LogError($"HandleEvent UpdateAdjusmentHeader Error: {ex.InnerException}");

                // Publish SignalR
            }
        }
    }
}