using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Invoice02.Models.Requests.Commands;

namespace Einvoice.Module.Invoice02.ValidationRules.CreateAdjustmentDetail
{
    /// <summary>
    /// kiểm tra có được thay thế cho mẫu hóa đơn cùng mẫu số, khác ký hiệu không 
    /// </summary>
    public class CreateInvoiceAdjustmentDetailCheckChangeSerialRule : IValidationRule<CreateInvoice02AdjustmentDetailRequest, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public CreateInvoiceAdjustmentDetailCheckChangeSerialRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateInvoice02AdjustmentDetailRequest input)
        {
            var invoiceReference = _validationContext.GetItem<Invoice02HeaderEntity>("InvoiceReference");

            //kiểm tra mẫu hóa đơn có được thay thế/điều chỉnh cho mẫu hóa đơn hiện tại không
            if (invoiceReference.TemplateNo != input.TemplateNo)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.CreateAdjustDetail.CannotAdjustInvoiceForOtherTemplateNo"]);

            //kiểm tra có đúng ký hiệu không
            if (!ValidateChangeSerial(invoiceReference.SerialNo, input.SerialNo))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.CreateAdjustDetail.CannotAdjustInvoiceForOtherSerialNo"]);

            return new ValidationResult(true);
        }

        public bool ValidateChangeSerial(string rootSerialNo, string serialNo)
        {
            if (serialNo == rootSerialNo)
                return true;

            //kiểm tra năm của ký hiệu
            if (!int.TryParse(rootSerialNo.Substring(1, 2), out int rootYear)
                || !int.TryParse(serialNo.Substring(1, 2), out int newYear))
                return false;

            if (newYear < rootYear)
                return false;

            return true;
        }
    }
}
