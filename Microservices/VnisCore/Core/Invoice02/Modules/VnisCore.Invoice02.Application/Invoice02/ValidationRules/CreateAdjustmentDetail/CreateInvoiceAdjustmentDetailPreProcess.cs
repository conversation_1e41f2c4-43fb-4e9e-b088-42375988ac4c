using Core.Shared.Factory;
using Core.Shared.Validations;
using Core.TenantManagement;
using System.Threading.Tasks;
using VnisCore.Invoice02.Application;
using VnisCore.Invoice02.Application.Invoice02.Models.Requests.Commands;

namespace Einvoice.Module.Invoice02.ValidationRules.CreateAdjustmentDetail
{
    public class CreateInvoiceAdjustmentDetailPreProcess : IValidationRuleAsync<CreateInvoice02AdjustmentDetailRequest, ValidationResult>
    {
        private readonly ITenantRepository _repoTenant;
        private readonly IValidationContext _validationContext;
        private readonly IAppFactory _appFactory;

        public CreateInvoiceAdjustmentDetailPreProcess(
            ITenantRepository repoTenant,
            IValidationContext validationContext,
            IAppFactory appFactory)
        {
            _repoTenant = repoTenant;
            _validationContext = validationContext;
            _appFactory = appFactory;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice02AdjustmentDetailRequest input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var tenant = new TenantInfo
            {
                Id = _appFactory.CurrentTenant.Id.Value,
                Code = _appFactory.CurrentTenant.TenantCode,
                Address = _appFactory.CurrentTenant.Address,
                FullNameVi = _appFactory.CurrentTenant.FullNameVi,
                FullNameEn = _appFactory.CurrentTenant.FullNameEn,
                Name = _appFactory.CurrentTenant.Name,
                City = _appFactory.CurrentTenant.City,
                Country = _appFactory.CurrentTenant.Country,
                District = _appFactory.CurrentTenant.District,
                Emails = _appFactory.CurrentTenant.Emails,
                LegalName = _appFactory.CurrentTenant.LegalName,
                TaxCode = _appFactory.CurrentTenant.TaxCode,
                Phone = _appFactory.CurrentTenant.Phones,
                Fax = _appFactory.CurrentTenant.Fax,
                BankAccount = _appFactory.CurrentTenant.BankAccount,
                BankName = _appFactory.CurrentTenant.BankName,
                Metadata = _appFactory.CurrentTenant.Metadata,
            };

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });

            _validationContext.GetOrAddItem("Tenant", () =>
            {
                return tenant;
            });

            _validationContext.GetOrAddItem("UserId", () =>
            {
                return userId;
            });

            return new ValidationResult(true);
        }
    }
}
