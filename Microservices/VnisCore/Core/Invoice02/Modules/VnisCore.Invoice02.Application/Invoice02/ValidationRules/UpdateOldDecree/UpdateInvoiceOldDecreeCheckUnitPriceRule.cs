using Core.Shared.Constants;
using Core.Shared.Validations;

using System.Linq;
using System.Threading.Tasks;

using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRules.UpdateOldDecree
{
    public class UpdateInvoiceOldDecreeCheckUnitPriceRule : IValidationRuleAsync<UpdateInvoice02HeaderOldDecreeDto, ValidationResult>
    {
        public UpdateInvoiceOldDecreeCheckUnitPriceRule()
        {
        }

        public async Task<ValidationResult> HandleAsync(UpdateInvoice02HeaderOldDecreeDto input)
        {
            if (input.InvoiceStatus == InvoiceStatus.ThayThe)
            {
                var unitPrices = input.InvoiceDetails.Where(x => x.UnitPrice < 0).ToList();
                if (unitPrices.Any())
                    return new ValidationResult(false, "Đơn giá của hóa đơn thay thế phải lớn hơn hoặc bằng 0");
            }

            return new ValidationResult(true);
        }
    }
}
