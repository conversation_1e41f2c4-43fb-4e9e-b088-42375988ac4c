using Core.Shared.Validations;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using Core.Domain.Repositories;
using VnisCore.Invoice02.Application.Invoice02.Dto;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;
using Core.Dto.Shared;
using System.Collections.Generic;
using System.Linq;
using Core.Shared.Constants;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateOldDecree
{
    /// <summary>
    /// Validate tiền tệ
    /// </summary>
    public class CreateInvoiceOldDecreeCheckExistCurrencyRule : IValidationRuleAsync<CreateInvoice02HeaderOldDecreeDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<CurrencyEntity, long> _repoCurrencyEntity;

        public CreateInvoiceOldDecreeCheckExistCurrencyRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IRepository<CurrencyEntity, long> repoCurrencyEntity)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoCurrencyEntity = repoCurrencyEntity;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice02HeaderOldDecreeDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataValidate");

            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //Nguyên tệ
            var fromCurrency = dataValidate.FirstOrDefault(x => x.Type == ValidateDataType.FromCurrency.GetHashCode());
            if (fromCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.FromCurrencyNotSet"]);

            _validationContext.AddItem("FromCurrency", new CurrencyEntity
            {
                CurrencyCode = fromCurrency.FromCurrencyCode,
                Rounding = fromCurrency.ToCurrencyRounding,
                NameVi = fromCurrency.NameVi,
                MinimumNameVi = fromCurrency.MinimumNameVi,
            });
            //Tiền tệ
            var toCurrency = dataValidate.FirstOrDefault(x => x.Type == ValidateDataType.ToCurrency.GetHashCode());
            if (toCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.ToCurrencyIncorrect"]);

            if (toCurrency == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.ToCurrencyIncorrect"]);

            _validationContext.AddItem("ToCurrency", new CurrencyEntity
            {
                CurrencyCode = toCurrency.ToCurrencyCode,
                Conversion = toCurrency.ToCurrencyConversion,
                Rounding = toCurrency.ToCurrencyRounding,
                NameVi = toCurrency.NameVi,
                MinimumNameVi = toCurrency.MinimumNameVi,
            });

            return new ValidationResult(true);
        }
    }
}
