using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Services;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateOldDecree
{
    /// <summary>
    /// Validate ngày hóa đơn: >= ActiveDate và >= LastInvoiceDate và <= Now
    /// </summary>
    public class CreateInvoiceOldDecreeCheckInvoiceDateRangeRule : IValidationRuleAsync<CreateInvoice02HeaderOldDecreeDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> _invoiceService;

        public CreateInvoiceOldDecreeCheckInvoiceDateRangeRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IInvoiceService<Invoice02HeaderEntity, Invoice02HeaderFieldEntity, Invoice02DetailFieldEntity> invoiceService)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _invoiceService = invoiceService;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice02HeaderOldDecreeDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;

            var range = await _invoiceService.InvoiceDateRangeAsync(tenantId, input.TemplateNo, input.SerialNo, null);
            if (range == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice.InvoiceDateRange.TemplateOutOfInvoiceNo"]);

            if (range.Min.Date > DateTime.Now.Date)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.InvoiceDateMin", new[] { range.Min.ToString("dd/MM/yyyy") }]);

            if (input.InvoiceDate > range.Max || input.InvoiceDate < range.Min)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.InvoiceDateRange", new[] { range.Min.ToString("dd/MM/yyyy"), range.Max.ToString("dd/MM/yyyy") }]);

            return new ValidationResult(true);
        }
    }
}
