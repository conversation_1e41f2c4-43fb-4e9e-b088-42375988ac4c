using Core.Dto.Shared;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Repositories;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateAdjustmentHeader
{
    /// <summary>
    /// check các fieldname của headerextra có đúng không
    /// </summary>
    public class CreateInvoiceAdjustmentHeaderCheckHeaderExtraRule : IValidationRuleAsync<CreateAdjustmentHeaderInvoice02HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IInvoiceHeaderFieldRepository<Invoice02HeaderFieldEntity> _repoInvoiceHeaderField;
        private readonly IValidationContext _validationContext;

        public CreateInvoiceAdjustmentHeaderCheckHeaderExtraRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IInvoiceHeaderFieldRepository<Invoice02HeaderFieldEntity> repoInvoiceHeaderField,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoiceHeaderField = repoInvoiceHeaderField;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateAdjustmentHeaderInvoice02HeaderDto input)
        {
            if (input.InvoiceHeaderExtras == null || !input.InvoiceHeaderExtras.Any())
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            var dataValidate = _validationContext.GetItem<List<CreateInvoice02GetDataValidDto>>("DataValidate");

            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //lấy các header field
            var headerFieldNames = dataValidate.Where(x => x.Type == ValidateDataType.HeaderField.GetHashCode()).Select(x => x.FieldName).ToList();

            var commandHeaderFieldNames = input.InvoiceHeaderExtras.Select(x => x.FieldName).ToList();
            var expects = commandHeaderFieldNames.Except(headerFieldNames);

            if (expects.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.HeaderExtraNotExist", new[] { string.Join(",", expects) }]);

            return new ValidationResult(true);
        }
    }
}
