using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.UpdateReplace
{
    /// <summary>
    /// Validate thông tin khách hàng có bị trùng mst không
    /// </summary>
    public class UpdateReplaceInvoiceCheckDuplicateBuyerCodeRule : IValidationRuleAsync<UpdateReplaceInvoice02HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<CustomerEntity, long> _repoCustomer;

        public UpdateReplaceInvoiceCheckDuplicateBuyerCodeRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IRepository<CustomerEntity, long> repoCustomer)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoCustomer = repoCustomer;
        }

        public async Task<ValidationResult> HandleAsync(UpdateReplaceInvoice02HeaderDto input)
        {
            if (string.IsNullOrEmpty(input.BuyerTaxCode))
                return new ValidationResult(true);

            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            //nếu là khách hàng mới
            var customer = await _repoCustomer.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.CustomerCode == input.BuyerCode && !x.IsDeleted);
            if (customer == null)
            {
                if (await _repoCustomer.AnyAsync(x => x.TaxCode == input.BuyerTaxCode && x.TenantId == tenantId && !x.IsDeleted))
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.TaxcodeDuplicate"]);
            }
            else
            {
                if (await _repoCustomer.AnyAsync(x => x.TaxCode == input.BuyerTaxCode && x.CustomerCode != input.BuyerCode && x.TenantId == tenantId && !x.IsDeleted))
                    return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.TaxcodeDuplicate"]);
            }

            return new ValidationResult(true);
        }
    }
}
