using Core.Shared.Validations;
using System;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using Core.Domain.Repositories;
using VnisCore.Invoice02.Application.Invoice02.Dto;
using Core.Shared.Factory;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateByGroupCustomer
{
    public class CreateInvoiceByGroupCustmerCheckExistTemplateRule : IValidationRuleAsync<CreateInvoice02ByGroupCustomerDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IRepository<InvoiceTemplateEntity, long> _repoInvoiceTemplate;

        public CreateInvoiceByGroupCustmerCheckExistTemplateRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IRepository<InvoiceTemplateEntity, long> repoInvoiceTemplate,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _repoInvoiceTemplate = repoInvoiceTemplate;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice02ByGroupCustomerDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var template = await _validationContext.GetOrAddItemAsync("Template", async () =>
            {
                return await _repoInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == input.TemplateNo && x.SerialNo == input.SerialNo && !x.IsDeleted);
            });

            if (template == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.TemplateIncorrect"]);

            return new ValidationResult(true);
        }
    }
}
