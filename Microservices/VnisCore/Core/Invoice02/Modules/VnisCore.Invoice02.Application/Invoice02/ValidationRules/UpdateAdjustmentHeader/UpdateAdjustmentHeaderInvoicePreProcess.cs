using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.UpdateAdjustmentHeader
{
    public class UpdateAdjustmentHeaderInvoicePreProcess : IValidationRuleAsync<UpdateAdjustmentHeaderInvoice02Dto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;

        public UpdateAdjustmentHeaderInvoicePreProcess(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(UpdateAdjustmentHeaderInvoice02Dto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;

            _validationContext.GetOrAddItem("TenantId", () =>
            {
                return tenantId;
            });

            _validationContext.GetOrAddItem("UserId", () =>
            {
                return userId;
            });
            var repoInvoice02Header = _appFactory.Repository<Invoice02HeaderEntity, long>();
            var invoice = await repoInvoice02Header.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == input.Id);
            if (invoice == null)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.InvoiceNotFound"]);

            _validationContext.GetOrAddItem("Invoice", () =>
            {
                return invoice;
            });

            return new ValidationResult(true);
        }
    }
}
