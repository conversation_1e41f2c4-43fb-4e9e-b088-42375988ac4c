using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateOldDecree
{
    /// <summary>
    /// kiểm tra exchangerate có đúng giá trị không
    /// nếu mã tiền tệ = nguyên tệ => exchangerate = 1
    /// </summary>
    public class CreateInvoiceOldDecreeCheckExchangeRateRule : IValidationRule<CreateInvoice02HeaderOldDecreeDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IValidationContext _validationContext;

        public CreateInvoiceOldDecreeCheckExchangeRateRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _validationContext = validationContext;
        }

        public ValidationResult Handle(CreateInvoice02HeaderOldDecreeDto input)
        {
            var fromCurrency = _validationContext.GetItem<CurrencyEntity>("FromCurrency");
            var toCurrency = _validationContext.GetItem<CurrencyEntity>("ToCurrency");

            if (fromCurrency.CurrencyCode == toCurrency.CurrencyCode && input.ExchangeRate != 1)
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.ExchangeRateMustBeOne"]);

            return new ValidationResult(true);
        }
    }
}
