using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Invoice02.Application.Factories.Repositories;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRules.CancelApprove
{
    public class CancelApproveInvoice02CheckStatusRule : IValidationRuleAsync<CancelApproveInvoice02HeaderDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly IValidationContext _validationContext;
        private readonly IInvoiceHeaderRepository<Invoice02HeaderEntity> _repoHeader;
        private readonly IInvoiceReferenceRepository<Invoice02ReferenceEntity> _repoReference;

        public CancelApproveInvoice02CheckStatusRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            IValidationContext validationContext,
            IInvoiceHeaderRepository<Invoice02HeaderEntity> repoHeader,
            IInvoiceReferenceRepository<Invoice02ReferenceEntity> repoReference)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _validationContext = validationContext;
            _repoHeader = repoHeader;
            _repoReference = repoReference;
        }

        public async Task<ValidationResult> HandleAsync(CancelApproveInvoice02HeaderDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            if (input.Ids == null || !input.Ids.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.CancelApprove.InvoiceIdsNotEmpty"]);

            var invoices = await _repoHeader.GetByIdsAsync(input.Ids);
            if (invoices == null || !invoices.Any())
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.InvoiceNotFound"]);

            // nếu hóa đơn đã ký mà không phải chờ duyệt của xóa bỏ thì không được hủy duyệt
            if (invoices.Any(x=>x.SignStatus == SignStatus.DaKy.GetHashCode() && x.ApproveDeleteStatus != ApproveStatus.ChoDuyet.GetHashCode()))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.CancelApprove.ExitSignedNotApprove"]);

            // nếu hóa đơn chưa ký mà không phải chờ duyệt của xóa hủy thì không được hủy duyệt
            if (invoices.Any(x => x.SignStatus != SignStatus.DaKy.GetHashCode() && x.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode()))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.CancelApprove.ExitUnSignedNotApprove"]);

            _validationContext.GetOrAddItem<List<Invoice02HeaderEntity>>("Invoices", () =>
            {
                return invoices;
            });

            return new ValidationResult(true);
        }
    }
}
