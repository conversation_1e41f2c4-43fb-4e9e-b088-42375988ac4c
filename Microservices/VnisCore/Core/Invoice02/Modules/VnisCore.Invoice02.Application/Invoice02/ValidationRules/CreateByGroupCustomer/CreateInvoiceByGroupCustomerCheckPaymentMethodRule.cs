using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Shared.Services;
using Core.Shared.Validations;
using Microsoft.Extensions.Localization;
using System;
using System.Linq;
using System.Threading.Tasks;
using VnisCore.Invoice02.Application.Invoice02.Dto;

namespace VnisCore.Invoice02.Application.Invoice02.ValidationRule.CreateByGroupCustomer
{
    public class CreateInvoiceByGroupCustomerCheckPaymentMethodRule : IValidationRuleAsync<CreateInvoice02ByGroupCustomerDto, ValidationResult>
    {
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;
        private readonly IAppFactory _appFactory;
        private readonly ISettingService _settingService;
        private readonly IValidationContext _validationContext;

        public CreateInvoiceByGroupCustomerCheckPaymentMethodRule(
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory,
            ISettingService settingService,
            IValidationContext validationContext)
        {
            _localizer = localizer;
            _appFactory = appFactory;
            _settingService = settingService;
            _validationContext = validationContext;
        }

        public async Task<ValidationResult> HandleAsync(CreateInvoice02ByGroupCustomerDto input)
        {
            var tenantId = _appFactory.CurrentTenant.Id.Value;
            var userId = _appFactory.CurrentUser.Id.Value;
            //var tenantId = _validationContext.GetItem<Guid>("TenantId");

            var setting = await _settingService.GetByCodeAsync(tenantId, SettingKey.PaymentMethod.ToString());
            //var setting = Utilities.SettingPaymentMethod;

            if (setting == null || string.IsNullOrEmpty(setting.Value))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.PaymentMethodNotYet", new[] { input.PaymentMethod }]);

            //cắt chuỗi value setting để lấy value của paymentmethod
            var values = setting.Value.Split(";");
            if (!values.Contains(input.PaymentMethod))
                return new ValidationResult(false, _localizer["Vnis.BE.Invoice02.PaymentMethodNotYet", new[] { input.PaymentMethod }]);

            return new ValidationResult(true);
        }
    }
}
