using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using Core.Domain.Repositories;
using Core.Shared.Models;
using VnisCore.Invoice02.Application.Factories.Models;
using Core.Shared.Factory;
using Core.Shared.Constants;
using Core.Shared.Extensions;

namespace VnisCore.Invoice02.Application.Factories.Repositories
{
    public class InvoiceDocumentInfoRepository<T> : IInvoiceDocumentInfoRepository<T>
        where T : BaseInvoiceDocumentInfo
    {
        private readonly IRepository<T, long> _repoDocumentInfo;
        private readonly IAppFactory _appFactory;

        public InvoiceDocumentInfoRepository(IAppFactory appFactory, IRepository<T, long> repoDocumentInfo)
        {
            _repoDocumentInfo = repoDocumentInfo;
            _appFactory = appFactory;
        }

        public async Task<T> GetByCodeInvoiceHeaderAsync(Guid tenantId, long invoiceId)
        {
            return await _repoDocumentInfo
                .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.InvoiceHeaderId == invoiceId);
        }

        public async Task<List<T>> GetByCodeInvoiceHeadersAsync(Guid tenantId, IEnumerable<long> invoiceIds)
        {
            return await _repoDocumentInfo
                .Where(x => x.TenantId == tenantId && invoiceIds.Contains(x.InvoiceHeaderId))
                .ToListAsync();
        }

        public async Task<List<T>> GetByCodeInvoiceHeadersAsNoTrackingAsync(Guid tenantId, IEnumerable<long> invoiceIds)
        {
            return await _repoDocumentInfo
                .Where(x => x.TenantId == tenantId && invoiceIds.Contains(x.InvoiceHeaderId))
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<T> GetByCodeFileAsync(long fileId)
        {
            return await _repoDocumentInfo
                .FirstOrDefaultAsync(x => x.FileId == fileId);
        }

        public async Task<Paged<T>> PagingAsync(Guid tenantId, PagingInvoiceDocumentInfoModel paging)
        {
            var query = Filter(tenantId, paging);
            return await query.OrderByDescending(x => x.DocumentDate)
                              .ToPaginationAsync(paging);
        }

        private IQueryable<T> Filter(Guid tenantId, PagingInvoiceDocumentInfoModel paging)
        {
            IQueryable<T> items = _repoDocumentInfo.Where(x => x.TenantId == tenantId);

            if (!string.IsNullOrEmpty(paging.Q))
                items = items.Where(x => x.DocumentNo.Contains(paging.Q));

            if (!string.IsNullOrEmpty(paging.DocumentNo))
                items = items.Where(x => x.DocumentNo.Contains(paging.DocumentNo));

            if (paging.Type != null && paging.Type != default(DocumentTemplateType))
                items = items.Where(x => x.Type == paging.Type.Value.GetHashCode());

            var fromDate = paging.DocumentDateFrom.Date;
            var toDate = paging.DocumentDateTo.AddDays(1).Date;
            items = items.Where(x => x.TenantId == tenantId
                                     && x.DocumentDate >= fromDate
                                     && x.DocumentDate < toDate);
            return items;
        }
        public async Task<T> GetByCodeInvoiceAsync(long invoiceId)
        {
            return await _repoDocumentInfo
                .FirstOrDefaultAsync(x => x.InvoiceHeaderId == invoiceId);
        }
    }
}
