using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;

namespace VnisCore.Invoice02.Application.Factories.Repositories
{
    public interface IInvoiceTemplateRepository
    {
        Task<InvoiceTemplateEntity> GetTemplateRawAsync(Guid tenantId, short templateNo, string serialNo);
    }
}
