using Core.Shared.Factory;
using Core.Shared.Services;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Invoice02.Application.Factories.Models;
using Dapper;
using Core.Shared.Extensions;
using System.Globalization;
using Microsoft.Extensions.Localization;
using Core.Localization.Resources.AbpLocalization;
using Core;
using Core.Shared.Constants;
using Core.SettingManagement;
using Newtonsoft.Json;
using Core.Shared.Models;
using System.Linq;
using Core.Caching;
using Microsoft.Extensions.Caching.Distributed;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using System.Text;

namespace VnisCore.Invoice02.Application.Factories.Services
{
    public class NumberService<THeader> : INumberService<THeader> where THeader : BaseInvoiceHeader
    {
        private readonly LockerStore _lockers;
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizer;

        public NumberService(
            LockerStore lockers,
            IStringLocalizer<CoreLocalizationResource> localizer,
            IAppFactory appFactory)
        {
            _lockers = lockers;
            _appFactory = appFactory;
            _localizer = localizer;
        }

        /// <summary>
        /// Sinh số hóa đơn
        /// </summary>
        /// <param name="tenantCode">Tenant cần sinh số</param>
        /// <param name="templateNo">Mẫu só</param>
        /// <param name="serialNo">Ký hiệu</param>
        /// <param name="date">Ngày</param>
        /// <returns></returns>
        public async Task<NumberResponseModel> GenerateNumberAsync(Guid tenantCode, short templateNo, string serialNo, DateTime date, int errorTimes = 0)
        {
            NumberResponseModel response;
            var locker = _lockers.GetOrAddLocker($"{tenantCode}|{templateNo}|{serialNo}");
            try
            {
                await locker.WaitAsync();
                response = await GenerateAsync(tenantCode, templateNo, serialNo, date);
            }
            catch (Exception ex)
            {
                return null;
            }
            finally
            {
                locker.Release();
            }

            return response;
        }

        /// <summary>
        /// Sinh hàng loạt số hóa đơn
        /// </summary>
        /// <param name="tenantCode">Tenant cần sinh số</param>
        /// <param name="templateNo">Mẫu só</param>
        /// <param name="serialNo">Ký hiệu</param>
        /// <param name="date">Ngày</param>
        /// <returns></returns>
        public async Task<List<NumberResponseModel>> GenerateBatchNumberAsync(Guid tenantCode, short templateNo, string serialNo, DateTime date, int numberQuantity, int errorTimes = 0)
        {
            List<NumberResponseModel> response;
            var locker = _lockers.GetOrAddLocker($"{tenantCode}|{templateNo}|{serialNo}");
            try
            {
                await locker.WaitAsync();
                response = await GenerateBatchAsync(tenantCode, templateNo, serialNo, date, numberQuantity);
            }
            catch (Exception ex)
            {
                return null;
            }
            finally
            {
                locker.Release();
            }

            return response;
        }

        public async Task SaveNumberAsync(long id, NumberResponseModel number)
        {
            //var invoice = await _repoInvoiceHeader.GetByIdAsync(id);

            var invoice = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<Invoice02HeaderEntity>($@"
                            SELECT ""Id"",
                            ""Number"",
                            ""InvoiceNo"",
                            ""RegistrationHeaderId"",
                            ""RegistrationDetailId"",
                            ""IssuedTime""
                            FROM ""Invoice02Header"" 
                            WHERE ""Id""={id}
                            FETCH FIRST 1 ROW ONLY ");


            //invoice.Number = number.Number;
            //invoice.InvoiceNo = number.InvoiceNo;
            //invoice.RegistrationHeaderId = number.RegistrationHeaderId;
            //invoice.RegistrationDetailId = number.RegistrationDetailId;
            ////invoice.IssuedAt = DateTime.Now;

            //await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var q = $@"
                            UPDATE  ""{DatabaseExtension<THeader>.GetTableName()}""
                            SET     ""Number""={number.Number}, 
                                    ""InvoiceNo""='{number.InvoiceNo}', 
                                    ""RegistrationHeaderId""={number.RegistrationHeaderId},
                                    ""RegistrationDetailId""={number.RegistrationDetailId},
                                    ""IssuedTime""='{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                            WHERE   ""Id""={invoice.Id}
                    ";
            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(q);
        }

        public async Task SaveBatchNumberAsync(List<long> ids, List<NumberResponseModel> numbers)
        {
            var invoices = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice02HeaderEntity>($@"
                            SELECT ""Id"",
                            ""Number"",
                            ""InvoiceNo"",
                            ""RegistrationHeaderId"",
                            ""RegistrationDetailId"",
                            ""IssuedTime""
                            FROM ""Invoice02Header"" 
                            WHERE ""Id"" in ({string.Join(",", ids)})");
            var i = 0;

            StringBuilder rawSql = new StringBuilder($@"BEGIN ");
            foreach (var invoice in invoices.ToList())
            {
                var number = numbers[i];

                //invoice.Number = number.Number;
                //invoice.InvoiceNo = number.InvoiceNo;
                //invoice.RegistrationHeaderId = number.RegistrationHeaderId;
                //invoice.RegistrationDetailId = number.RegistrationDetailId;
                //invoice.IssuedAt = DateTime.Now;

                rawSql.Append($@" 
                            UPDATE  ""{DatabaseExtension<THeader>.GetTableName()}""
                            SET     ""Number""= {number.Number}, 
                                    ""InvoiceNo""= '{number.InvoiceNo}', 
                                    ""RegistrationHeaderId""= {number.RegistrationHeaderId},
                                    ""RegistrationDetailId""= {number.RegistrationDetailId},
                                    ""IssuedTime""= '{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                            WHERE   ""Id""= {invoice.Id};
                ");

                i++;
            }
            rawSql.Append($@" END; ");
            await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(rawSql.ToString());
        }

        private async Task<NumberResponseModel> GenerateAsync(Guid tenantId, short templateNo, string serialNo, DateTime date, int errorTimes = 0)
        {
            //var template = await _repoInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == templateNo && x.SerialNo == serialNo && !x.IsDeleted);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var template = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<InvoiceTemplateEntity>($@"
                            SELECT * 
                            FROM ""InvoiceTemplate"" 
                            WHERE ""TenantId""='{rawTenantId}' AND ""TemplateNo""={templateNo} AND ""SerialNo""='{serialNo}' AND ""IsDeleted""=0
                            FETCH FIRST 1 ROW ONLY ");

            if (template == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.GenerateNumber.InvoiceTemplateNotFound", new string[] { tenantId.ToString(), templateNo.ToString(), serialNo }]);

            //var store = await _repoMonitorInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantId && x.Id == template.Id);

            var store = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<MonitorInvoiceTemplateEntity>($@"
                            SELECT * 
                            FROM ""MonitorInvoiceTemplate"" 
                            WHERE ""TenantId""='{rawTenantId}' AND ""Id""={template.Id}
                            FETCH FIRST 1 ROW ONLY ");

            if (store == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.GenerateNumber.MonitorNotFound", new string[] { tenantId.ToString(), templateNo.ToString(), serialNo }]);

            //Đồng bộ lại dữ liệu Đăng ký phát hành, chuyển dải số nếu hết số trước khi sinh số
            //NOTE: Case này chỉ xảy ra khi sinh số cuối cùng của dải xong nhưng ko chuyển sang dải mới được. 1 số nguyên nhân có thể xảy ra:
            //1. Vừa sinh số xong chưa kịp chuyển dải thì mất điện
            //2. Vừa sinh số xong chưa kịp chuyển dải thì reset app
            //3. Sinh số xong, quá trình chuyển dải thất bại
            //Do ko sử dụng transaction để tránh lock bảng Monitor nên đây là cách xử lý để ko bị nhảy sai dải số
            if (store.CurrentNumber.Value == store.EndNumber.Value || store.CurrentNumber.Value == store.StartNumber.Value)
                await SyncData(tenantId, template, false);

            if (date < store.ActiveDate.Value.Date)
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.GenerateNumber.MonitorActiveDateAt", new string[] { tenantId.ToString(), templateNo.ToString(), serialNo, store.StartNumber.ToString(), store.EndNumber.ToString(), $"{store.ActiveDate.Value:dd/MM/yyyy}" }]);

            if (store.LastDocumentDate.HasValue && store.LastDocumentDate.Value.Date > date)
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.GenerateNumber.InvoiceDateFromLastDocumentDate", new string[] { $"{store.LastDocumentDate.Value:dd/MM/yyyy}" }]);

            var response = new NumberResponseModel();
            response.Date = date;
            response.SerialNo = serialNo;
            response.TemplateNo = templateNo;
            response.TenantCode = tenantId;
            response.RegistrationHeaderId = store.RegistrationHeaderId.Value;
            //response.RegistrationDetailId = store.RegistrationDetailId.Value;

            //Sinh số
            var number = store.CurrentNumber.Value + 1;
            response.Number = number;
            response.InvoiceNo = number.ToString("00000000");

            try
            {
                var q = $@"
                            UPDATE  ""MonitorInvoiceTemplate""
                            SET     ""CurrentNumber""={number}, 
                                    ""LastDocumentDate""='{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                    ""ConcurrencyStamp""='{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                            WHERE   ""Id""={store.Id}
                        ";
                await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(q);

            }
            //Nếu đã có thread khác update bản ghi thì chạy lại hàm sinh số lần nữa để sinh số tiếp theo
            catch (DbUpdateConcurrencyException ex)
            {
                errorTimes++;

                //Nếu chạy lại {max} lần vẫn lỗi thì báo lỗi
                if (errorTimes == 3)
                    throw ex;

                response = await GenerateAsync(tenantId, templateNo, serialNo, date, errorTimes);
            }

            ////check xem có tới thời điểm có thể tự động tạo mẫu hóa đơn chưa
            //if (await IsThreholdAutoCreateInvoiceTemplateAsync(tenantId, template, store))
            //{
            //    //tự động dki mẫu theo cấu hình
            //    var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
            //    await distributedEventBus.PublishAsync(new CreateInvoiceTemplateAutoEventResultData
            //    {
            //        SerialNo = template.SerialNo,
            //        TemplateNo = template.TemplateNo,
            //        TenantId = tenantId,
            //        UserId = template.CreatorId.Value,
            //    });
            //}

            //Đồng bộ lại dữ liệu Đăng ký phát hành
            if (store.EndNumber.Value == number || store.StartNumber.Value == number)
                await SyncData(tenantId, template, true);

            return response;
        }

        private async Task<List<NumberResponseModel>> GenerateBatchAsync(Guid tenantId, short templateNo, string serialNo, DateTime date, int numberQuantity, int errorTimes = 0)
        {
            //var template = await _repoInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantCode && x.TemplateNo == templateNo && x.SerialNo == serialNo && !x.IsDeleted);
            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var template = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<InvoiceTemplateEntity>($@"
                            SELECT * 
                            FROM ""InvoiceTemplate"" 
                            WHERE ""TenantId""='{rawTenantId}' AND ""TemplateNo""={templateNo} AND ""SerialNo""='{serialNo}' AND ""IsDeleted""=0
                            FETCH FIRST 1 ROW ONLY ");

            if (template == null)
                //throw new Exception($"Không tìm thấy mẫu hóa đơn TenantId: {tenantId} - TemplateNo: {templateNo} - SerialNo: {serialNo}");
                throw new Exception(_localizer[$"Vnis.BE.Invoice02.InvoiceTemplateNotFound,{tenantId},{templateNo},{serialNo}"]);

            //var store = await _repoMonitorInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantCode && x.Id == template.Id);
            var store = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<MonitorInvoiceTemplateEntity>($@"
                            SELECT * 
                            FROM ""MonitorInvoiceTemplate"" 
                            WHERE ""TenantId""='{rawTenantId}' AND ""Id""={template.Id}
                            FETCH FIRST 1 ROW ONLY ");

            if (store == null)
                //throw new Exception($"Mẫu hóa đơn TenantId: {tenantId} - TemplateNo: {templateNo} - SerialNo: {serialNo} không có trong kho số");
                throw new Exception(_localizer[$"Vnis.BE.Invoice02.MonitorNotFound,{tenantId},{templateNo},{serialNo}"]);

            if (store.CurrentNumber == null)
                //throw new Exception($"Mẫu hóa đơn TenantId: {tenantId} - TemplateNo: {templateNo} - SerialNo: {serialNo} chưa đăng ký phát hành");
                throw new Exception(_localizer[$"Vnis.BE.Invoice02.MonitorNotYet,{tenantId},{templateNo},{serialNo}"]);

            //Đồng bộ lại dữ liệu Đăng ký phát hành, chuyển dải số nếu hết số trước khi sinh số
            //NOTE: Case này chỉ xảy ra khi sinh số cuối cùng của dải xong nhưng ko chuyển sang dải mới được. 1 số nguyên nhân có thể xảy ra:
            //1. Vừa sinh số xong chưa kịp chuyển dải thì mất điện
            //2. Vừa sinh số xong chưa kịp chuyển dải thì reset app
            //3. Sinh số xong, quá trình chuyển dải thất bại
            //Do ko sử dụng transaction để tránh lock bảng Monitor nên đây là cách xử lý để ko bị nhảy sai dải số
            if (store.CurrentNumber.Value == store.EndNumber.Value || store.CurrentNumber.Value == store.StartNumber.Value)
                await SyncData(tenantId, template, false);

            if (date < store.ActiveDate.Value.Date)
                //throw new Exception($"Mẫu hóa đơn TenantId: {tenantId} - TemplateNo: {templateNo} - SerialNo: {serialNo} có dải từ số {store.StartNumber} đến số {store.EndNumber} sẽ được sử dụng vào ngày {store.ActiveDate.Value:dd/MM/yyyy}");
                throw new Exception(_localizer[$"Vnis.BE.Invoice02.MonitorActiveDateAt,{tenantId},{templateNo},{serialNo},{store.StartNumber},{store.EndNumber},{store.ActiveDate.Value:dd/MM/yyyy}"]);

            if (store.LastDocumentDate.HasValue && store.LastDocumentDate.Value.Date > date)
                //throw new Exception($"Ngày tạo số phải bắt đầu từ ngày {store.LastDocumentDate.Value:dd/MM/yyyy}");
                throw new Exception(_localizer[$"Vnis.BE.Invoice02.InvoiceDateFromLastDocumentDate,{store.LastDocumentDate.Value:dd/MM/yyyy}"]);

            var response = new List<NumberResponseModel>();
            for (var i = 0; i < numberQuantity; i++)
            {
                var numberModel = new NumberResponseModel();
                numberModel.Date = date;
                numberModel.SerialNo = serialNo;
                numberModel.TemplateNo = templateNo;
                numberModel.TenantCode = tenantId;
                numberModel.RegistrationHeaderId = store.RegistrationHeaderId.Value;
                //numberModel.RegistrationDetailId = store.RegistrationDetailId.Value;

                //Sinh số
                var number = store.CurrentNumber.Value + 1;
                numberModel.Number = number;
                numberModel.InvoiceNo = number.ToString("00000000");

                response.Add(numberModel);
                try
                {
                    //store.CurrentNumber = number;
                    //store.LastDocumentDate = date;
                    //store.ConcurrencyStamp = DateTime.Now;

                    //await _repoMonitorInvoiceTemplate.UpdateAsync(store);
                    //await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                    var q = $@"
                            UPDATE  ""MonitorInvoiceTemplate""
                            SET     ""CurrentNumber""={number}, 
                                    ""LastDocumentDate""='{date.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}', 
                                    ""ConcurrencyStamp""='{DateTime.Now.ToString(FormatDate._ddMMMyyhhmmssffffffftt, CultureInfo.GetCultureInfo("en-US"))}'
                            WHERE   ""Id""={store.Id}
                        ";
                    await _appFactory.VnisCoreOracle.Connection.ExecuteScalarAsync(q);
                }
                //Nếu đã có thread khác update bản ghi thì chạy lại hàm sinh số lần nữa để sinh số tiếp theo
                catch (DbUpdateConcurrencyException ex)
                {
                    errorTimes++;

                    //Nếu chạy lại {max} lần vẫn lỗi thì báo lỗi
                    if (errorTimes == 3)
                        throw ex;

                    response = await GenerateBatchAsync(tenantId, templateNo, serialNo, date, numberQuantity, errorTimes);
                }

                //Đồng bộ lại dữ liệu Đăng ký phát hành
                if (store.EndNumber.Value == number || store.StartNumber.Value == number)
                    await SyncData(tenantId, template, true);
            }

            return response;
        }

        private async Task SyncData(Guid tenantId, InvoiceTemplateEntity template, bool isSaveNumber)
        {
            //var monitor = await _repoMonitorInvoiceTemplate.FirstOrDefaultAsync(x => x.TenantId == tenantCode && x.Id == template.Id);

            var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
            var monitor = await _appFactory.VnisCoreOracle.Connection.QueryFirstOrDefaultAsync<MonitorInvoiceTemplateEntity>($@"
                            SELECT * 
                            FROM ""MonitorInvoiceTemplate"" 
                            WHERE ""TenantId""='{rawTenantId}' AND ""Id""={template.Id}
                            FETCH FIRST 1 ROW ONLY ");

            if (monitor == null)
                throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.GenerateNumber.MonitorNotFound", new string[] { tenantId.ToString(), template.TemplateNo.ToString(), template.SerialNo }]);

            if (monitor.CurrentNumber.Value == monitor.EndNumber.Value)
                await NextMonitor(tenantId, template, monitor, isSaveNumber);

            if (monitor.CurrentNumber.Value == monitor.StartNumber.Value)
                await LockTemplateAsync(template);
        }

        /// <summary>
        /// Chuyển dải số mới khi đã sinh số cuối cùng của dải
        /// </summary>
        /// <param name="tenantCode"></param>
        /// <param name="idUser"></param>
        /// <param name="template"></param>
        /// <param name="repoMonitor"></param>
        /// <param name="monitor"></param>
        /// <returns></returns>
        private async Task NextMonitor(Guid tenantId, InvoiceTemplateEntity template, MonitorInvoiceTemplateEntity monitor, bool isSaveNumber)
        {
            //nếu đã hết số max
            if (monitor.CurrentNumber == StaticData.MaxInvoiceNo)
            {
                if (!isSaveNumber)
                {
                    throw new UserFriendlyException(_localizer["Vnis.BE.Invoice.GenerateNumber.OutOfNumber", new string[] { tenantId.ToString(), template.TemplateNo.ToString(), template.SerialNo }]);
                }
            }
            else
            {
                //tự động update số thành max
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync($@"
                            UPDATE ""MonitorInvoiceTemplate"" 
                            SET ""EndNumber"" = {StaticData.MaxInvoiceNo}
                            WHERE ""Id""='{monitor.Id}' ");

                ////check xem có tới thời điểm có thể tự động tạo mẫu hóa đơn chưa
                //if (await IsThreholdAutoCreateInvoiceTemplateAsync(tenantId, template, monitor))
                //{
                //    //tự động dki mẫu theo cấu hình
                //    var distributedEventBus = _appFactory.GetServiceDependency<IDistributedEventBus>();
                //    await distributedEventBus.PublishAsync(new CreateInvoiceTemplateAutoEventResultData
                //    {
                //        SerialNo = template.SerialNo,
                //        TemplateNo = template.TemplateNo,
                //        TenantId = tenantId,
                //        UserId = template.CreatorId.Value,
                //    });
                //}
            }
        }

        private async Task LockTemplateAsync(InvoiceTemplateEntity template)
        {
            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync($@"
                                                                UPDATE ""InvoiceTemplate"" 
                                                                SET ""IsLocked"" =  1
                                                                WHERE ""Id"" = {template.Id}");
        }

        private async Task<bool> IsThreholdAutoCreateInvoiceTemplateAsync(Guid tenantId, InvoiceTemplateEntity template, MonitorInvoiceTemplateEntity monitor)
        {
            try
            {
                var rawTenantId = OracleExtension.ConvertGuidToRaw(tenantId);
                var serialNoMissJump = template.SerialNo.Substring(0, template.SerialNo.Length - 2);
                var keyTemplateSerial = $"{template.TemplateNo}{serialNoMissJump}x";
                var setting = await GetSettingAsync(tenantId, rawTenantId, GroupSettingKey.RegistrationInvoice.ToString(), SettingKey.AutoRegistration.ToString());
                if (setting == null)
                    return false;

                var extraProperties = setting.ExtraProperties;

                var setTemplateSerials = JsonConvert.DeserializeObject<List<RegistrationInvoiceSettingModel>>(extraProperties["key"].ToString());
                var configs = setTemplateSerials.FirstOrDefault(x => x.Key == keyTemplateSerial).Value;

                var remainingInvoiceThreshold = configs.FirstOrDefault(x => x.Field == "remainingInvoiceThreshold")?.Value;
                if (string.IsNullOrEmpty(remainingInvoiceThreshold) || int.TryParse(remainingInvoiceThreshold, out int numRemainingInvoiceThreshold))
                    return false;

                if ((monitor.EndNumber - monitor.CurrentNumber) > numRemainingInvoiceThreshold)
                    return false;

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        private async Task<Setting> GetSettingAsync(Guid tenantId, string rawTenantId, string groupCode, string code)
        {
            var cache = _appFactory.GetServiceDependency<IDistributedCache<Setting>>();
            var cacheKey = SettingCacheItem.CalculateCacheKey($"{tenantId}:{code}", null, null);

            var setting = await cache.GetAsync(cacheKey);
            if (setting != null)
                return setting;

            setting = await _appFactory.AuthDatabase.Connection.QueryFirstOrDefaultAsync<Setting>($@"
                                                    SELECT * 
                                                    FROM ""VnisSettings"" 
                                                    WHERE ""GroupCode"" = '{groupCode}' AND ""Code"" = '{code}' 
                                                    AND ""TenantId"" = '{rawTenantId}' And ""IsDeleted"" = 0");

            if (setting == null)
                return null;

            await cache.SetAsync(cacheKey, setting, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1)
            });

            return setting;
        }
    }
}
