using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Invoice02.Application.Factories.Models;

namespace VnisCore.Invoice02.Application.Factories.Services
{
    public interface INumberService<THeader> where THeader : BaseInvoiceHeader
    {
        /// <summary>
        /// Sinh số hóa đơn
        /// </summary>
        /// <param name="tenantCode">Tenant cần sinh số</param>
        /// <param name="templateNo">Mẫu só</param>
        /// <param name="serialNo"><PERSON><PERSON> hiệu</param>
        /// <param name="date">Ngày</param>
        /// <returns></returns>
        Task<NumberResponseModel> GenerateNumberAsync(Guid tenantCode, short templateNo, string serialNo, DateTime date, int errorTimes = 0);

        /// <summary>
        /// Sinh hàng loạt số hóa đơn
        /// </summary>
        /// <param name="tenantCode">Tenant cần sinh số</param>
        /// <param name="templateNo">Mẫu só</param>
        /// <param name="serialNo"><PERSON>ý hiệu</param>
        /// <param name="date">Ngày</param>
        /// <returns></returns>
        Task<List<NumberResponseModel>> GenerateBatchNumberAsync(Guid tenantCode, short templateNo, string serialNo, DateTime date, int numberQuantity, int errorTimes = 0);

        /// <summary>
        /// Lưu số
        /// </summary>
        /// <param name="code"></param>
        /// <param name="number"></param>
        Task SaveNumberAsync(long id, NumberResponseModel number);

        /// <summary>
        /// Lưu 1 loạt số
        /// </summary>
        /// <param name="code"></param>
        /// <param name="number"></param>
        Task SaveBatchNumberAsync(List<long> ids, List<NumberResponseModel> numbers);
    }
}
