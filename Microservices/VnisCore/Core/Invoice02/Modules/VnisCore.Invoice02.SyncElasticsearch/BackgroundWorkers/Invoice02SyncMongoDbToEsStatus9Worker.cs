using System.Threading.Tasks;
using Core.BackgroundWorkers;
using Core.Shared.Constants;
using Core.Threading;
using Microsoft.Extensions.DependencyInjection;
using VnisCore.Invoice02.SyncElasticsearch.Interface;

namespace VnisCore.Invoice02.SyncElasticsearch.BackgroundWorkers
{
    public class Invoice02SyncMongoDbToEsStatus9Worker : AsyncPeriodicBackgroundWorkerBase
    {
        public Invoice02SyncMongoDbToEsStatus9Worker(
            AbpAsyncTimer timer,
            IServiceScopeFactory serviceScopeFactory) :
            base(timer, serviceScopeFactory)
        {
            Timer.Period = 1000; //30 s
            //int.TryParse(configuration.GetSection("Settings:TimePeriod").Value, out var period);
            //if (period > 0)
            //    timer.Period = period * 1000;
        }

        protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
        {
            await workerContext
                .ServiceProvider
                .GetRequiredService<IInvoice02SyncElasticsearchBusiness>()
                .SyncMongoDbToElasticsearch(SyncElasticSearchStatus.PendingSyncCreateInvoiceError.GetHashCode());
        }
    }
}