{
  "ConnectionStrings": {
    "Default": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=pdbvnis)))';User Id=einvoiceauth50staging;Password=Vnis@12A",
    "VnisCoreOracle": "Data Source='(DESCRIPTION= (ADDRESS=(PROTOCOL=tcp)(HOST=**************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=pdbvnis)))';User Id=einvoice50staging;Password=Vnis@12A",
    "VnisCoreMongoDb": "***************************************************************************************"
  },
  "Service": {
    "Name": "VnisCore.Invoice02.SyncElasticsearch",
    "Title": "VnisCore.Invoice02.SyncElasticsearch",
    "BaseUrl": "invoice02-sync-to-es",
    "AuthApiName": "VnisCore.Invoice02.SyncElasticsearch"
  },
  "Redis": {
    "IsUsing": "true",
    "Configuration": "**************,allowAdmin=true"
  },
  "Elasticsearch": {
    "Urls": "http://**************:9200"
  },
  "Settings": {
    "MaxDocumentIndexEs": 200,
    "IsEnableTenantGroupPrivate": 0,
    "TenantGroup": "0,1.1,1.2,1.3", // nhóm khách hàng được index riêng
    "TenantGroupsPrivate": "1.1,1.2,1.3", // danh sách khách  hàng index riêng, nếu IsEnableTenantGroupPrivate = 1 thì sẽ bỏ những group này không index
    "SchemaAuth": "einvoiceauth50staging.",
    "IsEnableSyncMongoDbToEsStatus0Worker": 1,
    "IsEnableSyncMongoDbToEsStatus2Worker": 1,
    "IsEnableSyncOracleToEsWorker": 0,
    "IsEnableReSyncOracleToEsWorker": 1,
    "IsEnableReSyncMongoToEsWorker": 1,
    "OracleElasticSearchStatus": 0
  },
  "Logging": {
    "RootFolder": {
      "Folder": "/home/<USER>/microservice-core50-staging/backgroundworkers/group-x/invoice02/elasticsearch"
    }
  }
}