using AutoMapper;

using Core.DependencyInjection;
using Core.Shared.Constants;
using Core.Shared.Factory;
using Core.Tvan.Constants;

using Dapper;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Tvan.Vnpay.Invoice01WithoutCode.Dtos;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace Tvan.Vnpay.Invoice01WithoutCode.Repositories
{
    public interface IInvoice01HeaderRepository
    {
        Task<List<Invoice01HeaderEntity>> GetListByIdsTemplateAsync(string tenantId, List<long> idsTemplate);

        Task<List<Invoice01HeaderEntity>> GetListByIdsAsync(List<long> idsInvoiceHeader, string tenantId);

        Task UpdateStatusTvanAsync(List<long> idInvoiceHeaders, TvanStatus tvanStatus);
    }

    public class Invoice01HeaderRepository : IInvoice01HeaderRepository, ITransientDependency
    {
        private readonly IAppFactory _appFactory;

        public Invoice01HeaderRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<List<Invoice01HeaderEntity>> GetListByIdsAsync(List<long> idsInvoiceHeader, string tenantId)
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Invoice01HeaderDto, Invoice01HeaderEntity>().ReverseMap();
            });
            var mapper = new Mapper(config);

            var query = @$"SELECT ""Number"" FROM ""Invoice01Header"" WHERE ""IsDeleted"" = 0 AND ""Id"" IN({string.Join(", ", idsInvoiceHeader.Select(x => $@"'{x}'"))}) ORDER BY ""Number"" DESC";

            var datas = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderDto>(query.ToString());
            var headers = datas.Select(x => mapper.Map<Invoice01HeaderDto, Invoice01HeaderEntity>(x)).ToList();

            return headers;
        }

        public async Task<List<Invoice01HeaderEntity>> GetListByIdsTemplateAsync(string tenantId, List<long> idsTemplate)
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Invoice01HeaderDto, Invoice01HeaderEntity>().ReverseMap();
            });
            var mapper = new Mapper(config);


            var query = @$"SELECT * FROM (SELECT ""Id"" FROM ""Invoice01Header"" WHERE ""IsDeleted"" = 0 AND 
                                                                   ""IsDeclared"" = 0 AND
                                                                   ""TenantId"" = '{tenantId}' AND 
                                                                   ""SignStatus"" = {(short)SignStatus.DaKy} AND
                                                                   (""StatusTvan"" = {(short)TvanStatus.UnSent} OR ""StatusTvan"" = {(short)TvanStatus.SendError}) AND
                                                                   ""InvoiceTemplateId"" IN ({string.Join(", ", idsTemplate.Select(x => $@"'{x}'"))}) ORDER BY ""Id"" DESC ) WHERE ROWNUM  < 1000";

            var datas = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice01HeaderDto>(query.ToString());
            var headers = datas.Select(x => mapper.Map<Invoice01HeaderDto, Invoice01HeaderEntity>(x)).ToList();

            return headers;
        }

        public async Task UpdateStatusTvanAsync(List<long> idInvoiceHeaders, TvanStatus tvanStatus)
        {

            var sql = $@"UPDATE ""Invoice01Header"" SET ""StatusTvan"" = {(short)tvanStatus} WHERE ""Id"" in ({string.Join(",", idInvoiceHeaders)}) AND ""StatusTvan"" != {(short)tvanStatus} ";
            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sql);
        }
    }
}
