using Core.Shared.Factory;

using Dapper;

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;

namespace Tvan.Vnpay.Invoice03WithoutCode.Repositories
{
    public interface IInvoice03XmlRepository
    {
        Task<List<Invoice03XmlEntity>> GetByInvoiceHeaderIdsAsync(string tenantId, List<long> idsInvoiceHeader);

        Task<Invoice03XmlEntity> GetByInvoiceHeaderIdAsync(string tenantId, long idInvoiceHeader);
    }

    public class Invoice03XmlRepository : IInvoice03XmlRepository
    {
        private readonly IAppFactory _appFactory;

        public Invoice03XmlRepository(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<Invoice03XmlEntity> GetByInvoiceHeaderIdAsync(string tenantId, long idInvoiceHeader)
        {
            var query = @$"SELECT * FROM ""Invoice03Xml"" WHERE ""IsDeleted"" = 0 AND ""TenantId"" = '{tenantId}' AND ""InvoiceHeaderId"" = {idInvoiceHeader}";

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice03XmlEntity>(query);

            if (data.Any())
                return data.FirstOrDefault();

            return null;
        }

        public async Task<List<Invoice03XmlEntity>> GetByInvoiceHeaderIdsAsync(string tenantId, List<long> idsInvoiceHeader)
        {
            var query = @$"SELECT * FROM ""Invoice03Xml"" WHERE ""IsDeleted"" = 0 AND ""TenantId"" = '{tenantId}' AND ""InvoiceHeaderId"" IN ({string.Join(", ", idsInvoiceHeader.Select(x => $@"'{x}'"))})";

            var data = await _appFactory.VnisCoreOracle.Connection.QueryAsync<Invoice03XmlEntity>(query);

            return data.ToList();
        }
    }
}
