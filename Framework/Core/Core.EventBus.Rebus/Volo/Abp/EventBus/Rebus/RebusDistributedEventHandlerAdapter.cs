using System.Threading.Tasks;
using Rebus.Handlers;

namespace Core.EventBus.Rebus
{
    public class RebusDistributedEventHandlerAdapter<TEventData> : IHandleMessages<TEventData>
    {
        protected RebusDistributedEventBus RebusDistributedEventBus { get; }

        public RebusDistributedEventHandlerAdapter(RebusDistributedEventBus rebusDistributedEventBus)
        {
            RebusDistributedEventBus = rebusDistributedEventBus;
        }

        public async Task Handle(TEventData message)
        {
            await RebusDistributedEventBus.TriggerHandlersAsync(typeof(TEventData), message);
        }
    }
}
