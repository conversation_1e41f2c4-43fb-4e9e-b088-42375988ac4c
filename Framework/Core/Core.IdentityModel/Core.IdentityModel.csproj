<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.IdentityModel</AssemblyName>
    <PackageId>Core.IdentityModel</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace />
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IdentityModel" Version="5.1.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="5.0.*" />
    <ProjectReference Include="..\Core.Caching\Core.Caching.csproj" />
    <ProjectReference Include="..\Core.MultiTenancy\Core.MultiTenancy.csproj" />
    <ProjectReference Include="..\Core.Threading\Core.Threading.csproj" />
  </ItemGroup>

</Project>
