using System.Security.Principal;
using Core.DependencyInjection;
using Core.Security.Claims;

namespace Core.Clients
{
    public class CurrentClient : ICurrentClient, ITransientDependency
    {
        public virtual string Id => _principalAccessor.Principal?.FindClientId();

        public virtual bool IsAuthenticated => Id != null;

        private readonly ICurrentPrincipalAccessor _principalAccessor;

        public CurrentClient(ICurrentPrincipalAccessor principalAccessor)
        {
            _principalAccessor = principalAccessor;
        }
    }
}
