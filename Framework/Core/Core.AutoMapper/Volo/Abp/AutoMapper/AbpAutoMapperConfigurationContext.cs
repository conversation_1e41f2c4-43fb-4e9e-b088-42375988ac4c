using System;
using AutoMapper;

namespace Core.AutoMapper
{
    public class AbpAutoMapperConfigurationContext : IAbpAutoMapperConfigurationContext
    {
        public IMapperConfigurationExpression MapperConfiguration { get; }
        public IServiceProvider ServiceProvider { get; }

        public AbpAutoMapperConfigurationContext(
            IMapperConfigurationExpression mapperConfigurationExpression, 
            IServiceProvider serviceProvider)
        {
            MapperConfiguration = mapperConfigurationExpression;
            ServiceProvider = serviceProvider;
        }
    }
}