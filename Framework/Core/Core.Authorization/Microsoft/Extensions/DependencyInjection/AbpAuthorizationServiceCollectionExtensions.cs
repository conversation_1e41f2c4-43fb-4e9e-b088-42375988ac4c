using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Core.Authorization;
using Core.Authorization.Permissions;

namespace Microsoft.Extensions.DependencyInjection
{
    public static class AbpAuthorizationServiceCollectionExtensions
    {
        public static IServiceCollection AddAlwaysAllowAuthorization(this IServiceCollection services)
        {
            services.Replace(ServiceDescriptor.Singleton<IAuthorizationService, AlwaysAllowAuthorizationService>());
            services.Replace(ServiceDescriptor.Singleton<IAbpAuthorizationService, AlwaysAllowAuthorizationService>());
            services.Replace(ServiceDescriptor.Singleton<IMethodInvocationAuthorizationService, AlwaysAllowMethodInvocationAuthorizationService>());
            return services.Replace(ServiceDescriptor.Singleton<IPermissionChecker, AlwaysAllowPermissionChecker>());
        }
    }
}
