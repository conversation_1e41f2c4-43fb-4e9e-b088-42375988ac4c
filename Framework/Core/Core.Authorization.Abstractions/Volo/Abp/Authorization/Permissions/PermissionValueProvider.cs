using System.Collections.Generic;
using System.Threading.Tasks;
using Core.DependencyInjection;

namespace Core.Authorization.Permissions
{
    public abstract class PermissionValueProvider : IPermissionValueProvider, ITransientDependency
    {
        public abstract string Name { get; }

        protected IPermissionStore PermissionStore { get; }

        protected PermissionValueProvider(IPermissionStore permissionStore)
        {
            PermissionStore = permissionStore;
        }

        public abstract Task<PermissionGrantResult> CheckAsync(PermissionValueCheckContext context);

        public abstract Task<MultiplePermissionGrantResult> CheckAsync(PermissionValuesCheckContext context);
    }
}
