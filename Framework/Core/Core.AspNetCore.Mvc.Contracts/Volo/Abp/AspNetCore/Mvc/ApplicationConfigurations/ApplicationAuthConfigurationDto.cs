using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Core.AspNetCore.Mvc.ApplicationConfigurations
{
    [Serializable]
    public class ApplicationAuthConfigurationDto
    {
        public Dictionary<string, bool> Policies { get; set; }

        public Dictionary<string, bool> GrantedPolicies { get; set; }

        public ApplicationAuthConfigurationDto()
        {
            Policies = new Dictionary<string, bool>();
            GrantedPolicies = new Dictionary<string, bool>();
        }
    }
}