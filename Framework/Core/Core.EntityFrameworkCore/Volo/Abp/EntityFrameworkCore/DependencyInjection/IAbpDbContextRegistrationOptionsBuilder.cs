using System;
using JetBrains.Annotations;
using Core.DependencyInjection;
using Core.Domain.Entities;

namespace Core.EntityFrameworkCore.DependencyInjection
{
    public interface IAbpDbContextRegistrationOptionsBuilder : IAbpCommonDbContextRegistrationOptionsBuilder
    {
        void Entity<TEntity>([NotNull] Action<AbpEntityOptions<TEntity>> optionsAction)
            where TEntity : IEntity;
    }
}