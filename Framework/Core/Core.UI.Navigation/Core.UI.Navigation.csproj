<Project Sdk="Microsoft.NET.Sdk">

  
  

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <AssemblyName>Core.UI.Navigation</AssemblyName>
    <PackageId>Core.UI.Navigation</PackageId>
    <AssetTargetFallback>portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <GenerateAssemblyConfigurationAttribute>false</GenerateAssemblyConfigurationAttribute>
    <GenerateAssemblyCompanyAttribute>false</GenerateAssemblyCompanyAttribute>
    <GenerateAssemblyProductAttribute>false</GenerateAssemblyProductAttribute>
    <RootNamespace />
  </PropertyGroup>

  <ItemGroup>
    <EmbeddedResource Include="Volo\Abp\Ui\Navigation\Localization\Resource\*.json" />
    <Content Remove="Volo\Abp\Ui\Navigation\Localization\Resource\*.json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core.Authorization\Core.Authorization.csproj" />
    <ProjectReference Include="..\Core.UI\Core.UI.csproj" />
  </ItemGroup>

</Project>
