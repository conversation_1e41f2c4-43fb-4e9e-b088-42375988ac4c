<Project Sdk="Microsoft.NET.Sdk.Web">

    
    

    <PropertyGroup>
        <TargetFramework>net5.0</TargetFramework>
        <IsPackable>true</IsPackable>
        <OutputType>Library</OutputType>
        <RootNamespace />
        <LangVersion>latest</LangVersion>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Core.AspNetCore.Components.Web\Core.AspNetCore.Components.Web.csproj" />
      <ProjectReference Include="..\Core.AspNetCore.Mvc.Contracts\Core.AspNetCore.Mvc.Contracts.csproj" />
      <ProjectReference Include="..\Core.EventBus\Core.EventBus.csproj" />
      <ProjectReference Include="..\Core.Http.Client\Core.Http.Client.csproj" />
      <ProjectReference Include="..\Core.AspNetCore.SignalR\Core.AspNetCore.SignalR.csproj" />
    </ItemGroup>

</Project>
