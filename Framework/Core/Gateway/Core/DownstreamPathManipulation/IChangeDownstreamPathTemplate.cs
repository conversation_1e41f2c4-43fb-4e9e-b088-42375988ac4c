using Gateway.Core.Configuration;
using Gateway.Core.DownstreamRouteFinder.UrlMatcher;
using Gateway.Core.Request.Middleware;
using Gateway.Core.Responses;
using Gateway.Core.Values;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Text;

namespace Gateway.Core.PathManipulation
{
    public interface IChangeDownstreamPathTemplate
    {
        Response ChangeDownstreamPath(List<ClaimToThing> claimsToThings, IEnumerable<Claim> claims,
            DownstreamPathTemplate downstreamPathTemplate, List<PlaceholderNameAndValue> placeholders);
    }
}
