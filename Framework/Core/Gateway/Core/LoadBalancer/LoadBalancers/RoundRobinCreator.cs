namespace Gateway.Core.LoadBalancer.LoadBalancers
{
    using Gateway.Core.Configuration;
    using Gateway.Core.ServiceDiscovery.Providers;
    using Gateway.Core.Responses;

    public class RoundRobinCreator : ILoadBalancerCreator
    {
        public Response<ILoadBalancer> Create(DownstreamRoute route, IServiceDiscoveryProvider serviceProvider)
        {
            return new OkResponse<ILoadBalancer>(new RoundRobin(async () => await serviceProvider.Get()));
        }

        public string Type => nameof(RoundRobin);
    }
}
