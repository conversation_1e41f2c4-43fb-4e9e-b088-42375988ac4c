using Core.Application.Localization.Resources.AbpDdd;
using Core.Auditing;
using Core.Localization;
using Core.Modularity;
using Core.VirtualFileSystem;

namespace Core.Application
{
    [DependsOn(
        typeof(AbpAuditingModule),
        typeof(AbpLocalizationModule)
        )]
    public class AbpDddApplicationContractsModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.AddEmbedded<AbpDddApplicationContractsModule>();
            });

            Configure<AbpLocalizationOptions>(options =>
            {
                options.Resources
                    .Add<AbpDddApplicationContractsResource>("en")
                    .AddVirtualJson("/Volo/Abp/Application/Localization/Resources/AbpDdd");
            });
        }
    }
}
