@typeparam TEntity
@typeparam TResourceType
@using Core.BlazoriseUI
@using Core.Localization

@if (PropertyInfo != null && Entity != null)
{
    <Field>
        <FieldLabel>@PropertyInfo.GetLocalizedDisplayName(StringLocalizerFactory)</FieldLabel>
        <TextEdit @bind-Text="@Value" Role="@PropertyInfo.GetTextRole()" InputMode="@PropertyInfo.GetTextInputMode()">
          
        </TextEdit>
    </Field>
}