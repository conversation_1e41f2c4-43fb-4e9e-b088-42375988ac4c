using System.Collections.Generic;
using Core.AspNetCore.Mvc.UI.Bundling;
using Core.AspNetCore.Mvc.UI.Packages.Clipboard;
using Core.Modularity;

namespace Core.AspNetCore.Mvc.UI.Packages.Prismjs
{
    [DependsOn(typeof(ClipboardScriptBundleContributor))]
    public class PrismjsScriptBundleContributor : BundleContributor
    {
        public override void ConfigureBundle(BundleConfigurationContext context)
        {
            context.Files.AddIfNotContains("/libs/prismjs/prism.js");
        }
    }
}
