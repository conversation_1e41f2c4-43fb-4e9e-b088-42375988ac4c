using System;
using System.Collections.Generic;

namespace Core.Localization.ExceptionHandling
{
    public class AbpExceptionLocalizationOptions
    {
        public Dictionary<string, Type> ErrorCodeNamespaceMappings { get; }

        public AbpExceptionLocalizationOptions()
        {
            ErrorCodeNamespaceMappings = new Dictionary<string, Type>();
        }

        public void MapCodeNamespace(string errorCodeNamespace, Type type)
        {
            ErrorCodeNamespaceMappings[errorCodeNamespace] = type;
        }
    }
}
