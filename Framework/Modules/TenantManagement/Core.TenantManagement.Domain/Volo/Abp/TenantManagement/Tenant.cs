using Core.Domain.Entities.Auditing;

using JetBrains.Annotations;

using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.TenantManagement
{
    public sealed class Tenant : FullAuditedAggregateRoot<Guid>
    {
        public string TenantCode { get; set; }
        public string Name { get; set; }
        public Guid? ParentId { get; set; }
        /// <summary>
        /// có đang sử dụng và có dkph/hóa đơn hay không
        /// -1: deactive: tenant khóa toàn bộ chức năng hệ thống và user nội bộ bị vô hiệu hóa, riêng user khách hàng vẫn sử dụng tra cứu bình thường
        /// 0: chưa dkph => được xóa/sửa
        /// 1: đã được dùng để tạo dkph => không được xóa/sửa
        /// </summary>
        public short IsEnable { get; set; }

        /// <summary>
        /// sau khi deactive chi nhánh giá trị trường này sẽ bằng ngày hiện tại + thêm số ngày theo setting
        /// khi ngày hiện tại == giá trị ngày này => chi nhánh đã bị deactive
        /// </summary>
        public DateTime? EffectiveDeactiveDate { get; set; }

        public DateTime? ExpireDate { get; set; }

        public bool IsCurrent { get; set; }

        /// <summary>
        /// Mã cơ quan thuế
        /// </summary>
        public string CodeTaxDepartment { get; set; }

        /// <summary>
        /// Cơ quan thuế
        /// </summary>
        public string TaxDepartment { get; set; }

        /// <summary>
        /// địa danh
        /// </summary>
        public string PlaceName { get; set; }

        public string TaxCode { get; set; }
        public string Address { get; set; }

        /// <summary>
        /// Tỉnh/Thành phố
        /// </summary>
        public string City { get; set; }
        public string Country { get; set; }

        /// <summary>
        /// Quận/Huyện
        /// </summary>
        public string District { get; set; }
        public string FullNameVi { get; set; }
        public string FullNameEn { get; set; }

        /// <summary>
        /// Đại diện pháp vân
        /// </summary>
        public string LegalName { get; set; }

        /// <summary>
        /// số fax
        /// </summary>
        public string Fax { get; set; }
        public string BusinessType { get; set; }

        /// <summary>
        /// thư điện tử
        /// </summary>
        public string Emails { get; set; }

        /// <summary>
        /// điện thoại liên hệ
        /// </summary>
        public string Phones { get; set; }
        public string Website { get; set; }

        /// <summary>
        /// tên ngân hàng
        /// độ dài 400
        /// </summary>
        public string BankName { get; set; }
        /// <summary>
        /// tài khoản ngân hàng
        /// đồ dài 30
        /// </summary>
        public string BankAccount { get; set; }
        /// <summary>
        /// ngày bắt đầu năm tài chính
        /// </summary>
        public DateTime? DayOfUser { get; set; }

        public string Metadata { get; set; }

        /// <summary>
        /// nhóm sinh số
        /// </summary>
        public decimal Group { get; set; } = 0;

        public List<TenantConnectionString> ConnectionStrings { get; protected set; }

        private Tenant()
        {

        }

        internal Tenant(Guid id, [NotNull] string name)
            : base(id)
        {
            SetName(name);

            ConnectionStrings = new List<TenantConnectionString>();
        }

        [CanBeNull]
        public string FindDefaultConnectionString()
        {
            return FindConnectionString(Data.ConnectionStrings.DefaultConnectionStringName);
        }

        [CanBeNull]
        public string FindConnectionString(string name)
        {
            return ConnectionStrings.FirstOrDefault(c => c.Name == name)?.Value;
        }

        public void SetDefaultConnectionString(string connectionString)
        {
            SetConnectionString(Data.ConnectionStrings.DefaultConnectionStringName, connectionString);
        }

        public void SetConnectionString(string name, string connectionString)
        {
            var tenantConnectionString = ConnectionStrings.FirstOrDefault(x => x.Name == name);

            if (tenantConnectionString != null)
            {
                tenantConnectionString.SetValue(connectionString);
            }
            else
            {
                ConnectionStrings.Add(new TenantConnectionString(Id, name, connectionString));
            }
        }

        public void RemoveDefaultConnectionString()
        {
            RemoveConnectionString(Data.ConnectionStrings.DefaultConnectionStringName);
        }

        public void RemoveConnectionString(string name)
        {
            var tenantConnectionString = ConnectionStrings.FirstOrDefault(x => x.Name == name);

            if (tenantConnectionString != null)
            {
                ConnectionStrings.Remove(tenantConnectionString);
            }
        }

        internal void SetName([NotNull] string name)
        {
            Name = Check.NotNullOrWhiteSpace(name, nameof(name), TenantConsts.MaxNameLength);
        }
    }
}