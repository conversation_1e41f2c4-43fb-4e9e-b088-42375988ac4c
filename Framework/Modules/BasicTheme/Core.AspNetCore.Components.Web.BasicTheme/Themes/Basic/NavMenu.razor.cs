using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Core.UI.Navigation;

namespace Core.AspNetCore.Components.Web.BasicTheme.Themes.Basic
{
    public partial class NavMenu
    {
        [Inject]
        protected IMenuManager MenuManager { get; set; }

        protected ApplicationMenu Menu { get; set; }

        protected override async Task OnInitializedAsync()
        {
            Menu = await MenuManager.GetMainMenuAsync();
        }
    }
}
