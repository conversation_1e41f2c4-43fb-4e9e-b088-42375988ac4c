using Core.Account.Web.Models;
using Microsoft.Extensions.Configuration;
using System;

namespace Core.Account.Web.Services
{
    public interface IRateLimit
    {
        RateLimitModel AllowRequest(string userName);
    }

    public class RateLimit : IRateLimit
    {
        private readonly IConfiguration _configuration;
        private readonly IRedisService _redisService;
        public RateLimit(
            IConfiguration configuration,
            IRedisService redisService)
        {
            _configuration = configuration;
            _redisService = redisService;
        }

        public RateLimitModel AllowRequest(string userName)
        {
            int.TryParse(_configuration.GetSection("Settings:LimitReq").Value, out var limitReq);
            int.TryParse(_configuration.GetSection("Settings:Expiry").Value, out var expiry);

            // mặc định
            // chỉ cho phép gửi 5 request
            // quên pass trong vòng 5 phút
            if (limitReq == 0)
            {
                limitReq = 5;
            }

            if (expiry == 0)
            {
                expiry = 5;
            }

            TimeSpan timeSpan = new TimeSpan(0, expiry, 0);
            var cacheKey = $@"t:ForgotPassword:LoginPage:{userName}";
            var count = _redisService.Increment(cacheKey, timeSpan);
            if (count > limitReq)
            {
                return new RateLimitModel
                {
                    AllowReq = false,
                    LimitReq = limitReq,
                    Expiry = expiry
                };
            }
            else
            {
                return new RateLimitModel
                {
                    AllowReq = true,
                    LimitReq = limitReq,
                    Expiry = expiry
                };
            }
        }
    }
}
