using Core.Emailing;
using Core.Identity;
using Core.IdentityServer.Clients;
using Core.Modularity;
using Core.Shared.Services;
using Core.UI.Navigation;
using Core.UI.Navigation.Urls;
using Core.VirtualFileSystem;
using Microsoft.Extensions.DependencyInjection;

namespace Core.Account
{
    [DependsOn(
        typeof(AbpAccountApplicationContractsModule),
        typeof(AbpIdentityApplicationModule),
        typeof(AbpUiNavigationModule),
        typeof(AbpEmailingModule)
    )]
    public class AbpAccountApplicationModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            context.Services.AddSingleton<ClientStore, ClientStore>();

            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.AddEmbedded<AbpAccountApplicationModule>();
            });

            Configure<AppUrlOptions>(options =>
            {
                options.Applications["MVC"].Urls[AccountUrlNames.PasswordReset] = "Account/ResetPassword";
            });
        }
    }
}
