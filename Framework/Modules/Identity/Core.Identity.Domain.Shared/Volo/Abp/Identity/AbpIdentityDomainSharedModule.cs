using Microsoft.Extensions.DependencyInjection;
using Core.Features;
using Core.Identity.Localization;
using Core.Localization;
using Core.Localization.ExceptionHandling;
using Core.Modularity;
using Core.Users;
using Core.Validation;
using Core.Validation.Localization;
using Core.VirtualFileSystem;

namespace Core.Identity
{
    [DependsOn(
        typeof(AbpUsersDomainSharedModule),
        typeof(AbpValidationModule),
        typeof(AbpFeaturesModule)
        )]
    public class AbpIdentityDomainSharedModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.AddEmbedded<AbpIdentityDomainSharedModule>();
            });

            Configure<AbpLocalizationOptions>(options =>
            {
                options.Resources
                    .Add<IdentityResource>("vi")
                    .AddBaseTypes(
                        typeof(AbpValidationResource)
                    ).AddVirtualJson("/Volo/Abp/Identity/Localization");
            });

            Configure<AbpExceptionLocalizationOptions>(options =>
            {
                options.MapCodeNamespace("Core.Identity", typeof(IdentityResource));
            });
        }
    }
}
