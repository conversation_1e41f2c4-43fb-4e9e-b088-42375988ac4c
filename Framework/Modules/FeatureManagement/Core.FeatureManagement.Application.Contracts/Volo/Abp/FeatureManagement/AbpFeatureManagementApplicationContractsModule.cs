using System.Collections.Generic;
using Core.Application;
using Core.Authorization;
using Core.FeatureManagement.JsonConverters;
using Core.Json.SystemTextJson;
using Core.Modularity;
using Core.VirtualFileSystem;

namespace Core.FeatureManagement
{
    [DependsOn(
        typeof(AbpFeatureManagementDomainSharedModule),
        typeof(AbpDddApplicationContractsModule),
        typeof(AbpAuthorizationAbstractionsModule)
        )]
    public class AbpFeatureManagementApplicationContractsModule : AbpModule
    {
        public override void ConfigureServices(ServiceConfigurationContext context)
        {
            Configure<AbpVirtualFileSystemOptions>(options =>
            {
                options.FileSets.AddEmbedded<AbpFeatureManagementApplicationContractsModule>();
            });

            Configure<AbpSystemTextJsonSerializerOptions>(options =>
            {
                options.JsonSerializerOptions.Converters.AddIfNotContains(new StringValueTypeJsonConverter());
            });
        }
    }
}
