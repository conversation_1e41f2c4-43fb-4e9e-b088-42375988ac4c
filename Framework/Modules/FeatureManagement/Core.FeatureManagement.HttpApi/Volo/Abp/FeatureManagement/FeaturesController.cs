using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Core.AspNetCore.Mvc;

namespace Core.FeatureManagement
{
    [RemoteService(Name = FeatureManagementRemoteServiceConsts.RemoteServiceName)]
    [Area("featureManagement")]
    [Route("api/feature-management/features")]
    public class FeaturesController : AbpController //IFeatureAppService
    {
        protected IFeatureAppService FeatureAppService { get; }

        public FeaturesController(IFeatureAppService featureAppService)
        {
            FeatureAppService = featureAppService;
        }

        //[HttpGet]
        //public virtual Task<GetFeatureListResultDto> GetAsync(string providerName, string providerKey)
        //{
        //    return FeatureAppService.GetAsync(providerName, providerKey);
        //}

        //[HttpPut]
        //public virtual Task UpdateAsync(string providerName, string providerKey, UpdateFeaturesDto input)
        //{
        //    return FeatureAppService.UpdateAsync(providerName, providerKey, input);
        //}
    }
}
