using System.IO;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;

namespace Core.Tvan.Abstractions
{
    public abstract class BaseMS01TBKTDLInvoiceErrorService
    {
        public async Task ReceiveXmlFromTvanAsync<T>(string xml) where T : class
        {
            using (var stream = new MemoryStream())
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xml);

                XmlSerializer serializer = new XmlSerializer(typeof(T));
                StringReader strReader = new StringReader(xml);
                var response = (T)serializer.Deserialize(strReader);

                var invoiceError = await GetInvoiceErrorAsync<T>(response);

                await UploadMinio<T>(xml, invoiceError, response);
            }
        }

        protected abstract Task<object> GetInvoiceErrorAsync<T>(object response) where T : class;

        public abstract Task UploadMinio<T>(string xml, object invoiceError, object response);
    }
}
