using System.ComponentModel;

using System.ComponentModel.DataAnnotations;

namespace Core.Tvan.Constants
{
    public enum TvanStatus
    {
        /// <summary>
        /// TH TCT reject gửi TBSS
        /// cho phản hồi 204
        /// </summary>
        [Display(Name = "TCT reject gửi TBSS")]
        [Description("TCT từ chối gửi TBSS")]
        TCTRejectTbss = -4,

        [Display(Name = "TCT từ chối")]
        [Description("TCT từ chối")]
        TCTReject = -3,

        /// <summary>
        /// TVAN từ chối
        /// </summary>
        [Display(Name = "TVAN từ chối")]
        [Description("TCT không tiếp nhận")]
        TvanReject = -2,

        /// <summary>
        /// TH gửi lên cho TVAN lỗi 
        /// </summary>
        [Display(Name = "gửi lên cho TVAN lỗi ")]
        [Description("Gửi TVAN lỗi")]
        SendError = -1,

        /// <summary>
        /// Chưa gửi
        /// </summary>
        [Display(Name = "Chưa gửi")]
        [Description("Chưa gửi TVAN")]
        UnSent = 0,

        /// <summary>
        /// Đã gửi
        /// </summary>
        [Display(Name = "Đã gửi")]
        [Description("Đã gửi TVAN")]
        Sended = 1,

        /// <summary>
        /// Tvan chấp nhận
        /// </summary>
        [Display(Name = "Tvan chấp nhận")]
        [Description("TCT tiếp nhận")]
        TvanAccept = 2,

        /// <summary>
        /// TCT chấp nhận
        /// </summary>
        [Display(Name = "TCT chấp nhận")]
        [Description("TCT chấp nhận")]
        TCTAccept = 3
    }
}
