using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore;

namespace Core.Tvan.StoredProcedure.Procedures
{
    public static class TvanInvoiceInitProcedure
    {
        public static void CreateProcedureInvoice01GetListInvoiceTvanResponse(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table
            //var v_json_conditions = '[{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********",
            //            "PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"}]';

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.Invoice01GetListInvoiceTvanResponse} 
                (
                    json_conditions IN NCLOB,
                    tenantRawId IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS 
                BEGIN
                    OPEN output_data FOR
                        SELECT*
                        FROM
                        (
                            select
                                A.""Id"",
                                A.""SellerTaxCode"",
                                A.""TemplateNo"",
                                A.""SerialNo"",
                                A.""InvoiceNo"",
                                A.""Number""
                            FROM ""Invoice01Header"" A
                            INNER JOIN(
                                SELECT J.TemplateNo, J.SerialNo, J.InvoiceNo
                                FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
                                    TemplateNo VARCHAR2(1)       PATH '$.TemplateNo',
                                    SerialNo VARCHAR2(6)         PATH '$.SerialNo',
                                    InvoiceNo VARCHAR2(10)        PATH '$.InvoiceNo'
                                ) J
                            ) R ON(
                                A.""TemplateNo"" = R.TemplateNo AND
                                A.""SerialNo"" = R.SerialNo AND
                                A.""Number"" = R.InvoiceNo
                                )
                            WHERE
                                A.""TenantId"" = tenantRawId
                        );

            END {TvanInvoiceProcedureName.Invoice01GetListInvoiceTvanResponse};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        } 
        
        public static void CreateProcedureInvoice02GetListInvoiceTvanResponse(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table
            //var v_json_conditions = '[{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********",
            //            "PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"}]';

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.Invoice02GetListInvoiceTvanResponse} 
                (
                    json_conditions IN NCLOB,
                    tenantRawId IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS 
                BEGIN
                    OPEN output_data FOR
                        SELECT*
                        FROM
                        (
                            select
                                A.""Id"",
                                A.""SellerTaxCode"",
                                A.""TemplateNo"",
                                A.""SerialNo"",
                                A.""InvoiceNo"",
                                A.""Number""
                            FROM ""Invoice02Header"" A
                            INNER JOIN(
                                SELECT J.TemplateNo, J.SerialNo, J.InvoiceNo
                                FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
                                    TemplateNo VARCHAR2(1)       PATH '$.TemplateNo',
                                    SerialNo VARCHAR2(6)         PATH '$.SerialNo',
                                    InvoiceNo VARCHAR2(10)        PATH '$.InvoiceNo'
                                ) J
                            ) R ON(
                                A.""TemplateNo"" = R.TemplateNo AND
                                A.""SerialNo"" = R.SerialNo AND
                                A.""Number"" = R.InvoiceNo
                                )
                            WHERE
                                A.""TenantId"" = tenantRawId
                        );

            END {TvanInvoiceProcedureName.Invoice02GetListInvoiceTvanResponse};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        } 
         
        public static void CreateProcedureInvoice03GetListInvoiceTvanResponse(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table
            //var v_json_conditions = '[{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********",
            //            "PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"}]';

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.Invoice03GetListInvoiceTvanResponse} 
                (
                    json_conditions IN NCLOB,
                    tenantRawId IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS 
                BEGIN
                    OPEN output_data FOR
                        SELECT*
                        FROM
                        (
                            select
                                A.""Id"",
                                A.""SellerTaxCode"",
                                A.""TemplateNo"",
                                A.""SerialNo"",
                                A.""InvoiceNo"",
                                A.""Number""
                            FROM ""Invoice03Header"" A
                            INNER JOIN(
                                SELECT J.TemplateNo, J.SerialNo, J.InvoiceNo
                                FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
                                    TemplateNo VARCHAR2(1)       PATH '$.TemplateNo',
                                    SerialNo VARCHAR2(6)         PATH '$.SerialNo',
                                    InvoiceNo VARCHAR2(10)        PATH '$.InvoiceNo'
                                ) J
                            ) R ON(
                                A.""TemplateNo"" = R.TemplateNo AND
                                A.""SerialNo"" = R.SerialNo AND
                                A.""Number"" = R.InvoiceNo
                                )
                            WHERE
                                A.""TenantId"" = tenantRawId
                        );

            END {TvanInvoiceProcedureName.Invoice03GetListInvoiceTvanResponse};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        } 


        public static void CreateProcedureInvoice04GetListInvoiceTvanResponse(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table
            //var v_json_conditions = '[{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********",
            //            "PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"}]';

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.Invoice04GetListInvoiceTvanResponse} 
                (
                    json_conditions IN NCLOB,
                    tenantRawId IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS 
                BEGIN
                    OPEN output_data FOR
                        SELECT*
                        FROM
                        (
                            select
                                A.""Id"",
                                A.""SellerTaxCode"",
                                A.""TemplateNo"",
                                A.""SerialNo"",
                                A.""InvoiceNo"",
                                A.""Number""
                            FROM ""Invoice04Header"" A
                            INNER JOIN(
                                SELECT J.TemplateNo, J.SerialNo, J.InvoiceNo
                                FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
                                    TemplateNo VARCHAR2(1)       PATH '$.TemplateNo',
                                    SerialNo VARCHAR2(6)         PATH '$.SerialNo',
                                    InvoiceNo VARCHAR2(10)        PATH '$.InvoiceNo'
                                ) J
                            ) R ON(
                                A.""TemplateNo"" = R.TemplateNo AND
                                A.""SerialNo"" = R.SerialNo AND
                                A.""Number"" = R.InvoiceNo
                                )
                            WHERE
                                A.""TenantId"" = tenantRawId
                        );

            END {TvanInvoiceProcedureName.Invoice04GetListInvoiceTvanResponse};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        }

        public static void CreateProcedureTvanTicketGetListByInfo(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table
            //var v_json_conditions = '[{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********",
            //            "PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********0","PayerCode":"huong test","TellSeq":"hh001"},{"InvoiceDate":"14-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"Hường Nguyễn"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"123"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"456"},{"InvoiceDate":"15-Oct-21 12.00.00.0000000 AM","TenantCode":"**********-765","BuyerBankAccount":"*********","PayerCode":"huong test","TellSeq":"*********"}]';

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.TvanTicketGetListByInfo} 
                (
                    json_conditions IN NCLOB,
                    tenantRawId IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS 
                BEGIN
                    OPEN output_data FOR
                        SELECT*
                        FROM
                        (
                            select
                                A.""Id"",
                                A.""SellerTaxCode"",
                                A.""TemplateNo"",
                                A.""SerialNo"",
                                A.""InvoiceNo"",
                                A.""Number""
                            FROM ""TicketHeader"" A
                            INNER JOIN(
                                SELECT J.TemplateNo, J.SerialNo, J.InvoiceNo
                                FROM JSON_TABLE(json_conditions, '$[*]' COLUMNS
                                    TemplateNo VARCHAR2(1)       PATH '$.TemplateNo',
                                    SerialNo VARCHAR2(6)         PATH '$.SerialNo',
                                    InvoiceNo VARCHAR2(10)        PATH '$.InvoiceNo'
                                ) J
                            ) R ON(
                                A.""TemplateNo"" = R.TemplateNo AND
                                A.""SerialNo"" = R.SerialNo AND
                                A.""Number"" = R.InvoiceNo
                                )
                            WHERE
                                A.""TenantId"" = tenantRawId
                        );

            END {TvanInvoiceProcedureName.TvanTicketGetListByInfo};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        }


        public static void CreateProcedureTvanInfoGetInvoiceErrorByMessageCode(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.TvanInfoErrorGetListByMessageCode} 
                (
                    message_code IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS
                    TypeInvoice INT := 0;
                BEGIN
                    -- TvanInfoInvoice01Error
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice 
                    FROM ""TvanInfoInvoice01Error""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 1 as ""Type"" , 'TvanInfoInvoice01Error' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                    --TvanInfoInvoice02Error
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice02Error""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 2 as ""Type"" , 'TvanInfoInvoice02Error' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                    --TvanInfoInvoice03Error
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice03Error""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 3 as ""Type"" , 'TvanInfoInvoice03Error' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;


                    --TvanInfoInvoice04Error
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice04Error""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 4 as ""Type"" , 'TvanInfoInvoice04Error' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;


                    --TvanInfoTicketError
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoTicketError""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 5 as ""Type"" , 'TvanInfoTicketError' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                            OPEN output_data FOR SELECT TypeInvoice as ""Type"" , '' as ""TableName"" From dual;


                    --TbssTvanInfo - Tbss Hoa don ngoai he thong
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TbssTvanInfo""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 11 as ""Type"" , 'TbssTvanInfo' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                            OPEN output_data FOR SELECT TypeInvoice as ""Type"" , '' as ""TableName"" From dual;

            END { TvanInvoiceProcedureName.TvanInfoErrorGetListByMessageCode};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        }

        public static void CreateProcedureTvanInfoGetInvoiceWithoutCodeByMessageCode(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.TvanInfoWithoutCodeGetListByMessageCode} 
                (
                    message_code IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS
                    TypeInvoice INT := 0;
                BEGIN
                    -- TvanInfoInvoice01WithoutCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice 
                    FROM ""TvanInfoInvoice01WithoutCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 1 as ""Type"" , 'TvanInfoInvoice01WithoutCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                    -- TvanInfoInvoice02WithoutCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice02WithoutCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 2 as ""Type"" , 'TvanInfoInvoice02WithoutCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                    -- TvanInfoInvoice03WithoutCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice03WithoutCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 3 as ""Type"" , 'TvanInfoInvoice03WithoutCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

 
                    -- TvanInfoInvoice04WithoutCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice04WithoutCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 4 as ""Type"" , 'TvanInfoInvoice04WithoutCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;


                    -- TvanInfoTicketWithoutCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoTicketWithoutCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 5 as ""Type"" , 'TvanInfoTicketWithoutCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                            OPEN output_data FOR SELECT TypeInvoice as ""Type"" , '' as ""TableName"" From dual;

            END { TvanInvoiceProcedureName.TvanInfoWithoutCodeGetListByMessageCode};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        }

        public static void CreateProcedureTvanInfoGetByMessageCode(VnisCoreOracleDbContext dbContext)
        {
            #region Version 2 - Join Json Table

            var spQuery = $@"
                create or replace NONEDITIONABLE PROCEDURE {TvanInvoiceProcedureName.TvanInfoHasCodeGetListByMessageCode} 
                (
                    message_code IN VARCHAR2,
                    output_data OUT SYS_REFCURSOR
                )
                AS
                    TypeInvoice INT := 0;
                BEGIN
                    -- TvanInfoInvoice01HasCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice 
                    FROM ""TvanInfoInvoice01HasCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 1 as ""Type"" , 'TvanInfoInvoice01HasCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                    --TvanInfoInvoice02HasCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice02HasCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 2 as ""Type"" , 'TvanInfoInvoice02HasCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                    --TvanInfoInvoice03HasCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice03HasCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 3 as ""Type"" , 'TvanInfoInvoice03HasCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;


                    --TvanInfoInvoice04HasCode
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoInvoice04HasCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 4 as ""Type"" , 'TvanInfoInvoice04HasCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;


                    --Ticket
                    SELECT Count(ROWNUM)
                    INTO TypeInvoice
                    FROM ""TvanInfoTicketHasCode""
                    WHERE ""MessageCode"" = message_code;

                            IF(TypeInvoice >= 1) THEN
                               BEGIN
                            OPEN output_data FOR SELECT 5 as ""Type"" , 'TvanInfoTicketHasCode' as ""TableName"" From dual;
                            RETURN;
                            END;
                            END IF;

                            OPEN output_data FOR SELECT TypeInvoice as ""Type"" , '' as ""TableName"" From dual;

            END { TvanInvoiceProcedureName.TvanInfoHasCodeGetListByMessageCode};
            ";
            #endregion

            dbContext.Database.ExecuteSqlRaw(spQuery);
        }

    }
} 