using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.ResponseUndefinedTct;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using Dapper;

using Microsoft.EntityFrameworkCore;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;

namespace Core.Tvan.Services.ResponseUndefinedTCT.InvoiceWithoutCode
{
    public class ResponseUndefinedTctInvoice04WithoutCodeService : IResponseUndefinedTctService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;

        public ResponseUndefinedTctInvoice04WithoutCodeService(
            IAppFactory appFactory,
            IFileService fileService)
        {
            _appFactory = appFactory;
            _fileService = fileService;
        }

        public async Task HandleResponseTvan(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            var responseModel = XmlExtension.XmlDeserialize<TDiepModel<TTChungModel, List<DLieuUndefinedModel>>>(xml);
            var repoInvoiceWithoutCodeTvanInfo = _appFactory.Repository<TvanInfoInvoice04WithoutCodeEntity, long>();

            var invoicesTvanInfo = await repoInvoiceWithoutCodeTvanInfo.Where(x => x.MessageCode == responseModel.TTChung.MTDTChieu).ToListAsync();
            if (!invoicesTvanInfo.Any())
                throw new UserFriendlyException($"Không tìm thấy hóa đơn có mã thông điệp {responseModel.TTChung.MTDTChieu}");

            var tenantId = invoicesTvanInfo.FirstOrDefault().TenantId;

            var fileName = $"ThongBaoChuaXacDinhTct-{responseModel.TTChung.MLTDiep}-{responseModel.TTChung.MTDiep}-{responseModel.TTChung.MTDTChieu}_{DateTime.Now.Ticks}.xml".Replace("/", "_");
            var tvanInvoiceWithoutCodeXmlEntity = new TvanInvoice04WithoutCodeXmlEntity
            {
                TenantId = tenantId,
                FileName = fileName,
                ContentType = ContentType.Xml,
                Length = Encoding.UTF8.GetBytes(xml).Length,
                PhysicalFileName = fileName,
                Invoice04HeaderId = invoicesTvanInfo.FirstOrDefault().InvoiceHeaderId
            };

            var pathFileMinio = $"{MediaFileType.Invoice04WithoutCodeTvanXml}/{tenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{tvanInvoiceWithoutCodeXmlEntity.CreationTime.Hour:00}/{tvanInvoiceWithoutCodeXmlEntity.PhysicalFileName}";
            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));
            var reposTvanInvoice04WithoutCodeXml = _appFactory.Repository<TvanInvoice04WithoutCodeXmlEntity, long>();
            await reposTvanInvoice04WithoutCodeXml.InsertAsync(tvanInvoiceWithoutCodeXmlEntity, true);

            var reposInvoiceWithoutCode = _appFactory.Repository<TvanInfoInvoice04WithoutCodeEntity, long>();

            foreach (var invoice in invoicesTvanInfo)
            {
                var sqlUpdate = $@"UPDATE ""{DatabaseExtension<Invoice04HeaderEntity>.GetTableName()}"" SET  ""StatusTvan"" = {(short)TvanStatus.TCTReject} WHERE ""Id"" = {invoice.InvoiceHeaderId} and ""StatusTvan"" != {(short)TvanStatus.TCTReject}";
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(sqlUpdate);

                var invoiceWithoutCodeErrorEntity = new TvanInfoInvoice04WithoutCodeEntity
                {
                    InvoiceHeaderId = invoice.InvoiceHeaderId,
                    FileId = tvanInvoiceWithoutCodeXmlEntity.Id,
                    TenantId = tenantId,
                    IsActive = true,
                    MessageTypeCode = responseModel.TTChung.MLTDiep.ToString(),
                    MessageCode = responseModel.TTChung.MTDiep,
                    MessageCodeReference = responseModel.TTChung.MTDTChieu,
                    Reason = responseModel.DLieu == null ? null : JsonConvert.SerializeObject(responseModel.DLieu),
                };

                await reposInvoiceWithoutCode.InsertAsync(invoiceWithoutCodeErrorEntity);
            }
        }
    }
}
