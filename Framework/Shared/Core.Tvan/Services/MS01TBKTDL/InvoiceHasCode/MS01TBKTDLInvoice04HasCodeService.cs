using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.TenantManagement;
using Core.Tvan.Abstractions;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceHasCode;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using Newtonsoft.Json;

using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;

namespace Core.Tvan.Services.MS01TBKTDL.InvoiceHasCode
{
    public class MS01TBKTDLInvoice04HasCodeService : BaseMS01TBKTDLInvoiceHasCodeService, IMS01TBKTDLInvoiceHasCodeService
    {
        private readonly IAppFactory _appFactory;
        private readonly IFileService _fileService;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;

        public MS01TBKTDLInvoice04HasCodeService(IAppFactory appFactory,
                                                 IFileService fileService,
                                                 IStringLocalizer<CoreLocalizationResource> localizier)
        {
            _appFactory = appFactory;
            _fileService = fileService;
            _localizier = localizier;
        }

        public async Task HandleResponseTvan(string xml, TransmissionPartnerEnum transmissionPartner)
        {
            await ReceiveXmlFromTvanAsync<TDiepModel<TTChungModel, DLieuMS01TBaoModel>>(xml, transmissionPartner);
        }

        public override async Task<object> UpdateInvoiceHeader<T>(object tvanResponse)
        {
            var notificationTDiep = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)tvanResponse;
            var reposInvoice04Header = _appFactory.Repository<Invoice04HeaderEntity, long>();
            var repoTenant = _appFactory.Repository<Tenant, Guid>();

            var tenant = await repoTenant.Where(x => x.TaxCode == notificationTDiep.DLieu.TBao.DLTBao.MST).FirstOrDefaultAsync();

            //lấy thông tin thông điệp
            var repoTvanInfoHasCode = _appFactory.Repository<TvanInfoInvoice04HasCodeEntity, long>();
            var tvanInfo = await repoTvanInfoHasCode.FirstOrDefaultAsync(x => x.MessageCode == notificationTDiep.TTChung.MTDTChieu);

            if (tvanInfo == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.TBKTDL.TvanHasCode", new string[] { notificationTDiep.TTChung.MTDTChieu }]);

            var invoiceHeader = await reposInvoice04Header.FirstOrDefaultAsync(x => x.TenantId == tenant.Id && x.Id == tvanInfo.InvoiceHeaderId);

            if (invoiceHeader == null)
                throw new UserFriendlyException(_localizier["Vnis.BE.TvanInvoice.TBKTDL.InvoiceNotFound", new string[] { notificationTDiep.TTChung.MTDTChieu }]);

            invoiceHeader.StatusTvan = (short)TvanStatus.TCTReject;
            await reposInvoice04Header.UpdateAsync(invoiceHeader);

            return invoiceHeader;
        }

        public override async Task UploadMinio<T>(string xml, object invoiceHeader, object responseInvoiceHasCode, TransmissionPartnerEnum transmissionPartner)
        {
            var invoice04Header = (Invoice04HeaderEntity)invoiceHeader;
            var fileName = $"{invoice04Header.SellerTaxCode}-{invoice04Header.TemplateNo}-{invoice04Header.SerialNo}-{invoice04Header.InvoiceNo}.xml".Replace("/", "_");
            var tvanInvoice04HasCodeXmlEntity = new TvanInvoice04HasCodeXmlEntity
            {
                PhysicalFileName = fileName,
                ContentType = ContentType.Xml,
                FileName = fileName,
                TenantId = invoice04Header.TenantId,
                Length = Encoding.UTF8.GetBytes(xml).Length,
            };

            var pathFileMinio = $"{MediaFileType.Invoice04HasCodeTvanXml}/{invoice04Header.TenantId}/{DateTime.Now.Year}/{DateTime.Now.Month:00}/{DateTime.Now.Day:00}/{DateTime.Now.Hour:00}/{fileName}";

            await _fileService.UploadAsync(pathFileMinio, Encoding.UTF8.GetBytes(xml));
            var repoXml = _appFactory.Repository<TvanInvoice04HasCodeXmlEntity, long>();
            await repoXml.InsertAsync(tvanInvoice04HasCodeXmlEntity);
            await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

            var notificationTDiep = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)responseInvoiceHasCode;
            var invoice04HasCodeTvanInfoEntity = new TvanInfoInvoice04HasCodeEntity
            {
                InvoiceHeaderId = invoice04Header.Id,
                MessageTypeCode = notificationTDiep.TTChung.MLTDiep.ToString(),
                MessageCode = notificationTDiep.TTChung.MTDiep,
                MessageCodeReference = notificationTDiep.TTChung.MTDTChieu,
                FileId = tvanInvoice04HasCodeXmlEntity.Id,
                IsActive = true,
                TenantId = invoice04Header.TenantId,
                Reason = notificationTDiep.DLieu.TBao.DLTBao.LCMa?.DSLDo?.LDo == null ? null : JsonConvert.SerializeObject(notificationTDiep.DLieu.TBao.DLTBao.LCMa.DSLDo.LDo),
                Title = MLTDiep._204.ToDisplayName(),
                TransmissionPartner = (short)transmissionPartner
            };

            var reposInvoice04HasCodeTvanInfo = _appFactory.Repository<TvanInfoInvoice04HasCodeEntity, long>();
            await reposInvoice04HasCodeTvanInfo.InsertAsync(invoice04HasCodeTvanInfoEntity);
        }
    }
}
