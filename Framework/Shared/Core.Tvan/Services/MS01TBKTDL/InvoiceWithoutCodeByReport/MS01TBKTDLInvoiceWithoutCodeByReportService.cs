using Core.Domain.Repositories;
using Core.Localization.Resources.AbpLocalization;
using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.Interfaces.MS01TBKTDL.InvoiceWithoutCodeByReport;
using Core.Tvan.Models.Xmls;
using Core.Tvan.Models.Xmls.TCTResponse;
using Dapper;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Serialization;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.ReportInvoice;

namespace Core.Tvan.Services.MS01TBKTDL.InvoiceWithoutCodeByReport
{
    public class MS01TBKTDLInvoiceWithoutCodeByReportService : IMS01TBKTDLInvoiceWithoutCodeByReportService
    {
        private readonly IAppFactory _appFactory;
        private readonly IStringLocalizer<CoreLocalizationResource> _localizier;
        private readonly IRepository<TaxReport01DetailMappingEntity, long> _repoTaxReport01DetailMapping;
        private readonly IRepository<TaxReport01TvanInfoEntity, long> _repoTaxReport01TvanInfo;
        private readonly IRepository<Invoice01HeaderEntity, long> _repoInvoice01Header;
        private readonly IRepository<Invoice02HeaderEntity, long> _repoInvoice02Header;
        private readonly IRepository<Invoice03HeaderEntity, long> _repoInvoice03Header;
        private readonly IRepository<Invoice04HeaderEntity, long> _repoInvoice04Header;
        private readonly IRepository<TicketHeaderEntity, long> _repoTicketHeader;
        private readonly IVnisCoreMongoInvoice01Repository _vnisCoreMongoInvoice01Repository;
        private readonly IVnisCoreMongoInvoice02Repository _vnisCoreMongoInvoice02Repository;
        private readonly IVnisCoreMongoInvoice01ReSyncRepository _mongoInvoice01ReSyncRepository;
        private readonly IVnisCoreMongoInvoice02ReSyncRepository _mongoInvoice02ReSyncRepository;

        public MS01TBKTDLInvoiceWithoutCodeByReportService(
            IAppFactory appFactory,
            IStringLocalizer<CoreLocalizationResource> localizier,
            IRepository<TaxReport01DetailMappingEntity, long> repoTaxReport01DetailMapping,
            IRepository<TaxReport01TvanInfoEntity, long> repoTaxReport01TvanInfo,
            IRepository<Invoice01HeaderEntity, long> repoInvoice01Header,
            IRepository<Invoice02HeaderEntity, long> repoInvoice02Header,
            IRepository<Invoice03HeaderEntity, long> repoInvoice03Header,
            IRepository<Invoice04HeaderEntity, long> repoInvoice04Header,
            IRepository<TicketHeaderEntity, long> repoTicketHeader,
            IVnisCoreMongoInvoice01Repository vnisCoreMongoInvoice01Repository,
            IVnisCoreMongoInvoice02Repository vnisCoreMongoInvoice02Repository,
            IVnisCoreMongoInvoice01ReSyncRepository mongoInvoice01ReSyncRepository,
            IVnisCoreMongoInvoice02ReSyncRepository mongoInvoice02ReSyncRepository
            )
        {
            _appFactory = appFactory;
            _localizier = localizier;
            _repoTaxReport01DetailMapping = repoTaxReport01DetailMapping;
            _repoTaxReport01TvanInfo = repoTaxReport01TvanInfo;
            _vnisCoreMongoInvoice01Repository = vnisCoreMongoInvoice01Repository;
            _vnisCoreMongoInvoice02Repository = vnisCoreMongoInvoice02Repository;
            _repoInvoice01Header = repoInvoice01Header;
            _repoInvoice02Header = repoInvoice02Header;
            _repoInvoice03Header = repoInvoice03Header;
            _repoInvoice04Header = repoInvoice04Header;
            _repoTicketHeader = repoTicketHeader;
            _mongoInvoice01ReSyncRepository = mongoInvoice01ReSyncRepository;
            _mongoInvoice02ReSyncRepository = mongoInvoice02ReSyncRepository;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="xml"></param>
        /// <param name="ltbao">Loại thông báo</param>
        /// <param name="transmissionPartner">Doanh nghiệp truyền nhận</param>
        /// <returns></returns>
        public async Task HandleResponseTvan(string xml, int ltbao, TransmissionPartnerEnum transmissionPartner)
        {
            using (var stream = new MemoryStream())
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xml);

                XmlSerializer serializer = new XmlSerializer(typeof(TDiepModel<TTChungModel, DLieuMS01TBaoModel>));
                StringReader strReader = new StringReader(xml);
                var responseTvan = (TDiepModel<TTChungModel, DLieuMS01TBaoModel>)serializer.Deserialize(strReader);

                if (string.IsNullOrEmpty(responseTvan.TTChung.MTDTChieu))
                {
                    Log.Error(_localizier["Vnis.BE.TvanInvoice.TBKTDL.MTDTChieuNotfound"]);
                    return;
                }

                var taxReportMappingByMessageCode = await _repoTaxReport01DetailMapping.FirstOrDefaultAsync(x => x.MessageCode == responseTvan.TTChung.MTDTChieu);
                if (taxReportMappingByMessageCode == null)
                {
                    Log.Error($@"Không tìm thấy Mã thông điệp tham chiếu: {responseTvan.TTChung.MTDTChieu}");
                    return;
                }

                switch (ltbao)
                {
                    case (int)LTBao.Loai2:
                        await LTbao2ProccessAsync(responseTvan, taxReportMappingByMessageCode, transmissionPartner, xml);
                        break;
                    case (int)LTBao.Loai4:
                        await LTbao4ProccessAsync(responseTvan, taxReportMappingByMessageCode, transmissionPartner, xml);
                        break;
                    case (int)LTBao.Loai9:
                        await LTbao9ProccessAsync(responseTvan, taxReportMappingByMessageCode, transmissionPartner, xml);
                        break;
                    default:
                        Log.Error("Loại thông báo không đúng");
                        break;
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thongDiep"></param>
        /// <param name="taxReport01DetailMappingEntity"></param>
        /// <param name="transmissionPartner"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        private async Task LTbao2ProccessAsync(TDiepModel<TTChungModel, DLieuMS01TBaoModel> thongDiep, TaxReport01DetailMappingEntity taxReport01DetailMappingEntity, TransmissionPartnerEnum transmissionPartner, string xml)
        {
            var mtdiep = thongDiep.TTChung.MTDiep; // mã thống điệp
            var mtdtchieu = thongDiep.TTChung.MTDTChieu; // mã thông điệp tham chiếu
            var ltbao = thongDiep.DLieu.TBao.DLTBao.LTBao; // loại thông báo
            var mltdiep = thongDiep.TTChung.MLTDiep.ToString(); // mã loại thông điệp

            #region UPDATE DETAILMAPPING
            taxReport01DetailMappingEntity.TvanReceivedTime = DateTime.Now;
            taxReport01DetailMappingEntity.StatusTvan = (short)TvanStatus.TCTAccept;
            taxReport01DetailMappingEntity.MessageCodeReference = mtdiep;

            await _repoTaxReport01DetailMapping.UpdateAsync(taxReport01DetailMappingEntity);
            #endregion

            #region Upload File lên Minio
            //upload lên xml
            var bytes = Encoding.UTF8.GetBytes(xml);
            var fileService = _appFactory.GetServiceDependency<IFileService>();
            var fileName = $"DLBTH01_{mltdiep}_{ltbao}_{DateTime.Now.Ticks}.xml".Replace("/", "-");
            var creationTime = DateTime.Now;
            var pathFileMinio = $"{MediaFileType.TvanTaxReportXml}/{taxReport01DetailMappingEntity.TenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
            await fileService.UploadAsync(pathFileMinio, bytes);

            //insert dữ liệu bảng tvaninfo + xml
            var repoTvanXml = _appFactory.Repository<TvanReportXmlEntity, long>();
            var file = new TvanReportXmlEntity
            {
                TaxReport01HeaderId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                TaxReport01DetailMappingId = taxReport01DetailMappingEntity.Id,
                TenantId = taxReport01DetailMappingEntity.TenantId,
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = bytes.Length,
                CreationTime = creationTime,
                StatusTvan = (short)TvanStatus.TCTAccept
            };

            await repoTvanXml.InsertAsync(file, true);
            #endregion

            #region INSERT TaxReport01TvanInfo
            var taxReport01TvanInfoEntitys = new List<TaxReport01TvanInfoEntity>();
            var idsInvoiceHeader = taxReport01DetailMappingEntity.InvoiceIds.Split(',').Select(long.Parse).ToList();
            foreach (var id in idsInvoiceHeader)
            {
                var taxReport01TvanInfoEntity = new TaxReport01TvanInfoEntity
                {
                    InvoiceType = taxReport01DetailMappingEntity.InvoiceType,
                    InvoiceHeaderId = id,
                    LTbao = ltbao,
                    TaxReport01DetailMappingEntityId = taxReport01DetailMappingEntity.Id,
                    TaxReport01HeaderEntityId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                    MessageTypeCode = mltdiep,
                    Title = MLTDiep._204.ToDisplayName(),
                    MessageCode = mtdiep,
                    MessageCodeReference = mtdtchieu,
                    Reason = "Success",
                    FileId = file.Id,
                    TransmissionPartner = (short)transmissionPartner,
                    TvanStatus = (short)TvanStatus.TCTAccept,
                    TenantId = taxReport01DetailMappingEntity.TenantId
                };

                taxReport01TvanInfoEntitys.Add(taxReport01TvanInfoEntity);
            }

            await _repoTaxReport01TvanInfo.InsertManyAsync(taxReport01TvanInfoEntitys);
            #endregion

            #region Update trang thai hoa don
            //update hóa đơn đã kê khai
            var updateIsDeclare = new StringBuilder(" BEGIN ");

            if (taxReport01DetailMappingEntity.InvoiceType == VnisType._01GTKT.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._02GTTT.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._03XKNB.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._04HGDL.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice04HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._05TVDT.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
            }
            updateIsDeclare.Append(" END; ");

            var queryUpdateIsDeclared = updateIsDeclare.ToString();
            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateIsDeclared);

            // update Mongo
            await UpdateMongoDbAsync(taxReport01DetailMappingEntity, idsInvoiceHeader, TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared);

            if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._01GTKT)
            { 
                await UpdateResync01Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
            }

            if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._02GTTT)
            {
                await UpdateResync02Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
            }
            #endregion
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thongDiep"></param>
        /// <param name="taxReport01DetailMappingEntity"></param>
        /// <param name="transmissionPartner"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        private async Task LTbao4ProccessAsync(TDiepModel<TTChungModel, DLieuMS01TBaoModel> thongDiep, TaxReport01DetailMappingEntity taxReport01DetailMappingEntity, TransmissionPartnerEnum transmissionPartner, string xml)
        {
            var tenantId = taxReport01DetailMappingEntity.TenantId;
            var mtdiep = thongDiep.TTChung.MTDiep; // mã thống điệp
            var mtdtchieu = thongDiep.TTChung.MTDTChieu; // mã thông điệp tham chiếu
            var ltbao = thongDiep.DLieu.TBao.DLTBao.LTBao; // loại thông báo
            var mltdiep = thongDiep.TTChung.MLTDiep.ToString(); // mã loại thông điệp

            #region UPDATE DETAILMAPPING
            taxReport01DetailMappingEntity.TvanReceivedTime = DateTime.Now;
            taxReport01DetailMappingEntity.StatusTvan = (short)TvanStatus.TCTAccept;
            taxReport01DetailMappingEntity.MessageCodeReference = mtdiep;

            await _repoTaxReport01DetailMapping.UpdateAsync(taxReport01DetailMappingEntity);
            #endregion

            #region Upload File lên Minio
            //upload lên xml
            var bytes = Encoding.UTF8.GetBytes(xml);
            var fileService = _appFactory.GetServiceDependency<IFileService>();
            var fileName = $"DLBTH01_{mltdiep}_{ltbao}_{DateTime.Now.Ticks}.xml".Replace("/", "-");
            var creationTime = DateTime.Now;
            var pathFileMinio = $"{MediaFileType.TvanTaxReportXml}/{taxReport01DetailMappingEntity.TenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
            await fileService.UploadAsync(pathFileMinio, bytes);

            //insert dữ liệu bảng tvaninfo + xml
            var repoTvanXml = _appFactory.Repository<TvanReportXmlEntity, long>();
            var file = new TvanReportXmlEntity
            {
                TaxReport01HeaderId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                TaxReport01DetailMappingId = taxReport01DetailMappingEntity.Id,
                TenantId = taxReport01DetailMappingEntity.TenantId,
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = bytes.Length,
                CreationTime = creationTime,
                StatusTvan = (short)TvanStatus.TCTAccept
            };

            await repoTvanXml.InsertAsync(file, true);
            #endregion

            #region INSERT TaxReport01TvanInfo
            var taxReport01TvanInfoEntitys = new List<TaxReport01TvanInfoEntity>();

            // danh sách ids gửi lên
            var idsInvoiceHeader = taxReport01DetailMappingEntity.InvoiceIds.Split(',').Select(long.Parse).ToList();

            // danh sách lý do không tiếp nhận
            // Trường hợp TĐ có kèm theo thẻ lỗi chung cho cả BTH: "TBao\DLTBao\LBTHKXDau\DSBTHop\BTHop\DSLDTTChung"
            // → Cập nhật trạng thái gửi CQT của tất cả các hóa đơn trong BTH là: CQT không chấp nhận
            var bthops = thongDiep.DLieu.TBao.DLTBao?.LBTHKXDau?.DSBTHop?.BTHop;

            var idsHeaderAccepted = new List<long>();
            var idsHeaderReject = new List<long>();

            var dsldttchung = bthops.Select(x => x.DSLDTTChung).ToList();
            if (dsldttchung.Any(x => x != null))
            {
                foreach (var id in idsInvoiceHeader)
                {
                    var taxReport01TvanInfoEntity = new TaxReport01TvanInfoEntity
                    {
                        InvoiceHeaderId = id,
                        LTbao = ltbao,
                        TaxReport01DetailMappingEntityId = taxReport01DetailMappingEntity.Id,
                        TaxReport01HeaderEntityId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                        MessageTypeCode = mltdiep,
                        Title = MLTDiep._204.ToDisplayName(),
                        MessageCode = mtdiep,
                        MessageCodeReference = mtdtchieu,
                        Reason = JsonConvert.SerializeObject(dsldttchung),
                        FileId = file.Id,
                        TransmissionPartner = (short)transmissionPartner,
                        TenantId = tenantId,
                        TvanStatus = (short)TvanStatus.TCTReject
                    };

                    taxReport01TvanInfoEntitys.Add(taxReport01TvanInfoEntity);
                }

                await _repoTaxReport01TvanInfo.InsertManyAsync(taxReport01TvanInfoEntitys);
            }
            else
            {
                var taxReport01TvanInfoAccepted = new List<TaxReport01TvanInfoEntity>();
                var taxReport01TvanInfoReject = new List<TaxReport01TvanInfoEntity>();

                var idsInvoiceHeaderErr = new Dictionary<long, string>();

                if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._01GTKT)
                {
                    foreach (var bthop in bthops)
                    {
                        foreach (var hdon in bthop.DSLHDon.HDon)
                        {
                            var invoiceHeader = await _repoInvoice01Header.FirstOrDefaultAsync(x => x.TenantId == tenantId
                                                                                                && x.TemplateNo == short.Parse(hdon.KHMSHDon)
                                                                                                && x.SerialNo == hdon.KHHDon
                                                                                                && x.Number == int.Parse(hdon.SHDon));
                            if (invoiceHeader != null)
                            {
                                idsInvoiceHeaderErr.Add(invoiceHeader.Id, JsonConvert.SerializeObject(hdon?.DSLDo?.LDo));
                            }
                        }
                    }
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._02GTTT)
                {
                    foreach (var bthop in bthops)
                    {
                        foreach (var hdon in bthop.DSLHDon.HDon)
                        {
                            var invoiceHeader = await _repoInvoice02Header.FirstOrDefaultAsync(x => x.TenantId == tenantId
                                                                                                && x.TemplateNo == short.Parse(hdon.KHMSHDon)
                                                                                                && x.SerialNo == hdon.KHHDon
                                                                                                && x.Number == int.Parse(hdon.SHDon));
                            if (invoiceHeader != null)
                            {
                                idsInvoiceHeaderErr.Add(invoiceHeader.Id, JsonConvert.SerializeObject(hdon?.DSLDo?.LDo));
                            }
                        }
                    }
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._03XKNB)
                {
                    foreach (var bthop in bthops)
                    {
                        foreach (var hdon in bthop.DSLHDon.HDon)
                        {
                            var invoiceHeader = await _repoInvoice03Header.FirstOrDefaultAsync(x => x.TenantId == tenantId
                                                                                                && x.TemplateNo == short.Parse(hdon.KHMSHDon)
                                                                                                && x.SerialNo == hdon.KHHDon
                                                                                                && x.Number == int.Parse(hdon.SHDon));
                            if (invoiceHeader != null)
                            {
                                idsInvoiceHeaderErr.Add(invoiceHeader.Id, JsonConvert.SerializeObject(hdon?.DSLDo?.LDo));
                            }
                        }
                    }
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._04HGDL)
                {
                    foreach (var bthop in bthops)
                    {
                        foreach (var hdon in bthop.DSLHDon.HDon)
                        {
                            var invoiceHeader = await _repoInvoice04Header.FirstOrDefaultAsync(x => x.TenantId == tenantId
                                                                                                && x.TemplateNo == short.Parse(hdon.KHMSHDon)
                                                                                                && x.SerialNo == hdon.KHHDon
                                                                                                && x.Number == int.Parse(hdon.SHDon));
                            if (invoiceHeader != null)
                            {
                                idsInvoiceHeaderErr.Add(invoiceHeader.Id, JsonConvert.SerializeObject(hdon?.DSLDo?.LDo));
                            }
                        }
                    }
                }
                else
                {
                    foreach (var bthop in bthops)
                    {
                        foreach (var hdon in bthop.DSLHDon.HDon)
                        {
                            var invoiceHeader = await _repoTicketHeader.FirstOrDefaultAsync(x => x.TenantId == tenantId
                                                                                                && x.TemplateNo == short.Parse(hdon.KHMSHDon)
                                                                                                && x.SerialNo == hdon.KHHDon
                                                                                                && x.Number == int.Parse(hdon.SHDon));
                            if (invoiceHeader != null)
                            {
                                idsInvoiceHeaderErr.Add(invoiceHeader.Id, JsonConvert.SerializeObject(hdon?.DSLDo?.LDo));
                            }
                        }
                    }
                }


                if (idsInvoiceHeaderErr.Any())
                {
                    foreach (var item in idsInvoiceHeaderErr)
                    {
                        var taxReport01TvanInfo = new TaxReport01TvanInfoEntity
                        {
                            InvoiceHeaderId = item.Key,
                            LTbao = ltbao,
                            TaxReport01DetailMappingEntityId = taxReport01DetailMappingEntity.Id,
                            TaxReport01HeaderEntityId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                            MessageTypeCode = mltdiep,
                            Title = MLTDiep._204.ToDisplayName(),
                            MessageCode = mtdiep,
                            MessageCodeReference = mtdtchieu,
                            Reason = item.Value,
                            FileId = file.Id,
                            TransmissionPartner = (short)transmissionPartner,
                            InvoiceType = taxReport01DetailMappingEntity.InvoiceType,
                            TenantId = tenantId,
                            TvanStatus = (short)TvanStatus.TCTReject
                        };

                        taxReport01TvanInfoReject.Add(taxReport01TvanInfo);
                    }
                }

                var idsInvoiceHeaderAccepted = idsInvoiceHeader.Except(idsInvoiceHeaderErr.Select(x => x.Key).ToList()).ToList();
                if (idsInvoiceHeaderAccepted.Any())
                {
                    foreach (var id in idsInvoiceHeaderAccepted)
                    {
                        var taxReport01TvanInfo = new TaxReport01TvanInfoEntity
                        {
                            InvoiceHeaderId = id,
                            LTbao = ltbao,
                            TaxReport01DetailMappingEntityId = taxReport01DetailMappingEntity.Id,
                            TaxReport01HeaderEntityId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                            MessageTypeCode = mltdiep,
                            Title = MLTDiep._204.ToDisplayName(),
                            MessageCode = mtdiep,
                            MessageCodeReference = mtdtchieu,
                            Reason = "Success",
                            FileId = file.Id,
                            TransmissionPartner = (short)transmissionPartner,
                            InvoiceType = taxReport01DetailMappingEntity.InvoiceType,
                            TenantId = tenantId,
                            TvanStatus = (short)TvanStatus.TCTAccept
                        };

                        taxReport01TvanInfoReject.Add(taxReport01TvanInfo);
                    }
                }

                if (taxReport01TvanInfoAccepted.Any())
                {
                    await _repoTaxReport01TvanInfo.InsertManyAsync(taxReport01TvanInfoAccepted);
                }

                if (taxReport01TvanInfoReject.Any())
                {
                    await _repoTaxReport01TvanInfo.InsertManyAsync(taxReport01TvanInfoReject);
                }

                idsHeaderAccepted = idsInvoiceHeaderAccepted;
                idsHeaderReject = idsInvoiceHeaderErr.Select(x => x.Key).ToList();
            }
            #endregion

            #region Update trang thai hoa don
            //update hóa đơn đã kê khai - được chấp nhận

            if (idsHeaderAccepted.Any())
            {
                var updateIsDeclareAccepted = new StringBuilder(" BEGIN ");

                if (taxReport01DetailMappingEntity.InvoiceType == VnisType._01GTKT.GetHashCode())
                {
                    updateIsDeclareAccepted.Append(ToRawUpdate(idsHeaderAccepted, $"{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._02GTTT.GetHashCode())
                {
                    updateIsDeclareAccepted.Append(ToRawUpdate(idsHeaderAccepted, $"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._03XKNB.GetHashCode())
                {
                    updateIsDeclareAccepted.Append(ToRawUpdate(idsHeaderAccepted, $"{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._04HGDL.GetHashCode())
                {
                    updateIsDeclareAccepted.Append(ToRawUpdate(idsHeaderAccepted, $"{DatabaseExtension<Invoice04HeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._05TVDT.GetHashCode())
                {
                    updateIsDeclareAccepted.Append(ToRawUpdate(idsHeaderAccepted, $"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}", TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared));
                }

                updateIsDeclareAccepted.Append(" END; ");

                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(updateIsDeclareAccepted.ToString());

                // update Mongo
                await UpdateMongoDbAsync(taxReport01DetailMappingEntity, idsInvoiceHeader, TvanStatus.TCTAccept, TvanDeclaredStatus.IsDeclared);


                if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._01GTKT)
                {
                    await UpdateResync01Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
                }

                if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._02GTTT)
                {
                    await UpdateResync02Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
                }
            }
            

            if (idsHeaderReject.Any())
            {
                // update hóa đơn đã kê khai - nhưng bị từ chối
                var updateIsDeclareReject = new StringBuilder(" BEGIN ");

                if (taxReport01DetailMappingEntity.InvoiceType == VnisType._01GTKT.GetHashCode())
                {
                    updateIsDeclareReject.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._02GTTT.GetHashCode())
                {
                    updateIsDeclareReject.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._03XKNB.GetHashCode())
                {
                    updateIsDeclareReject.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._04HGDL.GetHashCode())
                {
                    updateIsDeclareReject.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice04HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
                }
                else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._05TVDT.GetHashCode())
                {
                    updateIsDeclareReject.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
                }

                updateIsDeclareReject.Append(" END; ");
                
                await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(updateIsDeclareReject.ToString());


                // update Mongo
                await UpdateMongoDbAsync(taxReport01DetailMappingEntity, idsInvoiceHeader, TvanStatus.TCTReject, TvanDeclaredStatus.IsDeclared);

                if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._01GTKT)
                {
                    await UpdateResync01Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
                }

                if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._02GTTT)
                {
                    await UpdateResync02Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
                }
            }
            #endregion
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="thongDiep"></param>
        /// <param name="taxReport01DetailMappingEntity"></param>
        /// <param name="transmissionPartner"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        private async Task LTbao9ProccessAsync(TDiepModel<TTChungModel, DLieuMS01TBaoModel> thongDiep, TaxReport01DetailMappingEntity taxReport01DetailMappingEntity, TransmissionPartnerEnum transmissionPartner, string xml)
        {
            var mtdiep = thongDiep.TTChung.MTDiep; // mã thống điệp
            var mtdtchieu = thongDiep.TTChung.MTDTChieu; // mã thông điệp tham chiếu
            var ltbao = thongDiep.DLieu.TBao.DLTBao.LTBao; // loại thông báo
            var mltdiep = thongDiep.TTChung.MLTDiep.ToString(); // mã loại thông điệp

            #region UPDATE DETAILMAPPING
            taxReport01DetailMappingEntity.TvanReceivedTime = DateTime.Now;
            taxReport01DetailMappingEntity.StatusTvan = (short)TvanStatus.TCTReject;
            taxReport01DetailMappingEntity.MessageCodeReference = mtdiep;

            await _repoTaxReport01DetailMapping.UpdateAsync(taxReport01DetailMappingEntity);
            #endregion

            #region Upload File lên Minio
            //upload lên xml
            var bytes = Encoding.UTF8.GetBytes(xml);
            var fileService = _appFactory.GetServiceDependency<IFileService>();
            var fileName = $"DLBTH01_{mltdiep}_{ltbao}_{DateTime.Now.Ticks}.xml".Replace("/", "-");
            var creationTime = DateTime.Now;
            var pathFileMinio = $"{MediaFileType.TvanTaxReportXml}/{taxReport01DetailMappingEntity.TenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
            await fileService.UploadAsync(pathFileMinio, bytes);

            //insert dữ liệu bảng tvaninfo + xml
            var repoTvanXml = _appFactory.Repository<TvanReportXmlEntity, long>();
            var file = new TvanReportXmlEntity
            {
                TaxReport01HeaderId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                TaxReport01DetailMappingId = taxReport01DetailMappingEntity.Id,
                TenantId = taxReport01DetailMappingEntity.TenantId,
                ContentType = ContentType.Xml,
                FileName = fileName,
                PhysicalFileName = fileName,
                Length = bytes.Length,
                CreationTime = creationTime,
                StatusTvan = (short)TvanStatus.TCTReject
            };

            await repoTvanXml.InsertAsync(file, true);
            #endregion

            #region INSERT TaxReport01TvanInfo
            var taxReport01TvanInfoEntitys = new List<TaxReport01TvanInfoEntity>();
            var idsInvoiceHeader = taxReport01DetailMappingEntity.InvoiceIds.Split(',').Select(long.Parse).ToList();
            foreach (var id in idsInvoiceHeader)
            {
                var taxReport01TvanInfoEntity = new TaxReport01TvanInfoEntity
                {
                    InvoiceType = taxReport01DetailMappingEntity.InvoiceType,
                    InvoiceHeaderId = id,
                    LTbao = ltbao,
                    TaxReport01DetailMappingEntityId = taxReport01DetailMappingEntity.Id,
                    TaxReport01HeaderEntityId = taxReport01DetailMappingEntity.TaxReportHeaderId,
                    MessageTypeCode = mltdiep,
                    Title = MLTDiep._204.ToDisplayName(),
                    MessageCode = mtdiep,
                    MessageCodeReference = mtdtchieu,
                    Reason = JsonConvert.SerializeObject(thongDiep.DLieu.TBao.DLTBao.KHLKhac.DSLDo),
                    FileId = file.Id,
                    TransmissionPartner = (short)transmissionPartner,
                    TenantId = taxReport01DetailMappingEntity.TenantId,
                    TvanStatus = (short)TvanStatus.TCTReject
                };

                taxReport01TvanInfoEntitys.Add(taxReport01TvanInfoEntity);
            }

            await _repoTaxReport01TvanInfo.InsertManyAsync(taxReport01TvanInfoEntitys);
            #endregion

            #region Update trang thai hoa don
            //update hóa đơn đã kê khai
            var updateIsDeclare = new StringBuilder(" BEGIN ");

            if (taxReport01DetailMappingEntity.InvoiceType == VnisType._01GTKT.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice01HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._02GTTT.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice02HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._03XKNB.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice03HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._04HGDL.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<Invoice04HeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
            }
            else if (taxReport01DetailMappingEntity.InvoiceType == VnisType._05TVDT.GetHashCode())
            {
                updateIsDeclare.Append(ToRawUpdate(idsInvoiceHeader, $"{DatabaseExtension<TicketHeaderEntity>.GetTableName()}", TvanStatus.TCTReject, TvanDeclaredStatus.UnDeclared));
            }
            updateIsDeclare.Append(" END; ");

            var queryUpdateIsDeclared = updateIsDeclare.ToString();
            await _appFactory.VnisCoreOracle.Connection.ExecuteAsync(queryUpdateIsDeclared);

            // update Mongo
            await UpdateMongoDbAsync(taxReport01DetailMappingEntity, idsInvoiceHeader, TvanStatus.TCTReject, TvanDeclaredStatus.IsDeclared);


            if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._01GTKT)
            {
                await UpdateResync01Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
            }

            if (taxReport01DetailMappingEntity.InvoiceType == (short)VnisType._02GTTT)
            {
                await UpdateResync02Async(idsInvoiceHeader, SyncElasticSearchStatus.PendingSyncStatusTvanResponse);
            }
            #endregion
        }
        
        /// <summary>
        /// Update trạng thái CQT chấp nhận và đã kê khai
        /// </summary>
        /// <param name="invoiceHeaderIds">Danh sách Id invoice Header</param>
        /// <param name="tableName"></param>
        /// <returns></returns>
        private static string ToRawUpdate(List<long> invoiceHeaderIds, string tableName, TvanStatus tvanStatus, TvanDeclaredStatus tvanDeclaredStatus)
        {
            var i = 0;
            const int pageSize = 1000;
            var index = 0;
            var subSql = new StringBuilder();
            while (true)
            {
                if (i >= invoiceHeaderIds.Count)
                    break;

                var page = invoiceHeaderIds.Skip(i).Take(pageSize);
                subSql.Append(@$" Update ""{tableName}"" SET ""StatusTvan"" = {(short)tvanStatus}, 
                                                             ""IsDeclared"" = {(short)tvanDeclaredStatus} 
                                                         WHERE ""Id"" in ({string.Join(",", page)}) AND  ""IsDeclared"" = 0; ");
                i += pageSize;
                index++;
            }

            return subSql.ToString();
        }

        private async Task UpdateMongoDbAsync(TaxReport01DetailMappingEntity taxReportDetailMapping, List<long> invoiceHeaderIds, TvanStatus tvanStatus, TvanDeclaredStatus tvanDeclaredStatus)
        {
            if (taxReportDetailMapping.InvoiceType == VnisType._01GTKT.GetHashCode())
            {
                var i = 0;
                const int pageSize = 1000;
                var indx = 0;
                while (true)
                {
                    if (i >= invoiceHeaderIds.Count)
                    {
                        break;
                    }

                    var page = invoiceHeaderIds.Skip(i).Take(pageSize);
                    var ids01 = page.ToList();
                    await _vnisCoreMongoInvoice01Repository.UpdateDeclaresStatusTvan(ids01, (short)tvanDeclaredStatus, (short)tvanStatus);
                    i += pageSize;
                    indx++;
                }
            }

            if (taxReportDetailMapping.InvoiceType == VnisType._02GTTT.GetHashCode())
            {
                var i = 0;
                const int pageSize = 1000;
                var indx = 0;
                while (true)
                {
                    if (i >= invoiceHeaderIds.Count)
                    {
                        break;
                    }

                    var page = invoiceHeaderIds.Skip(i).Take(pageSize);
                    var ids02 = page.ToList();
                    await _vnisCoreMongoInvoice02Repository.UpdateDeclaresStatusTvan(ids02, (short)tvanDeclaredStatus, (short)tvanStatus);
                    i += pageSize;
                    indx++;
                }
            }
        }

        private async Task UpdateResync01Async(List<long> ids, SyncElasticSearchStatus syncElasticSearchStatus)
        {
            //var ids = invoiceHeaders.Select(x => x.Id).ToList();
            if (ids.Any())
            {
                var invoice01ReSyncs = await _mongoInvoice01ReSyncRepository.GetByIdsAsync(ids);

                var invoice01ReSyncInsert = new List<MongoInvoice01ReSyncEntity>();

                if (invoice01ReSyncs.Any())
                {
                    invoice01ReSyncs.ForEach(item =>
                    {
                        item.IsSyncedToElasticSearch = (short)syncElasticSearchStatus;
                    });

                    foreach (var item in ids)
                    {
                        var invoice01ReSync = invoice01ReSyncs.FirstOrDefault(x => x.Id == item);
                        if (invoice01ReSync == null)
                        {
                            invoice01ReSync = new MongoInvoice01ReSyncEntity
                            {
                                Id = item,
                                IsSyncedToElasticSearch = (short)syncElasticSearchStatus
                            };
                            invoice01ReSyncInsert.Add(invoice01ReSync);
                        }
                    }

                    await _mongoInvoice01ReSyncRepository.UpdateManyAsync(invoice01ReSyncs);

                    if (invoice01ReSyncInsert.Any())
                        await _mongoInvoice01ReSyncRepository.InsertManyAsync(invoice01ReSyncInsert);
                }
                else
                {
                    foreach (var item in ids)
                    {
                        var invoice01ReSync = new MongoInvoice01ReSyncEntity
                        {
                            Id = item,
                            IsSyncedToElasticSearch = (short)syncElasticSearchStatus
                        };
                        invoice01ReSyncInsert.Add(invoice01ReSync);
                    }

                    if (invoice01ReSyncInsert.Any())
                    {
                        await _mongoInvoice01ReSyncRepository.InsertManyAsync(invoice01ReSyncInsert);
                    }
                }
            }
        }

        private async Task UpdateResync02Async(List<long> ids, SyncElasticSearchStatus syncElasticSearchStatus)
        {
            //var ids = invoiceHeaders.Select(x => x.Id).ToList();
            if (ids.Any())
            {
                var invoice02ReSyncs = await _mongoInvoice02ReSyncRepository.GetByIdsAsync(ids);

                var invoice02ReSyncInsert = new List<MongoInvoice02ReSyncEntity>();

                if (invoice02ReSyncs.Any())
                {
                    invoice02ReSyncs.ForEach(item =>
                    {
                        item.IsSyncedToElasticSearch = (short)syncElasticSearchStatus;
                    });

                    foreach (var item in ids)
                    {
                        var invoice02ReSync = invoice02ReSyncs.FirstOrDefault(x => x.Id == item);
                        if (invoice02ReSync == null)
                        {
                            invoice02ReSync = new MongoInvoice02ReSyncEntity
                            {
                                Id = item,
                                IsSyncedToElasticSearch = (short)syncElasticSearchStatus
                            };
                            invoice02ReSyncInsert.Add(invoice02ReSync);
                        }
                    }

                    await _mongoInvoice02ReSyncRepository.UpdateManyAsync(invoice02ReSyncs);

                    if (invoice02ReSyncInsert.Any())
                        await _mongoInvoice02ReSyncRepository.InsertManyAsync(invoice02ReSyncInsert);
                }
                else
                {
                    foreach (var item in ids)
                    {
                        var invoice02ReSync = new MongoInvoice02ReSyncEntity
                        {
                            Id = item,
                            IsSyncedToElasticSearch = (short)syncElasticSearchStatus
                        };
                        invoice02ReSyncInsert.Add(invoice02ReSync);
                    }

                    if (invoice02ReSyncInsert.Any())
                    {
                        await _mongoInvoice02ReSyncRepository.InsertManyAsync(invoice02ReSyncInsert);
                    }
                }
            }
        }
    }
}
