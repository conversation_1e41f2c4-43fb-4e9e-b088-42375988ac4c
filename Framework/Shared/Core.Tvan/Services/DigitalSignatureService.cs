using Core.Shared.Constants;
using Core.Tvan.Extensions;
using Core.Tvan.Interfaces;

using Microsoft.Extensions.Configuration;

using Stimulsoft.Base.Localization;

using System;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Xml;
using System.Xml.Linq;

namespace Core.Tvan.Services
{
    public class DigitalSignatureService : IDigitalSignatureService
    {
        private readonly IConfiguration _configuration;

        public DigitalSignatureService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public string Sign(X509Certificate2 certificate, string xml, string signatureId, MediaFileType xmlType)
        {
            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(xml);
           
            if (MediaFileType.Invoice01Xml == xmlType || MediaFileType.Invoice02Xml == xmlType || MediaFileType.Invoice03Xml == xmlType || MediaFileType.Invoice04Xml == xmlType)
            {
                document.DocumentElement["DLHDon"].SetAttribute("id", "data");
            }

            if (MediaFileType.RegistrationInvoiceXml == xmlType)
            {
                var tkhaiNode = document.SelectSingleNode(@"//TKhai/DLTKhai");

                if (tkhaiNode != null)
                {
                    ((XmlElement)document.SelectSingleNode(@"//TKhai/DLTKhai")).SetAttribute("id", "data");
                }
            }

            var uri = _configuration.GetSection("SignatureId:Uri").Value;
            if (string.IsNullOrEmpty(uri))
                uri = "#data";
            // Sign the XML document. 
            AddSignature(document, certificate, "data", uri, xmlType);
            return document.OuterXml;
        }

        private static void AddSignature(XmlDocument document, X509Certificate2 certificate, string signatureId, string uri, MediaFileType xmlType)
        {
            // var timestamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = signatureId;
            signedXml.SigningKey = certificate.PrivateKey;

            var keyInfo = new KeyInfo();

            var keyInfoData = new KeyInfoX509Data(certificate);

            var xserial = new X509IssuerSerial();
            xserial.IssuerName = certificate.IssuerName.Name;
            xserial.SerialNumber = certificate.SerialNumber;
            keyInfoData.AddIssuerSerial(xserial.IssuerName, xserial.SerialNumber);
            keyInfoData.AddSubjectName(certificate.SubjectName.Name);

            keyInfo.AddClause(keyInfoData);
            keyInfo.Id = DateTimeOffset.UtcNow.ToEpochTime().ToString();

            signedXml.KeyInfo = keyInfo;

            var reference = new Reference(uri);
            var env = new XmlDsigEnvelopedSignatureTransform();
            reference.AddTransform(env);
            signedXml.AddReference(reference);

            signedXml.ComputeSignature();

            var xmlDigitalSignature = signedXml.GetXml();
            // Append the element to the XML document.
            //document.DocumentElement.AppendChild(document.ImportNode(xmlDigitalSignature, true));
            if (MediaFileType.Invoice01Xml == xmlType || MediaFileType.Invoice02Xml == xmlType || MediaFileType.Invoice03Xml == xmlType || MediaFileType.Invoice04Xml == xmlType)
            {
                var element = document.SelectSingleNode(@"//DSCKS/NBan");
                if (element != null)
                {
                    element.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                }
                else
                {
                    var tagDsck = document.SelectSingleNode(@"//DSCKS");
                    if (tagDsck != null)
                    {
                        var nnt = document.CreateElement("NBan");
                        nnt.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                        tagDsck.AppendChild(document.ImportNode(nnt, true));

                        var el = document.SelectSingleNode(@"//HDon");
                        el.AppendChild(document.ImportNode(tagDsck, true));
                    }
                    else
                    {
                        var dscks = document.CreateElement("DSCKS");
                        var nnt = document.CreateElement("NBan");
                        nnt.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                        dscks.AppendChild(document.ImportNode(nnt, true));

                        var el = document.SelectSingleNode(@"//HDon");
                        el.AppendChild(document.ImportNode(dscks, true));
                    }
                }
            }
            
            if (MediaFileType.RegistrationInvoiceXml == xmlType)
            {
                var element = document.SelectSingleNode(@"//DSCKS/NNT");
                if (element != null)
                {
                    element.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                }
                else
                {
                    var dscks = document.CreateElement("DSCKS");
                    var nnt = document.CreateElement("NNT");
                    nnt.AppendChild(document.ImportNode(xmlDigitalSignature, true));
                    dscks.AppendChild(document.ImportNode(nnt, true));

                    var el = document.SelectSingleNode(@"//TKhai");
                    el.AppendChild(document.ImportNode(dscks, true));
                }
            }    
        }

        public string Sign(X509Certificate2 certificate, string xml, List<string> signTags, string signatureId = null, string parentTag = null)
        {
            // Create a new XML document.
            XmlDocument document = new XmlDocument();

            // Load an XML file into the XmlDocument object.
            document.PreserveWhitespace = true;
            document.LoadXml(xml);

            // Sign the XML document. 
            var signedXml = new SignedXml(document);
            signedXml.Signature.Id = string.IsNullOrWhiteSpace(signatureId) ? null : signatureId;
            signedXml.SigningKey = certificate.PrivateKey;

            var xserial = new X509IssuerSerial();
            xserial.IssuerName = certificate.IssuerName.Name;
            xserial.SerialNumber = certificate.SerialNumber;

            var keyInfoData = new KeyInfoX509Data(certificate);
            keyInfoData.AddIssuerSerial(xserial.IssuerName, xserial.SerialNumber);
            keyInfoData.AddSubjectName(certificate.SubjectName.Name);

            var keyInfo = new KeyInfo();
            keyInfo.AddClause(keyInfoData);
            keyInfo.Id = DateTimeOffset.UtcNow.ToEpochTime().ToString();

            signedXml.KeyInfo = keyInfo;

            foreach (var tag in signTags)
            {
                var reference = new Reference(tag);
                reference.AddTransform(new XmlDsigEnvelopedSignatureTransform());
                signedXml.AddReference(reference);
            }

            signedXml.ComputeSignature();
            var signature = signedXml.GetXml();

            var verify = signedXml.CheckSignature();
            if (!verify)
                throw new Exception("Signature is not valid");
            // Append the element to the XML document.
            if (string.IsNullOrWhiteSpace(parentTag))
            {
                document.DocumentElement.AppendChild(document.ImportNode(signature, true));
            }
            else
            {
                var element = document.SelectSingleNode($"//*[@id='{parentTag}']");
                element.AppendChild(document.ImportNode(signature, true));
            }

            return document.OuterXml;
        }

        public X509Certificate2 GetPrivateKey(string path, string password)
        {
            return new X509Certificate2(path, password, X509KeyStorageFlags.Exportable);
        }

        public X509Certificate2 GetPublicKey(string path)
        {
            return new X509Certificate2(path);
        }
    }
}
