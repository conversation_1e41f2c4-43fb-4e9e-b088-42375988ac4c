using Core.Tvan.Models;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;

namespace Core.Tvan.Interfaces
{
    [Obsolete]
    public interface ITvanInvoiceService
    {
        /// <summary>
        /// gửi dkph
        /// </summary>
        /// <param name="idRegistration"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task SendRegistrationAsync(Guid tenantId, string sellerTaxCode, long idRegistration, string xml);

        /// <summary>
        /// g<PERSON>i hóa đơn 01 có mã yêu cầu cấp mã
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="invoiceHeaderId"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task<TvanResponseModel> SendInvoice01HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml);

        /// <summary>
        /// gửi hóa đơn 02 có mã yêu cầu cấp mã
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="invoiceHeaderId"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task<TvanResponseModel> SendInvoice02HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml);

        /// <summary>
        /// gửi hóa đơn 03 có mã yêu cầu cấp mã
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="invoiceHeaderId"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task<TvanResponseModel> SendInvoice03HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml);

        /// <summary>
        /// gửi hóa đơn 04 có mã yêu cầu cấp mã
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="id"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task<TvanResponseModel> SendInvoice04HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml);

        /// <summary>
        /// gửi vé có mã yêu cầu cấp mã
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="id"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task<TvanResponseModel> SendTicketHasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml);


        Task<TvanResponseModel> SendInvoiceWithoutCodeAsync(string xml);

        Task SendReport01Async(string xml);

        /// <summary>
        /// gửi báo cáo thuế 03
        /// </summary>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task SendReport03Async(string sellerTaxCode, string xml);

        /// <summary>
        /// Gửi hóa đơn Sai sót
        /// </summary>
        /// <param name="sellerTaxCode"></param>
        /// <param name="xml"></param>
        /// <param name="tenantId"></param>
        /// <param name="headerIds"></param>
        /// <returns></returns>
        Task<TvanResponseModel> SendInvoice01ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<Invoice01ErrorEntity> invoicesError);

        Task<TvanResponseModel> SendInvoice02ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<Invoice02ErrorEntity> invoicesError);

        Task<TvanResponseModel> SendInvoice03ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<Invoice03ErrorEntity> invoicesError);

        Task<TvanResponseModel> SendInvoice04ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<Invoice04ErrorEntity> invoicesError);

        Task<TvanResponseModel> SendTicketErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<TicketErrorEntity> invoicesError);
    }
}
