using Core.MultiTenancy;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Core.Host.Shared.Middleware
{
    public class AbpAspNetCoreCheckLockedUserOptions
    {
        /// <summary>
        /// Default: <see cref="TenantResolverConsts.DefaultTenantKey"/>.
        /// </summary>
        public string TenantKey { get; set; }

        public Func<HttpContext, Exception, Task> MultiTenancyMiddlewareErrorPageBuilder { get; set; }

        public AbpAspNetCoreCheckLockedUserOptions()
        {
            TenantKey = TenantResolverConsts.DefaultTenantKey;
            MultiTenancyMiddlewareErrorPageBuilder = async (context, exception) =>
            {
                //context.Response.StatusCode = (int)HttpStatusCode.Forbidden;
                context.Response.ContentType = "application/json";

                var message = exception.Message;

                ErrorMessage error = new ErrorMessage();
                error.StatusCode = context.Response.StatusCode;
                error.Message = message;
                await context.Response.WriteAsJsonAsync(error);

                // Note the 500 spaces are to work around an IE 'feature'
                await context.Response.WriteAsync(new string(' ', 401));
            };
        }
    }
    public class ErrorMessage
    {
        public string Message { get; set; }
        public int StatusCode { get; set; }
    }
}
