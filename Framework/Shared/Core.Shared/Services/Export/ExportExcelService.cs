using Core.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Shared.Services.ExportExcel
{
    public interface IExportExcelService : IScopedDependency
    {
    }

    public class ExportExcelService : IExportExcelService
    {
        public ExportExcelService()
        {
                
        }
    }
}
