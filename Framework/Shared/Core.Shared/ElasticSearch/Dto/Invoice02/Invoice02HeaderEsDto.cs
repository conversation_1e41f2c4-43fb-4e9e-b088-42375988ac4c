using Core.Shared.Constants;

using System;
using System.Collections.Generic;

namespace Core.Shared.ElasticSearch.Dto.Invoice02
{
    public class Invoice02HeaderEsDto
    {
        public long Id { get; set; }
        public DateTime CreationTime { get; set; }
        public int CreationTimeNumber { get; set; }
        public long IdReference { get; set; }
        public string ErpId { get; set; }
        public string TransactionId { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public string InvoiceNo { get; set; }
        public int? Number { get; set; }
        public InvoiceStatus InvoiceStatus { get; set; }
        public SignStatus SignStatus { get; set; }
        public ApproveStatus ApproveStatus { get; set; }
        public ApproveStatus ApproveCancelStatus { get; set; }
        public ApproveStatus ApproveDeleteStatus { get; set; }
        public DateTime InvoiceDate { get; set; }
        public int? InvoiceDateNumber { get; set; }
        public DateTime? InvoiceDateReference { get; set; }
        public string SerialNoReference { get; set; }
        public string InvoiceNoReference { get; set; }
        public string TemplateNoReference { get; set; }
        public int? NumberReference { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalPaymentAmount { get; set; }
        public string BuyerFullName { get; set; }
        public string BuyerEmail { get; set; }
        public string BuyerCode { get; set; }

        public DateTime? PrintedTime { get; set; }
        public int? PrintedTimeNumber { get; set; }
        public string FullNameCreator { get; set; }
        public string UserNameCreator { get; set; }
        public string CreatorErp { get; set; }
        public string Note { get; set; }
        public long? IdFileDocument { get; set; }
        public InvoiceSource Source { get; set; }
        public bool IsViewed { get; set; }
        public bool IsOpened { get; set; }

        public string BuyerBankAccount { get; set; }

        public long TotalItems { get; set; }

        public string InvoiceHeaderExtrasJson { get; set; }

        public bool IsDeclared { get; set; }

        /// <summary>
        /// mã cơ quan thuế cho trường hợp hóa đơn cấp mã
        /// </summary>
        public string VerificationCode { get; set; }

        /// <summary>
        /// các lỗi do TCT trả về
        /// </summary>
        public string InvoiceErrorMessage { get; set; }

        /// <summary>
        /// để thông tin loại sai sót nsd chọn khi lập sai sót
        /// </summary>
        public short InvoiceErrorType { get; set; }

        /// <summary>
        /// trạng thái ký của TBSS gần nhất
        /// </summary>
        public short? InvoiceErrorSignStatus { get; set; }

        /// <summary>
        /// trạng thái gửi lên Tvan của TBSS gần nhất
        /// null hoặc < 0: đc lập lại sai sót, không dc thay thế/dc
        /// = 0 : không đc lập ss, KHÔNG lập thay thế, điều chinh
        /// = 1, 2, 3: k lập ss => đc lập thay thế
        /// </summary>
        public short? InvoiceErrorTvanStatus { get; set; }

        /// <summary>
        /// trạng thái phản hồi của TCT với các nghiệp vụ về sai sót, rà soát, kiểm tra dữ liệu
        /// </summary>
        public short GDTResponseStatus { get; set; }

        /// <summary>
        /// reason của hóa đơn sai sót
        /// </summary>
        public string TvanInfoInvoiceErrorReason { get; set; }

        /// <summary>
        /// status của hóa đơn sai sót
        /// </summary>
        public short TvanInfoInvoiceErrorStatus { get; set; }

        /// <summary>
        /// reason của hóa đơn có mã mà TCT gửi về khi cấp mã
        /// </summary>
        public string TvanInfoInvoiceHasCodeReason { get; set; }

        /// <summary>
        /// reason của hóa đơn rà soát
        /// </summary>
        public string TvanInfoCheckInvoiceReason { get; set; }

        /// <summary>
        /// reason của hóa đơn k mã khi KTDL (nếu có)
        /// </summary>
        public string TvanInfoInvoiceWithoutCodeReason { get; set; }

        /// <summary>
        /// loại hóa đơn có liên quan
        /// 1-Hóa đơn điện tử theo Nghị định 123/2020/NĐ-CP
        /// 2-Hóa đơn điện tử có mã xác thực theo Quyết định số 1209/QĐ-BTC 
        /// 3-hóa đơn theo Nghị định số 51/2010/NĐ-CP 
        /// 4-Hóa đơn đặt in theo Nghị định 123/2020/NĐ-CP
        /// </summary>
        public short ReferenceInvoiceType { get; set; }

        public string ExtraProperties { get; set; }

        public Guid TenantId { get; set; }

        public Guid? CreatorId { get; set; }
        public Guid BatchId { get; set; }
        public string PaymentMethod { get; set; }
        public string BuyerPhoneNumber { get; set; }
        public string BuyerLegalName { get; set; }
        public string BuyerTaxCode { get; set; }
        public long InvoiceTemplateId { get; set; }
        public DateTime? IssuedTime { get; set; }
        public int? IssuedTimeNumber { get; set; }
        public DateTime? CancelTime { get; set; }
        public int? CancelTimeNumber { get; set; }
        public DateTime? DeleteTime { get; set; }
        public int? DeleteTimeNumber { get; set; }
        public string UserNamePrinted { get; set; }

        public List<PagingInvoice02HeaderExtraResponseModel> InvoiceHeaderExtras { get; set; }

        public class PagingInvoice02HeaderExtraResponseModel
        {
            public string FieldName { get; set; }
            public string FieldValue { get; set; }
        }

        public string BuyerFullNameUnicode { get; set; }
        public string BuyerLegalNameUnicode { get; set; }
    }
}
