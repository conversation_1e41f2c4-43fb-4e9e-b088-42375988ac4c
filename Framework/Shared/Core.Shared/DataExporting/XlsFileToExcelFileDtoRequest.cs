using System.Diagnostics;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Core.Shared.Dto;
using Core.Shared.Factory;
using FlexCel.XlsAdapter;
using MediatR;

using Serilog;

namespace Core.Shared.DataExporting
{
    public class XlsFileToExcelFileDtoRequest: IRequest<FileDto>
    {
        public XlsFile XlsResult { get; set; }
        public string OutputFileNameNotExtension { get; set; }
        public bool IsFileExcel2003 { get; set; } = false;
    }
    public class XlsFileToExcelFileDtoRequestHandler:IRequestHandler<XlsFileToExcelFileDtoRequest, FileDto>
    {
        private readonly IAppFactory _factory;

        public XlsFileToExcelFileDtoRequestHandler(IAppFactory factory)
        {
            _factory = factory;
        }

        public async Task<FileDto> Handle(XlsFileToExcelFileDtoRequest request, CancellationToken cancellationToken)
        {
            using (var outStream = new MemoryStream())
            {
                var stopwatch = new Stopwatch();

                stopwatch.Start();
                request.XlsResult.Save(outStream);
                var fileName = request.OutputFileNameNotExtension + ".xlsx";
                if (request.IsFileExcel2003)
                {
                    fileName = request.OutputFileNameNotExtension + ".xls";
                }
                stopwatch.Stop();

                //Log.Fatal($@"TIME MAP DATA TO EXCEL TAXREPORT: {stopwatch.ElapsedMilliseconds}");
                stopwatch.Reset();
                return new FileDto(fileName, outStream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
        }
    }
}
