using System;

namespace Core.Shared.Cached.Dtos
{
    public class CurrencyCachingDto
    {
        public long Id { get; set; }
        public string CurrencyCode { get; set; }
        public string NameEn { get; set; }
        public string NameVi { get; set; }
        public string MinimumNameEn { get; set; }
        public string MinimumNameVi { get; set; }
        public int Conversion { get; set; }
        public int Rounding { get; set; }
        public bool IsDefault { get; set; }
        public DateTime CreationTime { get; set; }
    }
}
