using Core.Shared.Extensions;
using System;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace Core.Shared.Helper
{
    public static class StringHelper
    {
        public static string GetMimeType(this string fileName)
        {
            var extension = System.IO.Path.GetExtension(fileName).ToLower();
            return FileMappingHelper.Mappings.TryGetValue(extension, out var mimeType) ? mimeType : "application/octet-stream";
        }

        public static string ExtractTaxCode(string strText)
        {
            string retstr = "";
            string pat = @"([0-9]{10})-?([0-9]{3})?";
            Regex r = new Regex(pat, RegexOptions.IgnoreCase);

            var index = 0;
            while (index < strText.Length)
            {
                Match m = r.Match(strText.Substring(index, strText.Length - index));
                if (!m.Success)
                    break;

                if (m.Groups.Count == 3)
                    retstr = m.Groups[0].ToString();
                else
                    retstr = m.Groups[1].ToString();

                if (!retstr.IsTaxCode())
                {
                    index = strText.IndexOf(retstr) + retstr.Length;
                }
                else
                {
                    break;
                }
            }

            return retstr;
        }


        public static string ExtractTaxCodeNew(string strText)
        {
            string retstr = "";
            // Pattern hỗ trợ cả MST 10 ký tự và MST 12 ký tự
            string pat = @"MST[:=]([0-9]{12}|[0-9]{10})-?([0-9]{3})?";

            Regex r = new Regex(pat, RegexOptions.IgnoreCase);

            var index = 0;
            while (index < strText.Length)
            {
                Match m = r.Match(strText.Substring(index, strText.Length - index));

                if (!m.Success)
                    break;

                // Lấy mã số thuế từ group 1 (bỏ qua "MST:")
                retstr = m.Groups[1].ToString();

                // Nếu có group 2 (3 chữ số cuối), thêm vào
                if (m.Groups.Count > 2 && !string.IsNullOrEmpty(m.Groups[2].ToString()))
                {
                    retstr += "-" + m.Groups[2].ToString();
                }

                if (!retstr.IsTaxCode())
                {
                    index = strText.IndexOf(m.Groups[0].ToString()) + m.Groups[0].ToString().Length;
                }
                else
                {
                    break;
                }
            }

            return retstr;
        }

        public static int RandomGroup(int maxValue)
        {
            var myObject = new Random();
            var ranNum = myObject.Next(0, maxValue);
            return ranNum;
        }
    }
}
