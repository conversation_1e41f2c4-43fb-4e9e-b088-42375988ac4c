using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Core.Shared.Extensions;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Core.Shared.Models
{
    public class ErrorResponse
    {
        public int Status { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; }
    }

    public class ErrorResponse<T> : ErrorResponse
    {
        public T Data { get; set; }
    }

    public class ErrorModel
    {
        public int Code { get; set; }
        public string Field { get; set; }
        public string Description { get; set; }

        public static implicit operator ErrorModel(ModelError error)
        {
            if (error == null)
                return null;

            return new ErrorModel
            {
                Field = error.Field,
                Description = error.Message
            };
        }
    }

    public class ServiceResult
    {
        public int Status { get; set; }
        public string Message { get; set; }
        public List<string> Errors { get; set; }
    }

    public class ServiceResult<T> : ServiceResult
    {
        public T Data { get; set; }
    }

    public class ResultModel
    {
        public bool Succeeded { get; set; }

        public int Code { get; set; }

        public string Message { get; set; }

        public ErrorModel[] Errors { get; set; }

        public ResultModel()
        {
        }

        public ResultModel(bool succeeded)
        {
            Succeeded = succeeded;
        }

        public ResultModel(bool succeeded, string message)
        {
            Succeeded = succeeded;
            Code = -1;
            Message = message;
        }

        public ResultModel(bool succeeded, int code)
        {
            Succeeded = succeeded;

            if (succeeded)
                Code = 0;
            else
                Code = code;

            Message = code.ToString(); //TODO: sau làm các code cho phần api

        }

        public ResultModel(bool succeeded, int code, string message)
        {
            Succeeded = succeeded;
            Code = code;
            Message = message;
        }
    }

    //API
    public class ResultApiModel
    {
        public bool Succeeded { get; set; }

        public int Code { get; set; }

        public string Message { get; set; }

        public ResultApiModel()
        {
        }


        public ResultApiModel(bool succeeded, int code)
        {
            Succeeded = succeeded;

            if (succeeded)
            {
                Code = 0;
                return;
            }

            Code = code;
        }
    }

    public class ResultApiModel<T> : ResultApiModel
    {
        public T Data { get; set; }

        public ResultApiModel()
        {
        }

        public ResultApiModel(T data)
        {
            Succeeded = true;
            Code = 0;
            Data = data;
        }

        public ResultApiModel(bool succeeded, int? code)
        {

            Succeeded = succeeded;

            if (succeeded)
            {
                Code = 0;
                return;
            }

            if (code.HasValue)
                Code = code.Value;
            else
                Code = -1;
        }
    }

    public class ResultApiViewModel
    {
        public bool Succeeded { get; set; }

        public int Code { get; set; }

        public string Message { get; set; }
        public string[] Errors { get; set; }


        public ResultApiViewModel() { }

        public ResultApiViewModel(ModelStateDictionary modelState)
        {
            var errors = modelState.GetErros().Select(x => x.Field + ": " + x.Message).ToArray();

            Succeeded = false;
            Code = -1;
            Message = string.Join(";", errors);
            Errors = errors;
        }

        public ResultApiViewModel(ResultApiModel model)
        {
            Succeeded = model.Succeeded;
            Code = (int)model.Code;
            Message = model.Message;
        }

        public ResultApiViewModel(bool succeeded, int code)
        {
            Succeeded = succeeded;

            if (succeeded)
            {
                Code = 0;
                Message = code.ToString(); //TODO: sau trả ra thêm code giống 2.3 
            }
            else
            {
                Code = -1;
                Message = "Chưa xác định"; //TODO: sau trả ra thêm code giống 2.3 
            }
        }

        public ResultApiViewModel(bool succeeded, int code, string message = null)
        {
            Succeeded = succeeded;

            if (succeeded)
            {
                Code = 0;
                Message = "Thành công"; //TODO: sau trả ra thêm code giống 2.3 
            }
            else
            {
                Code = code;
                Message = message;
                if (string.IsNullOrEmpty(message) || int.TryParse(message, out int outMess))
                    Message = message;

            }
        }

        public static implicit operator ResultApiViewModel(ResultApiModel model)
        {
            if (model == null)
                return null;

            var vm = new ResultApiViewModel();

            vm.Succeeded = model.Succeeded;
            vm.Code = (int)model.Code;
            vm.Message = model.Message; //TODO: sau trả ra thêm code giống 2.3 

            return vm;
        }

        public static implicit operator ResultApiViewModel(ModelStateDictionary modelState)
        {
            if (modelState == null)
            {
                return null;
            }

            var vm = new ResultApiViewModel();

            var errors = modelState.GetErros().Select(x => x.Field + ": " + x.Message).ToArray();
            vm.Succeeded = false;
            vm.Code = -1;
            vm.Message = string.Join("; ", errors);
            vm.Errors = errors;

            return vm;
        }
    }

    public class ResultApiViewModel<T> : ResultApiViewModel
    {
        public T Data { get; set; }


        public ResultApiViewModel() { }

        public ResultApiViewModel(ModelStateDictionary modelState)
        {
            var errors = modelState.GetErros().Select(x => x.Field + ": " + x.Message).ToArray();
            Succeeded = false;
            Code = -1;
            Message = string.Join("; ", errors);
            Errors = errors;
        }

        public ResultApiViewModel(IList<ValidationResult> validationResults)
        {
            var errors = validationResults.Select(x => x.ErrorMessage).ToArray();
            Succeeded = false;
            Code = -1;
            Message = string.Join("; ", errors);
            Errors = errors;
        }


        public ResultApiViewModel(ResultApiModel model)
        {
            Succeeded = model.Succeeded;
            Code = (int)model.Code;
            Message = model.Message; //TODO: sau trả ra thêm code giống 2.3 
        }

        public ResultApiViewModel(ResultApiModel<T> model)
        {
            Succeeded = model.Succeeded;
            Code = (int)model.Code;
            Message = model.Succeeded ? "Thành công" : model.Message; //TODO: sau trả ra thêm code giống 2.3 
            Data = model.Data;
        }

        public ResultApiViewModel(bool succeeded, int? code)
        {
            //chỉ dùng cho api nên để hardcode 0 là thành công, -1 là chưa xác định

            Succeeded = succeeded;

            if (succeeded)
            {
                Code = 0; //key = 0 là thành công
                Message = "Thành công";
            }
            else
            {
                if (code.HasValue)
                {
                    Code = (int)code.Value;
                    Message = code?.ToString(); //TODO: sau trả ra thêm code giống 2.3 
                }
                else
                {
                    Code = -1;
                    Message = "Chưa xác định"; //TODO: sau trả ra thêm code giống 2.3 
                }
            }
        }


        public ResultApiViewModel(bool succeeded, int code, string message = null)
        {
            Succeeded = succeeded;

            if (succeeded)
            {
                Code = 0;
                Message = message;
            }
            else
            {
                Code = code;
                Message = message;
                if (string.IsNullOrEmpty(message) || int.TryParse(message, out int outMess))
                    Message = "Chưa xác định"; //TODO: làm code cho các phần mess
            }
        }

        public static implicit operator ResultApiViewModel<T>(ResultApiModel<T> model)
        {
            if (model == null)
                return null;

            var vm = new ResultApiViewModel<T>();

            vm.Succeeded = model.Succeeded;
            vm.Code = (int)model.Code;
            vm.Message = model.Message;

            return vm;
        }

        public static implicit operator ResultApiViewModel<T>(ModelStateDictionary modelState)
        {
            if (modelState == null)
                return null;

            var vm = new ResultApiViewModel<T>();

            var errors = modelState.GetErros().Select(x => x.Field + ": " + x.Message).ToArray();
            vm.Succeeded = false;
            vm.Code = -1;
            vm.Message = string.Join("; ", errors);
            vm.Errors = errors;

            return vm;
        }

    }

    public class PrintApiModel
    {
        public string Id { get; set; }
        public string Data { get; set; }
        public string FileName { get; set; }

        public PrintApiModel(string id, string data)
        {
            Id = id;
            Data = data;
        }

        public PrintApiModel(string id, string data, string fileName)
        {
            Id = id;
            Data = data;
            FileName = fileName;
        }
    }
}
