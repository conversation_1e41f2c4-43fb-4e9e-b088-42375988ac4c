using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Core.Shared.Attributes.PITDeductionDocument
{
    /// <summary>
    /// Kiểm tra 2 ký tự đầu chứng từ khấu trừ thu nhập cá nhân
    /// </summary>
    public class ValidSerialNoAttribute : ValidationAttribute
    {
        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null)
                return new ValidationResult(ErrorMessage = $"Vui lòng nhập ký hiệu");

            var source = (string)value;

            if (!string.IsNullOrEmpty(source) && !IsMach(source))
                return new ValidationResult(ErrorMessage = $"Ký hiệu không đúng định dạng");

            return ValidationResult.Success;
        }
        private bool IsMach(string source)
        {
            if (Regex.IsMatch(source,
                @"^[A|B|C|D|E|G|H|K|L|M|N|P|Q|R|S|T|U|V|X|Y]{1}" +
                @"[A|B|C|D|E|G|H|K|L|M|N|P|Q|R|S|T|U|V|X|Y]{1}" + "/" +
                "[0-9]{1}[0-9]{1}[0-9]{1}[0-9]{1}" + "/" +
                "E{1}$",
                RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250)))
            {
                    return true;
            }

            string patternNd70 = "^CT/\\d{2}E$";

            if (Regex.IsMatch(source, patternNd70))
            {
                return true;
            }

            return false;
        }
    }
}
