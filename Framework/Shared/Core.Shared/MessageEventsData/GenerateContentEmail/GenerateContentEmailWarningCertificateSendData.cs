using Core.EventBus;

using System;

namespace Core.Shared.MessageEventsData.GenerateContentEmail
{
    [EventName("sendmail.generatecontentmailwarningcertificate")]
    public class GenerateContentEmailWarningCertificateSendData
    {
        /// <summary>
        /// Id của bảng ExpiredWarningHistory
        /// </summary>
        public Guid ExpiredWarningHistoryId { get; set; }

        /// <summary>
        /// Mẫu email
        /// </summary>
        public string Action { get; set; }

        public Guid TenantId { get; set; }

        /// <summary>
        /// số ngày hiệu lực còn lại
        /// </summary>
        public int RemainingDaysOfValidity { get; set; }


        /// <summary>
        /// ngày hết hạn
        /// </summary>
        public DateTime? EndDate { get; set; }


        /// <summary>
        /// Chủ thể
        /// </summary>
        public string SubjectName { get; set; }

        /// <summary>
        /// số serial của CTS
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// Danh sách email cảnh báo của khách hàng cấu hình
        /// </summary>
        public string CustomerEmails { get; set; }


        /// <summary>
        /// Danh sách mail Sale của VNPAY
        /// </summary>
        public string SaleEmails { get; set; }


        /// <summary>
        /// Loại cảnh báo
        /// </summary>
        public short WarningType { get; set; }

        /// <summary>
        /// Chu kỳ cảnh báo
        /// 0: theo ngày
        /// 1: theo tuần
        /// </summary>
        public short WarningPeriod { get; set; }
    }
}
