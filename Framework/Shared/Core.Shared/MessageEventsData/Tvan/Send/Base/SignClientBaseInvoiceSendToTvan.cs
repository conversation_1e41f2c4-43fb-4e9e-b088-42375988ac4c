using System;
using System.Collections.Generic;

namespace Core.Shared.MessageEventsData.Tvan.Send.Base
{
    public class SignClientBaseInvoiceSendToTvan
    {
        public Guid? TenantId { get; set; }
        public string SellerTaxCode { get; set; }
        public List<BaseInvoiceSendToTvan> Invoices { get; set; }
    }

    public class BaseInvoiceSendToTvan
    {
        public long InvoiceId { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }
        public string InvoiceNo { get; set; }
        public string Xml { get; set; }
    }
}
