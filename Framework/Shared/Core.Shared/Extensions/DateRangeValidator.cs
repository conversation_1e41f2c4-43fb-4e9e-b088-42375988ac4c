using System;

namespace Core.Shared.Extensions
{
    public static class DateRangeValidator
    {
        public static class DateRangeChecker
        {
            public static void ValidateDateRange(DateTime startDate, DateTime endDate, short monthsLimit)
            {
                if (endDate > startDate.AddMonths(monthsLimit))
                {
                    throw new UserFriendlyException(@$"Khoảng thời gian tìm kiếm không được vượt quá {monthsLimit} tháng. Vui lòng kiểm tra lại.");
                }
            }

            public static void ValidateDateRange(DateTime startDate, DateTime endDate, int maxDay)
            {
                var totalDay = (endDate - startDate).TotalDays;
                if(totalDay < 0)
                {
                    throw new UserFriendlyException($"Thời gian Từ ngày cần nhỏ hơn hoặc bằng thời gian đến ngày");
                }
                if ((endDate - startDate).TotalDays > maxDay)
                {
                    throw new UserFriendlyException($"Khoảng thời gian tìm kiếm không được vượt quá phạm vi {maxDay} ngày. Vui lòng kiểm tra lại.");
                }
            }
        }
    }
}
