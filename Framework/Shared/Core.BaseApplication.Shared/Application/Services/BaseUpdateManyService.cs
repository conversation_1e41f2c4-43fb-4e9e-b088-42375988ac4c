using Core.Application.Dtos;
using Core.BaseApplication.Shared.Models.Results;
using Core.DependencyInjection;
using Core.Shared.Factory;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace Core.BaseApplication.Shared.Application.Services
{
    public interface IBaseUpdateManyService<TKey, TEntity, TDto> : IScopedDependency
        where TEntity : TenantFullAuditedEntity<TKey>
        where TDto : IEntityDto<TKey>
    {
        Task<ResponseResult> UpdateAsync(List<TDto> inputs, List<TEntity> entities = null);
    }
    public class BaseUpdateManyService<TKey, TEntity, TDto> : IBaseUpdateManyService<TKey, TEntity, TDto>
        where TEntity : TenantFullAuditedEntity<TKey>
        where TDto : IEntityDto<TKey>
    {
        protected readonly IAppFactory _appFactory;
        public BaseUpdateManyService(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public async Task<ResponseResult> UpdateAsync(List<TDto> inputs, List<TEntity> entities = null)
        {
            try
            {
                if (entities == null)
                {
                    entities = _appFactory.ObjectMapper.Map<List<TDto>, List<TEntity>>(inputs);
                    entities.ForEach(x =>
                    {
                        if (_appFactory.CurrentTenant.Id != null)
                            x.TenantId = _appFactory.CurrentTenant.Id.Value;
                        x.LastModifierId = _appFactory.CurrentUser.Id;
                        x.LastModificationTime = DateTime.Now;
                    });
                }

                await _appFactory.Repository<TEntity, TKey>().UpdateManyAsync(entities);
                await _appFactory.CurrentUnitOfWork.SaveChangesAsync();

                inputs = _appFactory.ObjectMapper.Map<List<TEntity>, List<TDto>>(entities);
                return new ResponseResult
                {
                    IsSuccess = true,
                    Id = 0,
                    ErrorMessages = "",
                    Data = inputs
                };
            }
            catch (Exception ex)
            {
                Log.Error("UpdateAsync");
                Log.Error(ex.Message);
                Log.Error(ex.StackTrace);
                throw;
            }
        }
    }
}
