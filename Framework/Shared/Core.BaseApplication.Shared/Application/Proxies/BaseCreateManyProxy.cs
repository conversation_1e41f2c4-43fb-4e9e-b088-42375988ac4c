using Core.Application.Dtos;
using Core.BaseApplication.Shared.Application.Services;
using Core.BaseApplication.Shared.Models.Results;
using Core.Shared.Factory;
using System.Collections.Generic;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace Core.BaseApplication.Shared.Application.Proxies
{
    public interface IBaseCreateManyProxy<TKey, TEntity, TDto> : IBaseCreateManyService<TKey, TEntity, TDto>
        where TEntity : TenantFullAuditedEntity<TKey>
        where TDto : IEntityDto<TKey>
    {
    }

    public class BaseCreateManyProxy<TKey, TEntity, TDto> : IBaseCreateManyProxy<TKey, TEntity, TDto>
        where TEntity : TenantFullAuditedEntity<TKey>
        where TDto : IEntityDto<TKey>
    {
        protected readonly IAppFactory _appFactory;
        public BaseCreateManyProxy(IAppFactory appFactory)
        {
            _appFactory = appFactory;
        }

        public virtual async Task<ResponseResult> CreateAsync(List<TDto> inputs, List<TEntity> entities = null)
        {
            return await _appFactory.GetServiceDependency<IBaseCreateManyService<TKey, TEntity, TDto>>().CreateAsync(inputs, entities);
        }
    }
}
