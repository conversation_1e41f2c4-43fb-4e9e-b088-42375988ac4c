using Core.Shared.Constants;
using Core.Shared.Extensions;
using Core.Shared.Factory;
using Core.Shared.FileManager.Interfaces;
using Core.Tvan.Constants;
using Core.Tvan.Enums;
using Core.Tvan.MInvoice.Constants;
using Core.Tvan.MInvoice.Interfaces;
using Core.Tvan.MInvoice.Models;

using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

using VnisCore.Core.Oracle.Domain.Entities.Tvan;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.ReportInvoice;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;

namespace Core.Tvan.MInvoice.Services
{
    public class TvanMInvoiceService : ITvanMInvoiceService
    {
        private readonly ITvanMInvoiceInvoiceClient _tvanInvoiceClient;
        private readonly IAppFactory _appFactory;

        public TvanMInvoiceService(ITvanMInvoiceInvoiceClient tvanInvoiceClient,
            IAppFactory appFactory)
        {
            _appFactory = appFactory;
            _tvanInvoiceClient = tvanInvoiceClient;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice01HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_HASCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice01HasCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice01HasCodeXmlEntity, long>();
                var file = new TvanInvoice01HasCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                    Invoice01HeaderId = invoiceHeaderId
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice01HasCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice01HasCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._200.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._200.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;

            //lưu lại responseTvan
            //await SaveInvoice01HasCodeResponseTvanAsync(tenantId, invoiceHeaderId, sellerTaxcode, templateNo, serialNo, invoiceNo, responseTvan);
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice02HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_HASCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice02HasCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice02HasCodeXmlEntity, long>();
                var file = new TvanInvoice02HasCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice02HasCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice02HasCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._200.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._200.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;

            //var responseTvan = await _tvanInvoiceClient.SendAsync(PathUrl.SEND_INVOICE_HASCODE, sellerTaxcode, fullTvanXmlAfterAddElement.Xml);

            ////lưu lại responseTvan
            //await SaveInvoice02HasCodeResponseTvanAsync(tenantId, invoiceHeaderId, sellerTaxcode, templateNo, serialNo, invoiceNo, responseTvan);
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice03HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_HASCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice03HasCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice03HasCodeXmlEntity, long>();
                var file = new TvanInvoice03HasCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice03HasCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice03HasCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._200.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._200.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;

            //var responseTvan = await _tvanInvoiceClient.SendAsync(PathUrl.SEND_INVOICE_HASCODE, sellerTaxcode, fullTvanXmlAfterAddElement.Xml);

            ////lưu lại responseTvan
            //await SaveInvoice03HasCodeResponseTvanAsync(tenantId, invoiceHeaderId, sellerTaxcode, templateNo, serialNo, invoiceNo, responseTvan);
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice04HasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_HASCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice04HasCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice04HasCodeXmlEntity, long>();
                var file = new TvanInvoice04HasCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice04HasCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice04HasCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._200.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._200.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;

            //var responseTvan = await _tvanInvoiceClient.SendAsync(PathUrl.SEND_INVOICE_HASCODE, sellerTaxcode, fullTvanXmlAfterAddElement.Xml);

            ////lưu lại responseTvan
            //await SaveInvoice04HasCodeResponseTvanAsync(tenantId, invoiceHeaderId, sellerTaxcode, templateNo, serialNo, invoiceNo, responseTvan);
        }

        public async Task<TvanMInvoiceResponseModel> SendTicketHasCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_HASCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.TicketHasCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanTicketHasCodeXmlEntity, long>();
                var file = new TvanTicketHasCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoTicketHasCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoTicketHasCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._200.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._200.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;

            //var responseTvan = await _tvanInvoiceClient.SendAsync(PathUrl.SEND_INVOICE_HASCODE, sellerTaxcode, fullTvanXmlAfterAddElement.Xml);

            ////lưu lại responseTvan
            //await SaveTicketHasCodeResponseTvanAsync(tenantId, invoiceHeaderId, sellerTaxcode, templateNo, serialNo, invoiceNo, responseTvan);
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice01WithoutCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_WITHOUTCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice01WithoutCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice01WithoutCodeXmlEntity, long>();
                var file = new TvanInvoice01WithoutCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                    Invoice01HeaderId = invoiceHeaderId
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice01WithoutCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice01WithoutCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._203.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._203.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice02WithoutCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_WITHOUTCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice02WithoutCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice02WithoutCodeXmlEntity, long>();
                var file = new TvanInvoice02WithoutCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                    Invoice02HeaderId = invoiceHeaderId
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice02WithoutCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice02WithoutCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._203.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._203.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice03WithoutCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_WITHOUTCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice03WithoutCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice03WithoutCodeXmlEntity, long>();
                var file = new TvanInvoice03WithoutCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                    Invoice03HeaderId = invoiceHeaderId
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice03WithoutCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice03WithoutCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._203.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._203.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice04WithoutCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_WITHOUTCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice04WithoutCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice04WithoutCodeXmlEntity, long>();
                var file = new TvanInvoice04WithoutCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                    Invoice04HeaderId = invoiceHeaderId
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice04WithoutCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoInvoice04WithoutCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._203.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._203.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendTicketWithoutCodeAsync(Guid tenantId, long invoiceHeaderId, string sellerTaxcode, short templateNo, string serialNo, string invoiceNo, string xml)
        {
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxcode, PathUrl.SEND_INVOICE_WITHOUTCODE, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.TicketWithoutCodeTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanTicketWithoutCodeXmlEntity, long>();
                var file = new TvanTicketWithoutCodeXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"{sellerTaxcode}-{templateNo}-{serialNo}-{invoiceNo}-{invoiceHeaderId}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                    TicketHeaderId = invoiceHeaderId
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<TvanInfoTicketWithoutCodeEntity, long>();
                await repoTvanInfo.InsertAsync(new TvanInfoTicketWithoutCodeEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._203.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    InvoiceHeaderId = invoiceHeaderId,
                    Title = MLTDiep._203.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                }, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoiceErrorAsync(string sellerTaxCode, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_INVOICE_ERROR, xml);

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendRegistrationAsync(Guid tenantId, string sellerTaxCode, long idRegistration, string xml)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_REGISTRATION, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var physicalFileName = $"RegistrationSendTvanXml_{idRegistration}_{MLTDiep._100.GetHashCode()}_{DateTime.Now.Ticks}.xml";
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.TvanRegistrationXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{physicalFileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanRegistrationXmlEntity, long>();
                var file = new TvanRegistrationXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = $"RegistrationSendTvanXml_{idRegistration}_{MLTDiep._100.GetHashCode()}.xml",
                    PhysicalFileName = physicalFileName,
                    Length = bytes.Length,
                    CreationTime = creationTime
                };

                await repoTvanXml.InsertAsync(file, true);

                var repoTvanInfo = _appFactory.Repository<RegistrationTvanInfoEntity, long>();
                await repoTvanInfo.InsertAsync(new RegistrationTvanInfoEntity
                {
                    TenantId = tenantId,
                    FileId = file.Id,
                    MessageCode = responseTvan.Data?.MaThongdiep,
                    MessageCodeReference = null,
                    MessageTypeCode = MLTDiep._100.GetHashCode().ToString(),
                    Reason = responseTvan.Message,
                    RegistrationHeaderId = idRegistration,
                    Title = MLTDiep._100.ToDisplayName(),
                    TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                },true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice01ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<long> headerIds)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_INVOICE_ERROR, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var fileName = $"HoaDonCoSaiSot-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice01ErrorTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice01ErrorXmlEntity, long>();
                var file = new TvanInvoice01ErrorXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var tvanInfoInvoice01Errors = new List<TvanInfoInvoice01ErrorEntity>();

                foreach (var id in headerIds)
                {
                    var tvanInfoInvoice01Error = new TvanInfoInvoice01ErrorEntity
                    {
                        TenantId = tenantId,
                        FileId = file.Id,
                        MessageCode = responseTvan.Data?.MaThongdiep,
                        MessageCodeReference = null,
                        MessageTypeCode = MLTDiep._300.GetHashCode().ToString(),
                        Reason = responseTvan.Message,
                        InvoiceHeaderId = id,
                        Title = MLTDiep._300.ToDisplayName(),
                        TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                    };

                    tvanInfoInvoice01Errors.Add(tvanInfoInvoice01Error);
                }

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice01ErrorEntity, long>();
                await repoTvanInfo.InsertManyAsync(tvanInfoInvoice01Errors, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice02ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<long> headerIds)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_INVOICE_ERROR, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var fileName = $"HoaDonCoSaiSot-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice02ErrorTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice02ErrorXmlEntity, long>();
                var file = new TvanInvoice02ErrorXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var tvanInfoInvoice02Errors = new List<TvanInfoInvoice02ErrorEntity>();

                foreach (var id in headerIds)
                {
                    var tvanInfoInvoice02Error = new TvanInfoInvoice02ErrorEntity
                    {
                        TenantId = tenantId,
                        FileId = file.Id,
                        MessageCode = responseTvan.Data?.MaThongdiep,
                        MessageCodeReference = null,
                        MessageTypeCode = MLTDiep._300.GetHashCode().ToString(),
                        Reason = responseTvan.Message,
                        InvoiceHeaderId = id,
                        Title = MLTDiep._300.ToDisplayName(),
                        TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                    };

                    tvanInfoInvoice02Errors.Add(tvanInfoInvoice02Error);
                }

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice02ErrorEntity, long>();
                await repoTvanInfo.InsertManyAsync(tvanInfoInvoice02Errors, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice03ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<long> headerIds)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_INVOICE_ERROR, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var fileName = $"HoaDonCoSaiSot-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice03ErrorTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice03ErrorXmlEntity, long>();
                var file = new TvanInvoice03ErrorXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var tvanInfoInvoice03Errors = new List<TvanInfoInvoice03ErrorEntity>();

                foreach (var id in headerIds)
                {
                    var tvanInfoInvoice03Error = new TvanInfoInvoice03ErrorEntity
                    {
                        TenantId = tenantId,
                        FileId = file.Id,
                        MessageCode = responseTvan.Data?.MaThongdiep,
                        MessageCodeReference = null,
                        MessageTypeCode = MLTDiep._300.GetHashCode().ToString(),
                        Reason = responseTvan.Message,
                        InvoiceHeaderId = id,
                        Title = MLTDiep._300.ToDisplayName(),
                        TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                    };

                    tvanInfoInvoice03Errors.Add(tvanInfoInvoice03Error);
                }

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice03ErrorEntity, long>();
                await repoTvanInfo.InsertManyAsync(tvanInfoInvoice03Errors, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendInvoice04ErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<long> headerIds)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_INVOICE_ERROR, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var fileName = $"HoaDonCoSaiSot-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.Invoice04ErrorTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanInvoice04ErrorXmlEntity, long>();
                var file = new TvanInvoice04ErrorXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var tvanInfoInvoice04Errors = new List<TvanInfoInvoice04ErrorEntity>();

                foreach (var id in headerIds)
                {
                    var tvanInfoInvoice04Error = new TvanInfoInvoice04ErrorEntity
                    {
                        TenantId = tenantId,
                        FileId = file.Id,
                        MessageCode = responseTvan.Data?.MaThongdiep,
                        MessageCodeReference = null,
                        MessageTypeCode = MLTDiep._300.GetHashCode().ToString(),
                        Reason = responseTvan.Message,
                        InvoiceHeaderId = id,
                        Title = MLTDiep._300.ToDisplayName(),
                        TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                    };

                    tvanInfoInvoice04Errors.Add(tvanInfoInvoice04Error);
                }

                var repoTvanInfo = _appFactory.Repository<TvanInfoInvoice04ErrorEntity, long>();
                await repoTvanInfo.InsertManyAsync(tvanInfoInvoice04Errors, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendTicketErrorAsync(string sellerTaxCode, string xml, Guid tenantId, List<long> headerIds)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_INVOICE_ERROR, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var fileName = $"HoaDonCoSaiSot-04TBSS-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.TicketErrorTvanXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanTicketErrorXmlEntity, long>();
                var file = new TvanTicketErrorXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                await repoTvanXml.InsertAsync(file, true);

                var tvanInfoTicketErrors = new List<TvanInfoTicketErrorEntity>();

                foreach (var id in headerIds)
                {
                    var tvanInfoInvoiceTicketError = new TvanInfoTicketErrorEntity
                    {
                        TenantId = tenantId,
                        FileId = file.Id,
                        MessageCode = responseTvan.Data?.MaThongdiep,
                        MessageCodeReference = null,
                        MessageTypeCode = MLTDiep._300.GetHashCode().ToString(),
                        Reason = responseTvan.Message,
                        InvoiceHeaderId = id,
                        Title = MLTDiep._300.ToDisplayName(),
                        TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                    };

                    tvanInfoTicketErrors.Add(tvanInfoInvoiceTicketError);
                }

                var repoTvanInfo = _appFactory.Repository<TvanInfoTicketErrorEntity, long>();
                await repoTvanInfo.InsertManyAsync(tvanInfoTicketErrors, true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendReport01Async(string xml, long taxReportId, string sellerTaxCode, Guid tenantId, int fromNumber, int toNumber, long templateId, int AdditionalTimes, bool IsFirstTimeInPeriod, List<long> invoiceHeaderIds)
        {
            //gửi lên tvan rồi lấy mã thông điệp
            //gui len tvan
            var responseTvan = await _tvanInvoiceClient.SendAsync(sellerTaxCode, PathUrl.SEND_TAX_REPORT, xml);

            if (responseTvan == null)
                return null;

            try
            {
                //upload lên xml
                var bytes = Encoding.UTF8.GetBytes(xml);
                var fileService = _appFactory.GetServiceDependency<IFileService>();
                var fileName = $"Baocaothue-01TH-HDDT-{DateTime.UtcNow.Ticks}.xml".Replace("/", "-");
                var creationTime = DateTime.Now;
                var pathFileMinio = $"{MediaFileType.TvanTaxReportXml}/{tenantId}/{creationTime.Year}/{creationTime.Month:00}/{creationTime.Day:00}/{creationTime.Hour:00}/{fileName}";
                await fileService.UploadAsync(pathFileMinio, bytes);

                //insert dữ liệu bảng tvaninfo + xml
                var repoTvanXml = _appFactory.Repository<TvanReportXmlEntity, long>();
                var file = new TvanReportXmlEntity
                {
                    TenantId = tenantId,
                    ContentType = ContentType.Xml,
                    FileName = fileName,
                    PhysicalFileName = fileName,
                    Length = bytes.Length,
                    CreationTime = creationTime,
                };

                if(responseTvan.Code == "00")
                {
                    file.StatusTvan = (short)TvanStatus.Sended.GetHashCode();
                }
                else
                {
                    file.StatusTvan = (short)TvanStatus.SendError.GetHashCode();
                }

                await repoTvanXml.InsertAsync(file, true);

                var taxReportTvanMonitorRepo = _appFactory.Repository<TaxReportTvanMonitorEntity, long>();
                var taxReportTvanMonitor = new TaxReportTvanMonitorEntity
                {
                    FileId = file.Id,
                    TaxReportHeaderId = taxReportId,
                    FromNumber = fromNumber,
                    ToNumber = toNumber,
                    InvoiceTemplateId = templateId,
                    TenantId = tenantId,
                    Type = (short)TaxReportTvanMonitorType.TaxReport01
                };

                await taxReportTvanMonitorRepo.InsertAsync(taxReportTvanMonitor, true);

                var taxReport01TvanInfos = new List<TaxReport01TvanInfoEntity>();
                foreach(var id in invoiceHeaderIds)
                {
                    taxReport01TvanInfos.Add(new TaxReport01TvanInfoEntity
                    {
                        TenantId = tenantId,
                        FileId = file.Id,
                        MessageCode = responseTvan.Data?.MaThongdiep,
                        MessageCodeReference = null,
                        MessageTypeCode = MLTDiep._400.GetHashCode().ToString(),
                        Reason = responseTvan.Message,
                        InvoiceHeaderId = id,
                        TvanMonitorId = taxReportTvanMonitor.Id,
                        Title = MLTDiep._400.ToDisplayName(),
                        TransmissionPartner = (short)TransmissionPartnerEnum.Minvoice
                    });
                } 

                var repoTvanInfo = _appFactory.Repository<TaxReport01TvanInfoEntity, long>();
                await repoTvanInfo.InsertManyAsync(taxReport01TvanInfos,true);
            }
            catch (Exception ex)
            {
                throw new UserFriendlyException(ex.Message);
            }

            return responseTvan;
        }

        public async Task<TvanMInvoiceResponseModel> SendReport03Async(string xml, string sellerTaxCode, Guid tenantId)
        {
            //gui len tct
            //add xml full theo tvan
            return new TvanMInvoiceResponseModel();
            //await _tvanInvoiceClient.SendAsync(xml);
        }


    }
}
