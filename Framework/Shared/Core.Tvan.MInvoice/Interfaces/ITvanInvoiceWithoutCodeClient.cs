using Core.Tvan.MInvoice.Models;

using System.Threading.Tasks;

namespace Core.Tvan.MInvoice.Interfaces
{
    public interface ITvanInvoiceWithoutCodeClient
    {
        /// <summary>
        /// lấy token
        /// </summary>
        /// <param name="madvcs"></param>
        /// <param name="userName"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        Task<string> GetTokenAsync(string madvcs, string userName, string password);

        /// <summary>
        /// gửi xml lên tvan
        /// </summary>
        /// <param name="uri"></param>
        /// <param name="sellerTaxcode"></param>
        /// <param name="xml"></param>
        /// <returns></returns>
        Task<TvanMInvoiceResponseModel> SendAsync(string uri, string xml, string taxCode);
    }
}
