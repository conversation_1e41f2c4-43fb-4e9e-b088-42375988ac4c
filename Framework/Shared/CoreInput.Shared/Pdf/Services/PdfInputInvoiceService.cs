using Core.Shared.Constants;
using Core.Shared.FileManager.Interfaces;
using Core.Shared.Pdf.Interfaces;
using Core.Shared.Pdf.Models;
using Core.Shared.Pdf.Repositories;
using HtmlToPdf;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using PdfSharpCore.Pdf.IO;
using RazorLight;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;

namespace CoreInput.Shared.Pdf.Services
{
    public class PdfInputInvoiceService : IPdfService
    {
        private readonly IFileService _fileService;
        private readonly IMediaTemplateDesignService _mediaFileService;
        private readonly string _appId;
        private readonly string _urlAppDesign;
        private readonly FileDownloadOption _option;
        private readonly ILogger<PdfInputInvoiceService> _logger;
        private readonly ISemaphore _semaphore;
        private readonly IInvoiceTemplateRepository _invoiceTemplateRepository;

        public PdfInputInvoiceService(
             IInvoiceTemplateRepository invoiceTemplateRepository,
             IMediaTemplateDesignService mediaFileService,
             IFileService fileService,
             IOptions<FileDownloadOption> options,
             IConfiguration configuration,
             ILogger<PdfInputInvoiceService> logger,
             ISemaphore semaphore)
        {
            _invoiceTemplateRepository = invoiceTemplateRepository;
            _fileService = fileService;
            _mediaFileService = mediaFileService;
            _option = options.Value;
            _logger = logger;
            _semaphore = semaphore;
            _appId = configuration.GetSection("ApplicationInfo:AppId").Get<string>();
            _urlAppDesign = configuration.GetSection("DesignTemplate:Url").Get<string>();
        }

        public async Task<byte[]> GenerateAsync<TInvoiceDocument>(List<PdfInvoiceModel> command, List<TInvoiceDocument> invoiceDocuments) where TInvoiceDocument : BaseInvoiceDocument
        {
            //Lấy mẫu hóa đơn
            var idTemplates = command.Select(x => x.TemplateId).Distinct().ToList();
            var templates = await _invoiceTemplateRepository.QueryById(idTemplates);

            _logger.LogDebug($"Got templates: {templates.Count()}");

            var FileIdTemplates = templates.Values.Where(x => x.FileId.HasValue).Select(x => x.FileId.Value).ToList();
            var fileTemplates = (await _mediaFileService.GetByIdsAsync(FileIdTemplates))
                                .ToDictionary(x => x.Id, x => x);

            var bytes = new List<byte[]>();

            //in theo từng mẫu 
            var invoiceByTemplates = command.GroupBy(x => x.TemplateId);

            foreach (var group in invoiceByTemplates)
            {
                if (!templates.ContainsKey(group.Key))
                {
                    _logger.LogDebug($"Do not have template: {group.Key} ");
                    continue;
                }

                var template = templates[group.Key];

                // phân ra 3 loại 
                // nếu field FileIdOfPattern == null => chưa có file thiết kế
                // nếu field FileIdOfPattern == Guid.Empty => call API tool Design
                // nếu field FileIdOfPattern != null && != Guid.Empty => lấy file trong DB Core để in như luồng hiện tại
                if (template.FileId == null)
                    throw new Exception("Không có file in và mẫu thiết kế");

                var viewPath = "";
                if (template.FileId != 0)
                {
                    if (!fileTemplates.ContainsKey(template.FileId.Value))
                    {
                        _logger.LogDebug($"Do not have file template {template.Id} ");
                        continue;
                    }

                    viewPath = await GetViewPathAsync(fileTemplates[template.FileId.Value]);
                    _logger.LogDebug($"Get viewPath {template.Id}: {viewPath}");
                }

                foreach (var item in group)
                {
                    if (template.FileId != 0)
                    {
                        _logger.LogDebug($"Start generate pdf {item.Id} by template file");
                        bytes.Add(await GeneratePdfCoreAsync(item, template, viewPath));
                        _logger.LogDebug($"End generate pdf {item.Id} by template file");
                    }
                    else
                    {
                        _logger.LogDebug($"Start generate pdf {item.Id} by design app");
                        bytes.Add(await GeneratePdfDesign(item, template));
                        _logger.LogDebug($"Start generate pdf {item.Id} by design app");
                    }
                }

                //xóa file mẫu nếu có
                if (File.Exists(viewPath))
                {
                    _logger.LogDebug($"Delete viewPath {template.Id}: {viewPath}");
                    File.Delete(viewPath);
                }
            }

            //Nếu chỉ có 1 file thì trả về luôn, không cần merge => giảm performance
            if (bytes.Count == 1)
                return bytes[0];

            return MergeFiles(bytes);
        }

        private async Task<byte[]> GeneratePdfDesign(PdfInvoiceModel pdfInvoiceModel, InvoiceTemplateEntity template)
        {
            //gọi api in
            using var client = new HttpClient
            {
                BaseAddress = new Uri(_urlAppDesign),
            };
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var exportDesignModel = new ExportDesignInvoiceModel
            {
                Data = pdfInvoiceModel,
                TenantId = template.TenantId,
                AppId = _appId,
                TemplateId = template.Id
            };

            string jsonToken = JsonConvert.SerializeObject(exportDesignModel);
            var httpContent = new StringContent(jsonToken, Encoding.UTF8, "application/json");

            _logger.LogDebug($"Start request to design app");
            var response = client.PostAsync("invoice/export", httpContent).Result;
            if (!response.IsSuccessStatusCode)
                throw new Exception("Có lỗi trong quá trình in" + $"\n StatusCode: {response.IsSuccessStatusCode}. \n Message: {response.RequestMessage}");

            var responseBody = await response.Content.ReadAsStringAsync();
            _logger.LogDebug($"Got response from design app: {responseBody}");
            var data = JsonConvert.DeserializeObject<ResponsePdfModel>(responseBody);

            var bytes = data.Bytes;
            return bytes;
        }

        public async Task<byte[]> GeneratePdfCoreAsync(PdfInvoiceModel pdf, InvoiceTemplateEntity template, string viewPath)
        {
            viewPath = "D:/vnis/Document/cshtml/2021/8/28/637658718239426370_324324234-06923148-432d-41fd-a220-c04b75a832ee.cshtml";
            var pathFolder = Path.GetDirectoryName(viewPath);
            var engine = new RazorLightEngineBuilder()
              .UseFileSystemProject(pathFolder)
              .UseMemoryCachingProvider()
              .Build();

            string htmlContent = await engine.CompileRenderAsync<PdfInvoiceModel>(viewPath, pdf);

            var htmlToPdf = new HtmlToPdfConverter
            {
                PageWidth = float.Parse(template.Width.ToString(CultureInfo.InvariantCulture)),
                PageHeight = float.Parse(template.Height.ToString(CultureInfo.InvariantCulture)),
                Margins = new PageMargins
                {
                    Left = float.Parse(template.MarginLeft.ToString(CultureInfo.InvariantCulture)),
                    Top = float.Parse(template.MarginTop.ToString(CultureInfo.InvariantCulture)),
                    Right = float.Parse(template.MarginRight.ToString(CultureInfo.InvariantCulture)),
                    Bottom = float.Parse(template.MarginBottom.ToString(CultureInfo.InvariantCulture)),
                },
                PdfToolPath = _option.PdfToolPath,
                WkHtmlToPdfExeName = _option.WkHtmlToPdfExeName
            };

            try
            {
                _semaphore.WaitOne();

                var bytePdf = htmlToPdf.GeneratePdf(htmlContent);
                return bytePdf;

            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                _semaphore.Release();
            }
        }


        /// <summary>
        /// lưu file mẫu hóa đơn ra 1 file tạm và trả về đường dẫn file đó
        /// </summary>
        /// <param name="fileTemplate"></param>
        /// <returns></returns>
        public async Task<string> GetViewPathAsync(BaseMediaFile fileTemplate)
        {
            var pathFileMinio = $"{MediaFileType.TemplateDesign}/{fileTemplate.TenantId}/{fileTemplate.CreationTime.Year}/{fileTemplate.CreationTime.Month:00}/{fileTemplate.CreationTime.Day:00}/{fileTemplate.CreationTime.Hour:00}/{fileTemplate.PhysicalFileName}";

            try
            {
                //lấy file từ trên minio về và lưu vào file tạm
                var bytes = await _fileService.DownloadAsync(pathFileMinio);

                //lưu vào 1 đường dẫn file tạm
                var filePathLocal = Path.Combine(_mediaFileService.GetFilePath(_option.FolderTemplate, fileTemplate.CreationTime), $"{DateTime.Now.Ticks}_{fileTemplate.PhysicalFileName}");
                await File.WriteAllBytesAsync(filePathLocal, bytes);

                return filePathLocal;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        private static byte[] MergeFiles(List<byte[]> bytes)
        {
            using (var ms = new MemoryStream())
            {
                using (var document = new PdfSharpCore.Pdf.PdfDocument())
                {
                    foreach (var data in bytes)
                    {
                        using (var reader = PdfReader.Open(new MemoryStream(data), PdfDocumentOpenMode.Import))
                        {
                            foreach (var page in reader.Pages)
                            {
                                document.AddPage(page);
                            }
                        }
                    }
                    document.Save(ms, false);
                }
                return ms.ToArray();
            }
        }

        public Task<byte[]> GeneratePITAsync(List<PdfPITModel> pdfPITModels)
        {
            throw new NotImplementedException();
        }

        public Task<byte[]> GeneratePITLetterAsync(List<PdfPITLetterModel> pdfPITModels)
        {
            throw new NotImplementedException();
        }

        public async Task<string> GetViewPathAsync(BaseMediaFile fileTemplate, IFormFile FormFile)
        {
            try
            {
                using (var stream = new MemoryStream())
                {
                    FormFile.CopyTo(stream);
                    var bytes = stream.ToArray();

                    //lưu vào 1 đường dẫn file tạm
                    var filePathLocal = Path.Combine(_mediaFileService.GetFilePath(_option.FolderTemplate, fileTemplate.CreationTime), $"{DateTime.Now.Ticks}_{fileTemplate.PhysicalFileName}");
                    await File.WriteAllBytesAsync(filePathLocal, bytes);
                    return filePathLocal;
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("FileService"))
                    throw new Exception("Chưa có cấu hình Minio trong appsetting");
                else throw ex;
            }
        }
    }
}
