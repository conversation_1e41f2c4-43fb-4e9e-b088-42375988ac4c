using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;
using CoreInput.Shared.Abstractions;
using CoreInput.Shared.Interfaces;
using CoreInput.Shared.Models;

namespace CoreInput.Shared.Services.HiloServices
{
    public class Hilo02Service : BaseInvoiceInputService, IConvertToModelService
    {
        public override ResponseModel<object> ConvertToModel<T>(object resultData)
        {
            throw new NotImplementedException();
        }

        public Task<ResponseModel<object>> ConvertXml(IFormFile file)
        {
            throw new NotImplementedException();
        }
    }
}
