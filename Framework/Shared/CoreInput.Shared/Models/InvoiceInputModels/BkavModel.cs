using System.Collections.Generic;
using System.Xml.Serialization;

namespace CoreInput.Shared.Models.InvoiceInputModels
{
    [XmlRoot(ElementName = "invoice", Namespace = "http://laphoadon.gdt.gov.vn/2014/09/invoicexml/v1", DataType = "string", IsNullable = true)]
	public class BkavModel
    {
		[XmlElement(ElementName = "invoiceData")]
		public BkavInvoiceData InvoiceData { get; set; }
		[XmlElement(ElementName = "controlData")]
		public BkavControlData ControlData { get; set; }
		[XmlAttribute(AttributeName = "ds")]
		public string Ds { get; set; }
		[XmlAttribute(AttributeName = "inv")]
		public string Inv { get; set; }
	}

	[XmlRoot(ElementName = "payment")]
	public class BkavPayment
	{
		[XmlElement(ElementName = "paymentMethodName")]
		public string PaymentMethodName { get; set; }
		[XmlElement(ElementName = "paymentAmount")]
		public string PaymentAmount { get; set; }
	}

	[XmlRoot(ElementName = "payments")]
	public class BkavPayments
	{
		[XmlElement(ElementName = "payment")]
		public BkavPayment Payment { get; set; }
	}

	[XmlRoot(ElementName = "item")]
	public class BkavItem
	{
		[XmlElement(ElementName = "lineNumber")]
		public string LineNumber { get; set; }
		[XmlElement(ElementName = "itemCode")]
		public string ItemCode { get; set; }
		[XmlElement(ElementName = "itemName")]
		public string ItemName { get; set; }
		[XmlElement(ElementName = "unitCode")]
		public string UnitCode { get; set; }
		[XmlElement(ElementName = "unitName")]
		public string UnitName { get; set; }
		[XmlElement(ElementName = "unitPrice")]
		public string UnitPrice { get; set; }
		[XmlElement(ElementName = "quantity")]
		public string Quantity { get; set; }
		[XmlElement(ElementName = "itemTotalAmountWithoutVat")]
		public string ItemTotalAmountWithoutVat { get; set; }
		[XmlElement(ElementName = "vatPercentage")]
		public string VatPercentage { get; set; }
		[XmlElement(ElementName = "vatAmount")]
		public string VatAmount { get; set; }
		[XmlElement(ElementName = "promotion")]
		public string Promotion { get; set; }
	}

	[XmlRoot(ElementName = "items")]
	public class BkavItems
	{
		[XmlElement(ElementName = "item")]
		public List<BkavItem> Item { get; set; }
	}

	[XmlRoot(ElementName = "invoiceTaxBreakdown")]
	public class BkavInvoiceTaxBreakdown
	{
		[XmlElement(ElementName = "vatPercentage")]
		public string VatPercentage { get; set; }
		[XmlElement(ElementName = "vatTaxableAmount")]
		public string VatTaxableAmount { get; set; }
		[XmlElement(ElementName = "vatTaxAmount")]
		public string VatTaxAmount { get; set; }
	}

	[XmlRoot(ElementName = "invoiceTaxBreakdowns")]
	public class BkavInvoiceTaxBreakdowns
	{
		[XmlElement(ElementName = "invoiceTaxBreakdown")]
		public List<BkavInvoiceTaxBreakdown> InvoiceTaxBreakdown { get; set; }
	}

	[XmlRoot(ElementName = "invoiceData")]
	public class BkavInvoiceData
	{
		[XmlElement(ElementName = "sellerAppRecordId")]
		public string SellerAppRecordId { get; set; }
		[XmlElement(ElementName = "invoiceAppRecordId")]
		public string InvoiceAppRecordId { get; set; }
		[XmlElement(ElementName = "invoiceType")]
		public string InvoiceType { get; set; }
		[XmlElement(ElementName = "templateCode")]
		public string TemplateCode { get; set; }
		[XmlElement(ElementName = "invoiceSeries")]
		public string InvoiceSeries { get; set; }
		[XmlElement(ElementName = "invoiceNumber")]
		public string InvoiceNumber { get; set; }
		[XmlElement(ElementName = "invoiceName")]
		public string InvoiceName { get; set; }
		[XmlElement(ElementName = "invoiceIssuedDate")]
		public string InvoiceIssuedDate { get; set; }
		[XmlElement(ElementName = "currencyCode")]
		public string CurrencyCode { get; set; }
		[XmlElement(ElementName = "exchangeRate")]
		public string ExchangeRate { get; set; }
		[XmlElement(ElementName = "invoiceNote")]
		public string InvoiceNote { get; set; }
		[XmlElement(ElementName = "adjustmentType")]
		public string AdjustmentType { get; set; }
		[XmlElement(ElementName = "sellerLegalName")]
		public string SellerLegalName { get; set; }
		[XmlElement(ElementName = "sellerTaxCode")]
		public string SellerTaxCode { get; set; }
		[XmlElement(ElementName = "sellerAddressLine")]
		public string SellerAddressLine { get; set; }
		[XmlElement(ElementName = "sellerPhoneNumber")]
		public string SellerPhoneNumber { get; set; }
		[XmlElement(ElementName = "sellerEmail")]
		public string SellerEmail { get; set; }
		[XmlElement(ElementName = "sellerBankName")]
		public string SellerBankName { get; set; }
		[XmlElement(ElementName = "sellerBankAccount")]
		public string SellerBankAccount { get; set; }
		[XmlElement(ElementName = "sellerContactPersonName")]
		public string SellerContactPersonName { get; set; }
		[XmlElement(ElementName = "buyerDisplayName")]
		public string BuyerDisplayName { get; set; }
		[XmlElement(ElementName = "buyerLegalName")]
		public string BuyerLegalName { get; set; }
		[XmlElement(ElementName = "buyerTaxCode")]
		public string BuyerTaxCode { get; set; }
		[XmlElement(ElementName = "buyerAddressLine")]
		public string BuyerAddressLine { get; set; }
		[XmlElement(ElementName = "buyerPhoneNumber")]
		public string BuyerPhoneNumber { get; set; }
		[XmlElement(ElementName = "buyerEmail")]
		public string BuyerEmail { get; set; }
		[XmlElement(ElementName = "buyerBankAccount")]
		public string BuyerBankAccount { get; set; }
		[XmlElement(ElementName = "payments")]
		public BkavPayments Payments { get; set; }
		[XmlElement(ElementName = "delivery")]
		public string Delivery { get; set; }
		[XmlElement(ElementName = "items")]
		public BkavItems Items { get; set; }
		[XmlElement(ElementName = "invoiceTaxBreakdowns")]
		public BkavInvoiceTaxBreakdowns InvoiceTaxBreakdowns { get; set; }
		[XmlElement(ElementName = "sumOfTotalLineAmountWithoutVAT")]
		public string SumOfTotalLineAmountWithoutVAT { get; set; }
		[XmlElement(ElementName = "totalAmountWithoutVAT")]
		public string TotalAmountWithoutVAT { get; set; }
		[XmlElement(ElementName = "totalVATAmount")]
		public string TotalVATAmount { get; set; }
		[XmlElement(ElementName = "totalAmountWithVAT")]
		public string TotalAmountWithVAT { get; set; }
		[XmlElement(ElementName = "totalAmountWithVATFrn")]
		public string TotalAmountWithVATFrn { get; set; }
		[XmlElement(ElementName = "totalAmountWithVATInWords")]
		public string TotalAmountWithVATInWords { get; set; }
		[XmlElement(ElementName = "discountAmount")]
		public string DiscountAmount { get; set; }
		[XmlElement(ElementName = "isDiscountAmtPos")]
		public string IsDiscountAmtPos { get; set; }
		[XmlElement(ElementName = "userDefines")]
		public string UserDefines { get; set; }
		[XmlAttribute(AttributeName = "id")]
		public string Id { get; set; }
	}

	[XmlRoot(ElementName = "controlData")]
	public class BkavControlData
	{
		[XmlElement(ElementName = "systemCode")]
		public string SystemCode { get; set; }
	}
}
