namespace CoreInput.Shared.Models
{
    public class ResponseModel<T>
    {
        public T Data { get; set; }
        public string InvoiceType { get; set; }
        public bool IsSuccess { get; set; }

        public ResponseModel(T data, bool isSuccess)
        {
            Data = data;
            IsSuccess = isSuccess;
        }

        public ResponseModel(T data, string invoiceType, bool isSuccess)
        {
            Data = data;
            InvoiceType = invoiceType;
            IsSuccess = isSuccess;
        }
    }
}
