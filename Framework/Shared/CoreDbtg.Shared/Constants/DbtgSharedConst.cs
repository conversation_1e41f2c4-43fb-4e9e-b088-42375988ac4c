using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CoreDbtg.Shared.Constants
{
    public class DbtgSharedConst
    {
        public const string ApiUrlBase = "api/dbtg/invoice/InvoiceDbtgApi";

        public class ApiUrlDBTG
        {
            public const string CreateG1 = ApiUrlBase + "/g1/create";
            public const string CreateBatchG1 = ApiUrlBase + "/g1/create-batch";

            public const string CreateG2 = ApiUrlBase + "/g2/create";
            public const string CreateBatchG2 = ApiUrlBase + "/g2/create-batch";

            public const string CreateG3 = ApiUrlBase + "/g3/create";
            public const string CreateBatchG3 = ApiUrlBase + "/g3/create-batch";

            public const string CreateG4 = ApiUrlBase + "/g4/create";
            public const string CreateBatchG4 = ApiUrlBase + "/g4/create-batch";
        }

        public class BussinessType
        {
            public const int CreateInvoice = 1;
            public const int CreateBatchInvoice = 2;
            public const int Delete = 3;
        }

        public class SEQ_Name
        {
            public static string SEQ_Invoice01Header = "SEQ_Invoice01Header";
            public static string SEQ_Invoice01HeaderExtra = "SEQ_Invoice01HeaderExtra";
            public static string SEQ_Invoice01Detail = "SEQ_Invoice01Detail";
            public static string SEQ_Invoice01DetailExtra = "SEQ_Invoice01DetailExtra";
            public static string SEQ_Invoice01TaxBreakdown = "SEQ_Invoice01TaxBreakdown";
        }

        public class OracleFucntionName
        {
            public static string FUNC_GetNextSequenceValue = "getNextSequenceValue";
        }

        public class DefaultData
        {
            public const int InvoiceSize = 1000;
        }

        public class InvoiceStatusDbtg
        {
            // Tạo hđ gốc
            public const short Create = 1;

            // Hủy hđ
            public const short Cancel = 2; // Giai đoạn 2 làm
        }

        public class CreatorStatus
        {
            // VCB Đánh dấu hd chưa đầy đủ, chưa sẵng sàng để tạo lên core
            public const short IsNotReady = -3;

            // Validate hóa đơn lỗi
            public const short ValidateError = -2;

            // Lỗi
            public const short Error = -1;

            // Chưa xử lý
            public const short Untreated = 0;

            // Validate hóa đơn thành công
            public const short ValidateSuccess = 1;

            // Đang chờ tạo hóa đơn (Đã gửi dữ liệu tạo hóa đơn)
            public const short Waitting = 2;

            // Đã tạo hóa đơn (có số)
            public const short Success = 3;

            // Đang đợi Adapter lấy dữ liệu hóa đơn Dbtg
            public const short WaittingToGetData = 4;
        }

        public class IntegratedGroup
        {
            // Nhóm G1
            public const int G1 = 1;
            public const string G1TableName = "G1InvoiceHeader";

            // Nhóm G2
            public const int G2 = 2;
            public const string G2TableName = "G2InvoiceHeader";

            // Nhóm G3
            public const int G3 = 3;
            public const string G3TableName = "G3InvoiceHeader";

            // Nhóm G4
            public const int G4 = 4;
            public const string G4TableName = "G4InvoiceHeader";

            // Nhóm G5
            public const int G5 = 5;
            public const string G5TableName = "G5InvoiceHeader";
        }

        public class HeaderExtra
        {
            public const string AppName = "AppName";
            public const string OperationName = "OperationName";
            public const string TellSeq = "TellSeq";
            public const string MerNo = "MerNo";
            public const string BidNo = "BidNo";
            public const string TransactionAmount = "TransactionAmount";
        }

        public class DetailExtra
        {
            public const string GlNo = "GlNo";
            public const string RefNo = "RefNo";
            public const string Note2 = "Note2";

            // sử dụng cho hóa đơn gộp => lấy từ header đưa vào vào ExtraProps của Details
            public const string ErpId = "ErpId";
            public const string CreatorErp = "CreatorErp";
            public const string Note = "Note";
            public const string ExchangeRate = "ExchangeRate";
            public const string BuyerCode = "BuyerCode";
            public const string BuyerFullName = "BuyerFullName";
            public const string BuyerLegalName = "BuyerLegalName";
            public const string BuyerTaxCode = "BuyerTaxCode";
            public const string BuyerAddressLine = "BuyerAddressLine";
            public const string BuyerPhoneNumber = "BuyerPhoneNumber";
            public const string BuyerEmail = "BuyerEmail";
            public const string BuyerBankName = "BuyerBankName";
            public const string BuyerBankAccount = "BuyerBankAccount";
        }

        public class G4MergeCycle
        {
            public const int Month = 3;
            public const int Week = 2;
            public const int Day = 1;
        }

        public class G4SettingCode
        {
            public const string CIF = "CIF";
            public const string BID = "BID";
            public const string STK = "STK";
            public const string MID = "MID";
        }

        public class InvoiceProperty
        {
            public const string ErpIdFormat = "{0}_{1}";
            public const string ProductCode = "DV";
            public const int ProductType1 = 1;
            public const string ProductName1 = "Thu phí dịch vụ cho vay";
            public const int ProductType2 = 2;
            public const string ProductName2 = "Phí dịch vụ ngân hàng";
            public const string UnitName = "Lần";
            public const int Quantity = 1;

            public const string BuyerFullNameG5 = "Khách hàng không mã số thuế";
            public const string BuyerAddressLineG5 = "#";
            public const string HeaderNoteG5 = "suất hóa đơn tổng cho khách hàng không mã số thuế";
        }
    }
}
