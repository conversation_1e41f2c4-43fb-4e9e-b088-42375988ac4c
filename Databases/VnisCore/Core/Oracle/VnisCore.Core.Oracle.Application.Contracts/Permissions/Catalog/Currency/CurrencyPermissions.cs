using Core.Reflection;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Catalog.Currency
{
    public class CurrencyPermissions
    {
        public const string GroupName = "CurrencyManagement";

        public static class Currency
        {
            public const string Default = GroupName + ".Currency";
            public const string Create = Default + ".Create";
            public const string Update = Default + ".Update";
            public const string Delete = Default + ".Delete";
        }

        public static string[] GetAll()
        {
            return ReflectionHelper.GetPublicConstantsRecursively(typeof(CurrencyPermissions));
        }
    }
}