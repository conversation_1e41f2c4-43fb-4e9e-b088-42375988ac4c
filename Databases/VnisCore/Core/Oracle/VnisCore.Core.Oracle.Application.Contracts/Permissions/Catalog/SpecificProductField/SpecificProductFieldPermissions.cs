using Core.Reflection;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Catalog.SpecificProductField
{
    public class SpecificProductFieldPermissions
    {
        public const string GroupName = "SpecificProductFieldManagement";

        public static class SpecificProductField
        {
            public const string Default = GroupName + ".SpecificProductField";
            public const string Create = Default + ".Create";
            public const string Update = Default + ".Update";
            public const string Delete = Default + ".Delete";
        }

        public static string[] GetAll()
        {
            return ReflectionHelper.GetPublicConstantsRecursively(typeof(SpecificProductFieldPermissions));
        }
    }
}