using Core.Reflection;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Report.ETaxHistory
{
    public class ETaxHistoryPermissions
    {
        public const string GroupName = "ETaxHistoryManagement";

        public static class ETaxHistory
        {
            public const string Default = GroupName + ".ETaxHistory";
            public const string Create = Default + ".Create";
            public const string Update = Default + ".Update";
            public const string Delete = Default + ".Delete";
        }

        public static string[] GetAll()
        {
            return ReflectionHelper.GetPublicConstantsRecursively(typeof(ETaxHistoryPermissions));
        }
    }
}
