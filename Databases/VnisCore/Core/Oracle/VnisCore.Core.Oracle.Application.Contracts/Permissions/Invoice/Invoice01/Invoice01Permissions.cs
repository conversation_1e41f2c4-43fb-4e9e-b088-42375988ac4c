using Core.Reflection;

namespace VnisCore.Core.Oracle.Application.Contracts.Permissions.Invoice.Invoice01
{
    public class Invoice01Permissions
    {
        public const string GroupName = "Invoice01Management";

        public static class Invoice01
        {
            public const string Default = GroupName + ".Invoice01";
            public const string Create = Default + ".Create";
            public const string CreateBatch = Default + ".CreateBatch";
            public const string Update = Default + ".Update";
            public const string Approve = Default + ".Approve";
            public const string CancelApprove = Default + ".CancelApprove";
            public const string Cancel = Default + ".Cancel";
            public const string Delete = Default + ".Delete";
            public const string Replace = Default + ".Replace";
            public const string AdjustmentDetail = Default + ".AdjustmentDetail";
            public const string ImportExcel = Default + ".ImportExcel";
            public const string UpdateByImportExcel = Default + ".UpdateByImportExcel";
            public const string ExportExcel = Default + ".ExportExcel";
            public const string ExportXml = Default + ".ExportXml";
            public const string Sign = Default + ".Sign";
            public const string UnOfficial = Default + ".UnOfficial";
            public const string Official = Default + ".Official";
            public const string UploadDocument = Default + ".UploadDocument";
            public const string DeleteDocument = Default + ".DeleteDocument";
            public const string SendMail = Default + ".SendMail";
            public const string SendNotifyInvoiceError = Default + ".SendNotifyInvoiceError";
            public const string ExportExcelSchedule = Default + ".ExportExcelSchedule";
            public const string FamilyMartImportFile = Default + ".FamilyMartImportFile";
        }

        public static string[] GetAll()
        {
            return ReflectionHelper.GetPublicConstantsRecursively(typeof(Invoice01Permissions));
        }
    }
}