using Core.Application;
using Core.Modularity;
using VnisCore.Core.Oracle.Domain.Shared;
using Core.Localization;

namespace VnisCore.Core.Oracle.Application.Contracts
{
    [DependsOn(
        typeof(VnisCoreOracleDomainSharedModule),
        typeof(AbpDddApplicationModule)
    )]
    public class VnisCoreOracleModuleContractsModule : AbpModule
    {
        public override void PreConfigureServices(ServiceConfigurationContext context)
        {
            VnisCoreOracleDtoExtensions.Configure();
        }
    }
}
