using Core.Data;
using Core.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.Entities;
using VnisCore.Core.Oracle.Domain.Entities.AdministrativeDivision;
using VnisCore.Core.Oracle.Domain.Entities.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InputInvoice.Catalog;
using VnisCore.Core.Oracle.Domain.Entities.InputInvoice.InputInvoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITDeductionDocument;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.PITLetter;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Tbss;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Ticket;
using VnisCore.Core.Oracle.Domain.Entities.InvoiceTemplate;
using VnisCore.Core.Oracle.Domain.Entities.Licenses;
using VnisCore.Core.Oracle.Domain.Entities.Media;
using VnisCore.Core.Oracle.Domain.Entities.PrintNote;
using VnisCore.Core.Oracle.Domain.Entities.RegisterInvoice;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Core.Oracle.Domain.Entities.SettingG4;
using VnisCore.Core.Oracle.Domain.Entities.StatisticSummary;
using VnisCore.Core.Oracle.Domain.Entities.Tvan;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice02;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice03;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.PITDeductionDocument;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.ReportInvoice;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.Ticket;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.EntityFrameworkCore
{
    /* This is your actual DbContext used on runtime.
     * It includes only your entities.
     * It does not include entities of the used modules, because each module has already
     * its own DbContext class. If you want to share some database tables with the used modules,
     * just create a structure like done for AppUser.
     *
     * Don't use this DbContext for database migrations since it does not contain tables of the
     * used modules (as explained above). See cmsMigrationsDbContext for migrations.
     */
    [ConnectionStringName("VnisCoreOracle")]
    public class VnisCoreOracleDbContext : AbpDbContext<VnisCoreOracleDbContext>
    {

        /* Add DbSet properties for your Aggregate Roots / Entities here.
         * Also map them inside cmsDbContextModelCreatingExtensions.Configurecms
         *
         */

        public virtual DbSet<AdministrativeDivisionEntity> AdministrativeDivisionEntity { get; set; }
        public virtual DbSet<CountryEntity> CountryEntity { get; set; }
        public virtual DbSet<AccountTokenTemplateEntity> AccountTokenTemplateEntity { get; set; }
        //public virtual DbSet<CurrencyConversionEntity> CurrencyConversionEntity { get; set; }
        public virtual DbSet<CurrencyEntity> CurrencyEntity { get; set; }
        public virtual DbSet<CustomerEntity> CustomerEntity { get; set; }
        public virtual DbSet<CustomerTvanEntity> CustomerTvanEntity { get; set; }
        public virtual DbSet<EmailEntity> EmailEntity { get; set; }
        public virtual DbSet<EmailInvoiceEntity> EmailInvoiceEntity { get; set; }
        public virtual DbSet<EmailTemplateEntity> EmailTemplateEntity { get; set; }
        public virtual DbSet<GroupCustomerEntity> GroupCustomerEntity { get; set; }
        public virtual DbSet<ProductEntity> ProductEntity { get; set; }
        public virtual DbSet<ProductTypeEntity> ProductTypeEntity { get; set; }
        public virtual DbSet<SpecificProductFieldEntity> SpecificProductFieldEntity { get; set; }
        public virtual DbSet<TaxDepartmentEntity> TaxDepartmentEntity { get; set; }
        public virtual DbSet<TaxEntity> TaxEntity { get; set; }
        public virtual DbSet<UnitEntity> UnitEntity { get; set; }
        public virtual DbSet<UsbTokenEntity> UsbTokenEntity { get; set; }
        public virtual DbSet<UserReadTemplateEntity> UserReadTemplateEntity { get; set; }
        public virtual DbSet<Invoice01DetailEntity> Invoice01DetailEntity { get; set; }
        public virtual DbSet<Invoice01DetailExtraEntity> Invoice01DetailExtraEntity { get; set; }
        public virtual DbSet<Invoice01DetailFieldEntity> Invoice01DetailFieldEntity { get; set; }
        public virtual DbSet<Invoice01HeaderEntity> Invoice01HeaderEntity { get; set; }
        public virtual DbSet<Invoice01HeaderExtraEntity> Invoice01HeaderExtraEntity { get; set; }
        public virtual DbSet<Invoice01HeaderFieldEntity> Invoice01HeaderFieldEntity { get; set; }
        public virtual DbSet<Invoice01DocumentInfoEntity> Invoice01DocumentInfoEntity { get; set; }
        public virtual DbSet<Invoice01ReferenceEntity> Invoice01ReferenceEntity { get; set; }
        public virtual DbSet<Invoice01ReferenceOldDecreeEntity> Invoice01ReferenceOldDecreeEntity { get; set; }
        public virtual DbSet<Invoice01XmlEntity> Invoice01XmlEntity { get; set; }
        public virtual DbSet<Invoice01DocumentEntity> Invoice01Document { get; set; }
        public virtual DbSet<Invoice01UnOfficialEntity> Invoice01UnOfficial { get; set; }
        public virtual DbSet<Invoice01LogEntity> Invoice01LogEntity { get; set; }
        public virtual DbSet<Invoice01TaxBreakdownEntity> Invoice01TaxBreakdownEntity { get; set; }
        public virtual DbSet<Invoice01ExportJobEntity> Invoice01ExportJobEntity { get; set; }
        public virtual DbSet<Invoice01ErrorEntity> Invoice01ErrorEntity { get; set; }
        public virtual DbSet<Invoice01ErrorXmlEntity> Invoice01ErrorXmlEntity { get; set; }

        public virtual DbSet<Invoice01ErpIdEntity> Invoice01ErpIdEntity { get; set; }
        public virtual DbSet<Invoice01SpecificProductExtraEntity> Invoice01SpecificProductExtraEntity { get; set; }

        //public virtual DbSet<Invoice01SignedEntity> Invoice01SignedEntity { get; set; }

        //invoice02
        public virtual DbSet<Invoice02DetailEntity> Invoice02DetailEntity { get; set; }
        public virtual DbSet<Invoice02DetailFieldEntity> Invoice02DetailFieldEntity { get; set; }
        public virtual DbSet<Invoice02HeaderEntity> Invoice02HeaderEntity { get; set; }
        public virtual DbSet<Invoice02HeaderFieldEntity> Invoice02HeaderFieldEntity { get; set; }
        public virtual DbSet<Invoice02DocumentInfoEntity> Invoice02DocumentInfoEntity { get; set; }
        public virtual DbSet<Invoice02ReferenceEntity> Invoice02ReferenceEntity { get; set; }
        public virtual DbSet<Invoice02ReferenceOldDecreeEntity> Invoice02ReferenceOldDecreeEntity { get; set; }
        public virtual DbSet<Invoice02XmlEntity> Invoice02XmlEntity { get; set; }
        public virtual DbSet<Invoice02DocumentEntity> Invoice02Document { get; set; }
        public virtual DbSet<Invoice02UnOfficialEntity> Invoice02UnOfficial { get; set; }
        public virtual DbSet<Invoice02LogEntity> Invoice02LogEntity { get; set; }
        public virtual DbSet<Invoice02ExportJobEntity> Invoice02ExportJobEntity { get; set; }
        public virtual DbSet<Invoice02ErrorEntity> Invoice02ErrorEntity { get; set; }

        public virtual DbSet<Invoice02ErrorXmlEntity> Invoice02ErrorXmlEntity { get; set; }
        public virtual DbSet<Invoice02ErpIdEntity> Invoice02ErpIdEntity { get; set; }

        //public virtual DbSet<Invoice02SignedEntity> Invoice02SignedEntity { get; set; }
        public virtual DbSet<Invoice02SpecificProductExtraEntity> Invoice02SpecificProductExtraEntity { get; set; }

        //invoice03
        public virtual DbSet<Invoice03DetailEntity> Invoice03DetailEntity { get; set; }
        public virtual DbSet<Invoice03DetailFieldEntity> Invoice03DetailFieldEntity { get; set; }
        public virtual DbSet<Invoice03HeaderEntity> Invoice03HeaderEntity { get; set; }
        public virtual DbSet<Invoice03HeaderFieldEntity> Invoice03HeaderFieldEntity { get; set; }
        public virtual DbSet<Invoice03DocumentInfoEntity> Invoice03DocumentInfoEntity { get; set; }
        public virtual DbSet<Invoice03ReferenceEntity> Invoice03ReferenceEntity { get; set; }
        public virtual DbSet<Invoice03ReferenceOldDecreeEntity> Invoice03ReferenceOldDecreeEntity { get; set; }
        public virtual DbSet<Invoice03XmlEntity> Invoice03XmlEntity { get; set; }
        public virtual DbSet<Invoice03DocumentEntity> Invoice03Document { get; set; }
        public virtual DbSet<Invoice03UnOfficialEntity> Invoice03UnOfficial { get; set; }
        public virtual DbSet<Invoice03LogEntity> Invoice03LogEntity { get; set; }
        public virtual DbSet<Invoice03ExportJobEntity> Invoice03ExportJobEntity { get; set; }
        public virtual DbSet<Invoice03ErrorEntity> Invoice03ErrorEntity { get; set; }
        public virtual DbSet<Invoice03ErrorXmlEntity> Invoice03ErrorXmlEntity { get; set; }

        //invoice04
        public virtual DbSet<Invoice04DetailEntity> Invoice04DetailEntity { get; set; }
        public virtual DbSet<Invoice04DetailFieldEntity> Invoice04DetailFieldEntity { get; set; }
        public virtual DbSet<Invoice04HeaderEntity> Invoice04HeaderEntity { get; set; }
        public virtual DbSet<Invoice04HeaderFieldEntity> Invoice04HeaderFieldEntity { get; set; }
        public virtual DbSet<Invoice04DocumentInfoEntity> Invoice04DocumentInfoEntity { get; set; }
        public virtual DbSet<Invoice04ReferenceEntity> Invoice04ReferenceEntity { get; set; }
        public virtual DbSet<Invoice04ReferenceOldDecreeEntity> Invoice04ReferenceOldDecreeEntity { get; set; }
        public virtual DbSet<Invoice04XmlEntity> Invoice04XmlEntity { get; set; }
        public virtual DbSet<Invoice04DocumentEntity> Invoice04Document { get; set; }
        public virtual DbSet<Invoice04UnOfficialEntity> Invoice04UnOfficial { get; set; }
        public virtual DbSet<Invoice04LogEntity> Invoice04LogEntity { get; set; }
        public virtual DbSet<Invoice04ExportJobEntity> Invoice04ExportJobEntity { get; set; }
        public virtual DbSet<Invoice04ErrorEntity> Invoice04ErrorEntity { get; set; }

        public virtual DbSet<Invoice04ErrorXmlEntity> Invoice04ErrorXmlEntity { get; set; }

        // ticket
        public virtual DbSet<TicketDetailEntity> TicketDetailEntity { get; set; }
        public virtual DbSet<TicketDetailFieldEntity> TicketDetailFieldEntity { get; set; }
        public virtual DbSet<TicketHeaderEntity> TicketHeaderEntity { get; set; }
        public virtual DbSet<TicketHeaderFieldEntity> TicketHeaderFieldEntity { get; set; }
        public virtual DbSet<TicketDocumentInfoEntity> TicketDocumentInfoEntity { get; set; }
        public virtual DbSet<TicketReferenceEntity> TicketReferenceEntity { get; set; }
        public virtual DbSet<TicketReferenceOldDecreeEntity> TicketReferenceOldDecreeEntity { get; set; }
        public virtual DbSet<TicketXmlEntity> TicketXmlEntity { get; set; }
        public virtual DbSet<TicketDocumentEntity> TicketDocumentEntity { get; set; }
        public virtual DbSet<TicketUnOfficialEntity> TicketUnOfficialEntity { get; set; }
        public virtual DbSet<TicketLogEntity> TicketLogEntity { get; set; }
        public virtual DbSet<TicketTaxBreakdownEntity> TicketTaxBreakdownEntity { get; set; }
        public virtual DbSet<TicketExportJobEntity> TicketExportJobEntity { get; set; }
        public virtual DbSet<TicketErrorEntity> TicketErrorEntity { get; set; }
        public virtual DbSet<TicketErrorXmlEntity> TicketErrorXmlEntity { get; set; }
        public virtual DbSet<TicketErpIdEntity> TicketErpIdEntity { get; set; }


        public virtual DbSet<DocumentTemplateEntity> DocumentTemplateEntity { get; set; }
        public virtual DbSet<InvoiceTemplateEntity> InvoiceTemplateEntity { get; set; }
        public virtual DbSet<LicenseEntity> LicenseEntity { get; set; }
        public virtual DbSet<MediaFileEntity> FileMediaEntity { get; set; }
        public virtual DbSet<InvoiceTemplateDesignEntity> InvoiceTemplateDesignEntity { get; set; }
        public virtual DbSet<DesignInvoiceTemplateEntity> DesignInvoiceTemplateEntity { get; set; }

        public virtual DbSet<RegistrationHeaderEntity> RegisterInvoiceEntity { get; set; }
        public virtual DbSet<RegistrationDetailEntity> RegisterTemplateInvoiceEntity { get; set; }
        public virtual DbSet<RegistrationInvoiceXmlEntity> RegistrationInvoiceXmlEntity { get; set; }
        public virtual DbSet<MonitorInvoiceTemplateEntity> MonitorInvoiceTemplateEntity { get; set; }


        public virtual DbSet<NewRegistrationHeaderEntity> NewRegistrationHeaderEntity { get; set; }
        public virtual DbSet<NewRegistrationDetailEntity> NewRegistrationDetailEntity { get; set; }
        public virtual DbSet<NewRegistrationDetailExtensionEntity> NewRegistrationDetailExtensionEntity { get; set; }

        #region PITDeductionDocument
        public virtual DbSet<PITDeductionDocumentEntity> PITDeductionDocumentEntity { get; set; }
        public virtual DbSet<PITDeductionDocumentXmlEntity> PITDeductionDocumentXmlEntity { get; set; }
        public virtual DbSet<PITDeductionDocumentErrorEntity> PITDeductionDocumentErrorEntity { get; set; }
        public virtual DbSet<PITDeductionDocumentErrorXmlEntity> PITDeductionDocumentErrorXmlEntity { get; set; }
        #endregion

        #region PITLetter
        public virtual DbSet<PITLaborContractFileEntity> PITLaborContractFileEntity { get; set; }
        public virtual DbSet<PITLetterEntity> PITLetterEntity { get; set; }
        public virtual DbSet<PITLetterXmlEntity> PITLetterPdfEntity { get; set; }
        #endregion

        #region InvoicePrintNote
        public virtual DbSet<InvoicePrintNoteEntity> InvoicePrintNoteEntity { get; set; }
        #endregion

        #region report
        public virtual DbSet<Bc26DetailEntity> Bc26DetailEntity { get; set; }
        public virtual DbSet<Bc26Entity> Bc26Entity { get; set; }

        public virtual DbSet<TaxReport01DetailEntity> TaxReport01DetailEntity { get; set; }
        public virtual DbSet<TaxReport01HeaderEntity> TaxReport01HeaderEntity { get; set; }
        public virtual DbSet<TaxReport01DetailMappingEntity> TaxReport01DetailMappingEntity { get; set; }
        public virtual DbSet<TaxReport03HeaderEntity> TaxReport03HeaderEntity { get; set; }
        public virtual DbSet<TaxReport03DetailEntity> TaxReport03DetailEntity { get; set; }
        public virtual DbSet<TaxReport03DetailDataEntity> TaxReport03DetailDataEntity { get; set; }
        public virtual DbSet<ReportXmlEntity> ReportXmlEntity { get; set; }
        public virtual DbSet<StatisticSummaryEntity> StatisticSummaryEntity { get; set; }

        #endregion

        #region invoice input
        public virtual DbSet<InputCurrencyEntity> InputCurrencyEntity { get; set; }
        public virtual DbSet<InputCustomerEntity> InputCustomerEntity { get; set; }
        public virtual DbSet<InputProductEntity> InputProductEntity { get; set; }
        public virtual DbSet<InputUnitEntity> InputUnitEntity { get; set; }

        public virtual DbSet<InputInvoice01HeaderEntity> InputInvoice01HeaderEntity { get; set; }
        public virtual DbSet<InputInvoice01DetailEntity> InputInvoice01DetailEntity { get; set; }
        public virtual DbSet<InputInvoice01DetailExtraEntity> InputInvoice01DetailExtraEntity { get; set; }
        public virtual DbSet<InputInvoice01DetailFieldEntity> InputInvoice01DetailFieldEntity { get; set; }
        public virtual DbSet<InputInvoice01HeaderExtraEntity> InputInvoice01HeaderExtraEntity { get; set; }
        public virtual DbSet<InputInvoice01HeaderFieldEntity> InputInvoice01HeaderFieldEntity { get; set; }
        public virtual DbSet<InputInvoice01TaxBreakdownEntity> InputInvoice01TaxBreakdownEntity { get; set; }
        public virtual DbSet<InputInvoice01XmlEntity> InputInvoice01XmlEntity { get; set; }
        public virtual DbSet<InputInvoice01RequestDataEntity> InputInvoice01RequestDataEntity { get; set; }
        public virtual DbSet<InputInvoice01LogEntity> InputInvoice01LogEntity { get; set; }



        #endregion

        //setting G4
        public virtual DbSet<SettingG4HeaderEntity> SettingG4HeaderEntity { get; set; }
        public virtual DbSet<SettingG4DetailEntity> SettingG4DetailEntity { get; set; }

        //#region Customer VCB
        //public virtual DbSet<CustomerVCBEntity> CustomerVCBEntity { get; set; }
        //#endregion

        #region TVAN
        public virtual DbSet<TvanInvoice01HasCodeXmlEntity> TvanInvoice01HasCodeXmlEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice01HasCodeEntity> TvanInfoInvoice01HasCodeEntity { get; set; }

        public virtual DbSet<TvanInvoice01WithoutCodeMonitorEntity> TvanInvoice01WithoutCodeMonitorEntity { get; set; }
        public virtual DbSet<TvanInvoice01WithoutCodeXmlEntity> TvanInvoice01WithoutCodeXmlEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice01WithoutCodeEntity> TvanInfoInvoice01WithoutCodeEntity { get; set; }

        public virtual DbSet<TvanInfoInvoice01ErrorEntity> TvanInfoInvoice01ErrorEntity { get; set; }
        public virtual DbSet<TvanInvoice01ErrorXmlEntity> TvanInvoice01ErrorXmlEntity { get; set; }

        public virtual DbSet<TaxReportTvanMonitorEntity> TaxReportTvanMonitorEntity { get; set; }
        public virtual DbSet<TaxReport01TvanInfoEntity> TaxReport01TvanInfoEntity { get; set; }
        public virtual DbSet<TaxReport03TvanInfoEntity> TaxReport03TvanInfoEntity { get; set; }
        public virtual DbSet<TvanReportXmlEntity> TvanReportXmlEntity { get; set; }

        public virtual DbSet<PITDeductionDocumentDeclarationTvanInfoEntity> PITDeductionDocumentDeclarationTvanInfoEntity { get; set; }
        public virtual DbSet<TvanPITDeductionDocumentDeclarationXmlEntity> TvanPITDeductionDocumentDeclarationXmlEntity { get; set; }
        public virtual DbSet<RegistrationTvanInfoEntity> RegistrationTvanInfoEntity { get; set; }
        public virtual DbSet<TvanRegistrationXmlEntity> TvanRegistrationXmlEntity { get; set; }


        public virtual DbSet<TvanCheckInvoice01XmlEntity> TvanCheckInvoice01XmlEntity { get; set; }
        public virtual DbSet<TvanInfoCheckInvoice01Entity> TvanInfoCheckInvoice01Entity { get; set; }


        // Tvan invoice 02
        public virtual DbSet<TvanCheckInvoice02XmlEntity> TvanCheckInvoice02XmlEntity { get; set; }
        public virtual DbSet<TvanInfoCheckInvoice02Entity> TvanInfoCheckInvoice02Entity { get; set; }
        public virtual DbSet<TvanInfoInvoice02ErrorEntity> TvanInfoInvoice02ErrorEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice02HasCodeEntity> TvanInfoInvoice02HasCodeEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice02WithoutCodeEntity> TvanInfoInvoice02WithoutCodeEntity { get; set; }
        public virtual DbSet<TvanInvoice02WithoutCodeMonitorEntity> TvanInvoice02WithoutCodeMonitorEntity { get; set; }
        public virtual DbSet<TvanInvoice02ErrorXmlEntity> TvanInvoice02ErrorXmlEntity { get; set; }
        public virtual DbSet<TvanInvoice02HasCodeXmlEntity> TvanInvoice02HasCodeXmlEntity { get; set; }
        public virtual DbSet<TvanInvoice02WithoutCodeXmlEntity> TvanInvoice02WithoutCodeXmlEntity { get; set; }


        // Tvan invoice 03
        public virtual DbSet<TvanCheckInvoice03XmlEntity> TvanCheckInvoice03XmlEntity { get; set; }
        public virtual DbSet<TvanInfoCheckInvoice03Entity> TvanInfoCheckInvoice03Entity { get; set; }
        public virtual DbSet<TvanInfoInvoice03ErrorEntity> TvanInfoInvoice03ErrorEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice03HasCodeEntity> TvanInfoInvoice03HasCodeEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice03WithoutCodeEntity> TvanInfoInvoice03WithoutCodeEntity { get; set; }
        public virtual DbSet<TvanInvoice03WithoutCodeMonitorEntity> TvanInvoice03WithoutCodeMonitorEntity { get; set; }
        public virtual DbSet<TvanInvoice03ErrorXmlEntity> TvanInvoice03ErrorXmlEntity { get; set; }
        public virtual DbSet<TvanInvoice03HasCodeXmlEntity> TvanInvoice03HasCodeXmlEntity { get; set; }
        public virtual DbSet<TvanInvoice03WithoutCodeXmlEntity> TvanInvoice03WithoutCodeXmlEntity { get; set; }

        // Tvan invoice 04
        public virtual DbSet<TvanCheckInvoice04XmlEntity> TvanCheckInvoice04XmlEntity { get; set; }
        public virtual DbSet<TvanInfoCheckInvoice04Entity> TvanInfoCheckInvoice04Entity { get; set; }
        public virtual DbSet<TvanInfoInvoice04ErrorEntity> TvanInfoInvoice04ErrorEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice04HasCodeEntity> TvanInfoInvoice04HasCodeEntity { get; set; }
        public virtual DbSet<TvanInfoInvoice04WithoutCodeEntity> TvanInfoInvoice04WithoutCodeEntity { get; set; }
        public virtual DbSet<TvanInvoice04WithoutCodeMonitorEntity> TvanInvoice04WithoutCodeMonitorEntity { get; set; }
        public virtual DbSet<TvanInvoice04ErrorXmlEntity> TvanInvoice04ErrorXmlEntity { get; set; }
        public virtual DbSet<TvanInvoice04HasCodeXmlEntity> TvanInvoice04HasCodeXmlEntity { get; set; }
        public virtual DbSet<TvanInvoice04WithoutCodeXmlEntity> TvanInvoice04WithoutCodeXmlEntity { get; set; }

        // Tvan ticket
        public virtual DbSet<TvanCheckTicketXmlEntity> TvanCheckTicketXmlEntity { get; set; }
        public virtual DbSet<TvanInfoCheckTicketEntity> TvanInfoCheckTicketEntity { get; set; }
        public virtual DbSet<TvanInfoTicketErrorEntity> TvanInfoTicketErrorEntity { get; set; }
        public virtual DbSet<TvanInfoTicketHasCodeEntity> TvanInfoTicketHasCodeEntity { get; set; }
        public virtual DbSet<TvanInfoTicketWithoutCodeEntity> TvanInfoTicketWithoutCodeEntity { get; set; }
        public virtual DbSet<TvanTicketWithoutCodeMonitorEntity> TvanTicketWithoutCodeMonitorEntity { get; set; }
        public virtual DbSet<TvanTicketErrorXmlEntity> TvanTicketErrorXmlEntity { get; set; }
        public virtual DbSet<TvanTicketHasCodeXmlEntity> TvanTicketHasCodeXmlEntity { get; set; }
        public virtual DbSet<TvanTicketWithoutCodeXmlEntity> TvanTicketWithoutCodeXmlEntity { get; set; }

        //PITD
        public virtual DbSet<TvanInfoPITDeductionDocumentEntity> TvanInfoPITDeductionDocumentEntity { get; set; }
        public virtual DbSet<TvanPITDeductionDocumentXmlEntity> TvanPITDeductionDocumentXmlEntity { get; set; }
        public virtual DbSet<TvanInfoPITDeductionDocumentErrorEntity> TvanInfoPITDeductionDocumentErrorEntity { get; set; }
        public virtual DbSet<TvanPITDeductionDocumentErrorXmlEntity> TvanPITDeductionDocumentErrorXmlEntity { get; set; }
        #endregion

        #region TBSS cho hóa đơn ngoài hệ thống
        /// <summary>
        /// Lưu thông tin chung của TBSS ngoài hệ thống
        /// </summary>
        public virtual DbSet<TbssHeaderEntity> TbssHeaderEntity { get; set; }

        /// <summary>
        /// Lưu thông tin chi tiết của từng hóa đơn trong 1 TBSS
        /// </summary>
        public virtual DbSet<TbssDetailEntity> TbssDetailEntity { get; set; }

        /// <summary>
        /// Lưu thông tin xml sau khi ký và xml phản hồi về của CQT
        /// </summary>
        public virtual DbSet<TbssXmlEntity> TbssXmlEntity { get; set; }

        /// <summary>
        /// Lưu trạng thái gửi lên, phản hồi về của TBSS
        /// </summary>
        public virtual DbSet<TbssTvanInfoEntity> TbssTvanInfoEntity { get; set; }
        #endregion

        public virtual DbSet<PITDeductionDocumentDeclarationEntity> PITDeductionDocumentDeclaration { get; set; }
        public virtual DbSet<DigitalCertificateHistoryEntity> DigitalCertificateHistory { get; set; }
        public virtual DbSet<PITDeductionDocumentDeclarationXmlEntity> PITDeductionDocumentDeclarationXml { get; set; }

        public VnisCoreOracleDbContext(DbContextOptions<VnisCoreOracleDbContext> options)
            : base(options)
        {

        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            //builder.HasDefaultSchema("EINVOICEVCB");
            //builder.HasDefaultSchema(new Oracle.ManagedDataAccess.Client.OracleConnectionStringBuilder(this.Database.Connection.ConnectionString).UserID);

            /* Configure the shared tables (with included modules) here */

            /* Configure your own tables/entities inside the ConfigureEinvoice method */
            builder.ConfigureVnisCoreOracle();
        }
    }
}