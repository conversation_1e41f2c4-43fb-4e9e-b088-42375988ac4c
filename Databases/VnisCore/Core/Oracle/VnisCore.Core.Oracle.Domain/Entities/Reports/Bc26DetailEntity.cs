using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Core;
using Core.Domain.Entities;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Reports
{
    [Table("Bc26Detail")]
    public class Bc26DetailEntity : Entity<long>
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }
        //Hóa đơn
        public string InvoiceTypeCode { get; set; }
        public string InvoiceTypeName { get; set; }
        public long TemplateId { get; set; }
        public short TemplateNo { get; set; }
        public string SerialNo { get; set; }

        //----------------------------<PERSON><PERSON> tồn đầu kỳ, mua / phát hành trong kỳ----------------------------
        /// <summary>
        /// Tổng số tồn đầu kỳ và phát hành trong kỳ
        /// </summary>
        public int QuantityOfRegister { get; set; }

        /// <summary>
        /// Tồn đầu kỳ từ số
        /// </summary>
        public int StartNumberOfBeginBacklog { get; set; }

        /// <summary>
        /// Tồn đầu kỳ đến số
        /// </summary>
        public int EndNumberOfBeginBacklog { get; set; }

        /// <summary>
        /// Phát hành trong kỳ từ số
        /// </summary>
        public int StartNumberOfRegister { get; set; }

        /// <summary>
        /// Phát hành trong kỳ đến số
        /// </summary>
        public int EndNumberOfRegister { get; set; }

        //----------------------------Số sử dụng, xóa bỏ, hủy trong kỳ----------------------------
        /// <summary>
        /// Hủy bỏ, xóa bỏ, hủy bỏ trong kỳ từ số
        /// </summary>
        public int StartNumberUsedCancelDelete { get; set; }

        /// <summary>
        /// Hủy bỏ, xóa bỏ, hủy bỏ trong kỳ đến số
        /// </summary>
        public int EndNumberUsedCancelDelete { get; set; }

        /// <summary>
        /// Tổng số sử dụng, xóa bỏ, hủy bỏ trong kỳ
        /// </summary>
        public int TotalOfUsedCancelDelete { get; set; }

        /// <summary>
        /// Số lượng sử dụng trong kỳ
        /// </summary>
        public int QuantityOfUsed { get; set; }

        /// <summary>
        /// Số lượng xóa bỏ trong kỳ
        /// </summary>
        public int QuantityOfDelete { get; set; }

        /// <summary>
        /// Các số bị xóa bỏ trong kỳ vd: 7;10-20
        /// </summary>
        public string DeleteInvoices { get; set; }

        /// <summary>
        /// Số lượng hủy bỏ trong kỳ
        /// </summary>
        public int QuantityOfCancel { get; set; }

        /// <summary>
        /// Các số bị hủy bỏ trong kỳ vd: 21;2-3
        /// </summary>
        public string CancelInvoices { get; set; }

        //----------------------------Tồn cuối kỳ----------------------------
        /// <summary>
        /// Tồn cuối kỳ từ số
        /// </summary>
        public int StartNumberOfEndBacklog { get; set; }

        /// <summary>
        /// Tồn cuối kỳ đến số
        /// </summary>
        public int EndNumberOfEndBacklog { get; set; }

        /// <summary>
        /// Số lượng tồn cuối kỳ
        /// </summary>
        public int QuantityOfEndBacklog { get; set; }

        //----------------------------Other----------------------------
        /// <summary>
        /// Phân biệt đâu là dữ liệu khách hàng tự nhập thêm và đâu là dữ liệu lấy từ DB về
        /// </summary>
        public bool IsManually { get; set; }

        //FK
        public long Bc26Id { get; set; }
        public virtual Bc26Entity Bc26 { get; set; }
    }

    public static class VnisCoreOracleDbContextModeBc26DetailCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleBc26DetailEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Bc26DetailEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props
                b.HasIndex(x => x.Bc26Id).HasDatabaseName("IX_Bc26Detail_1");
                b.Property(x => x.TemplateNo).IsRequired().IsRequired();
                b.Property(x => x.SerialNo).IsRequired().HasMaxLength(6);
                b.Property(x => x.InvoiceTypeCode).IsRequired().HasMaxLength(20);
                b.Property(x => x.InvoiceTypeName).IsRequired().HasMaxLength(250);
                b.HasOne(x => x.Bc26).WithMany(x => x.Bc26Details).HasForeignKey(x => x.Bc26Id).HasConstraintName("FK_Detail_Bc26Id"); 
            });

        }
    }
}
