using Core;
using Core.Domain.Entities;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VnisCore.Core.Oracle.Domain.Entities.Reports
{
    [Table("TaxReport03Detail")]
    public class TaxReport03DetailEntity : Entity<long>
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        public short VatPercent { get; set; }

        /// <summary>
        /// tổng cộng doanh thu chưa có thuế
        /// </summary>
        public decimal SumAmount { get; set; }

        /// <summary>
        /// tổng cộng tiền thuế
        /// </summary>
        public decimal SumVatAmount { get; set; }

        //FK
        public long TaxReportHeaderId { get; set; }

        public virtual TaxReport03HeaderEntity TaxReportHeader { get; set; }

        public virtual ICollection<TaxReport03DetailDataEntity> TaxReportDetailDatas { get; set; }

    }

    public static class VnisCoreOracleDbContextModeTaxReport03DetailCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTaxReport03DetailEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TaxReport03DetailEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props
                b.HasIndex(x => x.TaxReportHeaderId).HasDatabaseName("IX_TaxReport03Detail_1");

                b.Property(x => x.SumAmount).HasColumnType("decimal(21,6)");
                b.Property(x => x.SumVatAmount).HasColumnType("decimal(21,6)");

                b.HasOne(x => x.TaxReportHeader).WithMany(x => x.TaxReportDetails).HasForeignKey(x => x.TaxReportHeaderId).HasConstraintName("FK_Detail03_TaxReportHeaderId");
            });
        }
    }
}
