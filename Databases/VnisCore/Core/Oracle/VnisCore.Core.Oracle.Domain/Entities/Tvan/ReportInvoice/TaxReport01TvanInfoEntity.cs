using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.Entities.Reports;
using VnisCore.Core.Oracle.Domain.Entities.Tvan.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Tvan.ReportInvoice
{
    [Table("TaxReport01TvanInfo")]
    public class TaxReport01TvanInfoEntity : BaseTvanInfoEntity
    {
        public long InvoiceHeaderId { get; set; }

        /// <summary>
        /// Loại thông báo: cho phản hồi 204
        /// 2: Thông báo kết quả đối chiếu thông tin gói dữ liệu hợp lệ
        /// 4: Thông báo kết quả đối chiếu sơ bộ thông tin của Bảng tổng hợp khác xăng dầu, <PERSON><PERSON> khai dữ liệu hóa đơn, chứng từ hàng hóa, d<PERSON><PERSON> vụ bán ra không hợp lệ
        /// 9: Thông báo kết quả đối chiếu thông tin gói dữ liệu không hợp lệ các trường hợp khác
        /// </summary>
        public int? LTbao { get; set; }

        /// <summary>
        /// Id của bảng TaxReportTvanMonitorEntity
        /// </summary>
        public long? TvanMonitorId { get; set; }

        /// <summary>
        /// FK
        /// </summary>
        public long TaxReport01DetailMappingEntityId { get; set; }

        /// <summary>
        /// FK
        /// </summary>
        public long TaxReport01HeaderEntityId { get; set; }

        /// <summary>
        /// trạng thái của CQT
        /// </summary>
        public short TvanStatus { get; set; }

        /// <summary>
        /// Loại hoá đơn
        /// </summary>
        public short? InvoiceType { get; set; }

        public virtual TaxReport01DetailMappingEntity TaxReport01DetailMappingEntity { get; set; }

        public virtual TaxReport01HeaderEntity TaxReport01HeaderEntity { get; set; }
    }

    public static class VnisCoreOracleDbContextModelTaxReport01TvanInfoCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTaxReport01TvanInfoEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TaxReport01TvanInfoEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.MessageTypeCode).HasMaxLength(10);
                b.Property(x => x.MessageCode).HasMaxLength(50);
                b.Property(x => x.MessageCodeReference).HasMaxLength(50);
                b.Property(x => x.Reason).HasColumnType("CLOB");
            });
        }
    }
}
