using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01;

namespace VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice01
{
    /// <summary>
    /// xml  rà soát của tct
    /// </summary>
    [Table("TvanCheckInvoice01Xml")]
    public class TvanCheckInvoice01XmlEntity : BaseMediaFile
    {
        public long Partition { get; set; }

        public long Invoice01HeaderId { get; set; }

        public virtual Invoice01HeaderEntity Invoice01Header { get; set; }
    }

    public static class VnisCoreOracleDbContextModelTvanCheckInvoice01XmlCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTvanCheckInvoice01XmlEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TvanCheckInvoice01XmlEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.PhysicalFileName).HasMaxLength(500).IsUnicode(false);
                b.Property(x => x.FileName).HasMaxLength(500).IsUnicode(true);
                b.Property(x => x.Partition).IsRequired();
            });
        }
    }
}
