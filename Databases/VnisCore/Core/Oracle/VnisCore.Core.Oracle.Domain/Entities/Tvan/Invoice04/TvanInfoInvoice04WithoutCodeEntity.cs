using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations.Schema;

using VnisCore.Core.Oracle.Domain.Entities.Tvan.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Tvan.Invoice04
{
    [Table("TvanInfoInvoice04WithoutCode")]
    public class TvanInfoInvoice04WithoutCodeEntity : BaseTvanInfoEntity
    {
        public long InvoiceHeaderId { get; set; }
    }

    public static class VnisCoreOracleDbContextModelTvanInfoInvoice04WithoutCodeCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTvanInfoInvoice04WithoutCodeEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TvanInfoInvoice04WithoutCodeEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.MessageTypeCode).HasMaxLength(10);
                b.Property(x => x.MessageCode).HasMaxLength(50);
                b.Property(x => x.MessageCodeReference).HasMaxLength(50);
                b.Property(x => x.Reason).HasColumnType("CLOB");

                b.HasIndex(x => new { x.MessageCode }).HasDatabaseName("IX_TvanInfoInvoice04WithoutCode_MessageCode");
            });
        }
    }
}
