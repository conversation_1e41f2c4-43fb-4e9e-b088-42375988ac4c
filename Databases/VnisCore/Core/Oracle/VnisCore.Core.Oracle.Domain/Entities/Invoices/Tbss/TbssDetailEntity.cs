using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Tbss
{
    /// <summary>
    /// Lưu thông tin chi tiết của từng hóa đơn trong 1 TBSS
    /// </summary>
    [Table("TbssDetail")]
    public class TbssDetailEntity : TenantFullAuditedEntity<long>
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        /// <summary>
        /// STT của mỗi hóa đơn trong TBSS
        /// </summary>
        public short Index { get; set; }

        /// <summary>
        /// mã của CQT
        /// </summary>
        public string VerificationCode { get; set; }

        /// <summary>
        /// ký hiệu mẫu số hóa đơn
        /// </summary>
        public string TemplateNo { get; set; }

        /// <summary>
        /// Ký hiệu hóa đơn
        /// </summary>
        public string SerialNo { get; set; }

        /// <summary>
        /// Số hóa đơn
        /// </summary>
        public string InvoiceNo { get; set; }

        /// <summary>
        /// Ngày hóa đơn
        /// </summary>
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// Loại áp dụng hóa đơn điện tử
        /// /// Enum: LADHDDTu
        /// 1	Hóa đơn điện tử theo Nghị định 123/2020/NĐ-CP
        /// 2	Hóa đơn điện tử có mã xác thực của cơ quan thuế theo Quyết định số 1209/QĐ-BTC ngày 23 tháng 6 năm 2015 và Quyết định số 2660/QĐ-BTC ngày 14 tháng 12 năm 2016 của Bộ Tài chính(Hóa đơn có mã xác thực của CQT theo Nghị định số 51/2010/NĐ-CP và Nghị định số 04/2014/NĐ-CP)
        /// 3	Các loại hóa đơn theo Nghị định số 51/2010/NĐ-CP và Nghị định số 04/2014/NĐ-CP(Trừ hóa đơn điện tử có mã xác thực của cơ quan thuế theo Quyết định số 1209/QĐ-BTC và Quyết định số 2660/QĐ-BTC)
        /// 4	Hóa đơn đặt in theo Nghị định 123/2020/NĐ-CP
        /// </summary>
        public short InvoiceType { get; set; }

        /// <summary>
        /// Tính chất thông báo
        /// Hủy/thay thế/điều chỉnh/giải trình 
        /// Enum: TThaiTBao
        /// </summary>
        public short Action { get; set; }


        /// <summary>
        /// Lý do
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Trạng thái TVAN/CQT
        /// </summary>
        public short TvanStatus { get; set; }

        /// <summary>
        /// FK bảng TbssHeader
        /// </summary>
        public long TbssHeaderId { get; set; }

        /// <summary>
        /// FK
        /// </summary>
        public virtual TbssHeaderEntity TbssHeader { get; set; }
    }

    public static class VnisCoreOracleDbContextModelTbssDetailCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleTbssDetailEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<TbssDetailEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props

                b.Property(x => x.VerificationCode).HasMaxLength(34);
                b.Property(x => x.SerialNo).IsRequired().HasMaxLength(8);
                b.Property(x => x.InvoiceNo).IsRequired().HasMaxLength(8);
                b.Property(x => x.TemplateNo).IsRequired().HasMaxLength(11);
                b.Property(x => x.InvoiceDate).IsRequired();
                b.Property(x => x.InvoiceType).IsRequired();
                b.Property(x => x.Reason).HasMaxLength(255);
            });
        }
    }
}
