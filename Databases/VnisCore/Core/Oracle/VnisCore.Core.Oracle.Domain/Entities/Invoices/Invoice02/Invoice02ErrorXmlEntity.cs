using Core;
using Core.EntityFrameworkCore.Modeling;

using Microsoft.EntityFrameworkCore;

using System.ComponentModel.DataAnnotations.Schema;

using VnisCore.Core.Oracle.Domain.BaseEntities;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice02
{
    /// <summary>
    /// lưu thông tin sai sót 04-TBSS sau khi ký
    /// </summary>
    [Table("Invoice02ErrorXml")]
    public class Invoice02ErrorXmlEntity : BaseInvoiceErrorMediaFile
    {
    }

    public static class VnisCoreOracleDbContextModelInvoice02ErrorXmlCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice02ErrorXmlEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice02ErrorXmlEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.GroupCode).IsRequired();
                b.Property(x => x.PhysicalFileName).HasMaxLength(500).IsUnicode(false);
                b.Property(x => x.FileName).HasMaxLength(500).IsUnicode(true);
            });
        }
    }
}
