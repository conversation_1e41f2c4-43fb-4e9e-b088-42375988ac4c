using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Invoices.Invoice01
{
    [Table("Invoice01ExportJob")]
    public class Invoice01ExportJobEntity : BaseInvoiceExportJob
    {
        
    }

    public static class VnisCoreOracleDbContextModeInvoice01ExportJobCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleInvoice01ExportJobEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<Invoice01ExportJobEntity>(b =>
            {
                //TODO: thêm index
                b.ConfigureByConvention(); //auto configure for the base class props
                
                b.HasIndex(x => x.Path).IsUnique();
                b.HasIndex(x => x.PhysicalName).IsUnique();
                b.HasIndex(x => x.TaskId).IsUnique();
                b.Property(x => x.Pages).IsUnicode(false).HasColumnType("CLOB");
            });
        }
    }
}
