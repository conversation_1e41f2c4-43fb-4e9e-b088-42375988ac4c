using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.Domain.Entities.Auditing;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Media
{
    [Table("MediaFile")]
    public class MediaFileEntity : BaseMediaFile
    {
    }

    public static class VnisCoreOracleDbContextModeMediaFileCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleMediaFileEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<MediaFileEntity>(b =>
            {
                //TODO: them index

                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.PhysicalFileName).IsRequired().HasMaxLength(250);
                b.Property(x => x.FileName).IsRequired().HasMaxLength(250);
                b.Property(x => x.ContentType).IsRequired().HasMaxLength(50);
                //b.Property(x => x.Data).HasMaxLength(int.MaxValue);
            });

        }
    }
}
