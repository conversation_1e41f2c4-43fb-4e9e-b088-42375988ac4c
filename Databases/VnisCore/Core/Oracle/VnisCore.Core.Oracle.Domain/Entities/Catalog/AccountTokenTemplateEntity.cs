using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using Core;
using Core.Domain.Entities.Auditing;
using Core.EntityFrameworkCore.Modeling;

namespace VnisCore.Core.Oracle.Domain.Entities.Catalog
{
    [Table("AccountTokenTemplate")]
    public class AccountTokenTemplateEntity : CreationAuditedEntity<long>, ITenantFullAuditedEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        public new DateTime CreationTime { get; set; }
        public new Guid? CreatorId { get; set; }
        public Guid? UserId { get; set; }
        public long? UsbTokenId { get; set; }
        public long? TemplateId { get; set; }
        public Guid TenantId { get; set; }
    }

    public static class VnisCoreOracleDbContextModeAccountTokenTemplateCreatingExtensions
    {
        public static void ConfigureVnisCoreOracleAccountTokenTemplateEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<AccountTokenTemplateEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props
            });

        }
    }
}
