using Core;
using Core.EntityFrameworkCore.Modeling;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using VnisCore.Core.Oracle.Domain.BaseEntities;
using VnisCore.Core.Oracle.Domain.Interfaces.DecreeNo70;

namespace VnisCore.Core.Oracle.Domain.Entities.Catalog
{
    [Table("SpecificProductField")]
    public class SpecificProductFieldEntity : TenantFullAuditedEntity<long>, ISpecificProductFieldDecreeNo70
    {
        /// <summary>
        /// Loại hàng hóa đặc trưng: SpecificProductTypeEnum
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// Tên trường
        /// </summary>
        public string FieldName { get; set; }

        /// <summary>
        /// Tên hiển thị
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// Metadata
        /// </summary>
        public string Metadata { get; set; }

        /// <summary>
        /// Bắt buộc
        /// </summary>
        public bool IsRequired { get; set; }
    }

    public static class DbContextModelSpecificProductFieldCreatingExtensions
    {
        public static void ConfigureSpecificProductFieldEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<SpecificProductFieldEntity>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props

                b.ToTable("SpecificProductField").HasComment("Bảng lưu trữ các giá trị của thông tin loại hàng hóa đặc trưng");
                b.HasKey(e => e.Id);
                b.Property(x => x.Id).HasDefaultValueSql(@"""SEQ_SpecificProductField_Id"".NEXTVAL");

                b.Property(x => x.Type).IsRequired().HasComment("Loại hàng hóa đặc trưng");
                b.Property(x => x.FieldName).IsRequired().HasMaxLength(20).HasComment("Tên trường");
                b.Property(x => x.DisplayName).IsRequired().HasMaxLength(100).HasComment("Tên hiển thị");

                b.HasIndex(x => new { x.Type, x.FieldName, x.TenantId, x.IsDeleted });
            });
        }
    }
}
