using Core.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace VnisCore.Core.Oracle.Domain.BaseEntities
{
    public class BaseInvoiceExportJob : Entity<long>, ITenantFullAuditedEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public new long Id { get; set; }

        public Guid TenantId { get; set; }

        public Guid TaskId { get; set; }

        /// <summary>
        /// tổng số lần export
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// page export hiện tại
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// Tên full tên file và đường dẫn các lần export lưu dưới dạng: path1.csv;path2.csv;....
        /// </summary>

        public string Pages { get; set; }

        /// <summary>
        /// tên file vật lý tổng hợp trên minio
        /// </summary>
        public string PhysicalName { get; set; }

        /// <summary>
        /// lưu đường dẫn tới file bao gồm cả tên file download tổng hợp
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// lưu tên file để download
        /// </summary>
        public string LogicalName { get; set; }

        /// <summary>
        /// lưu dữ liệu query bảng kê
        /// </summary>
        public string Metadata { get; set; }

        public short Status { get; set; }

        public string ErrorMessage { get; set; }
    }
}
