using System;

namespace VnisCore.Core.Oracle.Domain.BaseEntities
{
    public interface IBaseApprove<TKey>
    {
        /// <summary>
        /// Tài khoản người duyệt (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public TKey ApproverId { get; set; }

        /// <summary>
        /// Tên người duyệt (Sử dụng tài khoản đang đăng nhập hiện tại)
        /// </summary>
        public string ApproverName { get; set; }

        /// <summary>
        /// Thời gian duyệt
        /// </summary>
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// Trạng thái duyệt
        /// </summary>
        public short ApprovalStatus { get; set; }
    }
}
