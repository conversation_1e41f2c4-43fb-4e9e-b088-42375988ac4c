using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class AddTransmissionPartnerField : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoTicketWithoutCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoTicketWithoutCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoTicketHasCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoTicketHasCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoTicketError",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoTicketError",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice04WithoutCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice04WithoutCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice04HasCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice04HasCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice04Error",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice04Error",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice03WithoutCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice03WithoutCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice03HasCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice03HasCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice03Error",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice03Error",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice02WithoutCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice02WithoutCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice02HasCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice02HasCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice02Error",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice02Error",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice01WithoutCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice01WithoutCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice01HasCode",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice01HasCode",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoInvoice01Error",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice01Error",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoCheckTicket",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoCheckTicket",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoCheckInvoice04",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice04",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoCheckInvoice03",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice03",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoCheckInvoice02",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice02",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TvanInfoCheckInvoice01",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice01",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TaxReport03TvanInfo",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TaxReport03TvanInfo",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "TaxReport01TvanInfo",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "TaxReport01TvanInfo",
                type: "NUMBER(5)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "RegistrationTvanInfo",
                type: "NVARCHAR2(2000)",
                nullable: true);

            migrationBuilder.AddColumn<short>(
                name: "TransmissionPartner",
                table: "RegistrationTvanInfo",
                type: "NUMBER(5)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoTicketWithoutCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoTicketWithoutCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoTicketHasCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoTicketHasCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoTicketError");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoTicketError");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice04WithoutCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice04WithoutCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice04HasCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice04HasCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice04Error");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice04Error");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice03WithoutCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice03WithoutCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice03HasCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice03HasCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice03Error");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice03Error");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice02WithoutCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice02WithoutCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice02HasCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice02HasCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice02Error");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice02Error");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice01WithoutCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice01WithoutCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice01HasCode");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice01HasCode");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoInvoice01Error");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoInvoice01Error");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoCheckTicket");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoCheckTicket");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoCheckInvoice04");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice04");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoCheckInvoice03");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice03");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoCheckInvoice02");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice02");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TvanInfoCheckInvoice01");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TvanInfoCheckInvoice01");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TaxReport03TvanInfo");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TaxReport03TvanInfo");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "TaxReport01TvanInfo");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "RegistrationTvanInfo");

            migrationBuilder.DropColumn(
                name: "TransmissionPartner",
                table: "RegistrationTvanInfo");
        }
    }
}
