using Microsoft.EntityFrameworkCore.Migrations;

namespace VnisCore.Core.Oracle.EntityFrameworkCore.DbMigrations.Migrations
{
    public partial class VNPINVV5AdjReplacePITDAlterPITDeductionDocument : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "ReferenceId",
                table: "PITDeductionDocument",
                type: "NUMBER(19)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "RootId",
                table: "PITDeductionDocument",
                type: "NUMBER(19)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ReferenceId",
                table: "PITDeductionDocument");

            migrationBuilder.DropColumn(
                name: "RootId",
                table: "PITDeductionDocument");
        }
    }
}
