using Core.Domain.Repositories;

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;

namespace VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02
{
    public interface IVnisCoreMongoXmlInvoice02SignedRepository : IBasicRepository<MongoXmlInvoice02SignedEntity, Guid>
    {
        Task<List<MongoXmlInvoice02SignedEntity>> GetListByIsSyncAsync(List<decimal> groups, int isSync, int numberInvoicesTake, CancellationToken cancellationToken = default);
        Task<MongoXmlInvoice02SignedEntity> GetByInvoiceHeaderId(long? invoiceHeaderId, CancellationToken cancellationToken = default);

        Task<List<MongoXmlInvoice02SignedEntity>> GetUnSyncByGroupAsync(List<decimal> groups, List<int> invoiceGroups, CancellationToken cancellationToken = default);

        Task<List<MongoXmlInvoice02SignedEntity>> GetListByInvoiceHeaderId(List<long> invoiceHeaderIds, CancellationToken cancellationToken = default);
    }
}
