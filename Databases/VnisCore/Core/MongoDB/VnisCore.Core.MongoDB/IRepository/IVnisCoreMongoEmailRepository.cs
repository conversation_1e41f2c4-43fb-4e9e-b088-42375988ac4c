using Core.Domain.Repositories;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using VnisCore.Core.MongoDB.Entities;

namespace VnisCore.Core.MongoDB.IRepository
{
    public interface IVnisCoreMongoEmailRepository : IBasicRepository<MongoEmailEntity, Guid>
    {
        Task<List<MongoEmailEntity>> GetListAsync(
            Guid? tenanId,
            string sorting,
            int maxResultCount,
            int skipCount,
            string filter,
            short[] mailStatuses,
            DateTime? fromDate,
            DateTime? toDate,
            CancellationToken cancellationToken = default
        );

        Task<long> CountAsync(Guid? tenanId, 
            string sorting, 
            string filter,
            short[] mailStatuses,
            DateTime? fromDate,
            DateTime? toDate,
            CancellationToken cancellationToken = default
        );

        Task<MongoEmailEntity> GetById(Guid id, Guid tenantId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Count SL mail hệ thống đã gửi
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        Task<long> CountMailsSendedSuccessAsync(Guid tenantId, CancellationToken cancellationToken = default);
    }
}