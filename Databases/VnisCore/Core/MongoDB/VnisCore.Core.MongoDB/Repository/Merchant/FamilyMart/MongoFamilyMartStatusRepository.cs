using Core.Domain.Repositories.MongoDB;
using Core.MongoDB;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using VnisCore.Core.MongoDB.Entities.Merchant.FamilyMart;
using VnisCore.Core.MongoDB.IRepository.Merchant.FamilyMart;
using MongoDB.Bson;

namespace VnisCore.Core.MongoDB.Repository.Merchant.FamilyMart
{
    public class MongoFamilyMartStatusRepository : MongoDbRepository<IVnisCoreMongoDbContext, MongoFamilyMartStatusEntity, Guid>, IMongoFamilyMartStatusRepository
    {
        public MongoFamilyMartStatusRepository(IMongoDbContextProvider<IVnisCoreMongoDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }
        

        public async Task<List<MongoFamilyMartStatusEntity>> GetTaxCode(string taxCode, CancellationToken cancellationToken = default)
        {
            #region old
            //return await (await GetMongoQueryableAsync(cancellationToken))
            //        .Where(x=>x.TaxCode == taxCode)
            //        .As<IMongoQueryable<MongoFamilyMartStatusEntity>>()
            //        .ToListAsync(GetCancellationToken(cancellationToken));
            #endregion
            #region new
            BsonDocument filter = new BsonDocument();

            filter.Add("TaxCode", taxCode);

            var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, null, cancellationToken);

            return await cursor.ToListAsync();
            #endregion
        }

        public async Task<MongoFamilyMartStatusEntity> GetTaxCodeAndInvoiceGroup(string taxCode, int invoiceGroup, CancellationToken cancellationToken = default)
        {
            #region old
            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .FirstOrDefaultAsync(x => x.TaxCode == taxCode && x.InvoiceGroup == invoiceGroup,
            //        GetCancellationToken(cancellationToken));
            #endregion
            #region new
            BsonDocument filter = new BsonDocument();

            filter.Add("TaxCode", taxCode);
            filter.Add("InvoiceGroup", invoiceGroup);

            var options = new FindOptions<MongoFamilyMartStatusEntity>
            {
                Limit = 1
            };

            var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);

            return await cursor.FirstOrDefaultAsync();
            #endregion
        }
    }
}
