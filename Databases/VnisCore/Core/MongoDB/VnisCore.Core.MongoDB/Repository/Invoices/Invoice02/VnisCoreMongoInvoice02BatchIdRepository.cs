using Core.Domain.Repositories.MongoDB;
using Core.MongoDB;
using Core.Shared.Constants;
using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice02;
using VnisCore.Core.MongoDB.IRepository.Invoices.Invoice02;

namespace VnisCore.Core.MongoDB.Repository.Invoices.Invoice02
{
    public class VnisCoreMongoInvoice02BatchIdRepository : MongoDbRepository<IVnisCoreMongoDbContext, MongoInvoice02BatchIdEntity, Guid>, IVnisCoreMongoInvoice02BatchIdRepository
    {
        public VnisCoreMongoInvoice02BatchIdRepository(IMongoDbContextProvider<IVnisCoreMongoDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<List<MongoInvoice02BatchIdEntity>> GetBatchIdNoNumberByInvoiceTemplateIdAsync(List<long> invoiceTemplateIds, CancellationToken cancellationToken = default)
        {
            var invoiceTemplateIdsBsonArray = new BsonArray();
            invoiceTemplateIds.ForEach(x => { invoiceTemplateIdsBsonArray.Add($"{x}"); });

            var filter = new BsonDocument
            {
                {
                    "InvoiceTemplateId", new BsonDocument()
                        .Add("$in", invoiceTemplateIdsBsonArray)
                },
                {
                    "$or", new BsonArray()
                        .Add(new BsonDocument()
                            .Add("GenerateNumberStatus", new BsonInt64(0L))
                        )
                        .Add(new BsonDocument()
                            .Add("GenerateNumberStatus", new BsonInt64(1L))
                        )
                }
            };

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };

            var options = new FindOptions<MongoInvoice02BatchIdEntity>()
            {
                Sort = sort
            };

            var batch = new List<MongoInvoice02BatchIdEntity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => invoiceTemplateIds.Contains(x.InvoiceTemplateId) && (x.GenerateNumberStatus == 0 || x.GenerateNumberStatus == 1))
            //    .OrderBy(x => x.CreationTime)
            //    .As<IMongoQueryable<MongoInvoice02BatchIdEntity>>()
            //    .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice02BatchIdEntity>> GetBatchIdNoNumberAsync(List<decimal> groups, int take, CancellationToken cancellationToken)
        {
            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });

            var filter = new BsonDocument
            {
                {
                    "Group", new BsonDocument()
                        .Add("$in", tenantGroupBsonArray)
                },
                {
                    "CreatorStatus", new BsonDocument()
                        .Add("$gt", new BsonInt64(-1L))
                },
                {
                    "$or", new BsonArray()
                        .Add(new BsonDocument()
                            .Add("GenerateNumberStatus", new BsonInt64(0L))
                        )
                        .Add(new BsonDocument()
                            .Add("GenerateNumberStatus", new BsonInt64(1L))
                        )
                }
            };

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };

            var options = new FindOptions<MongoInvoice02BatchIdEntity>()
            {
                Sort = sort,
                Limit = take
            };

            var batch = new List<MongoInvoice02BatchIdEntity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task<List<MongoInvoice02BatchIdEntity>> GetByBatchIdsGenerateNumberErrorAsync(List<Guid> batchIds, CancellationToken cancellationToken = default)
        {
            var batch = await (await GetMongoQueryableAsync(cancellationToken))
                .Where(ct => batchIds.Contains(ct.BatchId) && ct.GenerateNumberStatus == -1)
                .As<IMongoQueryable<MongoInvoice02BatchIdEntity>>()
                .ToListAsync(GetCancellationToken(cancellationToken));

            return batch;
        }

        public async Task<List<MongoInvoice02BatchIdEntity>> GetByBatchIdDoneGenerateNumberAsync(List<decimal> groups, int take, CancellationToken cancellationToken)
        {
            var tenantGroupsArr = new BsonArray();
            groups.ForEach(x => { tenantGroupsArr.Add($"{x}"); });

            var filter = new BsonDocument
            {
                {
                    "TenantGroup", new BsonDocument().Add("$in", tenantGroupsArr)
                },
                {
                    "GenerateNumberStatus", new BsonInt64(2L)
                }
            };

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };

            var options = new FindOptions<MongoInvoice02BatchIdEntity>()
            {
                Sort = sort,
                Limit = take
            };

            var mongoInvoice01BatchIds = new List<MongoInvoice02BatchIdEntity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    mongoInvoice01BatchIds.AddRange(cursor.Current.ToList());
                }
            }

            return mongoInvoice01BatchIds;
        }

        public async Task<bool> GetByBatchIdDoneGenerateNumberAsync(Guid id, CancellationToken cancellationToken = default)
        {
            var batch = await (await GetMongoQueryableAsync(cancellationToken))
                .FirstOrDefaultAsync(ct => ct.BatchId == id && ct.GenerateNumberStatus == 2,
                    GetCancellationToken(cancellationToken));
            return batch != null;
        }

        public async Task<bool> GetByBatchIdGenerateNumberErrorAsync(Guid batchId, CancellationToken cancellationToken = default)
        {
            var batch = await (await GetMongoQueryableAsync(cancellationToken))
                .FirstOrDefaultAsync(ct => ct.BatchId == batchId && ct.GenerateNumberStatus == -1,
                    GetCancellationToken(cancellationToken));
            return batch != null;
        }

        public async Task<List<MongoInvoice02BatchIdEntity>> GetBatchIdUnSyncLicenseAsync(List<decimal> groups, int take, CancellationToken cancellationToken = default)
        {
            return await(await GetMongoQueryableAsync(cancellationToken))
                .Where(x => groups.Contains(x.Group) && (x.GenerateNumberStatus == GenerateNumberStatus.Synced.GetHashCode() || x.GenerateNumberStatus == GenerateNumberStatus.SyncError.GetHashCode()) && x.IsSyncedLicense == 0)
                .OrderBy(x => x.CreationTime)
                .Take(take)
                .As<IMongoQueryable<MongoInvoice02BatchIdEntity>>()
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task UpdateByCreationTime(DateTime? fromTime, DateTime toTime, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice02BatchIdEntity>.Filter.Where(item => item.CreationTime <= toTime && item.TenantId == tenantId);//.Where(fromTime.HasValue, x => x.CreationTime >= fromTime);
            if (fromTime.HasValue)
            {
                filter = Builders<MongoInvoice02BatchIdEntity>.Filter.Where(item => item.CreationTime >= fromTime && item.CreationTime <= toTime && item.TenantId == tenantId);
            }
            var update = Builders<MongoInvoice02BatchIdEntity>.Update.Set("IsSyncedLicense", (short)LicenseSyncStatus.Synced.GetHashCode());
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }
    }
}
