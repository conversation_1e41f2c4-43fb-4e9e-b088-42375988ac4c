using Core.Domain.Repositories.MongoDB;
using Core.MongoDB;
using Core.Shared.Constants;
using Core.Shared.Extensions;

using MongoDB.Bson;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading;
using System.Threading.Tasks;

using VnisCore.Core.MongoDB.Entities.Invoices.Invoice01;
using VnisCore.Core.MongoDB.IRepository;

namespace VnisCore.Core.MongoDB.Repository
{
    public class VnisCoreMongoInvoice01Repository : MongoDbRepository<IVnisCoreMongoDbContext, MongoInvoice01Entity, long>, IVnisCoreMongoInvoice01Repository
    {
        public VnisCoreMongoInvoice01Repository(IMongoDbContextProvider<IVnisCoreMongoDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }

        public async Task<int> CountAlreadyNumberAsync(Guid batchId, long invoiceTemplateId, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => x.BatchId == batchId && x.InvoiceTemplateId == invoiceTemplateId && x.Number.HasValue)
                .As<IMongoQueryable<MongoInvoice01Entity>>()
                .CountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<MongoInvoice01Entity> GetById(long id, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.Id == id,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<MongoInvoice01Entity> GetById(long id, short templateNo, string serialNo, string invoiceNo, string sellerTaxCode, string transactionId, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.Id == id 
                                            && ct.TemplateNo == templateNo 
                                            && ct.SerialNo == serialNo 
                                            && ct.InvoiceNo == invoiceNo 
                                            && ct.SellerTaxCode == sellerTaxCode
                                            && ct.TransactionId == transactionId,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<MongoInvoice01Entity> GetByErpId(string erpId, Guid tenantId, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(ct => ct.ErpId == erpId && ct.TenantId == tenantId,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetByErpIdsAsync(Guid tenantId, List<string> erpIds, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                                        .WhereIf(erpIds.Any(), x => x.TenantId == tenantId && erpIds.Contains(x.ErpId))
                                        .As<IMongoQueryable<MongoInvoice01Entity>>()
                                        .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetInvoiceToSignByIdsAsync(List<long> ids, CancellationToken cancellationToken = default)
        {
            var signstatus = new List<short> { (short)SignStatus.ChoKy.GetHashCode(), (short)SignStatus.KyLoi.GetHashCode() };
            return await (await GetMongoQueryableAsync(cancellationToken))
                            .WhereIf(ids.Any(), x => ids.Contains(x.Id)
                                                     && signstatus.Contains(x.SignStatus)
                                                     && x.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode()
                                                     && !string.IsNullOrEmpty(x.InvoiceNo)
                                                     && x.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode()
                                                     && x.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode())
                            .As<IMongoQueryable<MongoInvoice01Entity>>()
                            .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetByIdsAsync(List<long> ids, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                            .Where(x => ids.Contains(x.Id))
                            .As<IMongoQueryable<MongoInvoice01Entity>>()
                            .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<MongoInvoice01Entity> GetByInfoAsync(string sellerTaxCode, short templateNo, string serialNo, int number, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(x => x.SellerTaxCode == sellerTaxCode && x.TemplateNo == templateNo && x.SerialNo == serialNo && x.Number == number,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetListAsync(Guid? tenantId, string sorting, int maxResultCount, int skipCount, string filter, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .WhereIf<MongoInvoice01Entity, IMongoQueryable<MongoInvoice01Entity>>(
                    !filter.IsNullOrWhiteSpace(),
                    u => u.Note.Contains(filter)
                    || u.Note.Contains(filter)

                )
                .WhereIf(tenantId.HasValue, x => x.TenantId == tenantId)
                .OrderByDescending(x => x.CreationTime)
                .As<IMongoQueryable<MongoInvoice01Entity>>()
                .PageBy<MongoInvoice01Entity, IMongoQueryable<MongoInvoice01Entity>>(skipCount, maxResultCount)
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetListGenerateNumberAsync(List<Guid> batchIds, int take, CancellationToken cancellationToken = default)
        {
            var batchIdsBsonArray = new BsonArray();
            batchIds.ForEach(x =>
            {
                var bytes = GuidConverter.ToBytes(x, GuidRepresentation.CSharpLegacy);
                batchIdsBsonArray.Add(new BsonBinaryData(bytes, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy));
            });

            var filter = new BsonDocument()
            {
                {
                    "BatchId", new BsonDocument()
                    .Add("$in", batchIdsBsonArray)
                },
                {
                    "IsGeneratedNumber",  new BsonInt64(0L)
                }
            };

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) }, { "InvoiceDate", new BsonInt64(1L) } };
            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = take
            };

            var invoices01 = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                invoices01.AddRange(cursor.Current.ToList());
            }

            return invoices01;

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => batchIds.Contains(x.BatchId) && x.IsGeneratedNumber == 0)
            //    .OrderBy(x => x.CreationTime).ThenBy(x => x.InvoiceDate)
            //    .Take(take)
            //    .As<IMongoQueryable<MongoInvoice01Entity>>()
            //    .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetListUnSignedAsync(List<Guid> tenantIds, List<decimal> groups, List<int> invoiceGroups, short isSyncToCore, int invoiceTakeToSign, CancellationToken cancellationToken = default)
        {
            var tenantIdsBsonArray = new BsonArray();
            tenantIds.ForEach(x =>
            {
                var bytes = GuidConverter.ToBytes(x, GuidRepresentation.CSharpLegacy);
                tenantIdsBsonArray.Add(new BsonBinaryData(bytes, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy));
            });

            var invoicesGroupBsonArray = new BsonArray();
            invoiceGroups.ForEach(x =>
            {
                invoicesGroupBsonArray.Add(x);
            });

            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });

            var signStatuses = new List<int> { SignStatus.ChoKy.GetHashCode() };
            var signStatusBsonArray = new BsonArray();
            signStatuses.ForEach(x =>
            {
                signStatusBsonArray.Add(x);
            });

            var filter = new BsonDocument
            {
                {
                    "TenantGroup", new BsonDocument()
                        .Add("$in", tenantGroupBsonArray)
                },
                {
                    "InvoiceGroup", new BsonDocument()
                        .Add("$in", invoicesGroupBsonArray)
                },
                {
                      "SignStatus", new BsonDocument()
                        .Add("$in", signStatusBsonArray)
                },
                {
                    "InvoiceStatus", new BsonDocument()
                        .Add("$ne", new BsonInt64( InvoiceStatus.XoaHuy.GetHashCode()))
                },
                {
                    "ApproveStatus", new BsonDocument()
                        .Add("$ne", new BsonInt64( ApproveStatus.ChoDuyet.GetHashCode()))
                },
                {
                    "ApproveCancelStatus", new BsonDocument()
                        .Add("$ne", new BsonInt64( ApproveStatus.ChoDuyet.GetHashCode()))
                },
                {
                    "TenantId", new BsonDocument()
                        .Add("$in", tenantIdsBsonArray)
                },
                {
                    "InvoiceNo", new BsonDocument()
                        .Add("$ne", BsonNull.Value)
                },
                {
                    "IsSyncedToCore", new BsonInt64(isSyncToCore)
                },
                {
                    "IsSyncedToElasticSearch", new BsonInt64(1)
                }
            };

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };
            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = invoiceTakeToSign
            };

            var invoice01UnSigned = new List<MongoInvoice01Entity>();

            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                invoice01UnSigned.AddRange(cursor.Current.ToList());
            }

            return invoice01UnSigned;


            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => groups.Contains(x.TenantGroup) && invoiceGroups.Contains(x.InvoiceGroup) && tenantIds.Contains(x.TenantId)
            //                && (x.SignStatus == SignStatus.ChoKy.GetHashCode() || x.SignStatus == SignStatus.KyLoi.GetHashCode())
            //                && x.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode()
            //                && !string.IsNullOrEmpty(x.InvoiceNo)
            //                && x.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode()
            //                && x.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode()
            //                && x.IsSyncedToCore == isSyncToCore)
            //    .OrderBy(x => x.CreationTime)
            //    .Take(invoiceTakeToSign)
            //    .As<IMongoQueryable<MongoInvoice01Entity>>()
            //    .ToListAsync(GetCancellationToken(cancellationToken));
        }


        public async Task<List<MongoInvoice01Entity>> GetListUnSignedAsync(string[] sellerTaxCodes, List<int> invoiceGroups, short isSyncToCore, int invoiceTakeToSign, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                           .Where(x => sellerTaxCodes.Contains(x.SellerTaxCode) && invoiceGroups.Contains(x.InvoiceGroup)
                                       && (x.SignStatus == SignStatus.ChoKy.GetHashCode() || x.SignStatus == SignStatus.KyLoi.GetHashCode())
                                       && !string.IsNullOrEmpty(x.InvoiceNo)
                                       && x.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode()
                                       && x.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode()
                                       && x.IsSyncedToCore == isSyncToCore)
                           .OrderBy(x => x.CreationTime)
                           .Take(invoiceTakeToSign)
                           .As<IMongoQueryable<MongoInvoice01Entity>>()
                           .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// lấy các hóa đơn chờ ký để ký
        /// </summary>
        /// <param name="sellerTaxCodes"></param>
        /// <param name="invoiceGroups"></param>
        /// <param name="isSyncToCore"></param>
        /// <param name="invoiceSources"></param>
        /// <param name="invoiceTakeToSign"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<List<MongoInvoice01Entity>> GetListUnSignedAsync(string[] sellerTaxCodes, List<int> invoiceGroups, short isSyncToCore, List<short> invoiceSources, int invoiceTakeToSign, CancellationToken cancellationToken = default)
        {
            var invoicesGroupBsonArray = new BsonArray();
            invoiceGroups.ForEach(x =>
            {
                invoicesGroupBsonArray.Add(x);
            });

            var sellerTaxCodesBsonArray = new BsonArray();
            foreach (var taxcode in sellerTaxCodes)
            {
                sellerTaxCodesBsonArray.Add($"{taxcode}");
            }

            var signStatuses = new List<short> { (short)SignStatus.ChoKy.GetHashCode() };
            var signStatusBsonArray = new BsonArray();
            foreach (var signStatus in signStatuses)
            {
                signStatusBsonArray.Add(signStatus);
            }

            var filter = new BsonDocument
            {
                {
                    "SellerTaxCode", new BsonDocument()
                        .Add("$in", sellerTaxCodesBsonArray)
                },
                {
                    "InvoiceGroup", new BsonDocument()
                        .Add("$in", invoicesGroupBsonArray)
                },
                {
                    "SignStatus", new BsonDocument()
                        .Add("$in", signStatusBsonArray)
                },
                {
                    "InvoiceStatus", new BsonDocument()
                        .Add("$ne", new BsonInt64( InvoiceStatus.XoaHuy.GetHashCode()))
                },
                {
                    "ApproveStatus", new BsonDocument()
                        .Add("$ne", new BsonInt64( ApproveStatus.ChoDuyet.GetHashCode()))
                },
                {
                    "ApproveCancelStatus", new BsonDocument()
                        .Add("$ne", new BsonInt64( ApproveStatus.ChoDuyet.GetHashCode()))
                },
                {
                    "IsSyncedToCore", new BsonInt64(isSyncToCore)
                },
                {
                    "IsSyncedToElasticSearch", new BsonInt64(1L)
                },
                {
                    "InvoiceNo", new BsonDocument()
                        .Add("$ne", BsonNull.Value)
                }
            };

            if (invoiceSources != null && invoiceSources.Any())
            {
                var invoiceSourcesBsonArray = new BsonArray();
                invoiceSources.ForEach(x => { invoiceSourcesBsonArray.Add(x); });

                filter.Add("Source", new BsonDocument().Add("$in", invoiceSourcesBsonArray));
            }

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };
            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = invoiceTakeToSign
            };

            var invoice01UnSigned = new List<MongoInvoice01Entity>();

            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                invoice01UnSigned.AddRange(cursor.Current.ToList());
            }

            return invoice01UnSigned;

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //               .Where(x => sellerTaxCodes.Contains(x.SellerTaxCode) && invoiceGroups.Contains(x.InvoiceGroup)
            //                           && (x.SignStatus == SignStatus.ChoKy.GetHashCode() || x.SignStatus == SignStatus.KyLoi.GetHashCode())
            //                           && x.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode()
            //                           && !string.IsNullOrEmpty(x.InvoiceNo)
            //                           && x.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode()
            //                           && x.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode()
            //                           && x.IsSyncedToCore == isSyncToCore)
            //               .WhereIf(invoiceSources != null && invoiceSources.Any(), x => invoiceSources.Contains(x.Source))
            //               .OrderBy(x => x.CreationTime)
            //               .Take(invoiceTakeToSign)
            //               .As<IMongoQueryable<MongoInvoice01Entity>>()
            //               .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetUnSyncCatalogAsync(List<decimal> tenantGroups, int takeInvoice, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                .Where(x => tenantGroups.Contains(x.TenantGroup)
                            && (x.IsSyncedUnitToCore == 0 || x.IsSyncedProductToCore == 0 || x.IsSyncedCustomerToCore == 0)
                            && x.IsSyncedToCore == 1
                            && x.IsSyncedToElasticSearch == 1)
                .OrderBy(x => x.CreationTime)
                .Take(takeInvoice)
                .As<IMongoQueryable<MongoInvoice01Entity>>()
                .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<MongoInvoice01Entity> GetMaxNumberByBatchIdAndInvoiceTemplateIdAsync(long invoiceTemplateId, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument()
            {
                {"InvoiceTemplateId", invoiceTemplateId },
                {"IsGeneratedNumber", new BsonInt64(1L) }
            };

            var sort = new BsonDocument { { "Number", new BsonInt64(-1L) } };
            var options = new FindOptions<MongoInvoice01Entity>
            {
                Sort = sort,
                Limit = 1
            };

            var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);

            return await cursor.FirstOrDefaultAsync();

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => x.InvoiceTemplateId == invoiceTemplateId && x.IsGeneratedNumber == 1)
            //    .OrderByDescending(x => x.Number)
            //    .As<IMongoQueryable<MongoInvoice01Entity>>()
            //    .FirstOrDefaultAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> Invoice01ReSyncToCore(List<decimal> groups, int take, List<int> invoicesGroups, CancellationToken cancellationToken = default)
        {
            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });
            var filter = new BsonDocument();

            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", tenantGroupBsonArray));

            var invoiceGroupBsonArray = new BsonArray();
            invoicesGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });

            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", invoiceGroupBsonArray));

            filter.AddRange(new BsonDocument
            {
                    {"IsSyncedToCore", new BsonInt64(-1L) },
                    {"IsGeneratedNumber", new BsonInt64(1L) }
            });

            var options = new FindOptions<MongoInvoice01Entity>
            {
                //Sort = sort,
                Limit = take
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task<List<MongoInvoice01Entity>> Invoice01SyncToCore(List<decimal> groups, int take, List<int> invoicesGroups, bool isLargeBatch, CancellationToken cancellationToken = default)
        {
            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });
            var filter = new BsonDocument();

            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", tenantGroupBsonArray));

            var invoiceGroupBsonArray = new BsonArray();
            invoicesGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });

            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", invoiceGroupBsonArray));

            filter.AddRange(new BsonDocument
            {
                {"IsGeneratedNumber", new BsonInt64(1L)},
                {"IsSyncedToCore", new BsonInt64(0L)}
            });

            if (isLargeBatch)
                filter.Add("TotalInvoices", new BsonDocument().Add("$gt", new BsonInt64(1L)));
            else
                filter.Add("TotalInvoices", new BsonInt64(1L));


            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = take
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;

            #region old
            //var filter = new BsonDocument
            //{
            //    {"IsGeneratedNumber", new BsonInt64(1L)},
            //    {"IsSyncedToCore", new BsonInt64(0L)}
            //};

            //if (isLargeBatch)
            //    filter.Add("TotalInvoices", new BsonDocument().Add("$gt", new BsonInt64(1L)));
            //else
            //    filter.Add("TotalInvoices", new BsonInt64(1L));

            //var tenantGroupBsonArray = new BsonArray();
            //groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });
            //filter.Add("TenantGroup", new BsonDocument()
            //    .Add("$in", tenantGroupBsonArray));

            //var invoiceGroupBsonArray = new BsonArray();
            //invoicesGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });
            //filter.Add("InvoiceGroup", new BsonDocument()
            //    .Add("$in", invoiceGroupBsonArray));

            //var sort = new BsonDocument { { "CreationTime", 1 } };

            //var options = new FindOptions<MongoInvoice01Entity>()
            //{
            //    Sort = sort,
            //    Limit = take
            //};

            //var batch = new List<MongoInvoice01Entity>();
            //using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            //while (await cursor.MoveNextAsync(cancellationToken))
            //{
            //    batch.AddRange(cursor.Current.ToList());
            //}

            //return batch;
            #endregion
        }

        public async Task<List<MongoInvoice01Entity>> Invoice01SyncToEs(List<decimal> groups, int take, int esStatus, short isTenantGroupPrivate, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument { };

            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });

            if (isTenantGroupPrivate > 0)
                filter.Add("TenantGroup", new BsonDocument().Add("$in", tenantGroupBsonArray));
            else
            {
                filter.Add("TenantGroup", new BsonDocument()
                    .Add("$not", new BsonDocument()
                        .Add("$in", tenantGroupBsonArray)
                    )
                );
            }

            if (esStatus == 0)
                filter.Add("IsSyncedToElasticSearch", new BsonDocument().Add("$lt", new BsonInt64(1L)));
            else
                filter.Add("IsSyncedToElasticSearch", new BsonInt64(esStatus));

            filter.Add("IsGeneratedNumber", new BsonInt64(1L));
            filter.Add("IsSyncedToCore", new BsonInt64(1L));

            var options = new FindOptions<MongoInvoice01Entity>
            {
                Limit = take
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task<List<MongoInvoice01Entity>> Invoice01SignedSyncToCore(List<decimal> groups, int take, List<int> invoicesGroups, bool isLargeBatch, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument();

            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", tenantGroupBsonArray));

            var invoiceGroupBsonArray = new BsonArray();
            invoicesGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", invoiceGroupBsonArray));

            filter.AddRange(new BsonDocument
            {
                {"IsGeneratedNumber", new BsonInt64(1L)},
                {"IsSyncedToCore", new BsonInt64(1L)},
                {"SignStatus", new BsonInt64(5L)},
                {"IsSyncSignTocore", new BsonInt64(SyncSignedToCoreStatus.UnProcess.GetHashCode())}
            });

            if (isLargeBatch)
                filter.Add("TotalInvoices", new BsonDocument().Add("$gt", new BsonInt64(1L)));
            else
                filter.Add("TotalInvoices", new BsonInt64(1L));

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = take
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;

            #region old
            //var filter = new BsonDocument
            //{
            //    {"IsGeneratedNumber", new BsonInt64(1L)},
            //    {"IsSyncedToCore", new BsonInt64(1L)},
            //    {"SignStatus", new BsonInt64(5L)},
            //    {"IsSyncSignTocore", new BsonInt64(0L)}
            //};

            //if (isLargeBatch)
            //    filter.Add("TotalInvoices", new BsonDocument().Add("$gt", new BsonInt64(1L)));
            //else
            //    filter.Add("TotalInvoices", new BsonInt64(1L));

            //var tenantGroupBsonArray = new BsonArray();
            //groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });
            //filter.Add("TenantGroup", new BsonDocument()
            //    .Add("$in", tenantGroupBsonArray));

            //var invoiceGroupBsonArray = new BsonArray();
            //invoicesGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });
            //filter.Add("InvoiceGroup", new BsonDocument()
            //    .Add("$in", invoiceGroupBsonArray));

            //var sort = new BsonDocument { { "CreationTime", 1 } };

            //var options = new FindOptions<MongoInvoice01Entity>()
            //{
            //    Sort = sort,
            //    Limit = take
            //};

            //var batch = new List<MongoInvoice01Entity>();
            //using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            //while (await cursor.MoveNextAsync(cancellationToken))
            //{
            //    batch.AddRange(cursor.Current.ToList());
            //}

            //return batch;
            #endregion
        }

        public async Task<List<MongoInvoice01Entity>> Invoice01ReSyncSignedSyncToCore(List<decimal> groups, int take, List<int> invoicesGroups, bool isLargeBatch, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument();

            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", tenantGroupBsonArray));

            var invoiceGroupBsonArray = new BsonArray();
            invoicesGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", invoiceGroupBsonArray));

            filter.AddRange(new BsonDocument
            {
                {"IsGeneratedNumber", new BsonInt64(1L)},
                {"IsSyncedToCore", new BsonInt64(1L)},
                {"SignStatus", new BsonInt64(5L)},
                {"IsSyncSignTocore", new BsonInt64(SyncSignedToCoreStatus.Error.GetHashCode())}
            });

            if (isLargeBatch)
                filter.Add("TotalInvoices", new BsonDocument().Add("$gt", new BsonInt64(1L)));
            else
                filter.Add("TotalInvoices", new BsonInt64(1L));

            var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = take
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task<List<MongoInvoice01Entity>> GetListCreateContentEmailInvoiceNoCodeWithCursorAsync(int numberTake, List<decimal> tenantGroups, List<int> invoiceGroups, CancellationToken cancellationToken = default)
        {
            //FilterDefinition<MongoInvoice01Entity> filter = Builders<MongoInvoice01Entity>.Filter
            //                                                    .Where(x => x.IsSyncedToCore == 1 &&
            //                                                                x.IsGeneratedNumber == 1 &&
            //                                                                x.IsCreatedContentEmail == 0 &&
            //                                                                x.SerialNo.StartsWith("K") &&
            //                                                                x.SignStatus == (short)SignStatus.DaKy &&
            //                                                                x.TenantGroup == tenantGroup &&
            //                                                                x.InvoiceGroup == invoiceGroup);

            // 1
            //FilterDefinition<MongoInvoice01Entity> filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.IsSyncedToCore, 2);
            //filter &= Builders<MongoInvoice01Entity>.Filter.Eq(x => x.IsGeneratedNumber, 1);
            //filter &= Builders<MongoInvoice01Entity>.Filter.Eq(x => x.IsCreatedContentEmail, 0);
            //filter &= Builders<MongoInvoice01Entity>.Filter.Eq(x => x.SerialNo.StartsWith("K"), true);
            //filter &= Builders<MongoInvoice01Entity>.Filter.Eq(x => x.SignStatus, (short)SignStatus.DaKy);
            //filter &= Builders<MongoInvoice01Entity>.Filter.Eq(x => x.TenantGroup, tenantGroup);
            //filter &= Builders<MongoInvoice01Entity>.Filter.Eq(x => x.InvoiceGroup, invoiceGroup);

            BsonDocument filter = new BsonDocument();

            var grpTenantBsonArray = new BsonArray();
            tenantGroups.ForEach(x => { grpTenantBsonArray.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", grpTenantBsonArray));

            var grpInvBsonArray = new BsonArray();
            invoiceGroups.ForEach(x => { grpInvBsonArray.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", grpInvBsonArray));

            //filter.Add("SerialNo", new BsonRegularExpression("^K.*$"));
            filter.Add("PrefixSerialNo", "K");
            filter.Add("SignStatus", new BsonInt64((short)SignStatus.DaKy));
            filter.Add("IsGeneratedNumber", new BsonInt64(1L));
            filter.Add("IsCreatedContentEmail", new BsonInt64(0L));
            filter.Add("IsSyncedToCore", new BsonInt64(1L));

            BsonDocument sort = new BsonDocument();
            sort.Add("Number", 1);

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = numberTake
            };

            var batch = new List<MongoInvoice01Entity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    batch.AddRange(cursor.Current.ToList());
                }
            }
            return batch;
        }

        public async Task<List<MongoInvoice01Entity>> GetListCreateContentEmailInvoiceHasCodeWithCursorAsync(int numberTake, List<decimal> tenantGroups, List<int> invoiceGroups, CancellationToken cancellationToken = default)
        {
            BsonDocument filter = new BsonDocument();

            var grpTenantBsonArray = new BsonArray();
            tenantGroups.ForEach(x => { grpTenantBsonArray.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument()
                .Add("$in", grpTenantBsonArray));

            var grpInvBsonArray = new BsonArray();
            invoiceGroups.ForEach(x => { grpInvBsonArray.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument()
                .Add("$in", grpInvBsonArray));

            filter.Add("PrefixSerialNo", "C");
            filter.Add("SignStatus", new BsonInt64((short)SignStatus.DaKy));
            filter.Add("IsGeneratedNumber", new BsonInt64(1L));
            filter.Add("IsCreatedContentEmail", new BsonInt64(0L));
            filter.Add("VerificationCode", new BsonDocument().Add("$ne", BsonNull.Value));
            filter.Add("IsSyncedToCore", new BsonInt64(1L));

            //BsonDocument sort = new BsonDocument();
            //sort.Add("Number", 1);

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                //Sort = sort,
                Limit = numberTake
            };

            var batch = new List<MongoInvoice01Entity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    batch.AddRange(cursor.Current.ToList());
                }
            }
            return batch;

            #region
            //BsonDocument filter = new BsonDocument();

            //var grpTenantBsonArray = new BsonArray();
            //tenantGroups.ForEach(x => { grpTenantBsonArray.Add($"{x}"); });
            //filter.Add("TenantGroup", new BsonDocument()
            //    .Add("$in", grpTenantBsonArray));

            //var grpInvBsonArray = new BsonArray();
            //invoiceGroups.ForEach(x => { grpInvBsonArray.Add(x); });
            //filter.Add("InvoiceGroup", new BsonDocument()
            //    .Add("$in", grpInvBsonArray));

            //filter.Add("SerialNo", new BsonRegularExpression("^C.*$"));
            //filter.Add("SignStatus", new BsonInt64((short)SignStatus.DaKy));
            //filter.Add("IsGeneratedNumber", new BsonInt64(1L));
            //filter.Add("IsCreatedContentEmail", new BsonInt64(0L));
            //filter.Add("VerificationCode", new BsonDocument().Add("$ne", BsonNull.Value));
            //filter.Add("IsSyncedToCore", new BsonInt64(1L));

            //BsonDocument sort = new BsonDocument();
            //sort.Add("Number", 1);

            //var options = new FindOptions<MongoInvoice01Entity>()
            //{
            //    Sort = sort,
            //    Limit = numberTake
            //};

            //var batch = new List<MongoInvoice01Entity>();
            //using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            //{
            //    while (await cursor.MoveNextAsync())
            //    {
            //        batch.AddRange(cursor.Current.ToList());
            //    }
            //}
            //return batch;
            #endregion
        }

        public async Task<bool> AnyCreateContentEmailInvoiceNoCodeAsync(List<decimal> tenantGroups, List<int> invoiceGroups, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument();

            var tenantsGroupArr = new BsonArray();
            tenantGroups.ForEach(x => { tenantsGroupArr.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument().Add("$in", tenantsGroupArr));

            var invoicesGroupsArr = new BsonArray();
            invoiceGroups.ForEach(x => { invoicesGroupsArr.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument().Add("$in", invoicesGroupsArr));

            filter
                //.Add("SerialNo", new BsonRegularExpression("^K.*$"))
                .Add("PrefixSerialNo", "K")
                .Add("SignStatus", new BsonInt64((short)SignStatus.DaKy))
                .Add("IsGeneratedNumber", new BsonInt64(1L))
                .Add("IsCreatedContentEmail", new BsonInt64(0L))
                .Add("IsSyncedToCore", new BsonInt64(1L));

            return (await (await GetCollectionAsync()).FindAsync(filter, null, cancellationToken)).Any();


            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => x.IsSyncedToCore == 1 &&
            //                x.IsGeneratedNumber == 1 &&
            //                x.IsCreatedContentEmail == 0 &&
            //                x.SerialNo.StartsWith("K") &&
            //                x.SignStatus == (short)SignStatus.DaKy &&
            //                tenantGroups.Contains(x.TenantGroup) &&
            //                invoiceGroups.Contains(x.InvoiceGroup))
            //    .As<IMongoQueryable<MongoInvoice01Entity>>()
            //    .AnyAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<bool> AnyCreateContentEmailInvoiceHasCodeAsync(List<decimal> tenantGroups, List<int> invoiceGroups, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument();

            var tenantGroupsArr = new BsonArray();
            tenantGroups.ForEach(x => { tenantGroupsArr.Add($"{x}"); });
            filter.Add("TenantGroup", new BsonDocument().Add("$in", tenantGroupsArr));

            var invoicesGroupArr = new BsonArray();
            invoiceGroups.ForEach(x => { invoicesGroupArr.Add(x); });
            filter.Add("InvoiceGroup", new BsonDocument().Add("$in", invoicesGroupArr));

            filter
                .Add("PrefixSerialNo", "C")
                .Add("SignStatus", new BsonInt64((short)SignStatus.DaKy))
                .Add("IsGeneratedNumber", new BsonInt64(1L))
                .Add("IsCreatedContentEmail", new BsonInt64(0L))
                .Add("VerificationCode", new BsonDocument().Add("$ne", BsonNull.Value))
                .Add("IsSyncedToCore", new BsonInt64(1L));

            return (await (await GetCollectionAsync()).FindAsync(filter, null, cancellationToken)).Any();

            #region
            //var filter = new BsonDocument();

            //var tenantGroupsArr = new BsonArray();
            //tenantGroups.ForEach(x => { tenantGroupsArr.Add($"{x}"); });
            //filter.Add("TenantGroup", new BsonDocument().Add("$in", tenantGroupsArr));

            //var invoicesGroupArr = new BsonArray();
            //invoiceGroups.ForEach(x => { invoicesGroupArr.Add(x); });
            //filter.Add("InvoiceGroup", new BsonDocument().Add("$in", invoicesGroupArr));

            //filter
            //    .Add("SignStatus", new BsonInt64((short)SignStatus.DaKy))
            //    .Add("IsGeneratedNumber", new BsonInt64(1L))
            //    .Add("IsCreatedContentEmail", new BsonInt64(0L))
            //    .Add("SerialNo", new BsonRegularExpression("^C.*$"))
            //    .Add("IsSyncedToCore", new BsonInt64(1L))
            //    .Add("VerificationCode", new BsonDocument().Add("$ne", BsonNull.Value));

            //return (await (await GetCollectionAsync()).FindAsync(filter, null, cancellationToken)).Any();
            #endregion
        }

        public async Task UpdateManyIsSyncedToElasticSearchAsync(List<long> ids, short isSyncedToElasticSearch, short currentSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.IsSyncedToElasticSearch == currentSyncedToElasticSearch && ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManyIsSyncedToElasticSearchByLessThanStatusAsync(List<long> ids, short isSyncedToElasticSearch, short lessThanSyncedToElasticSearchStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id) && item.IsSyncedToElasticSearch < lessThanSyncedToElasticSearchStatus);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        /// <summary>
        /// Update trạng thái đồng bộ => TH ký xong nhưng dữ liệu mongo IsSyncToCore bị lỗi
        /// </summary>
        /// <param name="id"></param>
        /// <param name="isSyncedToCore"></param>
        /// <param name="isSyncedToCoreError"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task UpdateIsSyncedToCoreAsync(long id, short isSyncedToCore, short isSyncedToCoreError, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.Id == id);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToCore", isSyncedToCore);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManyStatusCreateContentEmailAsync(List<MongoInvoice01Entity> documents, short statusCreateContentEmail, CancellationToken cancellationToken = default)
        {
            var ids = documents.Select(x => x.Id);
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsCreatedContentEmail", statusCreateContentEmail);

            await (await GetCollectionAsync()).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateOneStatusCreateContentEmailAsync(MongoInvoice01Entity document, short statusCreateContentEmail, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.Id, document.Id);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsCreatedContentEmail", statusCreateContentEmail);

            await (await GetCollectionAsync()).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        //TODO: đổi sang bson
        public async Task<MongoInvoice01Entity> GetInvoicesUnsignedAsync(List<long> templates, Guid tenantId, CancellationToken cancellationToken = default)
        {
            var signStatus = new List<short> { (short)SignStatus.ChoKy.GetHashCode(), (short)SignStatus.KyLoi.GetHashCode() };
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(x =>
                             signStatus.Contains(x.SignStatus)
                             && x.ApproveStatus != ApproveStatus.ChoDuyet.GetHashCode()
                             && x.InvoiceStatus != InvoiceStatus.XoaHuy.GetHashCode()
                             && x.ApproveCancelStatus != ApproveStatus.ChoDuyet.GetHashCode()
                             && !string.IsNullOrEmpty(x.InvoiceNo)
                             && x.IsSyncedToCore == 1
                             && templates.Contains(x.InvoiceTemplateId)
                             && x.TenantId == tenantId,
                        GetCancellationToken(cancellationToken));   
        }

        public async Task<MongoInvoice01Entity> GetInvoiceAsync(Guid tenantId, short templateNo, string serialNo, string invoiceNo, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                    .FirstOrDefaultAsync(x => x.TenantId == tenantId && x.TemplateNo == templateNo && x.SerialNo == serialNo && x.InvoiceNo == invoiceNo,
                        GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> GetListByIdsTemplateAsync(Guid tenantId, List<long> idsTemplate, CancellationToken cancellationToken = default)
        {
            var statusTvan = new List<short> { 0, -1 };

            //// tạm thời hard code status Tvan vì ko add dc reference
            //return await (await GetMongoQueryableAsync(cancellationToken))
            //    .Where(x => x.TenantId == tenantId
            //    && x.SignStatus == (short)SignStatus.DaKy
            //    && idsTemplate.Contains(x.InvoiceTemplateId)
            //    && statusTvan.Contains(x.StatusTvan)
            //    && !x.IsDeclared)
            //    .OrderByDescending(x => x.Id)
            //    .As<IMongoQueryable<MongoInvoice01Entity>>()
            //    .ToListAsync(GetCancellationToken(cancellationToken));

            var byteTenantId = GuidConverter.ToBytes(tenantId, GuidRepresentation.CSharpLegacy);

            var invoiceTemplateIdBsonArray = new BsonArray();
            idsTemplate.ForEach(x => { invoiceTemplateIdBsonArray.Add(x); });

            var statusTvanBsonArray = new BsonArray();
            statusTvan.ForEach(x => { statusTvanBsonArray.Add(x); });

            var filter = new BsonDocument()
            {
                { "TenantId", new BsonBinaryData(byteTenantId, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy)},
                { "SignStatus", new BsonInt64((short)SignStatus.DaKy)},
                { "InvoiceTemplateId", new BsonDocument().Add("$in", invoiceTemplateIdBsonArray)},
                { "StatusTvan", new BsonDocument().Add("$in", statusTvanBsonArray)},
                { "IsDeclared", BsonBoolean.False.Value},
            };

            BsonDocument sort = new BsonDocument();
            sort.Add("Id", -1);

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
            };

            var batch = new List<MongoInvoice01Entity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    batch.AddRange(cursor.Current.ToList());
                }
            }
            return batch;
        }

        // tạm thời hard code status Tvan vì ko add dc reference
        public async Task UpdateManyStatusTvan(List<long> ids, short tvanStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update
                .Set("StatusTvan", tvanStatus)
                .Set("IsSyncedToElasticSearch", (short)SyncElasticSearchStatus.PendingSyncStatusTvanRequest.GetHashCode());

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task<List<MongoInvoice01Entity>> GetListInvoice01ErrorAsync(List<decimal> tenantGroup, List<string> erpIds, List<Guid> batchIdErrors, CancellationToken cancellationToken = default)
        {
            var numberOfRecords = 1000;

            var batchIdErrorsBsonArray = new BsonArray();
            batchIdErrors.ForEach(x =>
            {
                var bytes = GuidConverter.ToBytes(x, GuidRepresentation.CSharpLegacy);
                batchIdErrorsBsonArray.Add(new BsonBinaryData(bytes, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy));
            });

            var tenantGroupBsonArray = new BsonArray();
            tenantGroup.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });

            var erpIdsBsonArray = new BsonArray();
            erpIds.ForEach(x => { erpIdsBsonArray.Add($"{x}"); });

            var filter = new BsonDocument
            {
                {
                    "TenantGroup", new BsonDocument()
                        .Add("$in", tenantGroupBsonArray)
                },
                {
                    "ErpId", new BsonDocument()
                        .Add("$in", erpIdsBsonArray)
                },
                {
                    "BatchId", new BsonDocument()
                        .Add("$in", batchIdErrorsBsonArray)
                },
                {
                    "Source", new BsonDocument()
                        .Add("$ne", new BsonInt64((short)InvoiceSource.Form))
                }
            };

            var sort = new BsonDocument { { "_id", new BsonInt64(-1L) } };
            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = numberOfRecords
            };

            var Invoice01Errors = new List<MongoInvoice01Entity>();

            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                Invoice01Errors.AddRange(cursor.Current.ToList());
            }

            return Invoice01Errors;

            #region old
            //return await (await GetMongoQueryableAsync(cancellationToken))
            //   .Where(x => tenantGroup.Contains(x.TenantGroup) && erpIds.Contains(x.ErpId)
            //   && x.Source != (short)InvoiceSource.Form
            //   && batchIdErrors.Contains(x.BatchId))
            //   .OrderByDescending(x => x.Id)
            //   .Take(numberOfRecords)
            //   .As<IMongoQueryable<MongoInvoice01Entity>>()
            //   .ToListAsync(GetCancellationToken(cancellationToken));
            #endregion
        }

        public async Task<List<MongoInvoice01Entity>> GetListInvoice01IsSyncToCoreAsync(List<string> erpIds, CancellationToken cancellationToken = default)
        {
            var filter = new BsonDocument
            {
                //{"Number", new BsonDocument().Add("$ne", BsonNull.Value)}
            };

            var erpIdBsonArray = new BsonArray();
            erpIds.ForEach(x => { erpIdBsonArray.Add($"{x}"); });
            filter.Add("ErpId", new BsonDocument()
                .Add("$in", erpIdBsonArray));

            var projection = new BsonDocument
            {
                {"TenantId", "$TenantId"},
                {"BatchId", "$BatchId"},
                {"Number", "$Number"},
                {"ErpId", "$ErpId"},
                {"_id", 0}
            };

            var options = new FindOptions<MongoInvoice01Entity>
            {
                Projection = projection
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task<List<MongoInvoice01Entity>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
               .Where(x => !x.IsDeleted)
               .OrderByDescending(x => x.Id)
               .As<IMongoQueryable<MongoInvoice01Entity>>()
               .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task<List<MongoInvoice01Entity>> Invoice01ToSyncVerificationCodeAsync(List<decimal> groups, int take, List<int> invoiceGroups, bool isLargeBatch, CancellationToken cancellationToken = default)
        {
            var tenantGroupBsonArray = new BsonArray();
            groups.ForEach(x => { tenantGroupBsonArray.Add($"{x}"); });

            var invoiceGroupBsonArray = new BsonArray();
            invoiceGroups.ForEach(x => { invoiceGroupBsonArray.Add(x); });

            var filter = new BsonDocument
            {
                {
                    "TotalInvoices", isLargeBatch ? new BsonDocument().Add("$gt", new BsonInt64(1L)) : new BsonInt64(1L)
                },
                {
                    "TenantGroup", new BsonDocument()
                        .Add("$in", tenantGroupBsonArray)
                },
                {
                    "InvoiceGroup", new BsonDocument()
                        .Add("$in", invoiceGroupBsonArray)
                },
                {"IsGeneratedNumber", new BsonInt64(1L)},
                {"IsSyncedToCore", new BsonInt64(1L)},
                {"SignStatus", new BsonInt64(5L)},
                {"IsSyncVerificationCodeTocore", new BsonInt64(0L)},
                {"VerificationCode", new BsonDocument().Add("$ne", BsonNull.Value) }
            };

            //if (isLargeBatch)
            //    filter.Add("TotalInvoices", new BsonDocument().Add("$gt", new BsonInt64(1L)));
            //else
            //    filter.Add("TotalInvoices", new BsonInt64(1L));

            //var sort = new BsonDocument { { "CreationTime", 1 } };
            var options = new FindOptions<MongoInvoice01Entity>()
            {
                //Sort = sort,
                Limit = take
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task<List<MongoInvoice01Entity>> GetByTenantAsync(List<decimal> tenantGroups, int numberInvoicesTake, int numberInvoicesSkip, CancellationToken cancellationToken = default)
        {
            var tenantGroupsArr = new BsonArray();
            tenantGroups.ForEach(x => { tenantGroupsArr.Add($"{x}"); });

            var filter = new BsonDocument
            {
                {
                    "TenantGroup", new BsonDocument().Add("$in", tenantGroupsArr)
                },
                {
                    "SignStatus", new BsonInt64((short)SignStatus.DaKy)
                },
                {
                    "IsSyncedToCore", new BsonInt64(1L)
                },
                {
                    "IsSyncedToElasticSearch", new BsonInt64((short)SyncElasticSearchStatus.Synced)
                },
                //{
                //    "IsSyncVerificationCodeTocore", new BsonInt64(1)
                //}
            };

            //var sort = new BsonDocument { { "CreationTime", new BsonInt64(1L) } };
            var options = new FindOptions<MongoInvoice01Entity>()
            {
                //Sort = sort,
                Skip = numberInvoicesSkip,
                Limit = numberInvoicesTake
            };

            var mongoInvoice01s = new List<MongoInvoice01Entity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    mongoInvoice01s.AddRange(cursor.Current.ToList());
                }
            }

            return mongoInvoice01s;

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //   .Where(x => tenantGroups.Contains(x.TenantGroup) && x.SignStatus == SignStatus.DaKy.GetHashCode() && x.IsSyncedToElasticSearch == SyncElasticSearchStatus.Synced.GetHashCode() && x.IsSyncedToCore == 1)
            //   .OrderBy(x => x.CreationTime)
            //   .Take(numberInvoicesTake)
            //   .As<IMongoQueryable<MongoInvoice01Entity>>()
            //   .ToListAsync(GetCancellationToken(cancellationToken));
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="prefixSerialNo"></param>
        /// <param name="signStatus"></param>
        /// <param name="isSyncToCore"></param>
        /// <param name="isSyncToEs"></param>
        /// <param name="numberInvoicesTake"></param>
        /// <param name="numberInvoicesSkip"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<List<MongoInvoice01Entity>> GetInvoicesAsync(
            string prefixSerialNo,
            short signStatus,
            short isSyncToCore,
            short isSyncToEs,
            bool invoiceHasCode,
            short tvanStatus,
            short isSyncVerificationCodeTocore,
            List<short> mailStatus,
            List<short> syncCatalogtatus,
            int numberInvoicesTake,
            int numberInvoicesSkip,
            CancellationToken cancellationToken = default)
        {
            var mailStatusArr = new BsonArray();
            mailStatus.ForEach(x => { mailStatusArr.Add(x); });

            var syncCatalogStatusArr = new BsonArray();
            syncCatalogtatus.ForEach(x => { syncCatalogStatusArr.Add(x); });

            var filter = new BsonDocument
            {
                {
                    "SignStatus", new BsonInt64(signStatus) // trạng thái ký
                },
                {
                    "IsSyncedToCore", new BsonInt64(isSyncToCore) // trạng thái đồng bộ về core (Oracle)
                },
                {
                    "IsSyncedToElasticSearch", new BsonInt64(isSyncToEs) // trạng thái đồng bộ lên ES
                },
                {
                    "PrefixSerialNo", new BsonString(prefixSerialNo) // K: Không mã, C: Có mã
                },
                {
                    "IsCreatedContentEmail", new BsonDocument().Add("$in", mailStatusArr) // trạng thái gửi mail tự động
                },
                {
                    "IsSyncedCustomerToCore", new BsonDocument().Add("$in", syncCatalogStatusArr) // trạng thái đồng bộ danh mục KH về core
                },
                {
                    "IsSyncedProductToCore", new BsonDocument().Add("$in", syncCatalogStatusArr) // Trạng thái đồng bộ danh mục sản phầm về core
                },
                {
                    "IsSyncedUnitToCore", new BsonDocument().Add("$in", syncCatalogStatusArr) // trạng thái đồng bộ danh mục đơn vị tính về core
                }
            };

            if (invoiceHasCode)
            {
                filter.Add("StatusTvan", new BsonInt64(tvanStatus));
                filter.Add("VerificationCode", new BsonDocument().Add("$ne", BsonNull.Value));
                filter.Add("IsSyncVerificationCodeTocore", new BsonInt64(isSyncVerificationCodeTocore));
            }
            //else
            //{
            //    filter.Add("StatusTvan", new BsonInt64(tvanStatus));
            //    filter.Add("IsDeclared", BsonBoolean.True.Value);
            //}

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Skip = numberInvoicesSkip,
                Limit = numberInvoicesTake
            };

            var mongoInvoice01s = new List<MongoInvoice01Entity>();
            using (var cursor = await (await GetCollectionAsync()).FindAsync(filter, options, cancellationToken))
            {
                while (await cursor.MoveNextAsync())
                {
                    mongoInvoice01s.AddRange(cursor.Current.ToList());
                }
            }

            return mongoInvoice01s;
        }


        [Obsolete]
        public async Task<long> CountByBatchIdAsync(Guid batchId, CancellationToken cancellationToken = default)
        {
            var bytes = GuidConverter.ToBytes(batchId, GuidRepresentation.CSharpLegacy);
            var filter = new BsonDocument
            {
                {
                    "BatchId", new BsonBinaryData(bytes, BsonBinarySubType.UuidLegacy, GuidRepresentation.CSharpLegacy)
                }
            };

            var collecttion = await GetCollectionAsync();
            return await collecttion.CountAsync(filter, null, cancellationToken);

            //return await (await GetMongoQueryableAsync(cancellationToken))
            //   .Where(x => x.BatchId == batchId)
            //   .As<IMongoQueryable<MongoInvoice01Entity>>()
            //   .CountAsync(GetCancellationToken(cancellationToken));
        }

        public async Task UpdateManySyncUnitStatusAsync(List<long> ids, short syncUnitStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedUnitToCore", syncUnitStatus);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManySyncProductStatusAsync(List<long> ids, short syncProductStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedProductToCore", syncProductStatus);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManySyncCustomerToCoreStatusAsync(List<long> ids, short syncCustomerStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedCustomerToCore", syncCustomerStatus);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateSyncVerificationCodeToEsAsync(long id, string verificationCode, short statusTvan, short pendingSyncStatusTvanResponse, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.Id == id && item.VerificationCode != verificationCode);
            var update = Builders<MongoInvoice01Entity>.Update.Set("VerificationCode", verificationCode)
                                                              .Set("StatusTvan", statusTvan)
                                                              .Set("IsSyncedToElasticSearch", pendingSyncStatusTvanResponse);

            await (await GetCollectionAsync()).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateIsSyncVerificationCodeToCoreAsync(long id, short isSyncVerificationCodeTocore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.Id == id);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncVerificationCodeTocore", isSyncVerificationCodeTocore);

            await (await GetCollectionAsync()).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task<List<MongoInvoice01Entity>> GetListSyncVerificationCodeAsync(int take, int skip, CancellationToken cancellationToken = default)
        {
            //var filter = new BsonDocument
            //{
            //    { "SignStatus", new BsonInt64(5L)},
            //    { "SerialNo", new BsonRegularExpression("^C.*$") },
            //    { "VerificationCode",  BsonNull.Value }
            //};

            var filter = new BsonDocument
            {
                { "SignStatus", new BsonInt64(5L)},
                { "PrefixSerialNo", "C" },
                { "VerificationCode",  BsonNull.Value }
            };

            var sort = new BsonDocument { { "SellerSignedTime", -1 } };

            var options = new FindOptions<MongoInvoice01Entity>()
            {
                Sort = sort,
                Limit = take,
                Skip = skip
            };

            var batch = new List<MongoInvoice01Entity>();
            using var cursor = await (await GetCollectionAsync(cancellationToken)).FindAsync(filter, options, cancellationToken);
            while (await cursor.MoveNextAsync(cancellationToken))
            {
                batch.AddRange(cursor.Current.ToList());
            }

            return batch;
        }

        public async Task UpdateDeclaresStatusTvan(List<long> ids, short isDeclared, short statusTvan, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update
                .Set("IsDeclared", isDeclared)
                .Set("StatusTvan", statusTvan)
                .Set("IsSyncedToElasticSearch", 2);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        /// <summary>
        /// Refactor
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="isDeclared"></param>
        /// <param name="statusTvan"></param>
        /// <param name="syncElasticSearchStatus"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task UpdateDeclaresStatusTvanV2(List<long> ids, short isDeclared, short statusTvan, short syncElasticSearchStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update
                .Set("IsDeclared", isDeclared)
                .Set("StatusTvan", statusTvan)
                .Set("IsSyncedToElasticSearch", syncElasticSearchStatus);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateTraceRemoveAsync(List<MongoInvoice01Entity> invoiceMongoUpdates, CancellationToken cancellationToken = default)
        {
            foreach (var invoice in invoiceMongoUpdates)
            {
                var filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.Id, invoice.Id);
                var update = Builders<MongoInvoice01Entity>.Update
                    .Set("IsSyncedToCore", invoice.IsSyncedToCore)
                    .Set("VerificationCode", invoice.VerificationCode)
                    .Set("StatusTvan", invoice.StatusTvan)
                    .Set("IsSyncedToElasticSearch", invoice.IsSyncedToElasticSearch);

                await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
            }
        }

        public async Task UpdateIsSyncedToCoreAsync(List<long> ids, short isSyncedToCore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update
                .Set("IsSyncedToCore", isSyncedToCore);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }
        
        public async Task UpdateInvoiceStatusGocAsync(List<long> ids, short invoiceStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.InvoiceReference.InvoiceReferenceId, ids);
            var update = Builders<MongoInvoice01Entity>.Update.Set("InvoiceStatus", invoiceStatus);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManySyncSignedToCoreAsync(List<long> ids, int oldSyncSignedToCore, int newSyncSignedToCore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id) && item.IsSyncSignTocore == oldSyncSignedToCore);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncSignTocore", newSyncSignedToCore);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateSyncSignedToCoreAsync(long id, int syncSignedToCore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.Id == id);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncSignTocore", syncSignedToCore);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateSignStatusToCoreAsync(long id, short isSyncSignTocoreStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.Id == id);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncSignTocore", isSyncSignTocoreStatus);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateIsSyncedToElasticSearchAsync(long id, short isSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => item.Id == id);
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManyIsSyncedToElasticSearchAsync(List<long> ids, short isSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }


        public async Task UpdateIsSyncedToElasticSearchAsync(List<long> ids, short isSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }


        public async Task UpdateApproveCancelStatusAsync(List<long> ids, short invoiceStatus, DateTime? approvedCancelTime, Guid? approvedCancelId, string fullNameApproverCancel, short approveCancelStatus, short invoiceDeleteSource, short isSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("InvoiceStatus", invoiceStatus)
                                                              .Set("ApprovedCancelTime", approvedCancelTime)
                                                              .Set("ApprovedCancelId", approvedCancelId)
                                                              .Set("FullNameApproverCancel", fullNameApproverCancel)
                                                              .Set("ApproveCancelStatus", approveCancelStatus)
                                                              .Set("InvoiceDeleteSource", invoiceDeleteSource)
                                                              .Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateApproveStatusAsync(List<long> ids, string fullNameApprover, short approveStatus, Guid? approvedId, DateTime? approvedTime, short isSyncedToElasticSearch,  CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("ApproveStatus", approveStatus)
                                                              .Set("ApprovedId", approvedId)
                                                              .Set("ApprovedTime", approvedTime)
                                                              .Set("FullNameApprover", fullNameApprover)
                                                              .Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        /// <summary>
        /// CHỈ DÙNG TRÊN MÔI TRƯỜNG TEST
        /// FAKE CÂP MÃ
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task FakeVerificationCodeAsync(List<long> ids, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("StatusTvan", 3)
                                                              .Set("VerificationCode", OracleExtension.ConvertGuidToRaw(Guid.NewGuid()))
                                                              .Set("IsSyncVerificationCodeTocore", 0)
                                                              .Set("IsSyncedToElasticSearch", 8);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        /// <summary>
        /// Update InvoiceStatus cho hóa đơn gốc sau khi điều chỉnh định danh/tăng giảm/thay thế
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="invoiceStatus"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task UpdateManyInvoiceStatusAfterAdjustActionAsync(List<long> ids, short invoiceStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("InvoiceStatus", invoiceStatus);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateIsSyncedToElasticSearchAsync(List<long> ids, short isSyncedToElasticSearch, short invoiceStatus, short approveDeleteStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncedToElasticSearch", isSyncedToElasticSearch)
                                                              .Set("InvoiceStatus", invoiceStatus)
                                                              .Set("ApproveDeleteStatus", approveDeleteStatus);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        /// <summary>
        /// Update InvoiceStatus cho hóa đơn gốc sau khi điều chỉnh định danh/tăng giảm/thay thế
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="invoiceStatus"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task UpdateManyInvoiceStatusAfterAdjustActionAsync(List<long> ids, short invoiceStatus, short isSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("InvoiceStatus", invoiceStatus)
                                                              .Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task<List<MongoInvoice01Entity>> GetByIdsAndApproveStatusAsync(List<long> ids, short approveStatus, CancellationToken cancellationToken = default)
        {
            return await (await GetMongoQueryableAsync(cancellationToken))
                            .Where(x => ids.Contains(x.Id) && x.ApproveStatus == approveStatus)
                            .As<IMongoQueryable<MongoInvoice01Entity>>()
                            .ToListAsync(GetCancellationToken(cancellationToken));
        }

        public async Task UpdateApproveStatusAsync(List<long> ids, short approveStatus, short syncElasticSearchStatus, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.In(item => item.Id, ids);
            var update = Builders<MongoInvoice01Entity>.Update
                .Set("ApproveStatus", approveStatus)
                .Set("ApprovedTime", DateTime.Now)
                .Set("IsSyncedToElasticSearch", (short)syncElasticSearchStatus);

            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateManySyncSignedToCoreAsync(List<long> ids, int syncSignedToCore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Where(item => ids.Contains(item.Id));
            var update = Builders<MongoInvoice01Entity>.Update.Set("IsSyncSignTocore", syncSignedToCore);
            await (await GetCollectionAsync(cancellationToken)).UpdateManyAsync(filter, update, null, cancellationToken);
        }

        public async Task<List<MongoInvoice01Entity>> GetListAsync(List<string> taxCodes, List<long> invoiceHeaderIds, DateTime? fromDate, DateTime? toDate, DateTime? invoiceDate, short signStatus)
        {
            return await(await GetMongoQueryableAsync())
                                        .WhereIf(!taxCodes.IsNullOrEmpty(), x => taxCodes.Contains(x.SellerTaxCode))
                                        .WhereIf(!invoiceHeaderIds.IsNullOrEmpty(), x => invoiceHeaderIds.Contains(x.Id))
                                        .WhereIf(fromDate.HasValue, x => x.InvoiceDate >= fromDate)
                                        .WhereIf(toDate.HasValue, x => x.InvoiceDate <= toDate)
                                        .WhereIf(invoiceDate.HasValue, x => x.InvoiceDate == invoiceDate)
                                        .Where(x => x.SignStatus == signStatus)
                                        .As<IMongoQueryable<MongoInvoice01Entity>>()
            .ToListAsync(GetCancellationToken());
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="documents"></param>
        /// <param name="statusCreateContentEmail"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task UpdateInvoieAfterSignAsync(long id,
            short signStatus,
            DateTime sellerSignedTime,
            string sellerFullNameSigned,
            Guid sellerSignedId,
            short isSyncSignTocore,
            short isSyncedToElasticSearch,
            CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.Id, id);
            var update = Builders<MongoInvoice01Entity>.Update
                    .Set("SignStatus", signStatus)
                    .Set("SellerSignedTime", sellerSignedTime)
                    .Set("SellerFullNameSigned", sellerFullNameSigned)
                    .Set("SellerSignedId", sellerSignedId)
                    .Set("IsSyncSignTocore", isSyncSignTocore)
                    .Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateInvoieAfterSignAsync(long id, short signStatus, short isSyncSignTocore, short isSyncedToElasticSearch, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.Id, id);
            var update = Builders<MongoInvoice01Entity>.Update
                    .Set("SignStatus", signStatus)
                    .Set("IsSyncSignTocore", isSyncSignTocore)
                    .Set("IsSyncedToElasticSearch", isSyncedToElasticSearch);
            await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateInvoieIsSyncToCoreAfterSignAsync(long id, short isSyncedToCore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.Id, id);
            var update = Builders<MongoInvoice01Entity>.Update
                    .Set("IsSyncedToCore", isSyncedToCore);
            await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
        }

        public async Task UpdateInvoieAfterSignAsync(long id, short signStatus, short isSyncSignTocore, CancellationToken cancellationToken = default)
        {
            var filter = Builders<MongoInvoice01Entity>.Filter.Eq(x => x.Id, id);
            var update = Builders<MongoInvoice01Entity>.Update
                    .Set("SignStatus", signStatus)
                    .Set("IsSyncSignTocore", isSyncSignTocore);
            await (await GetCollectionAsync(cancellationToken)).UpdateOneAsync(filter, update, null, cancellationToken);
        }

    }
}
