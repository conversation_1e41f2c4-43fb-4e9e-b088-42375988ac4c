using System;
using Core;
using Core.Domain.Entities.Auditing;
using Core.EntityFrameworkCore.Modeling;
using Core.Identity;
using Microsoft.EntityFrameworkCore;

namespace VnisCore.AuthDatabase.Oracle.Domain.Entities
{
    public class LanguageTextEntity : FullAuditedEntity<Guid>
    {
        public string ResourceName { get; set; }
        public string Name { get; set; }
        public string Value { get; set; }
        public Guid LanguageId { get; set; }
        public Guid TenantId { get; set; }
    }

    public static class VnisCoreAuthDatabaseOracleDbContextModeLanguageTextCreatingExtensions
    {
        public static void ConfigureVnisCoreAuthDatabaseOracleLanguageTextEntity(this ModelBuilder builder)
        {
            Check.NotNull(builder, nameof(builder));

            builder.Entity<LanguageTextEntity>(b =>
            {
                b.ToTable(AbpIdentityDbProperties.DbTablePrefix + "LanguageText", AbpIdentityDbProperties.DbSchema);
                b.ConfigureByConvention(); //auto configure for the base class props
                b.Property(x => x.ResourceName).IsRequired().HasMaxLength(500);
                b.Property(x => x.Name).IsRequired().HasMaxLength(500);
            });

        }
    }
}