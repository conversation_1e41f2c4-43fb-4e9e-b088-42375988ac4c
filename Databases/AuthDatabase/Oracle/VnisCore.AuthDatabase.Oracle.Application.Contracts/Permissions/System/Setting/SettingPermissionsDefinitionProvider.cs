using Core.Authorization.Permissions;
using Core.Localization;
using Core.Localization.Resources.AbpLocalization;

namespace VnisCore.AuthDatabase.Oracle.Application.Contracts.Permissions.System.Setting
{
    public class SettingPermissionsDefinitionProvider : PermissionDefinitionProvider
    {
        public override void Define(IPermissionDefinitionContext context)
        {
            SettingTechnical(context);
            SettingBusiness(context);
            SettingIntegrated(context);
        }

        public static void SettingTechnical(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(SettingTechnicalPermissions.GroupName, L("Permission:SettingManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(SettingTechnicalPermissions.Setting.Default, L("Permission:SettingManagement"));
            settingPermission.AddChild(SettingTechnicalPermissions.Setting.UpdateBatch, L("Permission:SettingUpdateBatch"));
        }

        public static void SettingBusiness(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(SettingBusinessPermissions.GroupName, L("Permission:SettingManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(SettingBusinessPermissions.Setting.Default, L("Permission:SettingManagement"));
            settingPermission.AddChild(SettingBusinessPermissions.Setting.UpdateBatch, L("Permission:SettingUpdateBatch"));
        }

        public static void SettingIntegrated(IPermissionDefinitionContext context)
        {
            var settingGroup = context.AddGroup(SettingIntegratedPermissions.GroupName, L("Permission:SettingManagement"));

            if (settingGroup == null) return;

            var settingPermission = settingGroup.AddPermission(SettingIntegratedPermissions.Setting.Default, L("Permission:SettingManagement"));
            settingPermission.AddChild(SettingIntegratedPermissions.Setting.UpdateBatch, L("Permission:SettingUpdateBatch"));
        }

        private static LocalizableString L(string name)
        {
            return LocalizableString.Create<CoreLocalizationResource>(name);
        }
    }
}