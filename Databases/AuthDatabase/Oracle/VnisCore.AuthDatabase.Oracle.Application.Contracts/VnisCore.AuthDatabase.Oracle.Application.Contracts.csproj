<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Framework\Core\Core\Core.csproj" />
    <ProjectReference Include="..\..\..\..\Framework\Modules\Account\Core.Account.Application.Contracts\Core.Account.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\Framework\Modules\FeatureManagement\Core.FeatureManagement.Application.Contracts\Core.FeatureManagement.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\Framework\Modules\Identity\Core.Identity.Application.Contracts\Core.Identity.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\Framework\Modules\PermissionManagement\Core.PermissionManagement.Application.Contracts\Core.PermissionManagement.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\Framework\Modules\SettingManagement\Core.SettingManagement.Application.Contracts\Core.SettingManagement.Application.Contracts.csproj" />
    <ProjectReference Include="..\..\..\..\Framework\Modules\TenantManagement\Core.TenantManagement.Application.Contracts\Core.TenantManagement.Application.Contracts.csproj" />
    <ProjectReference Include="..\VnisCore.AuthDatabase.Oracle.Domain.Shared\VnisCore.AuthDatabase.Oracle.Domain.Shared.csproj" />
  </ItemGroup>
</Project>
